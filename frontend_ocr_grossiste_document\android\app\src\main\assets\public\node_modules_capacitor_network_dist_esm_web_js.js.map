{"version": 3, "file": "node_modules_capacitor_network_dist_esm_web_js.js", "mappings": ";;;;;;;;;;;;;;;;;AAA4C;AAC5C,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAMC,UAAU,GAAGC,MAAM,CAACC,SAAS,CAACF,UAAU,IAC1CC,MAAM,CAACC,SAAS,CAACC,aAAa,IAC9BF,MAAM,CAACC,SAAS,CAACE,gBAAgB;EACrC,IAAIC,MAAM,GAAG,SAAS;EACtB,MAAMC,IAAI,GAAGN,UAAU,GAAGA,UAAU,CAACM,IAAI,IAAIN,UAAU,CAACO,aAAa,GAAG,IAAI;EAC5E,IAAID,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAClC,QAAQA,IAAI;MACR;MACA,KAAK,WAAW;MAChB,KAAK,UAAU;QACXD,MAAM,GAAG,UAAU;QACnB;MACJ,KAAK,MAAM;QACPA,MAAM,GAAG,MAAM;QACf;MACJ,KAAK,UAAU;MACf,KAAK,MAAM;MACX,KAAK,OAAO;QACRA,MAAM,GAAG,MAAM;QACf;MACJ,KAAK,OAAO;MACZ,KAAK,SAAS;QACVA,MAAM,GAAG,SAAS;QAClB;MACJ;MACA,KAAK,SAAS;MACd,KAAK,IAAI;MACT,KAAK,IAAI;QACLA,MAAM,GAAG,UAAU;QACnB;MACJ,KAAK,IAAI;QACLA,MAAM,GAAG,MAAM;QACf;MACJ;QACI;IACR;EACJ;EACA,OAAOA,MAAM;AACjB;AACO,MAAMG,UAAU,SAASV,sDAAS,CAAC;EACtCW,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,YAAY,GAAG,MAAM;MACtB,MAAMC,cAAc,GAAGZ,oBAAoB,CAAC,CAAC;MAC7C,MAAMa,MAAM,GAAG;QACXC,SAAS,EAAE,IAAI;QACfF,cAAc,EAAEA;MACpB,CAAC;MACD,IAAI,CAACG,eAAe,CAAC,qBAAqB,EAAEF,MAAM,CAAC;IACvD,CAAC;IACD,IAAI,CAACG,aAAa,GAAG,MAAM;MACvB,MAAMH,MAAM,GAAG;QACXC,SAAS,EAAE,KAAK;QAChBF,cAAc,EAAE;MACpB,CAAC;MACD,IAAI,CAACG,eAAe,CAAC,qBAAqB,EAAEF,MAAM,CAAC;IACvD,CAAC;IACD,IAAI,OAAOX,MAAM,KAAK,WAAW,EAAE;MAC/BA,MAAM,CAACe,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACN,YAAY,CAAC;MACpDT,MAAM,CAACe,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACD,aAAa,CAAC;IAC1D;EACJ;EACME,SAASA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACd,IAAI,CAAClB,MAAM,CAACC,SAAS,EAAE;QACnB,MAAMgB,KAAI,CAACE,WAAW,CAAC,sDAAsD,CAAC;MAClF;MACA,MAAMP,SAAS,GAAGZ,MAAM,CAACC,SAAS,CAACmB,MAAM;MACzC,MAAMV,cAAc,GAAGZ,oBAAoB,CAAC,CAAC;MAC7C,MAAMa,MAAM,GAAG;QACXC,SAAS;QACTF,cAAc,EAAEE,SAAS,GAAGF,cAAc,GAAG;MACjD,CAAC;MACD,OAAOC,MAAM;IAAC;EAClB;AACJ;AACA,MAAMU,OAAO,GAAG,IAAId,UAAU,CAAC,CAAC", "sources": ["./node_modules/@capacitor/network/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nfunction translatedConnection() {\n    const connection = window.navigator.connection ||\n        window.navigator.mozConnection ||\n        window.navigator.webkitConnection;\n    let result = 'unknown';\n    const type = connection ? connection.type || connection.effectiveType : null;\n    if (type && typeof type === 'string') {\n        switch (type) {\n            // possible type values\n            case 'bluetooth':\n            case 'cellular':\n                result = 'cellular';\n                break;\n            case 'none':\n                result = 'none';\n                break;\n            case 'ethernet':\n            case 'wifi':\n            case 'wimax':\n                result = 'wifi';\n                break;\n            case 'other':\n            case 'unknown':\n                result = 'unknown';\n                break;\n            // possible effectiveType values\n            case 'slow-2g':\n            case '2g':\n            case '3g':\n                result = 'cellular';\n                break;\n            case '4g':\n                result = 'wifi';\n                break;\n            default:\n                break;\n        }\n    }\n    return result;\n}\nexport class NetworkWeb extends WebPlugin {\n    constructor() {\n        super();\n        this.handleOnline = () => {\n            const connectionType = translatedConnection();\n            const status = {\n                connected: true,\n                connectionType: connectionType,\n            };\n            this.notifyListeners('networkStatusChange', status);\n        };\n        this.handleOffline = () => {\n            const status = {\n                connected: false,\n                connectionType: 'none',\n            };\n            this.notifyListeners('networkStatusChange', status);\n        };\n        if (typeof window !== 'undefined') {\n            window.addEventListener('online', this.handleOnline);\n            window.addEventListener('offline', this.handleOffline);\n        }\n    }\n    async getStatus() {\n        if (!window.navigator) {\n            throw this.unavailable('Browser does not support the Network Information API');\n        }\n        const connected = window.navigator.onLine;\n        const connectionType = translatedConnection();\n        const status = {\n            connected,\n            connectionType: connected ? connectionType : 'none',\n        };\n        return status;\n    }\n}\nconst Network = new NetworkWeb();\nexport { Network };\n"], "names": ["WebPlugin", "translatedConnection", "connection", "window", "navigator", "mozConnection", "webkitConnection", "result", "type", "effectiveType", "NetworkWeb", "constructor", "handleOnline", "connectionType", "status", "connected", "notifyListeners", "handleOffline", "addEventListener", "getStatus", "_this", "_asyncToGenerator", "unavailable", "onLine", "Network"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}