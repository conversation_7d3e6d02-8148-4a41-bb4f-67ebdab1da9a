"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_guide_guide_module_ts"],{

/***/ 41442:
/*!***********************************************!*\
  !*** ./src/app/guide/guide-routing.module.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GuidePageRoutingModule: () => (/* binding */ GuidePageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _guide_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./guide.page */ 89668);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _GuidePageRoutingModule;




const routes = [{
  path: '',
  component: _guide_page__WEBPACK_IMPORTED_MODULE_0__.GuidePage
}];
class GuidePageRoutingModule {}
_GuidePageRoutingModule = GuidePageRoutingModule;
_GuidePageRoutingModule.ɵfac = function GuidePageRoutingModule_Factory(t) {
  return new (t || _GuidePageRoutingModule)();
};
_GuidePageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _GuidePageRoutingModule
});
_GuidePageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](GuidePageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 62763:
/*!***************************************!*\
  !*** ./src/app/guide/guide.module.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GuidePageModule: () => (/* binding */ GuidePageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _guide_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./guide-routing.module */ 41442);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _guide_page__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./guide.page */ 89668);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
var _GuidePageModule;




 // Import SharedModule


class GuidePageModule {}
_GuidePageModule = GuidePageModule;
_GuidePageModule.ɵfac = function GuidePageModule_Factory(t) {
  return new (t || _GuidePageModule)();
};
_GuidePageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
  type: _GuidePageModule
});
_GuidePageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _guide_routing_module__WEBPACK_IMPORTED_MODULE_0__.GuidePageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](GuidePageModule, {
    declarations: [_guide_page__WEBPACK_IMPORTED_MODULE_2__.GuidePage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _guide_routing_module__WEBPACK_IMPORTED_MODULE_0__.GuidePageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule]
  });
})();

/***/ }),

/***/ 89668:
/*!*************************************!*\
  !*** ./src/app/guide/guide.page.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GuidePage: () => (/* binding */ GuidePage)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _image_modal_image_modal_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../image-modal/image-modal.component */ 85528);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);

var _GuidePage;




const _c0 = ["swiper"];
const _c1 = ["popover"];
function GuidePage_swiper_slide_6_div_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 16)(1, "img", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function GuidePage_swiper_slide_6_div_12_Template_img_click_1_listener() {
      const item_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r5).$implicit;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r3.openImageModal(item_r6.image));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const item_r6 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵclassMap"](item_r6.class);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("src", item_r6.image, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsanitizeUrl"]);
  }
}
function GuidePage_swiper_slide_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "swiper-slide", 8)(1, "ion-row", 9)(2, "ion-col", 10)(3, "div", 11)(4, "h2");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "div", 12)(7, "img", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function GuidePage_swiper_slide_6_Template_img_click_7_listener() {
      const slide_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r2).$implicit;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r3.openImageModal(slide_r3.image));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "div", 11)(9, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "ion-col", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](12, GuidePage_swiper_slide_6_div_12_Template, 2, 4, "div", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const slide_r3 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](slide_r3.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("src", slide_r3.image, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](slide_r3.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("size", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", slide_r3.images_details);
  }
}
class GuidePage {
  constructor(navCtrl, modalController) {
    this.navCtrl = navCtrl;
    this.modalController = modalController;
    this.slidesData = [{
      image: 'assets/guide/welcome_guide.png',
      title: 'Bienvenue dans le Guide de Numérisation',
      description: 'Suivez ce guide pour capturer vos bons de livraison avec précision. Découvrez les bonnes pratiques et évitez les erreurs courantes.',
      images_details: []
    }, {
      image: 'assets/guide/good_one.jpg',
      title: 'Assurez-vous de l\'éclairage',
      description: 'Prenez des photos dans un environnement bien éclairé pour une meilleure qualité d\'image et évitez les ombres en tenant l\'appareil à un angle de 90 degrés par rapport au document.',
      images_details: [{
        image: 'assets/guide/lighting_error_01.JPG',
        class: 'img-details-2'
      }, {
        image: 'assets/guide/lighting_error_02.JPG',
        class: 'img-details-2'
      }]
    }, {
      image: 'assets/guide/good_one.jpg',
      title: 'Redressez les images inclinées',
      description: 'Assurez-vous que le document est bien droit avant de prendre la photo. Une image inclinée peut rendre l\'OCR moins précis.',
      images_details: [{
        image: 'assets/guide/rotated_error_01.png',
        class: 'img-details-2'
      }, {
        image: 'assets/guide/rotated_error_02.jpg',
        class: 'img-details-2'
      }]
    }, {
      image: 'assets/guide/good_one.jpg',
      title: 'Maintenez le document à plat',
      description: 'Placez le document sur une surface plane et assurez-vous qu\'il est bien aligné pour éviter les distorsions.',
      images_details: [{
        image: 'assets/guide/skew_error_01.jpg',
        class: 'img-details-2'
      }, {
        image: 'assets/guide/skew_error_02.JPG',
        class: 'img-details-2'
      }]
    }, {
      image: 'assets/guide/good_one.jpg',
      title: 'Lissez le papier froissé',
      description: 'Si le papier est froissé, essayez de le lisser autant que possible avant de prendre la photo pour améliorer la qualité de l\'image.',
      images_details: [{
        image: 'assets/guide/wrinkled_error_01.JPG',
        class: 'img-details-2'
      }, {
        image: 'assets/guide/wrinkled_error_02.JPG',
        class: 'img-details-2'
      }]
    }, {
      image: 'assets/guide/good_one_quality.jpg',
      title: 'Vérifiez la mise au point',
      description: 'Assurez-vous que le document est bien mis au point avant de prendre la photo pour des résultats clairs et lisibles.',
      images_details: [{
        image: 'assets/guide/quality_error_01.jpg',
        class: 'img-details-2'
      }, {
        image: 'assets/guide/quality_error_02.jpg',
        class: 'img-details-2'
      }]
    }, {
      image: 'assets/guide/good_one_quality.jpg',
      title: 'Ne rien écrire sur le document',
      description: 'Pour des résultats OCR optimaux, assurez-vous que le document ne contient aucune écriture manuelle avant la numérisation.',
      images_details: [{
        image: 'assets/guide/pen_error_01.JPG',
        class: 'img-details-2'
      }, {
        image: 'assets/guide/pen_error_02.JPG',
        class: 'img-details-2'
      }]
    }];
  }
  ngAfterViewInit() {
    if (this.swiper && this.swiper.nativeElement) {
      const swiperInstance = this.swiper.nativeElement.swiper;
    }
  }
  openImageModal(imageSrc) {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const modal = yield _this.modalController.create({
        component: _image_modal_image_modal_component__WEBPACK_IMPORTED_MODULE_1__.ImageModalComponent,
        cssClass: 'guide-modal',
        componentProps: {
          imageSrc: imageSrc
        }
      });
      return yield modal.present();
    })();
  }
  skip() {
    this.navCtrl.navigateRoot('/scan-bl');
  }
  next() {
    if (this.swiper && this.swiper.nativeElement) {
      const swiperInstance = this.swiper.nativeElement.swiper;
      const currentIndex = swiperInstance.activeIndex;
      const totalSlides = swiperInstance.slides.length - 1;
      if (currentIndex === totalSlides) {
        // Navigate to 'scan-bl' page if it's the last slide
        this.skip();
      } else {
        // Otherwise, navigate to the next slide
        swiperInstance.slideNext();
      }
    }
  }
}
_GuidePage = GuidePage;
_GuidePage.ɵfac = function GuidePage_Factory(t) {
  return new (t || _GuidePage)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_3__.NavController), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_4__.ModalController));
};
_GuidePage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
  type: _GuidePage,
  selectors: [["app-guide"]],
  viewQuery: function GuidePage_Query(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵviewQuery"](_c0, 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵviewQuery"](_c1, 7);
    }
    if (rf & 2) {
      let _t;
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵloadQuery"]()) && (ctx.swiper = _t.first);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵloadQuery"]()) && (ctx.popover = _t.first);
    }
  },
  decls: 13,
  vars: 1,
  consts: [["swiper", ""], [1, "fix-wrapper"], [1, "onboarding-wrapper"], ["pagination", "true"], ["class", "test", 4, "ngFor", "ngForOf"], [1, "buttons_nav"], ["fill", "solid", 1, "next-button", 3, "click"], ["slot", "end", "name", "arrow-forward-outline"], [1, "test"], [1, "img-details-content"], ["size", "12", 1, "slide-content"], [1, "content-slide"], ["data-icon", "\u2705", 1, "image-wrapper"], [1, "slide-image", 3, "click", "src"], [1, "img-details-col", 3, "size"], ["class", "image-wrapper", "data-icon", "\u274C", 4, "ngFor", "ngForOf"], ["data-icon", "\u274C", 1, "image-wrapper"], [3, "click", "src"]],
  template: function GuidePage_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 1);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "ion-header");
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](2, "ion-content")(3, "div", 2)(4, "swiper-container", 3, 0);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](6, GuidePage_swiper_slide_6_Template, 13, 5, "swiper-slide", 4);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "ion-footer")(8, "div", 5)(9, "ion-button", 6);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function GuidePage_Template_ion_button_click_9_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx.next());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "span");
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](11, "SUIVANT");
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](12, "ion-icon", 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.slidesData);
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _ionic_angular__WEBPACK_IMPORTED_MODULE_4__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_4__.IonCol, _ionic_angular__WEBPACK_IMPORTED_MODULE_4__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_4__.IonFooter, _ionic_angular__WEBPACK_IMPORTED_MODULE_4__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_4__.IonIcon, _ionic_angular__WEBPACK_IMPORTED_MODULE_4__.IonRow],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@charset \"UTF-8\";\n*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\n.footer-md[_ngcontent-%COMP%], .header-md[_ngcontent-%COMP%] {\n  box-shadow: none;\n}\n\n.fix-wrapper[_ngcontent-%COMP%] {\n  --ion-background-color: transparent;\n  display: flex;\n  flex-direction: column;\n  background: url(\"/assets/onboarding_images/bg_onboarding.png\") no-repeat;\n  background-size: cover;\n  height: 100dvh;\n}\n\n.slide-content[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 20px;\n}\n\n.slide-image[_ngcontent-%COMP%] {\n  width: 60%;\n  border-radius: 15px;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  position: relative;\n  width: 100%;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #2f4fcd;\n}\n\nion-button[_ngcontent-%COMP%] {\n  margin: 5px;\n}\n\nion-button.next-button[_ngcontent-%COMP%] {\n  --background: #2f4fcd;\n  --background-activated: #1e3aa8;\n  --border-radius: 8px;\n  --color: #fff;\n  width: 100%;\n}\n\nion-button.skip-button[_ngcontent-%COMP%] {\n  --color: #2f4fcd;\n  width: 20%;\n}\n\n.pagination[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  margin: 20px 0;\n  gap: 5px;\n}\n\n.pagination[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  display: block;\n  width: 10px;\n  height: 10px;\n  background-color: #ccc;\n  border-radius: 50%;\n}\n\n.pagination[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%] {\n  background-color: #2f4fcd;\n}\n\n.buttons_nav[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  gap: 5px;\n}\n\n\n\nswiper-container[_ngcontent-%COMP%] {\n  height: 100%;\n  --swiper-pagination-bullet-inactive-color: var(--ion-color-step-200, #cccccc);\n  --swiper-pagination-color: var(--ion-color-primary, #2f4fcd);\n  --swiper-pagination-progressbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.25);\n  --swiper-scrollbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.1);\n  --swiper-scrollbar-drag-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.5);\n}\n\nswiper-slide[_ngcontent-%COMP%] {\n  display: flex;\n  position: relative;\n  flex-direction: column;\n  flex-shrink: 0;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  font-size: 18px;\n  text-align: center;\n  box-sizing: border-box;\n  height: calc(100vh - 60px);\n  overflow-y: auto;\n  scrollbar-width: none !important;\n  -ms-overflow-style: none !important;\n}\n\nswiper-slide[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%] {\n  height: 100%;\n  padding-bottom: 20px;\n}\n\n  swiper-slide ion-row ion-col {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n  swiper-slide ion-col {\n  display: flex !important;\n  flex-direction: column;\n  justify-content: space-evenly;\n  align-items: center;\n}\n\n  swiper-slide img {\n  width: auto;\n  max-width: 100%;\n  height: auto;\n  max-height: 100%;\n}\n\n  swiper-slide .content-slide {\n  color: #2b2b2b;\n  text-align: center;\n  padding: 0 15px;\n}\n\n  swiper-slide .content-slide h2 {\n  font-size: 1.4rem;\n  font-weight: bold;\n  margin: 25px 0;\n}\n\n  swiper-slide .content-slide p {\n  letter-spacing: 0.9px;\n  font-size: 16px;\n  padding-right: 20px;\n}\n\n  swiper-slide .img-details-col {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n  swiper-slide .img-details-col .image-wrapper {\n  margin: 0 20px;\n}\n  swiper-slide .img-details-col img {\n  margin: 0 20px;\n  border-radius: 15px;\n  width: 90%;\n}\n\n.next-button[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  flex-direction: row;\n  align-items: center;\n}\n\n.skip-button[_ngcontent-%COMP%] {\n  font-size: 16px !important;\n  font-weight: bold;\n  opacity: 0.7;\n}\n\nswiper-container[_ngcontent-%COMP%]::part(bullet-active) {\n  width: 70px !important;\n  height: 5px !important;\n  border-radius: 8px !important;\n}\n\nswiper-container[_ngcontent-%COMP%]::part(pagination) {\n  top: var(--swiper-pagination-top, 97%) !important;\n}\n\n\n\n\n\n.img-details-content[_ngcontent-%COMP%], .img-details-row[_ngcontent-%COMP%] {\n  position: relative;\n}\n\n.image-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  flex-direction: column;\n}\n\n.img-details-content[_ngcontent-%COMP%]   .image-wrapper[_ngcontent-%COMP%]::before {\n  content: attr(data-icon);\n  position: absolute;\n  width: 45px;\n  height: 45px;\n  top: 0;\n  right: 20%;\n  border-radius: 15px;\n  font-size: 2rem;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.img-details-row[_ngcontent-%COMP%]   .image-wrapper[_ngcontent-%COMP%]::before {\n  content: attr(data-icon);\n  position: absolute;\n  width: 40px;\n  height: 40px;\n  top: 0;\n  right: 10px;\n  border-radius: 15px;\n  font-size: 1.8rem;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n  swiper-slide:first-child .content-slide h2 {\n  font-size: 1.6rem;\n  background: linear-gradient(45deg, #2f4fcd, #1e3aa8);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  margin-bottom: 30px;\n}\n  swiper-slide:first-child .content-slide p {\n  font-size: 18px;\n  line-height: 1.6;\n  color: #4a4a4a;\n  max-width: 90%;\n  margin: 0 auto;\n}\n  swiper-slide:first-child .image-wrapper {\n  margin: 40px 0;\n}\n  swiper-slide:first-child .image-wrapper img {\n  width: 100%;\n  transition: transform 0.3s ease;\n}\n  swiper-slide:first-child .image-wrapper img:hover {\n  transform: scale(1.02);\n}\n\n  swiper-slide:first-child .image-wrapper::before {\n  display: none !important;\n}\n\n  swiper-slide:not(:first-child) .image-wrapper::after {\n  content: \"\u2922\";\n  position: absolute;\n  bottom: 20%;\n  left: 70%;\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: white;\n  font-size: 20px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ })

}]);
//# sourceMappingURL=src_app_guide_guide_module_ts.js.map