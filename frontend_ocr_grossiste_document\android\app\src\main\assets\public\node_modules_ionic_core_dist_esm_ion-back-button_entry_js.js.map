{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-back-button_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2F;AACxB;AAC2B;AAClB;AACF;AAE1E,MAAMmB,gBAAgB,GAAG,o0IAAo0I;AAC71I,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,eAAe,GAAG,guJAAguJ;AACxvJ,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,UAAU,GAAG,MAAM;EACrBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjBzB,qDAAgB,CAAC,IAAI,EAAEwB,OAAO,CAAC;IAC/B,IAAI,CAACE,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO;MAAA,IAAAC,IAAA,GAAAC,6OAAA,CAAG,WAAOC,EAAE,EAAK;QACzB,MAAMC,GAAG,GAAGN,KAAI,CAACO,EAAE,CAACC,OAAO,CAAC,SAAS,CAAC;QACtCH,EAAE,CAACI,cAAc,CAAC,CAAC;QACnB,IAAIH,GAAG,WAAWA,GAAG,CAACI,SAAS,CAAC,CAAC,CAAC,EAAE;UAChC,OAAOJ,GAAG,CAACK,GAAG,CAAC;YAAEC,gBAAgB,EAAEZ,KAAI,CAACa,eAAe;YAAEC,UAAU,EAAE;UAAK,CAAC,CAAC;QAChF;QACA,OAAO/B,qDAAO,CAACiB,KAAI,CAACe,WAAW,EAAEV,EAAE,EAAE,MAAM,EAAEL,KAAI,CAACa,eAAe,CAAC;MACtE,CAAC;MAAA,iBAAAG,EAAA;QAAA,OAAAb,IAAA,CAAAc,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACL,WAAW,GAAGK,SAAS;IAC5B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGF,SAAS;IACrB,IAAI,CAACG,IAAI,GAAGH,SAAS;IACrB,IAAI,CAACI,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACX,eAAe,GAAGO,SAAS;EACpC;EACAK,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACxB,mBAAmB,GAAGpB,uDAAqB,CAAC,IAAI,CAAC0B,EAAE,CAAC;IACzD,IAAI,IAAI,CAACQ,WAAW,KAAKK,SAAS,EAAE;MAChC,IAAI,CAACL,WAAW,GAAGzB,wDAAM,CAACoC,GAAG,CAAC,uBAAuB,CAAC;IAC1D;EACJ;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,MAAML,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAIA,IAAI,IAAI,IAAI,EAAE;MACd;MACA,OAAOA,IAAI;IACf;IACA,IAAI9B,4DAAU,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;MAC5B;MACA,OAAOF,wDAAM,CAACoC,GAAG,CAAC,gBAAgB,EAAEvC,iDAAW,CAAC;IACpD;IACA;IACA,OAAOG,wDAAM,CAACoC,GAAG,CAAC,gBAAgB,EAAErC,iDAAc,CAAC;EACvD;EACA,IAAIuC,cAAcA,CAAA,EAAG;IACjB,MAAMC,qBAAqB,GAAGrC,4DAAU,CAAC,IAAI,CAAC,KAAK,KAAK,GAAG,MAAM,GAAG,IAAI;IACxE,OAAO,IAAI,CAAC+B,IAAI,IAAI,IAAI,GAAG,IAAI,CAACA,IAAI,GAAGjC,wDAAM,CAACoC,GAAG,CAAC,gBAAgB,EAAEG,qBAAqB,CAAC;EAC9F;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACH,cAAc,IAAI,CAAC,IAAI,CAACC,cAAc;EACtD;EACA,IAAIG,UAAUA,CAAA,EAAG;IACb;IACA;IACA,IAAI,IAAI,CAACD,WAAW,EAAE;MAClB,OAAO,WAAW;IACtB;IACA,OAAO,SAAS;EACpB;EACAE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEb,KAAK;MAAEJ,WAAW;MAAEM,QAAQ;MAAEG,IAAI;MAAEM,WAAW;MAAEH,cAAc;MAAEC,cAAc;MAAEN,IAAI;MAAErB;IAAqB,CAAC,GAAG,IAAI;IAC5H,MAAMgC,cAAc,GAAGlB,WAAW,KAAKK,SAAS;IAChD,MAAMc,IAAI,GAAG1C,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM2C,SAAS,GAAGlC,mBAAmB,CAAC,YAAY,CAAC,IAAI2B,cAAc,IAAI,MAAM;IAC/E,OAAQpD,qDAAC,CAACE,iDAAI,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAElC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEmC,KAAK,EAAEpD,qDAAkB,CAACkC,KAAK,EAAE;QACnH,CAACe,IAAI,GAAG,IAAI;QACZI,MAAM,EAAE,IAAI;QAAE;QACd,sBAAsB,EAAEjB,QAAQ;QAChC,2BAA2B,EAAES,WAAW;QACxC,YAAY,EAAE5C,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACqB,EAAE,CAAC;QACjD,kBAAkB,EAAErB,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACqB,EAAE,CAAC;QAC9D,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,IAAI;QACrB,kBAAkB,EAAE0B;MACxB,CAAC;IAAE,CAAC,EAAEzD,qDAAC,CAAC,QAAQ,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAEZ,IAAI,EAAEA,IAAI;MAAEH,QAAQ,EAAEA,QAAQ;MAAEgB,KAAK,EAAE,eAAe;MAAEE,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAEJ;IAAU,CAAC,EAAE3D,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAe,CAAC,EAAEV,cAAc,IAAKnD,qDAAC,CAAC,UAAU,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAEG,IAAI,EAAE,MAAM;MAAEjB,IAAI,EAAEK,cAAc;MAAE,aAAa,EAAE,MAAM;MAAEa,IAAI,EAAE,KAAK;MAAE,UAAU,EAAElB,IAAI,KAAKF;IAAU,CAAC,CAAE,EAAEQ,cAAc,IAAKpD,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAEG,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE,MAAM;MAAEF,KAAK,EAAE;IAAc,CAAC,EAAET,cAAc,CAAE,CAAC,EAAEM,IAAI,KAAK,IAAI,IAAI1D,qDAAC,CAAC,mBAAmB,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAEZ,IAAI,EAAE,IAAI,CAACO;IAAW,CAAC,CAAC,CAAC,CAAC;EAC3tB;EACA,IAAIxB,EAAEA,CAAA,EAAG;IAAE,OAAO3B,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDiB,UAAU,CAAC4C,KAAK,GAAG;EACfC,GAAG,EAAEhD,sBAAsB;EAC3BiD,EAAE,EAAE/C;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-back-button.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host, i as getElement } from './index-c71c5417.js';\nimport { i as inheritAriaAttributes } from './helpers-da915de8.js';\nimport { o as openURL, c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { c as chevronBack, a as arrowBackSharp } from './index-e2cf2ceb.js';\nimport { c as config, b as getIonMode } from './ionic-global-b9c0d1da.js';\n\nconst backButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--color-hover:currentColor;--icon-margin-top:0;--icon-margin-bottom:0;--icon-padding-top:0;--icon-padding-end:0;--icon-padding-bottom:0;--icon-padding-start:0;--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--min-width:auto;--min-height:auto;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--opacity:1;--ripple-color:currentColor;--transition:background-color, opacity 100ms linear;display:none;min-width:var(--min-width);min-height:var(--min-height);color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-font-kerning:none;font-kerning:none}ion-ripple-effect{color:var(--ripple-color)}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.show-back-button){display:block}:host(.back-button-disabled){cursor:default;opacity:0.5;pointer-events:none}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;opacity:var(--opacity);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}ion-icon{-webkit-padding-start:var(--icon-padding-start);padding-inline-start:var(--icon-padding-start);-webkit-padding-end:var(--icon-padding-end);padding-inline-end:var(--icon-padding-end);padding-top:var(--icon-padding-top);padding-bottom:var(--icon-padding-bottom);-webkit-margin-start:var(--icon-margin-start);margin-inline-start:var(--icon-margin-start);-webkit-margin-end:var(--icon-margin-end);margin-inline-end:var(--icon-margin-end);margin-top:var(--icon-margin-top);margin-bottom:var(--icon-margin-bottom);display:inherit;font-size:var(--icon-font-size);font-weight:var(--icon-font-weight);pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-hover:transparent;--background-hover-opacity:1;--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #0054e9);--icon-margin-end:1px;--icon-margin-start:-4px;--icon-font-size:1.6em;--min-height:32px;font-size:clamp(17px, 1.0625rem, 21.998px)}.button-native{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:visible;z-index:99}:host(.ion-activated) .button-native{opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\nconst IonBackButtonIosStyle0 = backButtonIosCss;\n\nconst backButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--color-hover:currentColor;--icon-margin-top:0;--icon-margin-bottom:0;--icon-padding-top:0;--icon-padding-end:0;--icon-padding-bottom:0;--icon-padding-start:0;--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--min-width:auto;--min-height:auto;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--opacity:1;--ripple-color:currentColor;--transition:background-color, opacity 100ms linear;display:none;min-width:var(--min-width);min-height:var(--min-height);color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-font-kerning:none;font-kerning:none}ion-ripple-effect{color:var(--ripple-color)}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.show-back-button){display:block}:host(.back-button-disabled){cursor:default;opacity:0.5;pointer-events:none}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;opacity:var(--opacity);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}ion-icon{-webkit-padding-start:var(--icon-padding-start);padding-inline-start:var(--icon-padding-start);-webkit-padding-end:var(--icon-padding-end);padding-inline-end:var(--icon-padding-end);padding-top:var(--icon-padding-top);padding-bottom:var(--icon-padding-bottom);-webkit-margin-start:var(--icon-margin-start);margin-inline-start:var(--icon-margin-start);-webkit-margin-end:var(--icon-margin-end);margin-inline-end:var(--icon-margin-end);margin-top:var(--icon-margin-top);margin-bottom:var(--icon-margin-bottom);display:inherit;font-size:var(--icon-font-size);font-weight:var(--icon-font-weight);pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--border-radius:4px;--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:0.04;--color:currentColor;--icon-margin-end:0;--icon-margin-start:0;--icon-font-size:1.5rem;--icon-font-weight:normal;--min-height:32px;--min-width:44px;--padding-start:12px;--padding-end:12px;font-size:0.875rem;font-weight:500;text-transform:uppercase}:host(.back-button-has-icon-only){--border-radius:50%;min-width:48px;min-height:48px;aspect-ratio:1/1}.button-native{-webkit-box-shadow:none;box-shadow:none}.button-text{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0}ion-icon{line-height:0.67;text-align:start}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}\";\nconst IonBackButtonMdStyle0 = backButtonMdCss;\n\nconst BackButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inheritedAttributes = {};\n        this.onClick = async (ev) => {\n            const nav = this.el.closest('ion-nav');\n            ev.preventDefault();\n            if (nav && (await nav.canGoBack())) {\n                return nav.pop({ animationBuilder: this.routerAnimation, skipIfBusy: true });\n            }\n            return openURL(this.defaultHref, ev, 'back', this.routerAnimation);\n        };\n        this.color = undefined;\n        this.defaultHref = undefined;\n        this.disabled = false;\n        this.icon = undefined;\n        this.text = undefined;\n        this.type = 'button';\n        this.routerAnimation = undefined;\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n        if (this.defaultHref === undefined) {\n            this.defaultHref = config.get('backButtonDefaultHref');\n        }\n    }\n    get backButtonIcon() {\n        const icon = this.icon;\n        if (icon != null) {\n            // icon is set on the component or by the config\n            return icon;\n        }\n        if (getIonMode(this) === 'ios') {\n            // default ios back button icon\n            return config.get('backButtonIcon', chevronBack);\n        }\n        // default md back button icon\n        return config.get('backButtonIcon', arrowBackSharp);\n    }\n    get backButtonText() {\n        const defaultBackButtonText = getIonMode(this) === 'ios' ? 'Back' : null;\n        return this.text != null ? this.text : config.get('backButtonText', defaultBackButtonText);\n    }\n    get hasIconOnly() {\n        return this.backButtonIcon && !this.backButtonText;\n    }\n    get rippleType() {\n        // If the button only has an icon we use the unbounded\n        // \"circular\" ripple effect\n        if (this.hasIconOnly) {\n            return 'unbounded';\n        }\n        return 'bounded';\n    }\n    render() {\n        const { color, defaultHref, disabled, type, hasIconOnly, backButtonIcon, backButtonText, icon, inheritedAttributes, } = this;\n        const showBackButton = defaultHref !== undefined;\n        const mode = getIonMode(this);\n        const ariaLabel = inheritedAttributes['aria-label'] || backButtonText || 'back';\n        return (h(Host, { key: '8351c93a1812c94c979fb115f07a9ad7b3152188', onClick: this.onClick, class: createColorClasses(color, {\n                [mode]: true,\n                button: true, // ion-buttons target .button\n                'back-button-disabled': disabled,\n                'back-button-has-icon-only': hasIconOnly,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'ion-activatable': true,\n                'ion-focusable': true,\n                'show-back-button': showBackButton,\n            }) }, h(\"button\", { key: '991b8baa784dbfbdf8d3581cbbf6a858ac1f1e6e', type: type, disabled: disabled, class: \"button-native\", part: \"native\", \"aria-label\": ariaLabel }, h(\"span\", { key: '929fcd83a4fdb00cf97ca803f234719171602a5e', class: \"button-inner\" }, backButtonIcon && (h(\"ion-icon\", { key: '0c10d412ae8342d96f221e626b421c9abadac748', part: \"icon\", icon: backButtonIcon, \"aria-hidden\": \"true\", lazy: false, \"flip-rtl\": icon === undefined })), backButtonText && (h(\"span\", { key: '96f505880a80f36ee8b8c78268b450f3629ee613', part: \"text\", \"aria-hidden\": \"true\", class: \"button-text\" }, backButtonText))), mode === 'md' && h(\"ion-ripple-effect\", { key: '0aecf5d415c608069eb1a10043530bd0929b6383', type: this.rippleType }))));\n    }\n    get el() { return getElement(this); }\n};\nBackButton.style = {\n    ios: IonBackButtonIosStyle0,\n    md: IonBackButtonMdStyle0\n};\n\nexport { BackButton as ion_back_button };\n"], "names": ["r", "registerInstance", "h", "f", "Host", "i", "getElement", "inheritAriaAttributes", "o", "openURL", "c", "createColorClasses", "hostContext", "chevronBack", "a", "arrowBackSharp", "config", "b", "getIonMode", "backButtonIosCss", "IonBackButtonIosStyle0", "backButtonMdCss", "IonBackButtonMdStyle0", "BackButton", "constructor", "hostRef", "_this", "inheritedAttributes", "onClick", "_ref", "_asyncToGenerator", "ev", "nav", "el", "closest", "preventDefault", "canGoBack", "pop", "animationBuilder", "routerAnimation", "skipIfBusy", "defaultHref", "_x", "apply", "arguments", "color", "undefined", "disabled", "icon", "text", "type", "componentWillLoad", "get", "backButtonIcon", "backButtonText", "defaultBackButtonText", "hasIconOnly", "rippleType", "render", "showBackButton", "mode", "aria<PERSON><PERSON><PERSON>", "key", "class", "button", "part", "lazy", "style", "ios", "md", "ion_back_button"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}