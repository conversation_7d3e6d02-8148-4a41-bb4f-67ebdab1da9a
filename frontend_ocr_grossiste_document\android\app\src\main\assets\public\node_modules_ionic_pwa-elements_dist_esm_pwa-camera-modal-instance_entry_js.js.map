{"version": 3, "file": "node_modules_ionic_pwa-elements_dist_esm_pwa-camera-modal-instance_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAkG;AAElG,MAAMO,sBAAsB,GAAG,2kBAA2kB;AAE1mB,MAAMC,cAAc,GAAG,MAAM;EAC3BC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACnBV,qDAAgB,CAAC,IAAI,EAAES,OAAO,CAAC;IAC/B,IAAI,CAACE,OAAO,GAAGT,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACU,aAAa,GAAGV,qDAAW,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IAC1D,IAAI,CAACW,WAAW;MAAA,IAAAC,IAAA,GAAAC,6OAAA,CAAG,WAAOC,KAAK,EAAK;QAClCN,KAAI,CAACC,OAAO,CAACM,IAAI,CAACD,KAAK,CAAC;MAC1B,CAAC;MAAA,iBAAAE,EAAA;QAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACC,mBAAmB;MAAA,IAAAC,KAAA,GAAAP,6OAAA,CAAG,WAAOC,KAAK,EAAK;QAC1CN,KAAI,CAACE,aAAa,CAACK,IAAI,CAACD,KAAK,CAAC;MAChC,CAAC;MAAA,iBAAAO,GAAA;QAAA,OAAAD,KAAA,CAAAH,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACI,UAAU,GAAG,MAAM;IACxB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,aAAa,GAAG,iBAAiB;IACtC,IAAI,CAACC,mBAAmB,GAAG,cAAc;EAC3C;EACAC,mBAAmBA,CAACC,CAAC,EAAE;IACrB,IAAIA,CAAC,CAACC,MAAM,KAAK,IAAI,CAACC,EAAE,EAAE;MACxB,IAAI,CAACpB,OAAO,CAACM,IAAI,CAAC,IAAI,CAAC;IACzB;EACF;EACAe,oBAAoBA,CAACH,CAAC,EAAE;IACtBA,CAAC,CAACI,eAAe,CAAC,CAAC;EACrB;EACAC,mBAAmBA,CAACL,CAAC,EAAE;IACrB,IAAIA,CAAC,CAACM,GAAG,KAAK,QAAQ,EAAE;MACtB,IAAI,CAACxB,OAAO,CAACM,IAAI,CAAC,IAAI,CAAC;IACzB;EACF;EACAmB,MAAMA,CAAA,EAAG;IACP,OAAQjC,qDAAC,CAAC,KAAK,EAAE;MAAEkC,KAAK,EAAE,SAAS;MAAEC,OAAO,EAAET,CAAC,IAAI,IAAI,CAACD,mBAAmB,CAACC,CAAC;IAAE,CAAC,EAAE1B,qDAAC,CAAC,KAAK,EAAE;MAAEkC,KAAK,EAAE;IAAU,CAAC,EAAElC,qDAAC,CAAC,YAAY,EAAE;MAAEmC,OAAO,EAAET,CAAC,IAAI,IAAI,CAACG,oBAAoB,CAACH,CAAC,CAAC;MAAEL,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEC,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEZ,WAAW,EAAE,IAAI,CAACA,WAAW;MAAEQ,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAAEM,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAAED,aAAa,EAAE,IAAI,CAACA;IAAc,CAAC,CAAC,CAAC,CAAC;EAC9Y;EACA,IAAIK,EAAEA,CAAA,EAAG;IAAE,OAAO1B,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACDE,cAAc,CAACgC,KAAK,GAAGjC,sBAAsB", "sources": ["./node_modules/@ionic/pwa-elements/dist/esm/pwa-camera-modal-instance.entry.js"], "sourcesContent": ["import { r as registerInstance, c as createEvent, h, g as getElement } from './index-1c5c47b4.js';\n\nconst cameraModalInstanceCss = \":host{z-index:1000;position:fixed;top:0;left:0;width:100%;height:100%;display:-ms-flexbox;display:flex;contain:strict;--inset-width:600px;--inset-height:600px}.wrapper{-ms-flex:1;flex:1;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;background-color:rgba(0, 0, 0, 0.15)}.content{-webkit-box-shadow:0px 0px 5px rgba(0, 0, 0, 0.2);box-shadow:0px 0px 5px rgba(0, 0, 0, 0.2);width:var(--inset-width);height:var(--inset-height);max-height:100%}@media only screen and (max-width: 600px){.content{width:100%;height:100%}}\";\n\nconst PWACameraModal = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onPhoto = createEvent(this, \"onPhoto\", 7);\n    this.noDeviceError = createEvent(this, \"noDeviceError\", 7);\n    this.handlePhoto = async (photo) => {\n      this.onPhoto.emit(photo);\n    };\n    this.handleNoDeviceError = async (photo) => {\n      this.noDeviceError.emit(photo);\n    };\n    this.facingMode = 'user';\n    this.hidePicker = false;\n    this.noDevicesText = 'No camera found';\n    this.noDevicesButtonText = 'Choose image';\n  }\n  handleBackdropClick(e) {\n    if (e.target !== this.el) {\n      this.onPhoto.emit(null);\n    }\n  }\n  handleComponentClick(e) {\n    e.stopPropagation();\n  }\n  handleBackdropKeyUp(e) {\n    if (e.key === \"Escape\") {\n      this.onPhoto.emit(null);\n    }\n  }\n  render() {\n    return (h(\"div\", { class: \"wrapper\", onClick: e => this.handleBackdropClick(e) }, h(\"div\", { class: \"content\" }, h(\"pwa-camera\", { onClick: e => this.handleComponentClick(e), facingMode: this.facingMode, hidePicker: this.hidePicker, handlePhoto: this.handlePhoto, handleNoDeviceError: this.handleNoDeviceError, noDevicesButtonText: this.noDevicesButtonText, noDevicesText: this.noDevicesText }))));\n  }\n  get el() { return getElement(this); }\n};\nPWACameraModal.style = cameraModalInstanceCss;\n\nexport { PWACameraModal as pwa_camera_modal_instance };\n"], "names": ["r", "registerInstance", "c", "createEvent", "h", "g", "getElement", "cameraModalInstanceCss", "PWACameraModal", "constructor", "hostRef", "_this", "onPhoto", "noDeviceError", "handlePhoto", "_ref", "_asyncToGenerator", "photo", "emit", "_x", "apply", "arguments", "handleNoDeviceError", "_ref2", "_x2", "facingMode", "hidePicker", "noDevicesText", "noDevicesButtonText", "handleBackdropClick", "e", "target", "el", "handleComponentClick", "stopPropagation", "handleBackdropKeyUp", "key", "render", "class", "onClick", "style", "pwa_camera_modal_instance"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}