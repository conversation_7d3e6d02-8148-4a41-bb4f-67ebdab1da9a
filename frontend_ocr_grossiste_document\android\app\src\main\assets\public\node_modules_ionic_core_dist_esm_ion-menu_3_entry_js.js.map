{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-menu_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AACnC;AAC6C;AAC5C;AACA;AACiC;AAClD;AACsB;AACN;AACH;AAC1C;AACa;AACb;AACI;AAEjC,MAAMqC,UAAU,GAAG,kxGAAkxG;AACryG,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,SAAS,GAAG,g0GAAg0G;AACl1G,MAAMC,eAAe,GAAGD,SAAS;AAEjC,MAAME,SAAS,GAAG,6BAA6B;AAC/C,MAAMC,QAAQ,GAAG,6BAA6B;AAC9C,MAAMC,gBAAgB,GAAG,gCAAgC;AACzD,MAAMC,eAAe,GAAG,8BAA8B;AACtD,MAAMC,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjB9C,qDAAgB,CAAC,IAAI,EAAE8C,OAAO,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG7C,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAAC8C,YAAY,GAAG9C,qDAAW,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;IACxD,IAAI,CAAC+C,UAAU,GAAG/C,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAACgD,WAAW,GAAGhD,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAACiD,aAAa,GAAGjD,qDAAW,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IAC1D,IAAI,CAACkD,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,OAAO,GAAGpC,8DAAkB,CAACqC,aAAa,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC;IACxE,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAIC,EAAE,IAAK;MACvB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,WAAW,GAAGpD,wDAAmB,CAACqD,QAAQ,CAAC;MACjD,IAAID,WAAW,IAAI,CAACA,WAAW,CAACE,QAAQ,CAAC,IAAI,CAACC,EAAE,CAAC,EAAE;QAC/C;MACJ;MACA,IAAI,CAACC,iBAAiB,CAACL,EAAE,EAAEE,QAAQ,CAAC;IACxC,CAAC;IACD,IAAI,CAACI,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACjD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACkD,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGD,SAAS;IACvB,IAAI,CAACE,IAAI,GAAGF,SAAS;IACrB,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAG,OAAO;IACnB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,EAAE;EAC1B;EACAC,WAAWA,CAACL,IAAI,EAAEM,OAAO,EAAE;IACvB,MAAMC,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIA,SAAS,EAAE;MACX,IAAID,OAAO,KAAKR,SAAS,EAAE;QACvBS,SAAS,CAACC,SAAS,CAACC,MAAM,CAAE,gBAAeH,OAAQ,EAAC,CAAC;MACzD;MACAC,SAAS,CAACC,SAAS,CAACE,GAAG,CAAE,gBAAeV,IAAK,EAAC,CAAC;MAC/CO,SAAS,CAACI,eAAe,CAAC,OAAO,CAAC;IACtC;IACA,IAAI,IAAI,CAACC,WAAW,EAAE;MAClB;MACA,IAAI,CAACA,WAAW,CAACD,eAAe,CAAC,OAAO,CAAC;IAC7C;IACA,IAAI,CAACE,SAAS,GAAGf,SAAS;EAC9B;EACAgB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACpC,aAAa,CAACqC,IAAI,CAAC;MACpBf,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBgB,IAAI,EAAE,IAAI,CAAC9B;IACf,CAAC,CAAC;EACN;EACA+B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACvE,SAAS,GAAGA,uDAAS,CAAC,IAAI,CAACuD,IAAI,CAAC;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACW,SAAS,GAAGf,SAAS;EAC9B;EACAqB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACJ,WAAW,CAAC,CAAC;EACtB;EACMK,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACtB;MACA;MACA,IAAI,OAAOC,cAAc,KAAK,WAAW,IAAIA,cAAc,IAAI,IAAI,EAAE;QACjE,MAAMA,cAAc,CAACC,WAAW,CAAC,UAAU,CAAC;MAChD;MACA,IAAIH,KAAI,CAACrB,IAAI,KAAKF,SAAS,EAAE;QACzBuB,KAAI,CAACrB,IAAI,GAAG3C,wDAAM,CAACoE,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC;MACjD;MACA,MAAMC,OAAO,GAAGL,KAAI,CAACxB,SAAS,KAAKC,SAAS,GAAGN,QAAQ,CAACmC,cAAc,CAACN,KAAI,CAACxB,SAAS,CAAC,GAAG,IAAI;MAC7F,IAAI6B,OAAO,KAAK,IAAI,EAAE;QAClBE,OAAO,CAACC,KAAK,CAAC,mEAAmE,CAAC;QAClF;MACJ;MACA,IAAIR,KAAI,CAAC3B,EAAE,CAACD,QAAQ,CAACiC,OAAO,CAAC,EAAE;QAC3BE,OAAO,CAACC,KAAK,CAAE,4GAA2G,CAAC;MAC/H;MACAR,KAAI,CAACd,SAAS,GAAGmB,OAAO;MACxB;MACAA,OAAO,CAAClB,SAAS,CAACE,GAAG,CAAC,cAAc,CAAC;MACrCW,KAAI,CAAChB,WAAW,CAACgB,KAAI,CAACrB,IAAI,EAAEF,SAAS,CAAC;MACtCuB,KAAI,CAACH,WAAW,CAAC,CAAC;MAClB;MACAjE,iDAAc,CAAC6E,SAAS,CAACT,KAAI,CAAC;MAC9BA,KAAI,CAACU,WAAW,CAAC,CAAC;MAClBV,KAAI,CAACW,OAAO,GAAG,OAAO,sHAA6B,EAAEC,aAAa,CAAC;QAC/DvC,EAAE,EAAEF,QAAQ;QACZ0C,WAAW,EAAE,YAAY;QACzBC,eAAe,EAAE,EAAE;QACnBC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAGhD,EAAE,IAAK+B,KAAI,CAACiB,QAAQ,CAAChD,EAAE,CAAC;QACnCiD,WAAW,EAAEA,CAAA,KAAMlB,KAAI,CAACkB,WAAW,CAAC,CAAC;QACrCC,OAAO,EAAEA,CAAA,KAAMnB,KAAI,CAACmB,OAAO,CAAC,CAAC;QAC7BC,MAAM,EAAGnD,EAAE,IAAK+B,KAAI,CAACoB,MAAM,CAACnD,EAAE,CAAC;QAC/BoD,KAAK,EAAGpD,EAAE,IAAK+B,KAAI,CAACqB,KAAK,CAACpD,EAAE;MAChC,CAAC,CAAC;MACF+B,KAAI,CAACN,WAAW,CAAC,CAAC;IAAC;EACvB;EACA4B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvD,mBAAmB,GAAGxC,uDAAqB,CAAC,IAAI,CAAC8C,EAAE,CAAC;EAC7D;EACMkD,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAvB,6OAAA;MACrBuB,MAAI,CAAC7D,OAAO,GAAG,IAAI;MACnB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,MAAM8D,SAAS,GAAGD,MAAI,CAACnD,EAAE,CAACqD,OAAO,CAAC,gBAAgB,CAAC;MACnD,IAAID,SAAS,KAAK,IAAI,EAAE;QACpBD,MAAI,CAACjD,aAAa,SAASkD,SAAS,CAACE,SAAS,CAAC,CAAC;MACpD;MACAH,MAAI,CAACd,WAAW,CAAC,CAAC;MAClBc,MAAI,CAAC9B,WAAW,CAAC,CAAC;IAAC;EACvB;EACAgB,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAAC/C,OAAO,EAAE;MACd,IAAI,CAACL,aAAa,CAACqC,IAAI,CAAC;QAAEf,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAEgB,IAAI,EAAE,IAAI,CAAC9B;MAAQ,CAAC,CAAC;IAC5E;EACJ;EACM8D,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA5B,6OAAA;MACzB;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,MAAM4B,MAAI,CAACC,KAAK,CAAC,KAAK,CAAC;MACvBD,MAAI,CAACrE,OAAO,CAACuE,OAAO,CAAC,CAAC;MACtBnG,iDAAc,CAACoG,WAAW,CAACH,MAAI,CAAC;MAChC,IAAIA,MAAI,CAACrC,SAAS,EAAE;QAChBqC,MAAI,CAACrC,SAAS,CAACuC,OAAO,CAAC,CAAC;MAC5B;MACA,IAAIF,MAAI,CAAClB,OAAO,EAAE;QACdkB,MAAI,CAAClB,OAAO,CAACoB,OAAO,CAAC,CAAC;QACtBF,MAAI,CAAClB,OAAO,GAAGlC,SAAS;MAC5B;MACAoD,MAAI,CAACrC,SAAS,GAAGf,SAAS;MAC1BoD,MAAI,CAAC3C,SAAS,GAAGT,SAAS;IAAC;EAC/B;EACAwD,kBAAkBA,CAAChE,EAAE,EAAE;IACnB,MAAMiE,gBAAgB,GAAG,IAAI,CAAC7D,EAAE,CAACqD,OAAO,CAAC,gBAAgB,CAAC;IAC1D,IAAIQ,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAKjE,EAAE,CAACkE,MAAM,EAAE;MAC7D,IAAI,CAAC5D,aAAa,GAAGN,EAAE,CAACmE,MAAM,CAACC,OAAO;MACtC,IAAI,CAAC3C,WAAW,CAAC,CAAC;IACtB;EACJ;EACA4C,eAAeA,CAACrE,EAAE,EAAE;IAChB;IACA,IAAI,IAAI,CAACH,OAAO,IAAI,IAAI,CAACP,SAAS,GAAGU,EAAE,CAACsE,SAAS,GAAG,GAAG,EAAE;MACrD,MAAMC,WAAW,GAAGvE,EAAE,CAACwE,YAAY,GAAG,CAACxE,EAAE,CAACwE,YAAY,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACnD,WAAW,CAAC,GAAG,KAAK;MAC3F,IAAIiD,WAAW,EAAE;QACbvE,EAAE,CAAC0E,cAAc,CAAC,CAAC;QACnB1E,EAAE,CAAC2E,eAAe,CAAC,CAAC;QACpB,IAAI,CAACd,KAAK,CAAC,CAAC;MAChB;IACJ;EACJ;EACAe,SAASA,CAAC5E,EAAE,EAAE;IACV,IAAIA,EAAE,CAAC6E,GAAG,KAAK,QAAQ,EAAE;MACrB,IAAI,CAAChB,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;EACIiB,MAAMA,CAAA,EAAG;IACL,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACnF,OAAO,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoF,QAAQA,CAAA,EAAG;IACP,OAAOF,OAAO,CAACC,OAAO,CAAC,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;EACIvD,IAAIA,CAACwD,QAAQ,GAAG,IAAI,EAAE;IAClB,OAAO,IAAI,CAACC,OAAO,CAAC,IAAI,EAAED,QAAQ,CAAC;EACvC;EACA;AACJ;AACA;AACA;EACItB,KAAKA,CAACsB,QAAQ,GAAG,IAAI,EAAE;IACnB,OAAO,IAAI,CAACC,OAAO,CAAC,KAAK,EAAED,QAAQ,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACIE,MAAMA,CAACF,QAAQ,GAAG,IAAI,EAAE;IACpB,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,IAAI,CAACvF,OAAO,EAAEsF,QAAQ,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACIC,OAAOA,CAACE,UAAU,EAAEH,QAAQ,GAAG,IAAI,EAAE;IACjC,OAAOxH,iDAAc,CAAC4H,QAAQ,CAAC,IAAI,EAAED,UAAU,EAAEH,QAAQ,CAAC;EAC9D;EACA9E,iBAAiBA,CAACL,EAAE,EAAEwF,GAAG,EAAE;IACvB,MAAMtB,MAAM,GAAGlE,EAAE,CAACkE,MAAM;IACxB,IAAI,CAACA,MAAM,EAAE;MACT;IACJ;IACA;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC9D,EAAE,CAACD,QAAQ,CAAC+D,MAAM,CAAC,EAAE;MAC1B,IAAI,CAACuB,SAAS,GAAGvB,MAAM;IAC3B,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,MAAM;QAAE9D;MAAG,CAAC,GAAG,IAAI;MACnB;AACZ;AACA;AACA;AACA;AACA;MACYrD,wDAAoB,CAACqD,EAAE,CAAC;MACxB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACqF,SAAS,KAAKD,GAAG,CAACE,aAAa,EAAE;QACtCzI,wDAAmB,CAACmD,EAAE,CAAC;MAC3B;IACJ;EACJ;EACMmF,QAAQA,CAACD,UAAU,EAAEH,QAAQ,GAAG,IAAI,EAAE;IAAA,IAAAQ,MAAA;IAAA,OAAA3D,6OAAA;MACxC;MACA,IAAI,CAAC2D,MAAI,CAACT,SAAS,CAAC,CAAC,IAAIS,MAAI,CAAC/F,WAAW,IAAI0F,UAAU,KAAKK,MAAI,CAAC9F,OAAO,EAAE;QACtE,OAAO,KAAK;MAChB;MACA8F,MAAI,CAACC,eAAe,CAACN,UAAU,CAAC;MAChC,MAAMK,MAAI,CAACE,aAAa,CAAC,CAAC;MAC1B,MAAMF,MAAI,CAACG,cAAc,CAACR,UAAU,EAAEH,QAAQ,CAAC;MAC/C;AACR;AACA;AACA;AACA;MACQ,IAAIQ,MAAI,CAAChG,kBAAkB,EAAE;QACzBgG,MAAI,CAAChG,kBAAkB,GAAG,KAAK;QAC/B,OAAO,KAAK;MAChB;MACAgG,MAAI,CAACI,cAAc,CAACT,UAAU,CAAC;MAC/B,OAAO,IAAI;IAAC;EAChB;EACMO,aAAaA,CAAA,EAAG;IAAA,IAAAG,MAAA;IAAA,OAAAhE,6OAAA;MAClB;MACA;MACA,MAAMiE,KAAK,GAAGD,MAAI,CAAC1E,WAAW,CAAC4E,WAAW;MAC1C;AACR;AACA;AACA;MACQ,MAAMC,WAAW,GAAG9I,uDAAS,CAAC2I,MAAI,CAACpF,IAAI,CAAC;MACxC,IAAIqF,KAAK,KAAKD,MAAI,CAACC,KAAK,IAAID,MAAI,CAACzE,SAAS,KAAKf,SAAS,IAAI2F,WAAW,KAAKH,MAAI,CAAC3I,SAAS,EAAE;QACxF;MACJ;MACA2I,MAAI,CAACC,KAAK,GAAGA,KAAK;MAClBD,MAAI,CAAC3I,SAAS,GAAG8I,WAAW;MAC5B;MACA,IAAIH,MAAI,CAACzE,SAAS,EAAE;QAChByE,MAAI,CAACzE,SAAS,CAACuC,OAAO,CAAC,CAAC;QACxBkC,MAAI,CAACzE,SAAS,GAAGf,SAAS;MAC9B;MACA;MACA,MAAMe,SAAS,GAAIyE,MAAI,CAACzE,SAAS,SAAS5D,iDAAc,CAACyI,gBAAgB,CAACJ,MAAI,CAACtF,IAAI,EAAEsF,MAAI,CAAE;MAC3F,IAAI,CAACjI,wDAAM,CAACsI,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE;QACtC9E,SAAS,CAAC+E,QAAQ,CAAC,CAAC,CAAC;MACzB;MACA/E,SAAS,CAACgF,IAAI,CAAC,MAAM,CAAC;IAAC;EAC3B;EACMT,cAAcA,CAACR,UAAU,EAAEH,QAAQ,EAAE;IAAA,IAAAqB,MAAA;IAAA,OAAAxE,6OAAA;MACvC,MAAMyE,UAAU,GAAG,CAACnB,UAAU;MAC9B,MAAMoB,IAAI,GAAGzI,4DAAU,CAACuI,MAAI,CAAC;MAC7B,MAAMG,MAAM,GAAGD,IAAI,KAAK,KAAK,GAAGhI,SAAS,GAAGC,QAAQ;MACpD,MAAMiI,aAAa,GAAGF,IAAI,KAAK,KAAK,GAAG9H,gBAAgB,GAAGC,eAAe;MACzE,MAAMgI,GAAG,GAAGL,MAAI,CAACjF,SAAS,CACrBuF,SAAS,CAACL,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC,CAC5CE,MAAM,CAACF,UAAU,GAAGG,aAAa,GAAGD,MAAM,CAAC;MAChD,IAAIxB,QAAQ,EAAE;QACV,MAAM0B,GAAG,CAACE,IAAI,CAAC,CAAC;MACpB,CAAC,MACI;QACDF,GAAG,CAACE,IAAI,CAAC;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MAC5B;MACA;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,IAAIH,GAAG,CAACI,YAAY,CAAC,CAAC,KAAK,SAAS,EAAE;QAClCJ,GAAG,CAACC,SAAS,CAAC,QAAQ,CAAC;MAC3B;IAAC;EACL;EACA5B,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACvE,QAAQ,IAAI,CAAC,IAAI,CAACL,aAAa;EAChD;EACA4G,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACrG,YAAY,IAAI,CAAC,IAAI,CAACjB,WAAW,IAAI,IAAI,CAACsF,SAAS,CAAC,CAAC;EACrE;EACAlC,QAAQA,CAACmB,MAAM,EAAE;IACb;IACA,MAAMgD,gBAAgB,GAAG,CAAC,CAACjH,QAAQ,CAACkH,aAAa,CAAC,sBAAsB,CAAC;IACzE,IAAID,gBAAgB,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACrH,OAAO,EAAE;MACd,OAAO,IAAI;IACf,CAAC,MACI,IAAIlC,iDAAc,CAAC0J,YAAY,CAAC,CAAC,EAAE;MACpC,OAAO,KAAK;IAChB;IACA,OAAOC,aAAa,CAACC,MAAM,EAAEpD,MAAM,CAACqD,QAAQ,EAAE,IAAI,CAACnK,SAAS,EAAE,IAAI,CAACyD,YAAY,CAAC;EACpF;EACAmC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2C,eAAe,CAAC,CAAC,IAAI,CAAC/F,OAAO,CAAC;IACnC,OAAO,IAAI,CAACgG,aAAa,CAAC,CAAC;EAC/B;EACA3C,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACtD,WAAW,IAAI,CAAC,IAAI,CAAC2B,SAAS,EAAE;MACtC/D,uDAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA;IACA,IAAI,CAAC+D,SAAS,CAACkG,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC5H,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5D;EACAsD,MAAMA,CAACgB,MAAM,EAAE;IACX,IAAI,CAAC,IAAI,CAACvE,WAAW,IAAI,CAAC,IAAI,CAAC2B,SAAS,EAAE;MACtC/D,uDAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA,MAAMkK,KAAK,GAAGC,YAAY,CAACxD,MAAM,CAACyD,MAAM,EAAE,IAAI,CAAC/H,OAAO,EAAE,IAAI,CAACxC,SAAS,CAAC;IACvE,MAAMwK,SAAS,GAAGH,KAAK,GAAG,IAAI,CAACzB,KAAK;IACpC,IAAI,CAAC1E,SAAS,CAACuG,YAAY,CAAC,IAAI,CAACjI,OAAO,GAAG,CAAC,GAAGgI,SAAS,GAAGA,SAAS,CAAC;EACzE;EACAzE,KAAKA,CAACe,MAAM,EAAE;IACV,IAAI,CAAC,IAAI,CAACvE,WAAW,IAAI,CAAC,IAAI,CAAC2B,SAAS,EAAE;MACtC/D,uDAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA,MAAMsH,MAAM,GAAG,IAAI,CAACjF,OAAO;IAC3B,MAAMxC,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMqK,KAAK,GAAGC,YAAY,CAACxD,MAAM,CAACyD,MAAM,EAAE9C,MAAM,EAAEzH,SAAS,CAAC;IAC5D,MAAM4I,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAM4B,SAAS,GAAGH,KAAK,GAAGzB,KAAK;IAC/B,MAAM8B,QAAQ,GAAG5D,MAAM,CAAC6D,SAAS;IACjC,MAAMC,CAAC,GAAGhC,KAAK,GAAG,GAAG;IACrB,MAAMiC,mBAAmB,GAAGH,QAAQ,IAAI,CAAC,KAAKA,QAAQ,GAAG,GAAG,IAAI5D,MAAM,CAACyD,MAAM,GAAGK,CAAC,CAAC;IAClF,MAAME,kBAAkB,GAAGJ,QAAQ,IAAI,CAAC,KAAKA,QAAQ,GAAG,CAAC,GAAG,IAAI5D,MAAM,CAACyD,MAAM,GAAG,CAACK,CAAC,CAAC;IACnF,MAAMG,cAAc,GAAGtD,MAAM,GACvBzH,SAAS,GACL6K,mBAAmB,GACnBC,kBAAkB,GACtB9K,SAAS,GACL8K,kBAAkB,GAClBD,mBAAmB;IAC7B,IAAI5C,UAAU,GAAG,CAACR,MAAM,IAAIsD,cAAc;IAC1C,IAAItD,MAAM,IAAI,CAACsD,cAAc,EAAE;MAC3B9C,UAAU,GAAG,IAAI;IACrB;IACA,IAAI,CAAChG,SAAS,GAAG6E,MAAM,CAACkE,WAAW;IACnC;IACA,IAAIC,YAAY,GAAGF,cAAc,GAAG,KAAK,GAAG,CAAC,KAAK;IAClD;AACR;AACA;AACA;AACA;IACQ,MAAMG,iBAAiB,GAAGV,SAAS,GAAG,CAAC,GAAG,IAAI,GAAGA,SAAS;IAC1D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQS,YAAY,IACR3L,4DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEe,uDAAK,CAAC,CAAC,EAAE6K,iBAAiB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5G,MAAMC,MAAM,GAAG,IAAI,CAAC3I,OAAO,GAAG,CAACuI,cAAc,GAAGA,cAAc;IAC9D,IAAI,CAAC7G,SAAS,CACToF,MAAM,CAAC,gCAAgC,CAAC,CACxC8B,QAAQ,CAAC,MAAM,IAAI,CAAC1C,cAAc,CAACT,UAAU,CAAC,EAAE;MAAEoD,eAAe,EAAE;IAAK,CAAC,CAAC,CAC1EC,WAAW,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC3I,OAAO,GAAG,CAAC,GAAGyI,YAAY,GAAGA,YAAY,EAAE,GAAG,CAAC;EACzF;EACA1C,eAAeA,CAACN,UAAU,EAAE;IACxB9H,uDAAM,CAAC,CAAC,IAAI,CAACoC,WAAW,EAAE,gDAAgD,CAAC;IAC3E;IACA;IACA,IAAI,CAACQ,EAAE,CAACc,SAAS,CAACE,GAAG,CAACwH,SAAS,CAAC;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACxI,EAAE,CAACyI,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;IACrC,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC5H,SAAS,CAACE,GAAG,CAAC2H,aAAa,CAAC;IAChD;IACA;IACA,IAAI,IAAI,CAAC9H,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACC,SAAS,CAACE,GAAG,CAAC4H,iBAAiB,CAAC;MAC/C;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAAC/H,SAAS,CAAC4H,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACtD;IACA,IAAI,CAACtJ,OAAO,CAAC0J,KAAK,CAAC,CAAC;IACpB,IAAI,CAACrJ,WAAW,GAAG,IAAI;IACvB,IAAI0F,UAAU,EAAE;MACZ,IAAI,CAACrG,WAAW,CAACyC,IAAI,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAACxC,YAAY,CAACwC,IAAI,CAAC,CAAC;IAC5B;EACJ;EACAqE,cAAcA,CAACjB,MAAM,EAAE;IACnB,IAAIoE,EAAE;IACN;IACA;IACA;IACA;IACA,IAAI,CAACrJ,OAAO,GAAGiF,MAAM;IACrB,IAAI,CAAClF,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACN,OAAO,CAAC4J,OAAO,CAAC,CAAC;IAC1B;IACA,IAAIrE,MAAM,EAAE;MACR;MACA,IAAI,CAAC3F,UAAU,CAACuC,IAAI,CAAC,CAAC;MACtB;AACZ;AACA;AACA;AACA;MACY,MAAM0H,WAAW,GAAG,CAACF,EAAE,GAAGhJ,QAAQ,CAACwF,aAAa,MAAM,IAAI,IAAIwD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACzF,OAAO,CAAC,UAAU,CAAC;MAC7G,IAAI2F,WAAW,KAAK,IAAI,CAAChJ,EAAE,EAAE;QACzB,IAAI,CAACA,EAAE,CAACiJ,KAAK,CAAC,CAAC;MACnB;MACA;MACAnJ,QAAQ,CAACoJ,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACvJ,WAAW,EAAE,IAAI,CAAC;IAC9D,CAAC,MACI;MACD;MACA,IAAI,CAACK,EAAE,CAACc,SAAS,CAACC,MAAM,CAACyH,SAAS,CAAC;MACnC;AACZ;AACA;AACA;MACY,IAAI,CAACxI,EAAE,CAACiB,eAAe,CAAC,UAAU,CAAC;MACnC,IAAI,IAAI,CAACJ,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACC,SAAS,CAACC,MAAM,CAAC6H,iBAAiB,CAAC;QAClD;AAChB;AACA;AACA;AACA;QACgB,IAAI,CAAC/H,SAAS,CAACI,eAAe,CAAC,aAAa,CAAC;MACjD;MACA,IAAI,IAAI,CAACyH,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAAC5H,SAAS,CAACC,MAAM,CAAC4H,aAAa,CAAC;MACnD;MACA,IAAI,IAAI,CAACxH,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACgI,IAAI,CAAC,CAAC;MACzB;MACA;MACA,IAAI,CAACnK,WAAW,CAACsC,IAAI,CAAC,CAAC;MACvB;MACAxB,QAAQ,CAACsJ,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACzJ,WAAW,EAAE,IAAI,CAAC;IACjE;EACJ;EACA0B,WAAWA,CAAA,EAAG;IACV,MAAMwD,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IACjC,IAAI,IAAI,CAACxC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAAC+G,MAAM,CAACxE,QAAQ,IAAI,IAAI,CAACpE,YAAY,CAAC;IACtD;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoE,QAAQ,EAAE;MACX;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACrF,WAAW,EAAE;QAClB,IAAI,CAACD,kBAAkB,GAAG,IAAI;MAClC;MACA;AACZ;AACA;AACA;MACY,IAAI,CAACoG,cAAc,CAAC,KAAK,CAAC;IAC9B;EACJ;EACA2D,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEhJ,IAAI;MAAEC,QAAQ;MAAEP,EAAE;MAAEE,aAAa;MAAER,mBAAmB;MAAEc;IAAK,CAAC,GAAG,IAAI;IAC7E,MAAM8F,IAAI,GAAGzI,4DAAU,CAAC,IAAI,CAAC;IAC7B;AACR;AACA;AACA;AACA;IACQ,OAAQ5B,qDAAC,CAACE,iDAAI,EAAE;MAAEsI,GAAG,EAAE,0CAA0C;MAAE8E,SAAS,EAAEvM,wFAAqB,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAACwH,SAAS;MAAEgF,IAAI,EAAE,YAAY;MAAE,YAAY,EAAE9J,mBAAmB,CAAC,YAAY,CAAC,IAAI,MAAM;MAAE+J,KAAK,EAAE;QAC7M,CAACnD,IAAI,GAAG,IAAI;QACZ,CAAE,aAAYhG,IAAK,EAAC,GAAG,IAAI;QAC3B,cAAc,EAAE,CAACC,QAAQ;QACzB,CAAE,aAAYC,IAAK,EAAC,GAAG,IAAI;QAC3B,mBAAmB,EAAEN,aAAa;QAClC,iBAAiB,EAAE1C,qDAAW,CAAC,gBAAgB,EAAEwC,EAAE;MACvD;IAAE,CAAC,EAAE/D,qDAAC,CAAC,KAAK,EAAE;MAAEwI,GAAG,EAAE,0CAA0C;MAAEgF,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE,WAAW;MAAEC,GAAG,EAAG3J,EAAE,IAAM,IAAI,CAACkB,WAAW,GAAGlB;IAAI,CAAC,EAAE/D,qDAAC,CAAC,MAAM,EAAE;MAAEwI,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAExI,qDAAC,CAAC,cAAc,EAAE;MAAEwI,GAAG,EAAE,0CAA0C;MAAEkF,GAAG,EAAG3J,EAAE,IAAM,IAAI,CAAC0I,UAAU,GAAG1I,EAAG;MAAEyJ,KAAK,EAAE,eAAe;MAAEG,QAAQ,EAAE,KAAK;MAAErF,eAAe,EAAE,KAAK;MAAEmF,IAAI,EAAE;IAAW,CAAC,CAAC,CAAC;EACzZ;EACA,IAAI1J,EAAEA,CAAA,EAAG;IAAE,OAAO3D,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWwN,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,aAAa,CAAC;MACvB,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,MAAM,EAAE,CAAC,aAAa,CAAC;MACvB,cAAc,EAAE,CAAC,qBAAqB;IAC1C,CAAC;EAAE;AACP,CAAC;AACD,MAAMtC,YAAY,GAAGA,CAACC,MAAM,EAAE9C,MAAM,EAAEzH,SAAS,KAAK;EAChD,OAAO6M,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErF,MAAM,KAAKzH,SAAS,GAAG,CAACuK,MAAM,GAAGA,MAAM,CAAC;AAC/D,CAAC;AACD,MAAMN,aAAa,GAAGA,CAAC8C,GAAG,EAAEC,IAAI,EAAEhN,SAAS,EAAEyD,YAAY,KAAK;EAC1D,IAAIzD,SAAS,EAAE;IACX,OAAOgN,IAAI,IAAID,GAAG,CAACE,UAAU,GAAGxJ,YAAY;EAChD,CAAC,MACI;IACD,OAAOuJ,IAAI,IAAIvJ,YAAY;EAC/B;AACJ,CAAC;AACD,MAAM8H,SAAS,GAAG,WAAW;AAC7B,MAAMG,aAAa,GAAG,eAAe;AACrC,MAAMC,iBAAiB,GAAG,mBAAmB;AAC7ClK,IAAI,CAACyL,KAAK,GAAG;EACTC,GAAG,EAAEjM,gBAAgB;EACrBkM,EAAE,EAAEhM;AACR,CAAC;;AAED;AACA,MAAMiM,gBAAgB;EAAA,IAAAC,IAAA,GAAA3I,6OAAA,CAAG,WAAO4I,IAAI,EAAK;IACrC,MAAMC,MAAM,SAASlN,iDAAc,CAACwE,GAAG,CAACyI,IAAI,CAAC;IAC7C,OAAO,CAAC,EAAEC,MAAM,WAAWA,MAAM,CAAC5F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC;EAAA,gBAHKyF,gBAAgBA,CAAAI,EAAA;IAAA,OAAAH,IAAA,CAAAI,KAAA,OAAAC,SAAA;EAAA;AAAA,GAGrB;AAED,MAAMC,gBAAgB,GAAG,+2FAA+2F;AACx4F,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,eAAe,GAAG,49FAA49F;AACp/F,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,UAAU,GAAG,MAAM;EACrBtM,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAsM,MAAA;IACjBpP,qDAAgB,CAAC,IAAI,EAAE8C,OAAO,CAAC;IAC/B,IAAI,CAACc,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACyL,OAAO,gBAAAvJ,6OAAA,CAAG,aAAY;MACvB,OAAOrE,iDAAc,CAAC0H,MAAM,CAACiG,MAAI,CAACV,IAAI,CAAC;IAC3C,CAAC;IACD,IAAI,CAACxG,OAAO,GAAG,KAAK;IACpB,IAAI,CAACoH,KAAK,GAAGhL,SAAS;IACtB,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACiK,IAAI,GAAGpK,SAAS;IACrB,IAAI,CAACiL,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC/K,IAAI,GAAG,QAAQ;EACxB;EACA2C,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvD,mBAAmB,GAAGxC,uDAAqB,CAAC,IAAI,CAAC8C,EAAE,CAAC;EAC7D;EACAkD,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACoI,iBAAiB,CAAC,CAAC;EAC5B;EACMA,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3J,6OAAA;MACtB2J,MAAI,CAACvH,OAAO,SAASsG,gBAAgB,CAACiB,MAAI,CAACf,IAAI,CAAC;IAAC;EACrD;EACAlB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE8B,KAAK;MAAE7K,QAAQ;MAAEb;IAAoB,CAAC,GAAG,IAAI;IACrD,MAAM4G,IAAI,GAAGzI,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM2N,QAAQ,GAAG7N,wDAAM,CAACoE,GAAG,CAAC,UAAU,EAAEuE,IAAI,KAAK,KAAK,GAAGvI,kDAAW,GAAGE,kDAAS,CAAC;IACjF,MAAMwN,MAAM,GAAG,IAAI,CAACJ,QAAQ,IAAI,CAAC,IAAI,CAACrH,OAAO;IAC7C,MAAM0H,KAAK,GAAG;MACVpL,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;IACD,MAAMqL,SAAS,GAAGjM,mBAAmB,CAAC,YAAY,CAAC,IAAI,MAAM;IAC7D,OAAQzD,qDAAC,CAACE,iDAAI,EAAE;MAAEsI,GAAG,EAAE,0CAA0C;MAAE0G,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,eAAe,EAAE5K,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE,aAAa,EAAEkL,MAAM,GAAG,MAAM,GAAG,IAAI;MAAEhC,KAAK,EAAE/L,qDAAkB,CAAC0N,KAAK,EAAE;QACrM,CAAC9E,IAAI,GAAG,IAAI;QACZsF,MAAM,EAAE,IAAI;QAAE;QACd,oBAAoB,EAAEH,MAAM;QAC5B,sBAAsB,EAAElL,QAAQ;QAChC,YAAY,EAAE/C,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACwC,EAAE,CAAC;QACjD,kBAAkB,EAAExC,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACwC,EAAE,CAAC;QAC9D,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE;MACrB,CAAC;IAAE,CAAC,EAAE/D,qDAAC,CAAC,QAAQ,EAAE4P,MAAM,CAACC,MAAM,CAAC;MAAErH,GAAG,EAAE;IAA2C,CAAC,EAAEiH,KAAK,EAAE;MAAEnL,QAAQ,EAAEA,QAAQ;MAAEkJ,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAEiC;IAAU,CAAC,CAAC,EAAE1P,qDAAC,CAAC,MAAM,EAAE;MAAEwI,GAAG,EAAE,0CAA0C;MAAEgF,KAAK,EAAE;IAAe,CAAC,EAAExN,qDAAC,CAAC,MAAM,EAAE;MAAEwI,GAAG,EAAE;IAA2C,CAAC,EAAExI,qDAAC,CAAC,UAAU,EAAE;MAAEwI,GAAG,EAAE,0CAA0C;MAAEiF,IAAI,EAAE,MAAM;MAAEqC,IAAI,EAAEP,QAAQ;MAAElF,IAAI,EAAEA,IAAI;MAAE0F,IAAI,EAAE,KAAK;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAC,CAAC,EAAE1F,IAAI,KAAK,IAAI,IAAIrK,qDAAC,CAAC,mBAAmB,EAAE;MAAEwI,GAAG,EAAE,0CAA0C;MAAEnE,IAAI,EAAE;IAAY,CAAC,CAAC,CAAC,CAAC;EACxlB;EACA,IAAIN,EAAEA,CAAA,EAAG;IAAE,OAAO3D,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD4O,UAAU,CAACd,KAAK,GAAG;EACfC,GAAG,EAAEU,sBAAsB;EAC3BT,EAAE,EAAEW;AACR,CAAC;AAED,MAAMiB,aAAa,GAAG,0CAA0C;AAChE,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,UAAU,GAAG,MAAM;EACrBxN,WAAWA,CAACC,OAAO,EAAE;IACjB9C,qDAAgB,CAAC,IAAI,EAAE8C,OAAO,CAAC;IAC/B,IAAI,CAACuM,OAAO,GAAG,MAAM;MACjB,OAAO5N,iDAAc,CAAC0H,MAAM,CAAC,IAAI,CAACuF,IAAI,CAAC;IAC3C,CAAC;IACD,IAAI,CAACxG,OAAO,GAAG,KAAK;IACpB,IAAI,CAACwG,IAAI,GAAGpK,SAAS;IACrB,IAAI,CAACiL,QAAQ,GAAG,IAAI;EACxB;EACA3J,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC4J,iBAAiB,CAAC,CAAC;EAC5B;EACMA,iBAAiBA,CAAA,EAAG;IAAA,IAAAc,MAAA;IAAA,OAAAxK,6OAAA;MACtBwK,MAAI,CAACpI,OAAO,SAASsG,gBAAgB,CAAC8B,MAAI,CAAC5B,IAAI,CAAC;IAAC;EACrD;EACAlB,MAAMA,CAAA,EAAG;IACL,MAAMhD,IAAI,GAAGzI,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4N,MAAM,GAAG,IAAI,CAACJ,QAAQ,IAAI,CAAC,IAAI,CAACrH,OAAO;IAC7C,OAAQ/H,qDAAC,CAACE,iDAAI,EAAE;MAAEsI,GAAG,EAAE,0CAA0C;MAAE0G,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,aAAa,EAAEM,MAAM,GAAG,MAAM,GAAG,IAAI;MAAEhC,KAAK,EAAE;QAChI,CAACnD,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAEmF;MAC1B;IAAE,CAAC,EAAExP,qDAAC,CAAC,MAAM,EAAE;MAAEwI,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;AACJ,CAAC;AACD0H,UAAU,CAAChC,KAAK,GAAG+B,mBAAmB", "sources": ["./node_modules/@ionic/core/dist/esm/ion-menu_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-c71c5417.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { o as getPresentedOverlay, n as focusFirstDescendant, q as focusLastDescendant } from './overlays-0d212972.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-314a54f6.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-7f93f261.js';\nimport { n as isEndSide, i as inheritAriaAttributes, m as assert, j as clamp } from './helpers-da915de8.js';\nimport { m as menuController } from './index-c8c3afda.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { c as config, b as getIonMode } from './ionic-global-b9c0d1da.js';\nimport { u as menuOutline, v as menuSharp } from './index-e2cf2ceb.js';\nimport './index-a5d50daf.js';\nimport './framework-delegate-63d1a679.js';\nimport './index-9b0d46f4.js';\nimport './animation-eab5a4ca.js';\n\nconst menuIosCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-push){z-index:1000}:host(.menu-type-push) .show-backdrop{display:block}\";\nconst IonMenuIosStyle0 = menuIosCss;\n\nconst menuMdCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-overlay) .menu-inner{-webkit-box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18);box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18)}\";\nconst IonMenuMdStyle0 = menuMdCss;\n\nconst iosEasing = 'cubic-bezier(0.32,0.72,0,1)';\nconst mdEasing = 'cubic-bezier(0.0,0.0,0.2,1)';\nconst iosEasingReverse = 'cubic-bezier(1, 0, 0.68, 0.28)';\nconst mdEasingReverse = 'cubic-bezier(0.4, 0, 0.6, 1)';\nconst Menu = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionWillOpen = createEvent(this, \"ionWillOpen\", 7);\n        this.ionWillClose = createEvent(this, \"ionWillClose\", 7);\n        this.ionDidOpen = createEvent(this, \"ionDidOpen\", 7);\n        this.ionDidClose = createEvent(this, \"ionDidClose\", 7);\n        this.ionMenuChange = createEvent(this, \"ionMenuChange\", 7);\n        this.lastOnEnd = 0;\n        this.blocker = GESTURE_CONTROLLER.createBlocker({ disableScroll: true });\n        this.didLoad = false;\n        /**\n         * Flag used to determine if an open/close\n         * operation was cancelled. For example, if\n         * an app calls \"menu.open\" then disables the menu\n         * part way through the animation, then this would\n         * be considered a cancelled operation.\n         */\n        this.operationCancelled = false;\n        this.isAnimating = false;\n        this._isOpen = false;\n        this.inheritedAttributes = {};\n        this.handleFocus = (ev) => {\n            /**\n             * Overlays have their own focus trapping listener\n             * so we do not want the two listeners to conflict\n             * with each other. If the top-most overlay that is\n             * open does not contain this ion-menu, then ion-menu's\n             * focus trapping should not run.\n             */\n            const lastOverlay = getPresentedOverlay(document);\n            if (lastOverlay && !lastOverlay.contains(this.el)) {\n                return;\n            }\n            this.trapKeyboardFocus(ev, document);\n        };\n        this.isPaneVisible = false;\n        this.isEndSide = false;\n        this.contentId = undefined;\n        this.menuId = undefined;\n        this.type = undefined;\n        this.disabled = false;\n        this.side = 'start';\n        this.swipeGesture = true;\n        this.maxEdgeStart = 50;\n    }\n    typeChanged(type, oldType) {\n        const contentEl = this.contentEl;\n        if (contentEl) {\n            if (oldType !== undefined) {\n                contentEl.classList.remove(`menu-content-${oldType}`);\n            }\n            contentEl.classList.add(`menu-content-${type}`);\n            contentEl.removeAttribute('style');\n        }\n        if (this.menuInnerEl) {\n            // Remove effects of previous animations\n            this.menuInnerEl.removeAttribute('style');\n        }\n        this.animation = undefined;\n    }\n    disabledChanged() {\n        this.updateState();\n        this.ionMenuChange.emit({\n            disabled: this.disabled,\n            open: this._isOpen,\n        });\n    }\n    sideChanged() {\n        this.isEndSide = isEndSide(this.side);\n        /**\n         * Menu direction animation is calculated based on the document direction.\n         * If the document direction changes, we need to create a new animation.\n         */\n        this.animation = undefined;\n    }\n    swipeGestureChanged() {\n        this.updateState();\n    }\n    async connectedCallback() {\n        // TODO: connectedCallback is fired in CE build\n        // before WC is defined. This needs to be fixed in Stencil.\n        if (typeof customElements !== 'undefined' && customElements != null) {\n            await customElements.whenDefined('ion-menu');\n        }\n        if (this.type === undefined) {\n            this.type = config.get('menuType', 'overlay');\n        }\n        const content = this.contentId !== undefined ? document.getElementById(this.contentId) : null;\n        if (content === null) {\n            console.error('Menu: must have a \"content\" element to listen for drag events on.');\n            return;\n        }\n        if (this.el.contains(content)) {\n            console.error(`Menu: \"contentId\" should refer to the main view's ion-content, not the ion-content inside of the ion-menu.`);\n        }\n        this.contentEl = content;\n        // add menu's content classes\n        content.classList.add('menu-content');\n        this.typeChanged(this.type, undefined);\n        this.sideChanged();\n        // register this menu with the app's menu controller\n        menuController._register(this);\n        this.menuChanged();\n        this.gesture = (await import('./index-39782642.js')).createGesture({\n            el: document,\n            gestureName: 'menu-swipe',\n            gesturePriority: 30,\n            threshold: 10,\n            blurOnStart: true,\n            canStart: (ev) => this.canStart(ev),\n            onWillStart: () => this.onWillStart(),\n            onStart: () => this.onStart(),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: (ev) => this.onEnd(ev),\n        });\n        this.updateState();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    async componentDidLoad() {\n        this.didLoad = true;\n        /**\n         * A menu inside of a split pane is assumed\n         * to be a side pane.\n         *\n         * When the menu is loaded it needs to\n         * see if it should be considered visible inside\n         * of the split pane. If the split pane is\n         * hidden then the menu should be too.\n         */\n        const splitPane = this.el.closest('ion-split-pane');\n        if (splitPane !== null) {\n            this.isPaneVisible = await splitPane.isVisible();\n        }\n        this.menuChanged();\n        this.updateState();\n    }\n    menuChanged() {\n        /**\n         * Inform dependent components such as ion-menu-button\n         * that the menu is ready. Note that we only want to do this\n         * once the menu has been rendered which is why we check for didLoad.\n         */\n        if (this.didLoad) {\n            this.ionMenuChange.emit({ disabled: this.disabled, open: this._isOpen });\n        }\n    }\n    async disconnectedCallback() {\n        /**\n         * The menu should be closed when it is\n         * unmounted from the DOM.\n         * This is an async call, so we need to wait for\n         * this to finish otherwise contentEl\n         * will not have MENU_CONTENT_OPEN removed.\n         */\n        await this.close(false);\n        this.blocker.destroy();\n        menuController._unregister(this);\n        if (this.animation) {\n            this.animation.destroy();\n        }\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.animation = undefined;\n        this.contentEl = undefined;\n    }\n    onSplitPaneChanged(ev) {\n        const closestSplitPane = this.el.closest('ion-split-pane');\n        if (closestSplitPane !== null && closestSplitPane === ev.target) {\n            this.isPaneVisible = ev.detail.visible;\n            this.updateState();\n        }\n    }\n    onBackdropClick(ev) {\n        // TODO(FW-2832): type (CustomEvent triggers errors which should be sorted)\n        if (this._isOpen && this.lastOnEnd < ev.timeStamp - 100) {\n            const shouldClose = ev.composedPath ? !ev.composedPath().includes(this.menuInnerEl) : false;\n            if (shouldClose) {\n                ev.preventDefault();\n                ev.stopPropagation();\n                this.close();\n            }\n        }\n    }\n    onKeydown(ev) {\n        if (ev.key === 'Escape') {\n            this.close();\n        }\n    }\n    /**\n     * Returns `true` is the menu is open.\n     */\n    isOpen() {\n        return Promise.resolve(this._isOpen);\n    }\n    /**\n     * Returns `true` is the menu is active.\n     *\n     * A menu is active when it can be opened or closed, meaning it's enabled\n     * and it's not part of a `ion-split-pane`.\n     */\n    isActive() {\n        return Promise.resolve(this._isActive());\n    }\n    /**\n     * Opens the menu. If the menu is already open or it can't be opened,\n     * it returns `false`.\n     */\n    open(animated = true) {\n        return this.setOpen(true, animated);\n    }\n    /**\n     * Closes the menu. If the menu is already closed or it can't be closed,\n     * it returns `false`.\n     */\n    close(animated = true) {\n        return this.setOpen(false, animated);\n    }\n    /**\n     * Toggles the menu. If the menu is already open, it will try to close, otherwise it will try to open it.\n     * If the operation can't be completed successfully, it returns `false`.\n     */\n    toggle(animated = true) {\n        return this.setOpen(!this._isOpen, animated);\n    }\n    /**\n     * Opens or closes the button.\n     * If the operation can't be completed successfully, it returns `false`.\n     */\n    setOpen(shouldOpen, animated = true) {\n        return menuController._setOpen(this, shouldOpen, animated);\n    }\n    trapKeyboardFocus(ev, doc) {\n        const target = ev.target;\n        if (!target) {\n            return;\n        }\n        /**\n         * If the target is inside the menu contents, let the browser\n         * focus as normal and keep a log of the last focused element.\n         */\n        if (this.el.contains(target)) {\n            this.lastFocus = target;\n        }\n        else {\n            /**\n             * Otherwise, we are about to have focus go out of the menu.\n             * Wrap the focus to either the first or last element.\n             */\n            const { el } = this;\n            /**\n             * Once we call `focusFirstDescendant`, another focus event\n             * will fire, which will cause `lastFocus` to be updated\n             * before we can run the code after that. We cache the value\n             * here to avoid that.\n             */\n            focusFirstDescendant(el);\n            /**\n             * If the cached last focused element is the same as the now-\n             * active element, that means the user was on the first element\n             * already and pressed Shift + Tab, so we need to wrap to the\n             * last descendant.\n             */\n            if (this.lastFocus === doc.activeElement) {\n                focusLastDescendant(el);\n            }\n        }\n    }\n    async _setOpen(shouldOpen, animated = true) {\n        // If the menu is disabled or it is currently being animated, let's do nothing\n        if (!this._isActive() || this.isAnimating || shouldOpen === this._isOpen) {\n            return false;\n        }\n        this.beforeAnimation(shouldOpen);\n        await this.loadAnimation();\n        await this.startAnimation(shouldOpen, animated);\n        /**\n         * If the animation was cancelled then\n         * return false because the operation\n         * did not succeed.\n         */\n        if (this.operationCancelled) {\n            this.operationCancelled = false;\n            return false;\n        }\n        this.afterAnimation(shouldOpen);\n        return true;\n    }\n    async loadAnimation() {\n        // Menu swipe animation takes the menu's inner width as parameter,\n        // If `offsetWidth` changes, we need to create a new animation.\n        const width = this.menuInnerEl.offsetWidth;\n        /**\n         * Menu direction animation is calculated based on the document direction.\n         * If the document direction changes, we need to create a new animation.\n         */\n        const isEndSide$1 = isEndSide(this.side);\n        if (width === this.width && this.animation !== undefined && isEndSide$1 === this.isEndSide) {\n            return;\n        }\n        this.width = width;\n        this.isEndSide = isEndSide$1;\n        // Destroy existing animation\n        if (this.animation) {\n            this.animation.destroy();\n            this.animation = undefined;\n        }\n        // Create new animation\n        const animation = (this.animation = await menuController._createAnimation(this.type, this));\n        if (!config.getBoolean('animated', true)) {\n            animation.duration(0);\n        }\n        animation.fill('both');\n    }\n    async startAnimation(shouldOpen, animated) {\n        const isReversed = !shouldOpen;\n        const mode = getIonMode(this);\n        const easing = mode === 'ios' ? iosEasing : mdEasing;\n        const easingReverse = mode === 'ios' ? iosEasingReverse : mdEasingReverse;\n        const ani = this.animation\n            .direction(isReversed ? 'reverse' : 'normal')\n            .easing(isReversed ? easingReverse : easing);\n        if (animated) {\n            await ani.play();\n        }\n        else {\n            ani.play({ sync: true });\n        }\n        /**\n         * We run this after the play invocation\n         * instead of using ani.onFinish so that\n         * multiple onFinish callbacks do not get\n         * run if an animation is played, stopped,\n         * and then played again.\n         */\n        if (ani.getDirection() === 'reverse') {\n            ani.direction('normal');\n        }\n    }\n    _isActive() {\n        return !this.disabled && !this.isPaneVisible;\n    }\n    canSwipe() {\n        return this.swipeGesture && !this.isAnimating && this._isActive();\n    }\n    canStart(detail) {\n        // Do not allow swipe gesture if a modal is open\n        const isModalPresented = !!document.querySelector('ion-modal.show-modal');\n        if (isModalPresented || !this.canSwipe()) {\n            return false;\n        }\n        if (this._isOpen) {\n            return true;\n        }\n        else if (menuController._getOpenSync()) {\n            return false;\n        }\n        return checkEdgeSide(window, detail.currentX, this.isEndSide, this.maxEdgeStart);\n    }\n    onWillStart() {\n        this.beforeAnimation(!this._isOpen);\n        return this.loadAnimation();\n    }\n    onStart() {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        // the cloned animation should not use an easing curve during seek\n        this.animation.progressStart(true, this._isOpen ? 1 : 0);\n    }\n    onMove(detail) {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        const delta = computeDelta(detail.deltaX, this._isOpen, this.isEndSide);\n        const stepValue = delta / this.width;\n        this.animation.progressStep(this._isOpen ? 1 - stepValue : stepValue);\n    }\n    onEnd(detail) {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        const isOpen = this._isOpen;\n        const isEndSide = this.isEndSide;\n        const delta = computeDelta(detail.deltaX, isOpen, isEndSide);\n        const width = this.width;\n        const stepValue = delta / width;\n        const velocity = detail.velocityX;\n        const z = width / 2.0;\n        const shouldCompleteRight = velocity >= 0 && (velocity > 0.2 || detail.deltaX > z);\n        const shouldCompleteLeft = velocity <= 0 && (velocity < -0.2 || detail.deltaX < -z);\n        const shouldComplete = isOpen\n            ? isEndSide\n                ? shouldCompleteRight\n                : shouldCompleteLeft\n            : isEndSide\n                ? shouldCompleteLeft\n                : shouldCompleteRight;\n        let shouldOpen = !isOpen && shouldComplete;\n        if (isOpen && !shouldComplete) {\n            shouldOpen = true;\n        }\n        this.lastOnEnd = detail.currentTime;\n        // Account for rounding errors in JS\n        let newStepValue = shouldComplete ? 0.001 : -0.001;\n        /**\n         * stepValue can sometimes return a negative\n         * value, but you can't have a negative time value\n         * for the cubic bezier curve (at least with web animations)\n         */\n        const adjustedStepValue = stepValue < 0 ? 0.01 : stepValue;\n        /**\n         * Animation will be reversed here, so need to\n         * reverse the easing curve as well\n         *\n         * Additionally, we need to account for the time relative\n         * to the new easing curve, as `stepValue` is going to be given\n         * in terms of a linear curve.\n         */\n        newStepValue +=\n            getTimeGivenProgression([0, 0], [0.4, 0], [0.6, 1], [1, 1], clamp(0, adjustedStepValue, 0.9999))[0] || 0;\n        const playTo = this._isOpen ? !shouldComplete : shouldComplete;\n        this.animation\n            .easing('cubic-bezier(0.4, 0.0, 0.6, 1)')\n            .onFinish(() => this.afterAnimation(shouldOpen), { oneTimeCallback: true })\n            .progressEnd(playTo ? 1 : 0, this._isOpen ? 1 - newStepValue : newStepValue, 300);\n    }\n    beforeAnimation(shouldOpen) {\n        assert(!this.isAnimating, '_before() should not be called while animating');\n        // this places the menu into the correct location before it animates in\n        // this css class doesn't actually kick off any animations\n        this.el.classList.add(SHOW_MENU);\n        /**\n         * We add a tabindex here so that focus trapping\n         * still works even if the menu does not have\n         * any focusable elements slotted inside. The\n         * focus trapping utility will fallback to focusing\n         * the menu so focus does not leave when the menu\n         * is open.\n         */\n        this.el.setAttribute('tabindex', '0');\n        if (this.backdropEl) {\n            this.backdropEl.classList.add(SHOW_BACKDROP);\n        }\n        // add css class and hide content behind menu from screen readers\n        if (this.contentEl) {\n            this.contentEl.classList.add(MENU_CONTENT_OPEN);\n            /**\n             * When the menu is open and overlaying the main\n             * content, the main content should not be announced\n             * by the screenreader as the menu is the main\n             * focus. This is useful with screenreaders that have\n             * \"read from top\" gestures that read the entire\n             * page from top to bottom when activated.\n             * This should be done before the animation starts\n             * so that users cannot accidentally scroll\n             * the content while dragging a menu open.\n             */\n            this.contentEl.setAttribute('aria-hidden', 'true');\n        }\n        this.blocker.block();\n        this.isAnimating = true;\n        if (shouldOpen) {\n            this.ionWillOpen.emit();\n        }\n        else {\n            this.ionWillClose.emit();\n        }\n    }\n    afterAnimation(isOpen) {\n        var _a;\n        // keep opening/closing the menu disabled for a touch more yet\n        // only add listeners/css if it's enabled and isOpen\n        // and only remove listeners/css if it's not open\n        // emit opened/closed events\n        this._isOpen = isOpen;\n        this.isAnimating = false;\n        if (!this._isOpen) {\n            this.blocker.unblock();\n        }\n        if (isOpen) {\n            // emit open event\n            this.ionDidOpen.emit();\n            /**\n             * Move focus to the menu to prepare focus trapping, as long as\n             * it isn't already focused. Use the host element instead of the\n             * first descendant to avoid the scroll position jumping around.\n             */\n            const focusedMenu = (_a = document.activeElement) === null || _a === void 0 ? void 0 : _a.closest('ion-menu');\n            if (focusedMenu !== this.el) {\n                this.el.focus();\n            }\n            // start focus trapping\n            document.addEventListener('focus', this.handleFocus, true);\n        }\n        else {\n            // remove css classes and unhide content from screen readers\n            this.el.classList.remove(SHOW_MENU);\n            /**\n             * Remove tabindex from the menu component\n             * so that is cannot be tabbed to.\n             */\n            this.el.removeAttribute('tabindex');\n            if (this.contentEl) {\n                this.contentEl.classList.remove(MENU_CONTENT_OPEN);\n                /**\n                 * Remove aria-hidden so screen readers\n                 * can announce the main content again\n                 * now that the menu is not the main focus.\n                 */\n                this.contentEl.removeAttribute('aria-hidden');\n            }\n            if (this.backdropEl) {\n                this.backdropEl.classList.remove(SHOW_BACKDROP);\n            }\n            if (this.animation) {\n                this.animation.stop();\n            }\n            // emit close event\n            this.ionDidClose.emit();\n            // undo focus trapping so multiple menus don't collide\n            document.removeEventListener('focus', this.handleFocus, true);\n        }\n    }\n    updateState() {\n        const isActive = this._isActive();\n        if (this.gesture) {\n            this.gesture.enable(isActive && this.swipeGesture);\n        }\n        /**\n         * If the menu is disabled but it is still open\n         * then we should close the menu immediately.\n         * Additionally, if the menu is in the process\n         * of animating {open, close} and the menu is disabled\n         * then it should still be closed immediately.\n         */\n        if (!isActive) {\n            /**\n             * It is possible to disable the menu while\n             * it is mid-animation. When this happens, we\n             * need to set the operationCancelled flag\n             * so that this._setOpen knows to return false\n             * and not run the \"afterAnimation\" callback.\n             */\n            if (this.isAnimating) {\n                this.operationCancelled = true;\n            }\n            /**\n             * If the menu is disabled then we should\n             * forcibly close the menu even if it is open.\n             */\n            this.afterAnimation(false);\n        }\n    }\n    render() {\n        const { type, disabled, el, isPaneVisible, inheritedAttributes, side } = this;\n        const mode = getIonMode(this);\n        /**\n         * If the Close Watcher is enabled then\n         * the ionBackButton listener in the menu controller\n         * will handle closing the menu when Escape is pressed.\n         */\n        return (h(Host, { key: '30c0c9bfb8973e4a6feb658f8c4ee8e362f464ed', onKeyDown: shouldUseCloseWatcher() ? null : this.onKeydown, role: \"navigation\", \"aria-label\": inheritedAttributes['aria-label'] || 'menu', class: {\n                [mode]: true,\n                [`menu-type-${type}`]: true,\n                'menu-enabled': !disabled,\n                [`menu-side-${side}`]: true,\n                'menu-pane-visible': isPaneVisible,\n                'split-pane-side': hostContext('ion-split-pane', el),\n            } }, h(\"div\", { key: '34b0e5840906862cf1bc27207e089004b0402c56', class: \"menu-inner\", part: \"container\", ref: (el) => (this.menuInnerEl = el) }, h(\"slot\", { key: '2cd7e61a8c0987ca4b3f1f8b33cba7152f1275fe' })), h(\"ion-backdrop\", { key: 'd190b1f9b66c76e276f27bfe074d3aab796180fb', ref: (el) => (this.backdropEl = el), class: \"menu-backdrop\", tappable: false, stopPropagation: false, part: \"backdrop\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"type\": [\"typeChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"side\": [\"sideChanged\"],\n        \"swipeGesture\": [\"swipeGestureChanged\"]\n    }; }\n};\nconst computeDelta = (deltaX, isOpen, isEndSide) => {\n    return Math.max(0, isOpen !== isEndSide ? -deltaX : deltaX);\n};\nconst checkEdgeSide = (win, posX, isEndSide, maxEdgeStart) => {\n    if (isEndSide) {\n        return posX >= win.innerWidth - maxEdgeStart;\n    }\n    else {\n        return posX <= maxEdgeStart;\n    }\n};\nconst SHOW_MENU = 'show-menu';\nconst SHOW_BACKDROP = 'show-backdrop';\nconst MENU_CONTENT_OPEN = 'menu-content-open';\nMenu.style = {\n    ios: IonMenuIosStyle0,\n    md: IonMenuMdStyle0\n};\n\n// Given a menu, return whether or not the menu toggle should be visible\nconst updateVisibility = async (menu) => {\n    const menuEl = await menuController.get(menu);\n    return !!(menuEl && (await menuEl.isActive()));\n};\n\nconst menuButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #0054e9);--padding-start:5px;--padding-end:5px;min-height:32px;font-size:clamp(31px, 1.9375rem, 38.13px)}:host(.ion-activated){opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\nconst IonMenuButtonIosStyle0 = menuButtonIosCss;\n\nconst menuButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:.04;--border-radius:50%;--color:initial;--padding-start:8px;--padding-end:8px;width:3rem;height:3rem;font-size:1.5rem}:host(.ion-color.ion-focused)::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}\";\nconst IonMenuButtonMdStyle0 = menuButtonMdCss;\n\nconst MenuButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inheritedAttributes = {};\n        this.onClick = async () => {\n            return menuController.toggle(this.menu);\n        };\n        this.visible = false;\n        this.color = undefined;\n        this.disabled = false;\n        this.menu = undefined;\n        this.autoHide = true;\n        this.type = 'button';\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    componentDidLoad() {\n        this.visibilityChanged();\n    }\n    async visibilityChanged() {\n        this.visible = await updateVisibility(this.menu);\n    }\n    render() {\n        const { color, disabled, inheritedAttributes } = this;\n        const mode = getIonMode(this);\n        const menuIcon = config.get('menuIcon', mode === 'ios' ? menuOutline : menuSharp);\n        const hidden = this.autoHide && !this.visible;\n        const attrs = {\n            type: this.type,\n        };\n        const ariaLabel = inheritedAttributes['aria-label'] || 'menu';\n        return (h(Host, { key: '95a8b9f09c7fae9713a8dc003ed277f6f31403da', onClick: this.onClick, \"aria-disabled\": disabled ? 'true' : null, \"aria-hidden\": hidden ? 'true' : null, class: createColorClasses(color, {\n                [mode]: true,\n                button: true, // ion-buttons target .button\n                'menu-button-hidden': hidden,\n                'menu-button-disabled': disabled,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'ion-activatable': true,\n                'ion-focusable': true,\n            }) }, h(\"button\", Object.assign({ key: '39f3ce20c400d2fac4890a042e8e44426709fca5' }, attrs, { disabled: disabled, class: \"button-native\", part: \"native\", \"aria-label\": ariaLabel }), h(\"span\", { key: '310978dc1cdef668de6720cde2a2304253679176', class: \"button-inner\" }, h(\"slot\", { key: '2a2b9de524c1fc3c526fe9559cb077b976852725' }, h(\"ion-icon\", { key: '9c22d7ea9fc3d76c32ec1c1b4b13d982c60b8c2d', part: \"icon\", icon: menuIcon, mode: mode, lazy: false, \"aria-hidden\": \"true\" }))), mode === 'md' && h(\"ion-ripple-effect\", { key: 'c58c9e29c763070383472f65a9d322a684bcb564', type: \"unbounded\" }))));\n    }\n    get el() { return getElement(this); }\n};\nMenuButton.style = {\n    ios: IonMenuButtonIosStyle0,\n    md: IonMenuButtonMdStyle0\n};\n\nconst menuToggleCss = \":host(.menu-toggle-hidden){display:none}\";\nconst IonMenuToggleStyle0 = menuToggleCss;\n\nconst MenuToggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.onClick = () => {\n            return menuController.toggle(this.menu);\n        };\n        this.visible = false;\n        this.menu = undefined;\n        this.autoHide = true;\n    }\n    connectedCallback() {\n        this.visibilityChanged();\n    }\n    async visibilityChanged() {\n        this.visible = await updateVisibility(this.menu);\n    }\n    render() {\n        const mode = getIonMode(this);\n        const hidden = this.autoHide && !this.visible;\n        return (h(Host, { key: '90e621f09792383f1badcc1b402b1ac7d08c5f98', onClick: this.onClick, \"aria-hidden\": hidden ? 'true' : null, class: {\n                [mode]: true,\n                'menu-toggle-hidden': hidden,\n            } }, h(\"slot\", { key: 'c0abdd1d91e9d80ee3704e3e374ebe1f29078460' })));\n    }\n};\nMenuToggle.style = IonMenuToggleStyle0;\n\nexport { Menu as ion_menu, MenuButton as ion_menu_button, MenuToggle as ion_menu_toggle };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "g", "getTimeGivenProgression", "o", "getPresentedOverlay", "n", "focusFirstDescendant", "q", "focusLastDescendant", "G", "GESTURE_CONTROLLER", "shouldUseCloseWatcher", "isEndSide", "inheritAriaAttributes", "m", "assert", "j", "clamp", "menuController", "hostContext", "c", "createColorClasses", "config", "b", "getIonMode", "u", "menuOutline", "v", "menuSharp", "menuIosCss", "IonMenuIosStyle0", "menuMdCss", "IonMenuMdStyle0", "iosEasing", "mdEasing", "iosEasingReverse", "mdEasingReverse", "<PERSON><PERSON>", "constructor", "hostRef", "ionWillOpen", "ionWillClose", "ionDidOpen", "ionDidClose", "ionMenuChange", "lastOnEnd", "blocker", "createBlocker", "disableScroll", "didLoad", "operationCancelled", "isAnimating", "_isOpen", "inheritedAttributes", "handleFocus", "ev", "lastOverlay", "document", "contains", "el", "trapKeyboardFocus", "isPaneVisible", "contentId", "undefined", "menuId", "type", "disabled", "side", "swipeGesture", "maxEdgeStart", "typeChanged", "oldType", "contentEl", "classList", "remove", "add", "removeAttribute", "menuInnerEl", "animation", "disabled<PERSON><PERSON>ed", "updateState", "emit", "open", "sideChanged", "swipeGestureChanged", "connectedCallback", "_this", "_asyncToGenerator", "customElements", "whenDefined", "get", "content", "getElementById", "console", "error", "_register", "menuChanged", "gesture", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "blurOnStart", "canStart", "onWillStart", "onStart", "onMove", "onEnd", "componentWillLoad", "componentDidLoad", "_this2", "splitPane", "closest", "isVisible", "disconnectedCallback", "_this3", "close", "destroy", "_unregister", "onSplitPaneChanged", "closestSplitPane", "target", "detail", "visible", "onBackdropClick", "timeStamp", "shouldClose", "<PERSON><PERSON><PERSON>", "includes", "preventDefault", "stopPropagation", "onKeydown", "key", "isOpen", "Promise", "resolve", "isActive", "_isActive", "animated", "<PERSON><PERSON><PERSON>", "toggle", "shouldOpen", "_setOpen", "doc", "lastFocus", "activeElement", "_this4", "beforeAnimation", "loadAnimation", "startAnimation", "afterAnimation", "_this5", "width", "offsetWidth", "isEndSide$1", "_createAnimation", "getBoolean", "duration", "fill", "_this6", "isReversed", "mode", "easing", "easingReverse", "ani", "direction", "play", "sync", "getDirection", "canSwipe", "isModalPresented", "querySelector", "_getOpenSync", "checkEdgeSide", "window", "currentX", "progressStart", "delta", "computeDelta", "deltaX", "<PERSON><PERSON><PERSON><PERSON>", "progressStep", "velocity", "velocityX", "z", "shouldCompleteRight", "shouldCompleteLeft", "shouldComplete", "currentTime", "newStepValue", "adjustedStepValue", "playTo", "onFinish", "oneTimeCallback", "progressEnd", "SHOW_MENU", "setAttribute", "backdropEl", "SHOW_BACKDROP", "MENU_CONTENT_OPEN", "block", "_a", "unblock", "focusedMenu", "focus", "addEventListener", "stop", "removeEventListener", "enable", "render", "onKeyDown", "role", "class", "part", "ref", "tappable", "watchers", "Math", "max", "win", "posX", "innerWidth", "style", "ios", "md", "updateVisibility", "_ref", "menu", "menuEl", "_x", "apply", "arguments", "menuButtonIosCss", "IonMenuButtonIosStyle0", "menuButtonMdCss", "IonMenuButtonMdStyle0", "MenuButton", "_this7", "onClick", "color", "autoHide", "visibilityChanged", "_this8", "menuIcon", "hidden", "attrs", "aria<PERSON><PERSON><PERSON>", "button", "Object", "assign", "icon", "lazy", "menuToggleCss", "IonMenuToggleStyle0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this9", "ion_menu", "ion_menu_button", "ion_menu_toggle"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}