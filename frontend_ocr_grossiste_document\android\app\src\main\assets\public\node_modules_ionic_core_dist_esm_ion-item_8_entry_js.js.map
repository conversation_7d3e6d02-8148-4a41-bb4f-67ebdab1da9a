{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-item_8_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC+H;AACtD;AACqB;AACpC;AACgB;AAE1E,MAAMsB,UAAU,GAAG,qjSAAqjS;AACxkS,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,SAAS,GAAG,0gUAA0gU;AAC5hU,MAAMC,eAAe,GAAGD,SAAS;AAEjC,MAAME,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,uBAAuB,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,MAAM,GAAGF,SAAS;IACvB,IAAI,CAACG,UAAU,GAAGrB,iDAAc;IAChC,IAAI,CAACsB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAGL,SAAS;IACzB,IAAI,CAACM,IAAI,GAAGN,SAAS;IACrB,IAAI,CAACO,GAAG,GAAGP,SAAS;IACpB,IAAI,CAACQ,KAAK,GAAGR,SAAS;IACtB,IAAI,CAACS,eAAe,GAAGT,SAAS;IAChC,IAAI,CAACU,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,MAAM,GAAGX,SAAS;IACvB,IAAI,CAACY,IAAI,GAAG,QAAQ;EACxB;EACAC,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,CAACf,SAAS,GAAG,IAAI,CAACgB,WAAW,CAAC,CAAC;EACvC;EACAC,iBAAiBA,CAACC,EAAE,EAAE;IAClB,MAAM;MAAEjB;IAAM,CAAC,GAAG,IAAI;IACtB;IACA;IACA;IACA,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACrB,IAAI,CAACP,gBAAgB,GAAGuB,EAAE,CAACd,MAAM;IACrC;EACJ;EACAe,SAASA,CAACD,EAAE,EAAE;IACVA,EAAE,CAACE,eAAe,CAAC,CAAC;IACpB,MAAMC,OAAO,GAAGH,EAAE,CAACL,MAAM,CAACQ,OAAO;IACjC,MAAMC,aAAa,GAAGJ,EAAE,CAACd,MAAM;IAC/B,MAAMmB,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,WAAW,GAAG,IAAI,CAAC5B,UAAU,CAAC6B,GAAG,CAACJ,OAAO,CAAC,IAAI,CAAC,CAAC;IACtD,IAAIK,cAAc,GAAG,KAAK;IAC1BC,MAAM,CAACC,IAAI,CAACN,aAAa,CAAC,CAACO,OAAO,CAAEC,GAAG,IAAK;MACxC,IAAIR,aAAa,CAACQ,GAAG,CAAC,EAAE;QACpB,MAAMC,OAAO,GAAI,QAAOD,GAAI,EAAC;QAC7B,IAAI,CAACN,WAAW,CAACO,OAAO,CAAC,EAAE;UACvBL,cAAc,GAAG,IAAI;QACzB;QACAH,SAAS,CAACQ,OAAO,CAAC,GAAG,IAAI;MAC7B;IACJ,CAAC,CAAC;IACF,IAAI,CAACL,cAAc,IAAIC,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACS,MAAM,KAAKL,MAAM,CAACC,IAAI,CAACJ,WAAW,CAAC,CAACQ,MAAM,EAAE;MACtFN,cAAc,GAAG,IAAI;IACzB;IACA,IAAIA,cAAc,EAAE;MAChB,IAAI,CAAC9B,UAAU,CAACqC,GAAG,CAACZ,OAAO,EAAEE,SAAS,CAAC;MACvCtD,qDAAW,CAAC,IAAI,CAAC;IACrB;EACJ;EACAiE,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACtC,uBAAuB,GAAGrB,uDAAiB,CAAC,IAAI,CAAC4D,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EAC7E;EACAC,gBAAgBA,CAAA,EAAG;IACf5D,uDAAG,CAAC,MAAM;MACN,IAAI,CAAC6D,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACvC,SAAS,GAAG,IAAI,CAACgB,WAAW,CAAC,CAAC;IACvC,CAAC,CAAC;EACN;EACA;EACA;EACA;EACAuB,iBAAiBA,CAAA,EAAG;IAChB;IACA,MAAMC,MAAM,GAAG,IAAI,CAACH,EAAE,CAACI,gBAAgB,CAAC,mDAAmD,CAAC;IAC5F;IACA;IACA;IACA,MAAMC,MAAM,GAAG,IAAI,CAACL,EAAE,CAACI,gBAAgB,CAAC,4EAA4E,CAAC;IACrH;IACA,MAAME,UAAU,GAAG,IAAI,CAACN,EAAE,CAACI,gBAAgB,CAAC,mCAAmC,CAAC;IAChF;IACA;IACA,IAAI,CAAC1C,cAAc,GACfyC,MAAM,CAACR,MAAM,GAAGU,MAAM,CAACV,MAAM,GAAG,CAAC,IAC7BQ,MAAM,CAACR,MAAM,GAAGW,UAAU,CAACX,MAAM,GAAG,CAAC,IACpCQ,MAAM,CAACR,MAAM,GAAG,CAAC,IAAI,IAAI,CAACY,WAAW,CAAC,CAAE;EACrD;EACA;EACA;EACA;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,MAAMH,MAAM,GAAG,IAAI,CAACL,EAAE,CAACI,gBAAgB,CAAC,mDAAmD,CAAC;IAC5F,OAAOC,MAAM,CAACV,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAACjC,cAAc;EACtD;EACA;EACA;EACA6C,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACpC,IAAI,KAAKN,SAAS,IAAI,IAAI,CAACC,MAAM;EACjD;EACA2C,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,WAAW,CAAC,CAAC,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC;EAChD;EACA7B,WAAWA,CAAA,EAAG;IACV,MAAM+B,cAAc,GAAG,IAAI,CAACV,EAAE,CAACW,aAAa,CAAC,gBAAgB,CAAC;IAC9D,OAAO,IAAI,CAACF,WAAW,CAAC,CAAC,IAAIC,cAAc,KAAK,IAAI;EACxD;EACAZ,UAAUA,CAAA,EAAG;IACT,MAAMc,OAAO,GAAG,IAAI,CAACZ,EAAE,CAACW,aAAa,CAAC,gBAAgB,CAAC;IACvD,IAAIC,OAAO,KAAK,IAAI,EAAE;MAClB,IAAI,CAACZ,EAAE,CAACa,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAChD;EACJ;EACAC,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,QAAQ,GAAG,IAAI,CAAChB,EAAE,CAACI,gBAAgB,CAAC,0KAA0K,CAAC;IACrN,OAAOY,QAAQ,CAAC,CAAC,CAAC;EACtB;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAElD,MAAM;MAAEC,UAAU;MAAEE,QAAQ;MAAEZ,gBAAgB;MAAEe,KAAK;MAAEJ,QAAQ;MAAEE,IAAI;MAAEC,GAAG;MAAEI,MAAM;MAAEF,eAAe;MAAEC,eAAe;MAAEd,uBAAuB;MAAEC;IAAgB,CAAC,GAAG,IAAI;IAC/K,MAAMyB,WAAW,GAAG,CAAC,CAAC;IACtB,MAAM+B,IAAI,GAAGrE,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMsE,SAAS,GAAG,IAAI,CAACZ,WAAW,CAAC,CAAC;IACpC,MAAME,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC;IACtC,MAAMW,OAAO,GAAGD,SAAS,GAAIhD,IAAI,KAAKN,SAAS,GAAG,QAAQ,GAAG,GAAG,GAAI,KAAK;IACzE,MAAMwD,KAAK,GAAGD,OAAO,KAAK,QAAQ,GAC5B;MAAE3C,IAAI,EAAE,IAAI,CAACA;IAAK,CAAC,GACnB;MACEP,QAAQ;MACRC,IAAI;MACJC,GAAG;MACHI;IACJ,CAAC;IACL,IAAI8C,OAAO,GAAG,CAAC,CAAC;IAChB,MAAMC,gBAAgB,GAAG,IAAI,CAACR,mBAAmB,CAAC,CAAC;IACnD;IACA;IACA,IAAII,SAAS,IAAKI,gBAAgB,KAAK1D,SAAS,IAAI,CAACH,cAAe,EAAE;MAClE4D,OAAO,GAAG;QACNE,OAAO,EAAG3C,EAAE,IAAK;UACb,IAAIsC,SAAS,EAAE;YACXzE,qDAAO,CAACyB,IAAI,EAAEU,EAAE,EAAEN,eAAe,EAAED,eAAe,CAAC;UACvD;UACA,IAAIiD,gBAAgB,KAAK1D,SAAS,IAAI,CAACH,cAAc,EAAE;YACnD,MAAM+D,IAAI,GAAG5C,EAAE,CAAC6C,YAAY,CAAC,CAAC;YAC9B,MAAMlD,MAAM,GAAGiD,IAAI,CAAC,CAAC,CAAC;YACtB,IAAI5C,EAAE,CAAC8C,SAAS,EAAE;cACd;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;cAC4B,MAAMC,uBAAuB,GAAG,IAAI,CAAC5B,EAAE,CAAC6B,UAAU,CAACC,QAAQ,CAACtD,MAAM,CAAC;cACnE,IAAIoD,uBAAuB,EAAE;gBACzB;AAChC;AACA;AACA;AACA;gBACgC,IAAIL,gBAAgB,CAACvC,OAAO,KAAK,WAAW,IAAIuC,gBAAgB,CAACvC,OAAO,KAAK,cAAc,EAAE;kBACzFuC,gBAAgB,CAACQ,QAAQ,CAAC,CAAC;gBAC/B,CAAC,MACI;kBACDR,gBAAgB,CAACS,KAAK,CAAC,CAAC;gBAC5B;cACJ;YACJ;UACJ;QACJ;MACJ,CAAC;IACL;IACA,MAAMC,UAAU,GAAGlE,MAAM,KAAKF,SAAS,GAAGE,MAAM,GAAGmD,IAAI,KAAK,KAAK,IAAIC,SAAS;IAC9E,IAAI,CAAC5D,UAAU,CAACiC,OAAO,CAAE0C,KAAK,IAAK;MAC/B5C,MAAM,CAAC6C,MAAM,CAAChD,WAAW,EAAE+C,KAAK,CAAC;IACrC,CAAC,CAAC;IACF,MAAME,YAAY,GAAGnE,QAAQ,IAAIkB,WAAW,CAAC,2BAA2B,CAAC,GAAG,MAAM,GAAG,IAAI;IACzF,MAAMkD,MAAM,GAAG/F,qDAAW,CAAC,UAAU,EAAE,IAAI,CAAC0D,EAAE,CAAC,IAAI,CAAC1D,qDAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC0D,EAAE,CAAC;IAC3F;AACR;AACA;AACA;IACQ,MAAMsC,kCAAkC,GAAGf,gBAAgB,KAAK1D,SAAS,IAAI,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC0E,QAAQ,CAAChB,gBAAgB,CAACvC,OAAO,CAAC;IAC9I,OAAQnD,qDAAC,CAACE,iDAAI,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE,eAAe,EAAE2C,YAAY;MAAEI,KAAK,EAAElD,MAAM,CAAC6C,MAAM,CAAC7C,MAAM,CAAC6C,MAAM,CAAC7C,MAAM,CAAC6C,MAAM,CAAC,CAAC,CAAC,EAAEhD,WAAW,CAAC,EAAE7B,gBAAgB,CAAC,EAAEd,qDAAkB,CAAC,IAAI,CAACoB,KAAK,EAAE;QAC/M6E,IAAI,EAAE,IAAI;QACV,CAACvB,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAE7C,KAAK,KAAKR,SAAS;QACzC,CAAE,cAAaQ,KAAM,EAAC,GAAGA,KAAK,KAAKR,SAAS;QAC5C,mCAAmC,EAAEyE,kCAAkC;QACvE,eAAe,EAAErE,QAAQ;QACzB,SAAS,EAAEoE,MAAM;QACjB,sBAAsB,EAAE,IAAI,CAAC3E,cAAc;QAC3C,iBAAiB,EAAE+C,WAAW;QAC9B,eAAe,EAAE,IAAI,CAAC9C,SAAS;QAC/B,UAAU,EAAE+E,QAAQ,CAACC,GAAG,KAAK;MACjC,CAAC,CAAC,CAAC;MAAEC,IAAI,EAAEP,MAAM,GAAG,UAAU,GAAG;IAAK,CAAC,EAAExG,qDAAC,CAACuF,OAAO,EAAE9B,MAAM,CAAC6C,MAAM,CAAC;MAAE1C,GAAG,EAAE;IAA2C,CAAC,EAAE4B,KAAK,EAAE5D,uBAAuB,EAAE;MAAE+E,KAAK,EAAE,aAAa;MAAEK,IAAI,EAAE,QAAQ;MAAE5E,QAAQ,EAAEA;IAAS,CAAC,EAAEqD,OAAO,CAAC,EAAEzF,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAEqD,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEjH,qDAAC,CAAC,KAAK,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAE+C,KAAK,EAAE;IAAa,CAAC,EAAE3G,qDAAC,CAAC,KAAK,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAE+C,KAAK,EAAE;IAAgB,CAAC,EAAE3G,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE5D,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAEqD,IAAI,EAAE;IAAM,CAAC,CAAC,EAAEb,UAAU,IAAKpG,qDAAC,CAAC,UAAU,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAEsD,IAAI,EAAE/E,UAAU;MAAEgF,IAAI,EAAE,KAAK;MAAER,KAAK,EAAE,kBAAkB;MAAEK,IAAI,EAAE,aAAa;MAAE,aAAa,EAAE,MAAM;MAAE,UAAU,EAAE7E,UAAU,KAAKrB,iDAAcA;IAAC,CAAC,CAAE,CAAC,EAAE8D,WAAW,IAAIS,IAAI,KAAK,IAAI,IAAIrF,qDAAC,CAAC,mBAAmB,EAAE;MAAE4D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACh8B;EACA,IAAIO,EAAEA,CAAA,EAAG;IAAE,OAAO/D,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWgH,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,eAAe;IAC9B,CAAC;EAAE;AACP,CAAC;AACD9F,IAAI,CAAC+F,KAAK,GAAG;EACTC,GAAG,EAAEnG,gBAAgB;EACrBoG,EAAE,EAAElG;AACR,CAAC;AAED,MAAMmG,iBAAiB,GAAG,wkHAAwkH;AAClmH,MAAMC,uBAAuB,GAAGD,iBAAiB;AAEjD,MAAME,gBAAgB,GAAG,o+IAAo+I;AAC7/I,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,WAAW,GAAG,MAAM;EACtBrG,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACO,KAAK,GAAGC,SAAS;IACtB,IAAI,CAAC6F,MAAM,GAAG,KAAK;EACvB;EACAzC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGrE,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQhB,qDAAC,CAACE,iDAAI,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE+C,KAAK,EAAEhG,qDAAkB,CAAC,IAAI,CAACoB,KAAK,EAAE;QACjG,CAACsD,IAAI,GAAG,IAAI;QACZ,qBAAqB,EAAE,IAAI,CAACwC,MAAM;QAClCjB,IAAI,EAAE;MACV,CAAC;IAAE,CAAC,EAAE5G,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAEqD,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEjH,qDAAC,CAAC,KAAK,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAE+C,KAAK,EAAE;IAAqB,CAAC,EAAE3G,qDAAC,CAAC,KAAK,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAE+C,KAAK,EAAE;IAAuB,CAAC,EAAE3G,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE5D,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAEqD,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC;EACna;EACA,IAAI9C,EAAEA,CAAA,EAAG;IAAE,OAAO/D,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDwH,WAAW,CAACP,KAAK,GAAG;EAChBC,GAAG,EAAEG,uBAAuB;EAC5BF,EAAE,EAAEI;AACR,CAAC;AAED,MAAMG,eAAe,GAAG,+BAA+B;AACvD,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,cAAc,GAAG,+BAA+B;AACtD,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,SAAS,GAAG,MAAM;EACpB3G,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;EACnC;EACA4D,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGrE,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQhB,qDAAC,CAACE,iDAAI,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEmD,IAAI,EAAE,OAAO;MAAEJ,KAAK,EAAE;QACjF,CAACtB,IAAI,GAAG,IAAI;QACZ;QACA,CAAE,cAAaA,IAAK,EAAC,GAAG,IAAI;QAC5BuB,IAAI,EAAE;MACV;IAAE,CAAC,CAAC;EACZ;AACJ,CAAC;AACDsB,SAAS,CAACb,KAAK,GAAG;EACdC,GAAG,EAAES,qBAAqB;EAC1BR,EAAE,EAAEU;AACR,CAAC;AAED,MAAME,WAAW,GAAG,23HAA23H;AAC/4H,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,gvKAAgvK;AACnwK,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBhH,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACgH,QAAQ,GAAGlI,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACmI,QAAQ,GAAGnI,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACoI,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC3G,KAAK,GAAGC,SAAS;IACtB,IAAI,CAAC2G,QAAQ,GAAG3G,SAAS;IACzB,IAAI,CAAC4G,SAAS,GAAG,KAAK;EAC1B;EACA1E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACwE,OAAO,GAAG,CAAC,CAAC,IAAI,CAACvE,EAAE,CAAC0E,OAAO,CAAC,WAAW,CAAC;IAC7C,IAAI,CAACD,SAAS,GAAG,IAAI,CAACD,QAAQ,KAAK,UAAU;IAC7C,IAAI,CAACG,SAAS,CAAC,CAAC;IAChB,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACA3E,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACwE,SAAS,EAAE;MAChBI,UAAU,CAAC,MAAM;QACb,IAAI,CAACJ,SAAS,GAAG,KAAK;MAC1B,CAAC,EAAE,IAAI,CAAC;IACZ;EACJ;EACAK,YAAYA,CAAA,EAAG;IACX,IAAI,CAACF,SAAS,CAAC,CAAC;EACpB;EACAG,eAAeA,CAAA,EAAG;IACd,IAAI,CAACJ,SAAS,CAAC,CAAC;EACpB;EACAC,SAASA,CAAA,EAAG;IACR,MAAM;MAAEhH;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAACyG,QAAQ,CAACW,IAAI,CAAC;MACf,kBAAkB,EAAEpH,KAAK,KAAKC,SAAS;MACvC,CAAE,aAAYD,KAAM,EAAC,GAAGA,KAAK,KAAKC;IACtC,CAAC,CAAC;EACN;EACA8G,SAASA,CAAA,EAAG;IACR,MAAM;MAAEJ,OAAO;MAAEC;IAAS,CAAC,GAAG,IAAI;IAClC;IACA;IACA;IACA,IAAI,CAACD,OAAO,EAAE;MACV,IAAI,CAACD,QAAQ,CAACU,IAAI,CAAC;QACfC,KAAK,EAAE,IAAI;QACX,CAAE,SAAQT,QAAS,EAAC,GAAGA,QAAQ,KAAK3G;MACxC,CAAC,CAAC;IACN;EACJ;EACAoD,MAAMA,CAAA,EAAG;IACL,MAAMuD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMtD,IAAI,GAAGrE,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQhB,qDAAC,CAACE,iDAAI,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE+C,KAAK,EAAEhG,qDAAkB,CAAC,IAAI,CAACoB,KAAK,EAAE;QACjG,CAACsD,IAAI,GAAG,IAAI;QACZ,eAAe,EAAE5E,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC0D,EAAE,CAAC;QAC3D,CAAE,SAAQwE,QAAS,EAAC,GAAGA,QAAQ,KAAK3G,SAAS;QAC7C,CAAE,kBAAiB,GAAG,IAAI,CAAC4G,SAAS;QACpC,WAAW,EAAE/B,QAAQ,CAACC,GAAG,KAAK;MAClC,CAAC;IAAE,CAAC,EAAE9G,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;EACA,IAAIO,EAAEA,CAAA,EAAG;IAAE,OAAO/D,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWgH,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACDmB,KAAK,CAAClB,KAAK,GAAG;EACVC,GAAG,EAAEc,iBAAiB;EACtBb,EAAE,EAAEe;AACR,CAAC;AAED,MAAMe,UAAU,GAAG,knCAAknC;AACroC,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,SAAS,GAAG,4zCAA4zC;AAC90C,MAAMC,eAAe,GAAGD,SAAS;AAEjC,MAAME,IAAI,GAAG,MAAM;EACflI,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACgB,KAAK,GAAGR,SAAS;IACtB,IAAI,CAAC0H,KAAK,GAAG,KAAK;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACUC,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACtB,MAAMjD,IAAI,GAAGgD,KAAI,CAACzF,EAAE,CAACW,aAAa,CAAC,kBAAkB,CAAC;MACtD,IAAI8B,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACkD,WAAW,EAAE;QAC9D,OAAOlD,IAAI,CAACkD,WAAW,CAAC,CAAC;MAC7B;MACA,OAAO,KAAK;IAAC;EACjB;EACA1E,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGrE,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEwB,KAAK;MAAEkH;IAAM,CAAC,GAAG,IAAI;IAC7B,OAAQ1J,qDAAC,CAACE,iDAAI,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEmD,IAAI,EAAE,MAAM;MAAEJ,KAAK,EAAE;QAChF,CAACtB,IAAI,GAAG,IAAI;QACZ;QACA,CAAE,QAAOA,IAAK,EAAC,GAAG,IAAI;QACtB,YAAY,EAAEqE,KAAK;QACnB,CAAE,cAAalH,KAAM,EAAC,GAAGA,KAAK,KAAKR,SAAS;QAC5C,CAAE,QAAOqD,IAAK,UAAS7C,KAAM,EAAC,GAAGA,KAAK,KAAKR;MAC/C;IAAE,CAAC,CAAC;EACZ;EACA,IAAImC,EAAEA,CAAA,EAAG;IAAE,OAAO/D,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDqJ,IAAI,CAACpC,KAAK,GAAG;EACTC,GAAG,EAAEgC,gBAAgB;EACrB/B,EAAE,EAAEiC;AACR,CAAC;AAED,MAAMO,gBAAgB,GAAG,q2EAAq2E;AAC93E,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,eAAe,GAAG,u/DAAu/D;AAC/gE,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,UAAU,GAAG,MAAM;EACrB5I,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACO,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACQ,KAAK,GAAGR,SAAS;EAC1B;EACAoD,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE5C;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM6C,IAAI,GAAGrE,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQhB,qDAAC,CAACE,iDAAI,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE+C,KAAK,EAAEhG,qDAAkB,CAAC,IAAI,CAACoB,KAAK,EAAE;QACjG,CAACsD,IAAI,GAAG,IAAI;QACZ,CAAE,qBAAoB7C,KAAM,EAAC,GAAGA,KAAK,KAAKR;MAC9C,CAAC;IAAE,CAAC,EAAEhC,qDAAC,CAAC,KAAK,EAAE;MAAE4D,GAAG,EAAE,0CAA0C;MAAE+C,KAAK,EAAE;IAAoB,CAAC,EAAE3G,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACxK;AACJ,CAAC;AACDuG,UAAU,CAAC9C,KAAK,GAAG;EACfC,GAAG,EAAE0C,sBAAsB;EAC3BzC,EAAE,EAAE2C;AACR,CAAC;AAED,MAAME,UAAU,GAAG,oRAAoR;AACvS,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,SAAS,GAAG,6QAA6Q;AAC/R,MAAMC,eAAe,GAAGD,SAAS;AAEjC,MAAME,IAAI,GAAG,MAAM;EACfjJ,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACO,KAAK,GAAGC,SAAS;EAC1B;EACAoD,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGrE,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQhB,qDAAC,CAACE,iDAAI,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE+C,KAAK,EAAEhG,qDAAkB,CAAC,IAAI,CAACoB,KAAK,EAAE;QACjG,CAACsD,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAErF,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;AACJ,CAAC;AACD4G,IAAI,CAACnD,KAAK,GAAG;EACTC,GAAG,EAAE+C,gBAAgB;EACrB9C,EAAE,EAAEgD;AACR,CAAC;AAED,MAAME,eAAe,GAAG,umDAAumD;AAC/nD,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,YAAY,GAAG,MAAM;EACvBpJ,WAAWA,CAACC,OAAO,EAAE;IACjB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACiH,QAAQ,GAAGnI,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACsK,QAAQ,GAAG,KAAK;EACzB;EACA1G,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC4E,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR;IACA;IACA;IACA,MAAMzB,KAAK,GAAG;MACV,eAAe,EAAE;IACrB,CAAC;IACD,IAAI,CAACoB,QAAQ,CAACU,IAAI,CAAC9B,KAAK,CAAC;EAC7B;EACAjC,MAAMA,CAAA,EAAG;IACL,MAAMwF,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI3J,wDAAM,CAAC4J,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;IACrE,MAAMC,OAAO,GAAGrK,qDAAW,CAAC,YAAY,EAAE,IAAI,CAAC0D,EAAE,CAAC,IAAI1D,qDAAW,CAAC,eAAe,EAAE,IAAI,CAAC0D,EAAE,CAAC;IAC3F,MAAMkB,IAAI,GAAGrE,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQhB,qDAAC,CAACE,iDAAI,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE+C,KAAK,EAAE;QAClE,CAACtB,IAAI,GAAG,IAAI;QACZ,wBAAwB,EAAEuF,QAAQ;QAClC,UAAU,EAAEE;MAChB;IAAE,CAAC,EAAE9K,qDAAC,CAAC,MAAM,EAAE;MAAE4D,GAAG,EAAE;IAA2C,CAAC,EAAE,QAAQ,CAAC,CAAC;EACtF;EACA,IAAIO,EAAEA,CAAA,EAAG;IAAE,OAAO/D,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDuK,YAAY,CAACtD,KAAK,GAAGqD,qBAAqB", "sources": ["./node_modules/@ionic/core/dist/esm/ion-item_8.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, j as forceUpdate, h, f as Host, i as getElement, d as createEvent } from './index-c71c5417.js';\nimport { h as inheritAttributes, r as raf } from './helpers-da915de8.js';\nimport { h as hostContext, c as createColorClasses, o as openURL } from './theme-01f3f29c.js';\nimport { o as chevronForward } from './index-e2cf2ceb.js';\nimport { b as getIonMode, c as config } from './ionic-global-b9c0d1da.js';\n\nconst itemIosCss = \":host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color) .item-native,:host(.ion-color) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-control-needs-pointer-cursor){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host{--min-height:44px;--transition:background-color 200ms linear, opacity 200ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0px 0px 0.55px 0px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:var(--ion-text-color, #000);--background-focused:var(--ion-text-color, #000);--background-hover:currentColor;--background-activated-opacity:.12;--background-focused-opacity:.15;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--color:var(--ion-item-color, var(--ion-text-color, #000));font-size:1rem}:host(.ion-activated){--transition:none}:host(.ion-color.ion-focused) .item-native::after{background:#000;opacity:0.15}:host(.ion-color.ion-activated) .item-native::after{background:#000;opacity:0.12}:host(.item-lines-full){--border-width:0px 0px 0.55px 0px}:host(.item-lines-inset){--inner-border-width:0px 0px 0.55px 0px}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0px}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0px}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}::slotted(.button-small){--padding-top:1px;--padding-bottom:1px;--padding-start:.5em;--padding-end:.5em;min-height:24px;font-size:0.8125rem}::slotted(ion-avatar){width:36px;height:36px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px}:host(.item-radio) ::slotted(ion-label),:host(.item-toggle) ::slotted(ion-label){-webkit-margin-start:0px;margin-inline-start:0px}::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host(.item-label-floating),:host(.item-label-stacked){--min-height:68px}\";\nconst IonItemIosStyle0 = itemIosCss;\n\nconst itemMdCss = \":host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color) .item-native,:host(.ion-color) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-control-needs-pointer-cursor){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host{--min-height:48px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--color:var(--ion-item-color, var(--ion-text-color, #000));--transition:opacity 15ms linear, background-color 15ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0 0 1px 0;font-size:1rem;font-weight:normal;text-transform:none}:host(.ion-color.ion-activated) .item-native::after{background:transparent}:host(.item-interactive){--border-width:0 0 1px 0;--inner-border-width:0}:host(.item-lines-full){--border-width:0 0 1px 0}:host(.item-lines-inset){--inner-border-width:0 0 1px 0}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0}:host(.item-multi-line) ::slotted([slot=start]),:host(.item-multi-line) ::slotted([slot=end]){margin-top:16px;margin-bottom:16px;-ms-flex-item-align:start;align-self:flex-start}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.5em}:host(.ion-color) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:10px;margin-bottom:10px}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}:host(.item-toggle) ::slotted(ion-label),:host(.item-radio) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0}::slotted(.button-small){--padding-top:2px;--padding-bottom:2px;--padding-start:.6em;--padding-end:.6em;min-height:25px;font-size:0.75rem}:host(.item-label-floating),:host(.item-label-stacked){--min-height:55px}:host(.ion-focused:not(.ion-color)) ::slotted(.label-stacked),:host(.ion-focused:not(.ion-color)) ::slotted(.label-floating),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-stacked),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-floating){color:var(--ion-color-primary, #0054e9)}\";\nconst IonItemMdStyle0 = itemMdCss;\n\nconst Item = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.labelColorStyles = {};\n        this.itemStyles = new Map();\n        this.inheritedAriaAttributes = {};\n        this.multipleInputs = false;\n        this.focusable = true;\n        this.color = undefined;\n        this.button = false;\n        this.detail = undefined;\n        this.detailIcon = chevronForward;\n        this.disabled = false;\n        this.download = undefined;\n        this.href = undefined;\n        this.rel = undefined;\n        this.lines = undefined;\n        this.routerAnimation = undefined;\n        this.routerDirection = 'forward';\n        this.target = undefined;\n        this.type = 'button';\n    }\n    buttonChanged() {\n        // Update the focusable option when the button option is changed\n        this.focusable = this.isFocusable();\n    }\n    labelColorChanged(ev) {\n        const { color } = this;\n        // There will be a conflict with item color if\n        // we apply the label color to item, so we ignore\n        // the label color if the user sets a color on item\n        if (color === undefined) {\n            this.labelColorStyles = ev.detail;\n        }\n    }\n    itemStyle(ev) {\n        ev.stopPropagation();\n        const tagName = ev.target.tagName;\n        const updatedStyles = ev.detail;\n        const newStyles = {};\n        const childStyles = this.itemStyles.get(tagName) || {};\n        let hasStyleChange = false;\n        Object.keys(updatedStyles).forEach((key) => {\n            if (updatedStyles[key]) {\n                const itemKey = `item-${key}`;\n                if (!childStyles[itemKey]) {\n                    hasStyleChange = true;\n                }\n                newStyles[itemKey] = true;\n            }\n        });\n        if (!hasStyleChange && Object.keys(newStyles).length !== Object.keys(childStyles).length) {\n            hasStyleChange = true;\n        }\n        if (hasStyleChange) {\n            this.itemStyles.set(tagName, newStyles);\n            forceUpdate(this);\n        }\n    }\n    connectedCallback() {\n        this.hasStartEl();\n    }\n    componentWillLoad() {\n        this.inheritedAriaAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    componentDidLoad() {\n        raf(() => {\n            this.setMultipleInputs();\n            this.focusable = this.isFocusable();\n        });\n    }\n    // If the item contains multiple clickable elements and/or inputs, then the item\n    // should not have a clickable input cover over the entire item to prevent\n    // interfering with their individual click events\n    setMultipleInputs() {\n        // The following elements have a clickable cover that is relative to the entire item\n        const covers = this.el.querySelectorAll('ion-checkbox, ion-datetime, ion-select, ion-radio');\n        // The following elements can accept focus alongside the previous elements\n        // therefore if these elements are also a child of item, we don't want the\n        // input cover on top of those interfering with their clicks\n        const inputs = this.el.querySelectorAll('ion-input, ion-range, ion-searchbar, ion-segment, ion-textarea, ion-toggle');\n        // The following elements should also stay clickable when an input with cover is present\n        const clickables = this.el.querySelectorAll('ion-anchor, ion-button, a, button');\n        // Check for multiple inputs to change the position of the input cover to relative\n        // for all of the covered inputs above\n        this.multipleInputs =\n            covers.length + inputs.length > 1 ||\n                covers.length + clickables.length > 1 ||\n                (covers.length > 0 && this.isClickable());\n    }\n    // If the item contains an input including a checkbox, datetime, select, or radio\n    // then the item will have a clickable input cover that covers the item\n    // that should get the hover, focused and activated states UNLESS it has multiple\n    // inputs, then those need to individually get each click\n    hasCover() {\n        const inputs = this.el.querySelectorAll('ion-checkbox, ion-datetime, ion-select, ion-radio');\n        return inputs.length === 1 && !this.multipleInputs;\n    }\n    // If the item has an href or button property it will render a native\n    // anchor or button that is clickable\n    isClickable() {\n        return this.href !== undefined || this.button;\n    }\n    canActivate() {\n        return this.isClickable() || this.hasCover();\n    }\n    isFocusable() {\n        const focusableChild = this.el.querySelector('.ion-focusable');\n        return this.canActivate() || focusableChild !== null;\n    }\n    hasStartEl() {\n        const startEl = this.el.querySelector('[slot=\"start\"]');\n        if (startEl !== null) {\n            this.el.classList.add('item-has-start-slot');\n        }\n    }\n    getFirstInteractive() {\n        const controls = this.el.querySelectorAll('ion-toggle:not([disabled]), ion-checkbox:not([disabled]), ion-radio:not([disabled]), ion-select:not([disabled]), ion-input:not([disabled]), ion-textarea:not([disabled])');\n        return controls[0];\n    }\n    render() {\n        const { detail, detailIcon, download, labelColorStyles, lines, disabled, href, rel, target, routerAnimation, routerDirection, inheritedAriaAttributes, multipleInputs, } = this;\n        const childStyles = {};\n        const mode = getIonMode(this);\n        const clickable = this.isClickable();\n        const canActivate = this.canActivate();\n        const TagType = clickable ? (href === undefined ? 'button' : 'a') : 'div';\n        const attrs = TagType === 'button'\n            ? { type: this.type }\n            : {\n                download,\n                href,\n                rel,\n                target,\n            };\n        let clickFn = {};\n        const firstInteractive = this.getFirstInteractive();\n        // Only set onClick if the item is clickable to prevent screen\n        // readers from reading all items as clickable\n        if (clickable || (firstInteractive !== undefined && !multipleInputs)) {\n            clickFn = {\n                onClick: (ev) => {\n                    if (clickable) {\n                        openURL(href, ev, routerDirection, routerAnimation);\n                    }\n                    if (firstInteractive !== undefined && !multipleInputs) {\n                        const path = ev.composedPath();\n                        const target = path[0];\n                        if (ev.isTrusted) {\n                            /**\n                             * Dispatches a click event to the first interactive element,\n                             * when it is the result of a user clicking on the item.\n                             *\n                             * We check if the click target is in the shadow root,\n                             * which means the user clicked on the .item-native or\n                             * .item-inner padding.\n                             */\n                            const clickedWithinShadowRoot = this.el.shadowRoot.contains(target);\n                            if (clickedWithinShadowRoot) {\n                                /**\n                                 * For input/textarea clicking the padding should focus the\n                                 * text field (thus making it editable). For everything else,\n                                 * we want to click the control so it activates.\n                                 */\n                                if (firstInteractive.tagName === 'ION-INPUT' || firstInteractive.tagName === 'ION-TEXTAREA') {\n                                    firstInteractive.setFocus();\n                                }\n                                else {\n                                    firstInteractive.click();\n                                }\n                            }\n                        }\n                    }\n                },\n            };\n        }\n        const showDetail = detail !== undefined ? detail : mode === 'ios' && clickable;\n        this.itemStyles.forEach((value) => {\n            Object.assign(childStyles, value);\n        });\n        const ariaDisabled = disabled || childStyles['item-interactive-disabled'] ? 'true' : null;\n        const inList = hostContext('ion-list', this.el) && !hostContext('ion-radio-group', this.el);\n        /**\n         * Inputs and textareas do not need to show a cursor pointer.\n         * However, other form controls such as checkboxes and radios do.\n         */\n        const firstInteractiveNeedsPointerCursor = firstInteractive !== undefined && !['ION-INPUT', 'ION-TEXTAREA'].includes(firstInteractive.tagName);\n        return (h(Host, { key: '6fe6bae954259703482f175312a051ff1b315a95', \"aria-disabled\": ariaDisabled, class: Object.assign(Object.assign(Object.assign({}, childStyles), labelColorStyles), createColorClasses(this.color, {\n                item: true,\n                [mode]: true,\n                'item-lines-default': lines === undefined,\n                [`item-lines-${lines}`]: lines !== undefined,\n                'item-control-needs-pointer-cursor': firstInteractiveNeedsPointerCursor,\n                'item-disabled': disabled,\n                'in-list': inList,\n                'item-multiple-inputs': this.multipleInputs,\n                'ion-activatable': canActivate,\n                'ion-focusable': this.focusable,\n                'item-rtl': document.dir === 'rtl',\n            })), role: inList ? 'listitem' : null }, h(TagType, Object.assign({ key: 'fcfcffacb7d4e52f9458724332efa93d140cf637' }, attrs, inheritedAriaAttributes, { class: \"item-native\", part: \"native\", disabled: disabled }, clickFn), h(\"slot\", { key: '2c1be89990d1b4264f056f3afca2657dc25e868f', name: \"start\" }), h(\"div\", { key: 'a12010f2f251c29870806050357e247f297f752d', class: \"item-inner\" }, h(\"div\", { key: 'd0dc5901d634d3b7d8c9b4339ed02585b5b88828', class: \"input-wrapper\" }, h(\"slot\", { key: '5589d491f6058858310c72971de50ddf441379b1' })), h(\"slot\", { key: '7b2d2182336abef508d00882759df1a99a1cf93e', name: \"end\" }), showDetail && (h(\"ion-icon\", { key: 'f93f7e7166413f1f2691430dcda83ecf858f7e1f', icon: detailIcon, lazy: false, class: \"item-detail-icon\", part: \"detail-icon\", \"aria-hidden\": \"true\", \"flip-rtl\": detailIcon === chevronForward }))), canActivate && mode === 'md' && h(\"ion-ripple-effect\", { key: '3d13ce7623813d650ada97974b29a2e94e2405b1' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"button\": [\"buttonChanged\"]\n    }; }\n};\nItem.style = {\n    ios: IonItemIosStyle0,\n    md: IonItemMdStyle0\n};\n\nconst itemDividerIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--inner-padding-top:0px;--inner-padding-end:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);overflow:hidden;z-index:100;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.item-divider-sticky){position:-webkit-sticky;position:sticky;top:0}.item-divider-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border:0;overflow:hidden}:host-context([dir=rtl]) .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-divider-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-divider-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:hidden}:host{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--padding-start:16px;--inner-padding-end:8px;border-radius:0;position:relative;min-height:28px;font-size:1.0625rem;font-weight:600}:host([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(h1){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h2){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h3),::slotted(h4),::slotted(h5),::slotted(h6){margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px}::slotted(p){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;color:var(--ion-text-color-step-550, #a3a3a3);font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}::slotted(h2:last-child) ::slotted(h3:last-child),::slotted(h4:last-child),::slotted(h5:last-child),::slotted(h6:last-child),::slotted(p:last-child){margin-bottom:0}\";\nconst IonItemDividerIosStyle0 = itemDividerIosCss;\n\nconst itemDividerMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--inner-padding-top:0px;--inner-padding-end:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);overflow:hidden;z-index:100;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.item-divider-sticky){position:-webkit-sticky;position:sticky;top:0}.item-divider-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border:0;overflow:hidden}:host-context([dir=rtl]) .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-divider-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-divider-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:hidden}:host{--background:var(--ion-background-color, #fff);--color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999));--padding-start:16px;--inner-padding-end:16px;min-height:30px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));font-size:0.875rem}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:13px;margin-bottom:10px}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.7142857143em}:host(.ion-color) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(h1){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h2){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px}::slotted(h3,h4,h5,h6){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px}::slotted(p){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}\";\nconst IonItemDividerMdStyle0 = itemDividerMdCss;\n\nconst ItemDivider = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.color = undefined;\n        this.sticky = false;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '60fda1dab7dbc0038ec7ff68a661896430f7d5c5', class: createColorClasses(this.color, {\n                [mode]: true,\n                'item-divider-sticky': this.sticky,\n                item: true,\n            }) }, h(\"slot\", { key: '6ce072dfc2adfa699a2c34ffe25ed221c74d9eea', name: \"start\" }), h(\"div\", { key: '9a441be204ee2f0b567432722407c75e3cbbe942', class: \"item-divider-inner\" }, h(\"div\", { key: 'fd6f2969b345dba51400a290473e594d2d019dc5', class: \"item-divider-wrapper\" }, h(\"slot\", { key: 'ebf5601b21c4cf199c01bf142865b8da0c1ba4a6' })), h(\"slot\", { key: '249af8f30113f2c986976d518126661f65531121', name: \"end\" }))));\n    }\n    get el() { return getElement(this); }\n};\nItemDivider.style = {\n    ios: IonItemDividerIosStyle0,\n    md: IonItemDividerMdStyle0\n};\n\nconst itemGroupIosCss = \"ion-item-group{display:block}\";\nconst IonItemGroupIosStyle0 = itemGroupIosCss;\n\nconst itemGroupMdCss = \"ion-item-group{display:block}\";\nconst IonItemGroupMdStyle0 = itemGroupMdCss;\n\nconst ItemGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '24ff047b7c45f963f0dad072c65d38a230c2bc97', role: \"group\", class: {\n                [mode]: true,\n                // Used internally for styling\n                [`item-group-${mode}`]: true,\n                item: true,\n            } }));\n    }\n};\nItemGroup.style = {\n    ios: IonItemGroupIosStyle0,\n    md: IonItemGroupMdStyle0\n};\n\nconst labelIosCss = \".item.sc-ion-label-ios-h,.item .sc-ion-label-ios-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-ios-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-ios-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-ios-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-ios-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-ios-h,.item-input .sc-ion-label-ios-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-ios-h,.item-textarea .sc-ion-label-ios-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-ios-h,.item-skeleton-text .sc-ion-label-ios-h{overflow:hidden}.label-fixed.sc-ion-label-ios-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-ios-h,.label-floating.sc-ion-label-ios-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-ios-h{-webkit-transition:none;transition:none}.sc-ion-label-ios-s h1,.sc-ion-label-ios-s h2,.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-ios-h{font-size:0.875rem;line-height:1.5}.label-stacked.sc-ion-label-ios-h{margin-bottom:4px;font-size:0.875rem}.label-floating.sc-ion-label-ios-h{margin-bottom:0;-webkit-transform:translate(0, 29px);transform:translate(0, 29px);-webkit-transform-origin:left top;transform-origin:left top;-webkit-transition:-webkit-transform 150ms ease-in-out;transition:-webkit-transform 150ms ease-in-out;transition:transform 150ms ease-in-out;transition:transform 150ms ease-in-out, -webkit-transform 150ms ease-in-out}[dir=rtl].sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl] .sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl].label-floating.sc-ion-label-ios-h,[dir=rtl] .label-floating.sc-ion-label-ios-h{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.label-floating.sc-ion-label-ios-h:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.item-textarea.label-floating.sc-ion-label-ios-h,.item-textarea .label-floating.sc-ion-label-ios-h{-webkit-transform:translate(0, 28px);transform:translate(0, 28px)}.item-has-focus.label-floating.sc-ion-label-ios-h,.item-has-focus .label-floating.sc-ion-label-ios-h,.item-has-placeholder.sc-ion-label-ios-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-ios-h,.item-has-value.label-floating.sc-ion-label-ios-h,.item-has-value .label-floating.sc-ion-label-ios-h{-webkit-transform:scale(0.82);transform:scale(0.82)}.sc-ion-label-ios-s h1{margin-left:0;margin-right:0;margin-top:3px;margin-bottom:2px;font-size:1.375rem;font-weight:normal}.sc-ion-label-ios-s h2{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.0625rem;font-weight:normal}.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-ios-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}.sc-ion-label-ios-s>p{color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999))}.sc-ion-label-ios-h.in-item-color.sc-ion-label-ios-s>p{color:inherit}.sc-ion-label-ios-s h2:last-child,.sc-ion-label-ios-s h3:last-child,.sc-ion-label-ios-s h4:last-child,.sc-ion-label-ios-s h5:last-child,.sc-ion-label-ios-s h6:last-child,.sc-ion-label-ios-s p:last-child{margin-bottom:0}\";\nconst IonLabelIosStyle0 = labelIosCss;\n\nconst labelMdCss = \".item.sc-ion-label-md-h,.item .sc-ion-label-md-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-md-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-md-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-md-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-md-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-md-h,.item-input .sc-ion-label-md-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-md-h,.item-textarea .sc-ion-label-md-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-md-h,.item-skeleton-text .sc-ion-label-md-h{overflow:hidden}.label-fixed.sc-ion-label-md-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-md-h{-webkit-transition:none;transition:none}.sc-ion-label-md-s h1,.sc-ion-label-md-s h2,.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-md-h{line-height:1.5}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:top left;transform-origin:top left}.label-stacked.label-rtl.sc-ion-label-md-h,.label-floating.label-rtl.sc-ion-label-md-h{-webkit-transform-origin:top right;transform-origin:top right}.label-stacked.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.label-floating.sc-ion-label-md-h{-webkit-transform:translateY(96%);transform:translateY(96%);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1)}.ion-focused.label-floating.sc-ion-label-md-h,.ion-focused .label-floating.sc-ion-label-md-h,.item-has-focus.label-floating.sc-ion-label-md-h,.item-has-focus .label-floating.sc-ion-label-md-h,.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h,.item-has-value.label-floating.sc-ion-label-md-h,.item-has-value .label-floating.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75)}.ion-focused.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-primary, #0054e9)}.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-contrast)}.ion-invalid.ion-touched.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--highlight-color-invalid)}.sc-ion-label-md-s h1{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.5rem;font-weight:normal}.sc-ion-label-md-s h2{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:1rem;font-weight:normal}.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-md-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:1.25rem;text-overflow:inherit;overflow:inherit}.sc-ion-label-md-s>p{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.sc-ion-label-md-h.in-item-color.sc-ion-label-md-s>p{color:inherit}\";\nconst IonLabelMdStyle0 = labelMdCss;\n\nconst Label = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionColor = createEvent(this, \"ionColor\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inRange = false;\n        this.color = undefined;\n        this.position = undefined;\n        this.noAnimate = false;\n    }\n    componentWillLoad() {\n        this.inRange = !!this.el.closest('ion-range');\n        this.noAnimate = this.position === 'floating';\n        this.emitStyle();\n        this.emitColor();\n    }\n    componentDidLoad() {\n        if (this.noAnimate) {\n            setTimeout(() => {\n                this.noAnimate = false;\n            }, 1000);\n        }\n    }\n    colorChanged() {\n        this.emitColor();\n    }\n    positionChanged() {\n        this.emitStyle();\n    }\n    emitColor() {\n        const { color } = this;\n        this.ionColor.emit({\n            'item-label-color': color !== undefined,\n            [`ion-color-${color}`]: color !== undefined,\n        });\n    }\n    emitStyle() {\n        const { inRange, position } = this;\n        // If the label is inside of a range we don't want\n        // to override the classes added by the label that\n        // is a direct child of the item\n        if (!inRange) {\n            this.ionStyle.emit({\n                label: true,\n                [`label-${position}`]: position !== undefined,\n            });\n        }\n    }\n    render() {\n        const position = this.position;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'c2c0f388dab910d294efb9fbb409ee4ef829c1ed', class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item-color': hostContext('ion-item.ion-color', this.el),\n                [`label-${position}`]: position !== undefined,\n                [`label-no-animate`]: this.noAnimate,\n                'label-rtl': document.dir === 'rtl',\n            }) }, h(\"slot\", { key: '4de6b69950f417873a13c851018ec31ea2686f0a' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"color\": [\"colorChanged\"],\n        \"position\": [\"positionChanged\"]\n    }; }\n};\nLabel.style = {\n    ios: IonLabelIosStyle0,\n    md: IonLabelMdStyle0\n};\n\nconst listIosCss = \"ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-ios{background:var(--ion-item-background, var(--ion-background-color, #fff))}.list-ios.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:10px}.list-ios.list-inset ion-item:only-child,.list-ios.list-inset ion-item:not(:only-of-type):last-of-type,.list-ios.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-ios.list-inset+ion-list.list-inset{margin-top:0}.list-ios-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-ios-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 0.55px 0}.list-ios-lines-inset .item-lines-default{--inner-border-width:0 0 0.55px 0;--border-width:0px}ion-card .list-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonListIosStyle0 = listIosCss;\n\nconst listMdCss = \"ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;background:var(--ion-item-background, var(--ion-background-color, #fff))}.list-md>.input:last-child::after{inset-inline-start:0}.list-md.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:2px}.list-md.list-inset ion-item:not(:only-of-type):last-of-type,.list-md.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-md.list-inset ion-item:only-child{--border-width:0;--inner-border-width:0}.list-md.list-inset+ion-list.list-inset{margin-top:0}.list-md-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-md-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 1px 0}.list-md-lines-inset .item-lines-default{--inner-border-width:0 0 1px 0;--border-width:0px}ion-card .list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonListMdStyle0 = listMdCss;\n\nconst List = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.lines = undefined;\n        this.inset = false;\n    }\n    /**\n     * If `ion-item-sliding` are used inside the list, this method closes\n     * any open sliding item.\n     *\n     * Returns `true` if an actual `ion-item-sliding` is closed.\n     */\n    async closeSlidingItems() {\n        const item = this.el.querySelector('ion-item-sliding');\n        if (item === null || item === void 0 ? void 0 : item.closeOpened) {\n            return item.closeOpened();\n        }\n        return false;\n    }\n    render() {\n        const mode = getIonMode(this);\n        const { lines, inset } = this;\n        return (h(Host, { key: '8bde220025a7eeca6da075379c6487c4c9bdddc1', role: \"list\", class: {\n                [mode]: true,\n                // Used internally for styling\n                [`list-${mode}`]: true,\n                'list-inset': inset,\n                [`list-lines-${lines}`]: lines !== undefined,\n                [`list-${mode}-lines-${lines}`]: lines !== undefined,\n            } }));\n    }\n    get el() { return getElement(this); }\n};\nList.style = {\n    ios: IonListIosStyle0,\n    md: IonListMdStyle0\n};\n\nconst listHeaderIosCss = \":host{--border-style:solid;--border-width:0;--inner-border-width:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:40px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);color:var(--color);overflow:hidden}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.list-header-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex:1 1 auto;flex:1 1 auto}:host(.list-header-lines-inset),:host(.list-header-lines-none){--border-width:0}:host(.list-header-lines-full),:host(.list-header-lines-none){--inner-border-width:0}:host{--background:transparent;--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));padding-right:var(--ion-safe-area-right);padding-left:calc(var(--ion-safe-area-left, 0px) + 16px);position:relative;-ms-flex-align:end;align-items:flex-end;font-size:min(1.375rem, 56.1px);font-weight:700;letter-spacing:0}:host-context([dir=rtl]){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}}::slotted(ion-button),::slotted(ion-label){margin-top:29px;margin-bottom:6px}::slotted(ion-button){--padding-top:0;--padding-bottom:0;-webkit-margin-start:3px;margin-inline-start:3px;-webkit-margin-end:3px;margin-inline-end:3px;min-height:1.4em}:host(.list-header-lines-full){--border-width:0 0 0.55px 0}:host(.list-header-lines-inset){--inner-border-width:0 0 0.55px 0}\";\nconst IonListHeaderIosStyle0 = listHeaderIosCss;\n\nconst listHeaderMdCss = \":host{--border-style:solid;--border-width:0;--inner-border-width:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:40px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);color:var(--color);overflow:hidden}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.list-header-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex:1 1 auto;flex:1 1 auto}:host(.list-header-lines-inset),:host(.list-header-lines-none){--border-width:0}:host(.list-header-lines-full),:host(.list-header-lines-none){--inner-border-width:0}:host{--background:transparent;--color:var(--ion-text-color, #000);--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));padding-right:var(--ion-safe-area-right);padding-left:calc(var(--ion-safe-area-left, 0px) + 16px);min-height:45px;font-size:0.875rem}:host-context([dir=rtl]){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}}:host(.list-header-lines-full){--border-width:0 0 1px 0}:host(.list-header-lines-inset){--inner-border-width:0 0 1px 0}\";\nconst IonListHeaderMdStyle0 = listHeaderMdCss;\n\nconst ListHeader = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.color = undefined;\n        this.lines = undefined;\n    }\n    render() {\n        const { lines } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: '7e2e050f13722f2b870a2415d99a9e631e9ca267', class: createColorClasses(this.color, {\n                [mode]: true,\n                [`list-header-lines-${lines}`]: lines !== undefined,\n            }) }, h(\"div\", { key: '6117bebc45800d874e9b75355476fbced5cc8398', class: \"list-header-inner\" }, h(\"slot\", { key: '9165fb274cd2c45a5a65c271d8b1f30e8a00c890' }))));\n    }\n};\nListHeader.style = {\n    ios: IonListHeaderIosStyle0,\n    md: IonListHeaderMdStyle0\n};\n\nconst noteIosCss = \":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6));font-size:max(14px, 1rem)}\";\nconst IonNoteIosStyle0 = noteIosCss;\n\nconst noteMdCss = \":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));font-size:0.875rem}\";\nconst IonNoteMdStyle0 = noteMdCss;\n\nconst Note = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.color = undefined;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '90ec2fe8c9487608ed8c0bdc32c2e80a6a0890b3', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: '115ee3f79e6c526b0644443aad468e99385d0eda' })));\n    }\n};\nNote.style = {\n    ios: IonNoteIosStyle0,\n    md: IonNoteMdStyle0\n};\n\nconst skeletonTextCss = \":host{--background:rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065);border-radius:var(--border-radius, inherit);display:block;width:100%;height:inherit;margin-top:4px;margin-bottom:4px;background:var(--background);line-height:10px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;pointer-events:none}span{display:inline-block}:host(.in-media){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;height:100%}:host(.skeleton-text-animated){position:relative;background:-webkit-gradient(linear, left top, right top, color-stop(8%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065)), color-stop(18%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.135)), color-stop(33%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065)));background:linear-gradient(to right, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065) 8%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.135) 18%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065) 33%);background-size:800px 104px;-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-name:shimmer;animation-name:shimmer;-webkit-animation-timing-function:linear;animation-timing-function:linear}@-webkit-keyframes shimmer{0%{background-position:-400px 0}100%{background-position:400px 0}}@keyframes shimmer{0%{background-position:-400px 0}100%{background-position:400px 0}}\";\nconst IonSkeletonTextStyle0 = skeletonTextCss;\n\nconst SkeletonText = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.animated = false;\n    }\n    componentWillLoad() {\n        this.emitStyle();\n    }\n    emitStyle() {\n        // The emitted property is used by item in order\n        // to add the item-skeleton-text class which applies\n        // overflow: hidden to its label\n        const style = {\n            'skeleton-text': true,\n        };\n        this.ionStyle.emit(style);\n    }\n    render() {\n        const animated = this.animated && config.getBoolean('animated', true);\n        const inMedia = hostContext('ion-avatar', this.el) || hostContext('ion-thumbnail', this.el);\n        const mode = getIonMode(this);\n        return (h(Host, { key: '1a3e78e9a6f740d609d1f0b7a16cb6eff4a2d617', class: {\n                [mode]: true,\n                'skeleton-text-animated': animated,\n                'in-media': inMedia,\n            } }, h(\"span\", { key: 'be3eabe196ec6e8ec19857375ba30f4c8aa58e7f' }, \"\\u00A0\")));\n    }\n    get el() { return getElement(this); }\n};\nSkeletonText.style = IonSkeletonTextStyle0;\n\nexport { Item as ion_item, ItemDivider as ion_item_divider, ItemGroup as ion_item_group, Label as ion_label, List as ion_list, ListHeader as ion_list_header, Note as ion_note, SkeletonText as ion_skeleton_text };\n"], "names": ["r", "registerInstance", "j", "forceUpdate", "h", "f", "Host", "i", "getElement", "d", "createEvent", "inheritAttributes", "raf", "hostContext", "c", "createColorClasses", "o", "openURL", "chevronForward", "b", "getIonMode", "config", "itemIosCss", "IonItemIosStyle0", "itemMdCss", "IonItemMdStyle0", "<PERSON><PERSON>", "constructor", "hostRef", "labelColorStyles", "itemStyles", "Map", "inheritedAriaAttributes", "multipleInputs", "focusable", "color", "undefined", "button", "detail", "detailIcon", "disabled", "download", "href", "rel", "lines", "routerAnimation", "routerDirection", "target", "type", "buttonChanged", "isFocusable", "labelColorChanged", "ev", "itemStyle", "stopPropagation", "tagName", "updatedStyles", "newStyles", "childStyles", "get", "hasStyleChange", "Object", "keys", "for<PERSON>ach", "key", "itemKey", "length", "set", "connectedCallback", "hasStartEl", "componentWillLoad", "el", "componentDidLoad", "setMultipleInputs", "covers", "querySelectorAll", "inputs", "clickables", "isClickable", "hasCover", "canActivate", "focus<PERSON><PERSON><PERSON><PERSON>", "querySelector", "startEl", "classList", "add", "getFirstInteractive", "controls", "render", "mode", "clickable", "TagType", "attrs", "clickFn", "firstInteractive", "onClick", "path", "<PERSON><PERSON><PERSON>", "isTrusted", "clickedWithinShadowRoot", "shadowRoot", "contains", "setFocus", "click", "showDetail", "value", "assign", "ariaDisabled", "inList", "firstInteractiveNeedsPointerCursor", "includes", "class", "item", "document", "dir", "role", "part", "name", "icon", "lazy", "watchers", "style", "ios", "md", "itemDividerIosCss", "IonItemDividerIosStyle0", "itemDividerMdCss", "IonItemDividerMdStyle0", "ItemDivider", "sticky", "itemGroupIosCss", "IonItemGroupIosStyle0", "itemGroupMdCss", "IonItemGroupMdStyle0", "ItemGroup", "labelIosCss", "IonLabelIosStyle0", "labelMdCss", "IonLabelMdStyle0", "Label", "ionColor", "ionStyle", "inRange", "position", "noAnimate", "closest", "emitStyle", "emitColor", "setTimeout", "colorChanged", "positionChanged", "emit", "label", "listIosCss", "IonListIosStyle0", "listMdCss", "IonListMdStyle0", "List", "inset", "closeSlidingItems", "_this", "_asyncToGenerator", "closeOpened", "listHeaderIosCss", "IonListHeaderIosStyle0", "listHeaderMdCss", "IonListHeaderMdStyle0", "ListHeader", "noteIosCss", "IonNoteIosStyle0", "noteMdCss", "IonNoteMdStyle0", "Note", "skeletonTextCss", "IonSkeletonTextStyle0", "SkeletonText", "animated", "getBoolean", "inMedia", "ion_item", "ion_item_divider", "ion_item_group", "ion_label", "ion_list", "ion_list_header", "ion_note", "ion_skeleton_text"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}