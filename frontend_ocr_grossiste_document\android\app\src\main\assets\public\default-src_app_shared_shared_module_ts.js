"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["default-src_app_shared_shared_module_ts"],{

/***/ 93648:
/*!**********************************************************!*\
  !*** ./src/app/check-network/check-network.component.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CheckNetworkComponent: () => (/* binding */ CheckNetworkComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _services_network_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/network.service */ 32404);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 60316);
var _CheckNetworkComponent;




function CheckNetworkComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "ion-spinner", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3, "Connecting...");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
  }
}
class CheckNetworkComponent {
  constructor(platform, networkService) {
    this.platform = platform;
    this.networkService = networkService;
    this.isConnected = false; // Track network status
  }
  ngOnInit() {
    this.platform.ready().then(() => {
      this.networkService.getNetworkStatus().subscribe(connected => {
        this.isConnected = connected;
        console.log('Network status:', this.isConnected);
      });
      this.networkService.isOnline$.subscribe(result => {
        this.isConnected = result;
        console.log(result ? 'User is online' : 'User is offline');
      });
    });
  }
}
_CheckNetworkComponent = CheckNetworkComponent;
_CheckNetworkComponent.ɵfac = function CheckNetworkComponent_Factory(t) {
  return new (t || _CheckNetworkComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_2__.Platform), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_network_service__WEBPACK_IMPORTED_MODULE_0__.NetworkService));
};
_CheckNetworkComponent.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
  type: _CheckNetworkComponent,
  selectors: [["app-check-network"]],
  decls: 1,
  vars: 1,
  consts: [["class", "connecting", 4, "ngIf"], [1, "connecting"], ["name", "crescent"]],
  template: function CheckNetworkComponent_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](0, CheckNetworkComponent_div_0_Template, 4, 0, "div", 0);
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx.isConnected);
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.NgIf, _ionic_angular__WEBPACK_IMPORTED_MODULE_4__.IonSpinner],
  styles: ["\n\n\n\n.connecting[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n  gap: 10px;\n}\n\n.connecting[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #505050;\n  opacity: 0.7;\n  font-size: 16px;\n  font-weight: 500;\n  margin: 0;\n}\n\n  -shadowcsshost-no-combinator.spinner-crescent svg {\n  width: 10% !important;\n  height: 10% !important;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ }),

/***/ 32374:
/*!********************************************************!*\
  !*** ./src/app/custom-alert/custom-alert.component.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CustomAlertComponent: () => (/* binding */ CustomAlertComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _custom_loading_custom_loading_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../custom-loading/custom-loading.component */ 52170);
var _CustomAlertComponent;



class CustomAlertComponent {
  constructor(modalController) {
    this.modalController = modalController;
    this.header = "Chargement...";
    this.message = "";
    this.progress = 0;
  }
  dismiss() {
    this.modalController.dismiss();
  }
}
_CustomAlertComponent = CustomAlertComponent;
_CustomAlertComponent.ɵfac = function CustomAlertComponent_Factory(t) {
  return new (t || _CustomAlertComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_2__.ModalController));
};
_CustomAlertComponent.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
  type: _CustomAlertComponent,
  selectors: [["app-custom-alert"]],
  inputs: {
    header: "header",
    message: "message",
    progress: "progress"
  },
  decls: 2,
  vars: 1,
  consts: [[1, "wrapper-progress-bar"], [3, "progress"]],
  template: function CustomAlertComponent_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "app-custom-loading", 1);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("progress", ctx.progress);
    }
  },
  dependencies: [_custom_loading_custom_loading_component__WEBPACK_IMPORTED_MODULE_0__.CustomLoadingComponent],
  styles: [".wrapper-progress-bar[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 15px;\n  padding: 10px 20px;\n  border: 1px solid #adadad;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY3VzdG9tLWFsZXJ0L2N1c3RvbS1hbGVydC5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uLy4uLy4uLy4uLy4uL1dvcmslMjBfX0FiZGVycmFobWFuZV9vdWhuYS9PQ1JfRE9DVU1FTlRfR1JPU1NJU1RFL0Zyb250ZW5kJTIwb2NyJTIwZ3Jvc3Npc3RlJTIwZG9jdW1lbnQvZnJvbnRlbmRfb2NyX2dyb3NzaXN0ZV9kb2N1bWVudC9zcmMvYXBwL2N1c3RvbS1hbGVydC9jdXN0b20tYWxlcnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBS0U7RUFDRSx5QkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtBQ0pKIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xyXG4gICAgLy8gZGlzcGxheTogYmxvY2s7XHJcbiAgICAvLyBoZWlnaHQ6IDEwMCU7XHJcbiAgfVxyXG4gIFxyXG4gIC53cmFwcGVyLXByb2dyZXNzLWJhcntcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG4gICAgcGFkZGluZzogMTBweCAyMHB4O1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2FkYWRhZDtcclxuICAgIC8vIHNoYWRvd1xyXG4gICAgLy8gYm94LXNoYWRvdzogMHB4IDBweCAxMHB4IDBweCByZ2JhKDAsMCwwLDAuMSk7XHJcbiAgfVxyXG5cclxuXHJcbiIsIi53cmFwcGVyLXByb2dyZXNzLWJhciB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIHBhZGRpbmc6IDEwcHggMjBweDtcbiAgYm9yZGVyOiAxcHggc29saWQgI2FkYWRhZDtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */"]
});

/***/ }),

/***/ 40816:
/*!******************************************************!*\
  !*** ./src/app/custom-icon/custom-icon.component.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CustomIconComponent: () => (/* binding */ CustomIconComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/platform-browser */ 80436);
var _CustomIconComponent;


class CustomIconComponent {
  constructor(sanitizer) {
    this.sanitizer = sanitizer;
  }
  ngOnInit() {
    this.iconUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`assets/icons/${this.name}.svg`);
  }
}
_CustomIconComponent = CustomIconComponent;
_CustomIconComponent.ɵfac = function CustomIconComponent_Factory(t) {
  return new (t || _CustomIconComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdirectiveInject"](_angular_platform_browser__WEBPACK_IMPORTED_MODULE_1__.DomSanitizer));
};
_CustomIconComponent.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
  type: _CustomIconComponent,
  selectors: [["app-custom-icon"]],
  inputs: {
    name: "name"
  },
  decls: 1,
  vars: 2,
  consts: [[1, "custom-icon", 3, "src", "alt"]],
  template: function CustomIconComponent_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](0, "img", 0);
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("src", ctx.iconUrl, _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵsanitizeUrl"])("alt", ctx.name);
    }
  },
  styles: [".custom-icon[_ngcontent-%COMP%] {\n  width: 24px;\n  height: 24px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY3VzdG9tLWljb24vY3VzdG9tLWljb24uY29tcG9uZW50LnNjc3MiLCJ3ZWJwYWNrOi8vLi8uLi8uLi8uLi8uLi9Xb3JrJTIwX19BYmRlcnJhaG1hbmVfb3VobmEvT0NSX0RPQ1VNRU5UX0dST1NTSVNURS9Gcm9udGVuZCUyMG9jciUyMGdyb3NzaXN0ZSUyMGRvY3VtZW50L2Zyb250ZW5kX29jcl9ncm9zc2lzdGVfZG9jdW1lbnQvc3JjL2FwcC9jdXN0b20taWNvbi9jdXN0b20taWNvbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLFdBQUE7RUFDQSxZQUFBO0FDQ0oiLCJzb3VyY2VzQ29udGVudCI6WyIuY3VzdG9tLWljb24ge1xyXG4gICAgd2lkdGg6IDI0cHg7XHJcbiAgICBoZWlnaHQ6IDI0cHg7XHJcbiAgfSIsIi5jdXN0b20taWNvbiB7XG4gIHdpZHRoOiAyNHB4O1xuICBoZWlnaHQ6IDI0cHg7XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */"]
});

/***/ }),

/***/ 52170:
/*!************************************************************!*\
  !*** ./src/app/custom-loading/custom-loading.component.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CustomLoadingComponent: () => (/* binding */ CustomLoadingComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ionic/angular */ 21507);
var _CustomLoadingComponent;


class CustomLoadingComponent {
  constructor() {
    this.progress = 0;
    this.progress_displayed = 0;
  }
  ngOnInit() {
    setInterval(() => {
      // progress_displayed for be integer not have coma
      this.progress_displayed = Math.round(this.progress);
    }, 100);
  }
}
_CustomLoadingComponent = CustomLoadingComponent;
_CustomLoadingComponent.ɵfac = function CustomLoadingComponent_Factory(t) {
  return new (t || _CustomLoadingComponent)();
};
_CustomLoadingComponent.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
  type: _CustomLoadingComponent,
  selectors: [["app-custom-loading"]],
  inputs: {
    progress: "progress"
  },
  decls: 14,
  vars: 2,
  consts: [[1, "loading-content"], [1, "head-progress-bar"], ["version", "1.1", "id", "L7", "xmlns", "http://www.w3.org/2000/svg", 0, "xmlns", "xlink", "http://www.w3.org/1999/xlink", "x", "0px", "y", "0px", "viewBox", "0 0 100 100", "enable-background", "new 0 0 100 100", 0, "xml", "space", "preserve", 1, "circles"], ["fill", "#fff", "d", "M31.6,3.5C5.9,13.6-6.6,42.7,3.5,68.4c10.1,25.7,39.2,38.3,64.9,28.1l-3.1-7.9c-21.3,8.4-45.4-2-53.8-23.3\n    c-8.4-21.3,2-45.4,23.3-53.8L31.6,3.5z"], ["attributeName", "transform", "attributeType", "XML", "type", "rotate", "dur", "2s", "from", "0 50 50", "to", "360 50 50", "repeatCount", "indefinite"], ["fill", "#fff", "d", "M42.3,39.6c5.7-4.3,13.9-3.1,18.1,2.7c4.3,5.7,3.1,13.9-2.7,18.1l4.1,5.5c8.8-6.5,10.6-19,4.1-27.7\n    c-6.5-8.8-19-10.6-27.7-4.1L42.3,39.6z"], ["attributeName", "transform", "attributeType", "XML", "type", "rotate", "dur", "1s", "from", "0 50 50", "to", "-360 50 50", "repeatCount", "indefinite"], ["fill", "#fff", "d", "M82,35.7C74.1,18,53.4,10.1,35.7,18S10.1,46.6,18,64.3l7.6-3.4c-6-13.5,0-29.3,13.5-35.3s29.3,0,35.3,13.5\n    L82,35.7z"], [3, "value"]],
  template: function CustomLoadingComponent_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "p");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3, "Chargement...");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnamespaceSVG"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "svg", 2)(5, "path", 3);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](6, "animateTransform", 4);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "path", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](8, "animateTransform", 6);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "path", 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](10, "animateTransform", 4);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnamespaceHTML"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](11, "ion-progress-bar", 8);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](12, "p");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](13);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](11);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("value", ctx.progress / 100);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("", ctx.progress_displayed, "% complet");
    }
  },
  dependencies: [_ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonProgressBar],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\n  font-family: \"Poppins\", sans-serif;\n  text-align: center;\n}\n\n.loading-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333333;\n  margin-bottom: 10px;\n}\n\n  svg.circles {\n  width: 40px !important;\n  height: 40px !important;\n  filter: invert(29%) sepia(100%) saturate(3273%) hue-rotate(219deg) brightness(101%) contrast(100%);\n  margin-bottom: 20px !important;\n}\n\n.loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  font-size: 18px;\n  font-weight: 700;\n}\n\n.head-progress-bar[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.head-progress-bar[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 18px;\n  font-weight: 700;\n  color: #333333;\n  margin-bottom: 10px;\n}\n\nion-progress-bar[_ngcontent-%COMP%]::part(progress) {\n  background: #0040ff;\n}\n\nion-progress-bar[_ngcontent-%COMP%] {\n  height: 10px;\n  width: 70%;\n  border-radius: 50px;\n}\n\nion-spinner[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  color: #0040ff;\n  margin-bottom: 10px;\n}\n\np[_ngcontent-%COMP%] {\n  text-align: center;\n  font-size: 16px;\n  font-weight: 500;\n  color: #333333;\n  margin-top: 10px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ }),

/***/ 85528:
/*!******************************************************!*\
  !*** ./src/app/image-modal/image-modal.component.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ImageModalComponent: () => (/* binding */ ImageModalComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ionic/angular */ 21507);
var _ImageModalComponent;


class ImageModalComponent {
  constructor(modalController) {
    this.modalController = modalController;
  }
  dismiss() {
    this.modalController.dismiss();
  }
}
_ImageModalComponent = ImageModalComponent;
_ImageModalComponent.ɵfac = function ImageModalComponent_Factory(t) {
  return new (t || _ImageModalComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_1__.ModalController));
};
_ImageModalComponent.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
  type: _ImageModalComponent,
  selectors: [["app-image-modal"]],
  inputs: {
    imageSrc: "imageSrc"
  },
  decls: 10,
  vars: 1,
  consts: [["slot", "end"], [1, "close-modal", 3, "click"], ["name", "close-circle-outline"], [1, "ion-padding"], [1, "image-container"], [1, "enlarged-image", 3, "src"]],
  template: function ImageModalComponent_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3, "Image");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](4, "ion-buttons", 0)(5, "ion-button", 1);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ImageModalComponent_Template_ion_button_click_5_listener() {
        return ctx.dismiss();
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](6, "ion-icon", 2);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "ion-content", 3)(8, "div", 4);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](9, "img", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](9);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("src", ctx.imageSrc, _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵsanitizeUrl"]);
    }
  },
  dependencies: [_ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonButtons, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonIcon, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_1__.IonToolbar],
  styles: ["ion-toolbar[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background-color: #2f4fcd;\n  color: #fff;\n  border-bottom-left-radius: 20px;\n  border-bottom-right-radius: 20px;\n  box-shadow: 0px 0px 10px 0px #000000;\n}\n\n.image-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 90%;\n}\n\nion-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 10%;\n}\n\n.enlarged-image[_ngcontent-%COMP%] {\n  max-width: 100%;\n  max-height: 100%;\n}\n\nion-content[_ngcontent-%COMP%]::part(scroll) {\n  overflow-y: hidden !important;\n  --overflow: hidden !important;\n}\n\n  ion-content {\n  --background: rgba(255, 255, 255, 0.8) !important;\n}\n\n.footer-md[_ngcontent-%COMP%], .header-md[_ngcontent-%COMP%] {\n  box-shadow: none;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  position: relative;\n  width: 100%;\n  padding: 10px;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #2f4fcd;\n  --border-width: 0px !important;\n}\n\nion-title[_ngcontent-%COMP%] {\n  font-size: 1.8rem;\n  color: #fff;\n  letter-spacing: 2px;\n  text-align: center;\n}\n\n.close-modal[_ngcontent-%COMP%] {\n  position: absolute;\n  right: -5px;\n  top: -5px;\n  --color: red;\n  font-weight: bold;\n  font-size: 1.8rem;\n}\n\nion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  background: #fff !important;\n  border-radius: 50% !important;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ }),

/***/ 93887:
/*!*****************************************!*\
  !*** ./src/app/shared/shared.module.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SharedModule: () => (/* binding */ SharedModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../custom-icon/custom-icon.component */ 40816);
/* harmony import */ var _check_network_check_network_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../check-network/check-network.component */ 93648);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _custom_loading_custom_loading_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../custom-loading/custom-loading.component */ 52170);
/* harmony import */ var _custom_alert_custom_alert_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../custom-alert/custom-alert.component */ 32374);
/* harmony import */ var _image_modal_image_modal_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../image-modal/image-modal.component */ 85528);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
var _SharedModule;








class SharedModule {}
_SharedModule = SharedModule;
_SharedModule.ɵfac = function SharedModule_Factory(t) {
  return new (t || _SharedModule)();
};
_SharedModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineNgModule"]({
  type: _SharedModule
});
_SharedModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.CommonModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonicModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsetNgModuleScope"](SharedModule, {
    declarations: [_custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_0__.CustomIconComponent, _check_network_check_network_component__WEBPACK_IMPORTED_MODULE_1__.CheckNetworkComponent, _custom_loading_custom_loading_component__WEBPACK_IMPORTED_MODULE_2__.CustomLoadingComponent, _custom_alert_custom_alert_component__WEBPACK_IMPORTED_MODULE_3__.CustomAlertComponent, _image_modal_image_modal_component__WEBPACK_IMPORTED_MODULE_4__.ImageModalComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.CommonModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonicModule],
    exports: [_custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_0__.CustomIconComponent, _check_network_check_network_component__WEBPACK_IMPORTED_MODULE_1__.CheckNetworkComponent, _custom_loading_custom_loading_component__WEBPACK_IMPORTED_MODULE_2__.CustomLoadingComponent, _custom_alert_custom_alert_component__WEBPACK_IMPORTED_MODULE_3__.CustomAlertComponent, _image_modal_image_modal_component__WEBPACK_IMPORTED_MODULE_4__.ImageModalComponent]
  });
})();

/***/ })

}]);
//# sourceMappingURL=default-src_app_shared_shared_module_ts.js.map