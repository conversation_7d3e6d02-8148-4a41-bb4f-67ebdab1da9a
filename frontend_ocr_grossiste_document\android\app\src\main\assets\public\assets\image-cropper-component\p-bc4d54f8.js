const t="image-cropper-component";let e;let n;let s=false;let o=false;const l=(t,e="")=>{{return()=>{}}};const c=(t,e)=>{{return()=>{}}};const i="{visibility:hidden}.hydrated{visibility:inherit}";const f="slot-fb{display:contents}slot-fb[hidden]{display:none}";const r={};const u="http://www.w3.org/2000/svg";const a="http://www.w3.org/1999/xhtml";const d=t=>t!=null;const v=t=>{t=typeof t;return t==="object"||t==="function"};function p(t){var e,n,s;return(s=(n=(e=t.head)===null||e===void 0?void 0:e.querySelector('meta[name="csp-nonce"]'))===null||n===void 0?void 0:n.getAttribute("content"))!==null&&s!==void 0?s:undefined}const y=(t,e,...n)=>{let s=null;let o=null;let l=false;let c=false;const i=[];const f=e=>{for(let n=0;n<e.length;n++){s=e[n];if(Array.isArray(s)){f(s)}else if(s!=null&&typeof s!=="boolean"){if(l=typeof t!=="function"&&!v(s)){s=String(s)}if(l&&c){i[i.length-1].t+=s}else{i.push(l?h(null,s):s)}c=l}}};f(n);if(e){if(e.key){o=e.key}{const t=e.className||e.class;if(t){e.class=typeof t!=="object"?t:Object.keys(t).filter((e=>t[e])).join(" ")}}}if(typeof t==="function"){return t(e===null?{}:e,i,w)}const r=h(t,null);r.o=e;if(i.length>0){r.l=i}{r.i=o}return r};const h=(t,e)=>{const n={u:0,v:t,t:e,p:null,l:null};{n.o=null}{n.i=null}return n};const m={};const b=t=>t&&t.v===m;const w={forEach:(t,e)=>t.map($).forEach(e),map:(t,e)=>t.map($).map(e).map(g)};const $=t=>({vattrs:t.o,vchildren:t.l,vkey:t.i,vname:t.h,vtag:t.v,vtext:t.t});const g=t=>{if(typeof t.vtag==="function"){const e=Object.assign({},t.vattrs);if(t.vkey){e.key=t.vkey}if(t.vname){e.name=t.vname}return y(t.vtag,e,...t.vchildren||[])}const e=h(t.vtag,t.vtext);e.o=t.vattrs;e.l=t.vchildren;e.i=t.vkey;e.h=t.vname;return e};const S=(t,e)=>{if(t!=null&&!v(t)){if(e&2){return parseFloat(t)}if(e&1){return String(t)}return t}return t};const j=t=>pt(t).$hostElement$;const O=(t,e,n)=>{const s=j(t);return{emit:t=>k(s,e,{bubbles:!!(n&4),composed:!!(n&2),cancelable:!!(n&1),detail:t})}};const k=(t,e,n)=>{const s=Ot.ce(e,n);t.dispatchEvent(s);return s};const C=new WeakMap;const M=(t,e,n)=>{let s=gt.get(t);if(Ct&&n){s=s||new CSSStyleSheet;if(typeof s==="string"){s=e}else{s.replaceSync(e)}}else{s=e}gt.set(t,s)};const x=(t,e,n)=>{var s;const o=E(e);const l=gt.get(o);t=t.nodeType===11?t:jt;if(l){if(typeof l==="string"){t=t.head||t;let n=C.get(t);let c;if(!n){C.set(t,n=new Set)}if(!n.has(o)){{c=jt.createElement("style");c.innerHTML=l;const e=(s=Ot.m)!==null&&s!==void 0?s:p(jt);if(e!=null){c.setAttribute("nonce",e)}t.insertBefore(c,t.querySelector("link"))}if(e.u&4){c.innerHTML+=f}if(n){n.add(o)}}}else if(!t.adoptedStyleSheets.includes(l)){t.adoptedStyleSheets=[...t.adoptedStyleSheets,l]}}return o};const P=t=>{const e=t.$;const n=t.$hostElement$;const s=e.u;const o=l("attachStyles",e.S);const c=x(n.shadowRoot?n.shadowRoot:n.getRootNode(),e);if(s&10){n["s-sc"]=c;n.classList.add(c+"-h")}o()};const E=(t,e)=>"sc-"+t.S;const U=(t,e,n,s,o,l)=>{if(n!==s){let c=mt(t,e);let i=e.toLowerCase();if(e==="class"){const e=t.classList;const o=F(n);const l=F(s);e.remove(...o.filter((t=>t&&!l.includes(t))));e.add(...l.filter((t=>t&&!o.includes(t))))}else if(e==="style"){{for(const e in n){if(!s||s[e]==null){if(e.includes("-")){t.style.removeProperty(e)}else{t.style[e]=""}}}}for(const e in s){if(!n||s[e]!==n[e]){if(e.includes("-")){t.style.setProperty(e,s[e])}else{t.style[e]=s[e]}}}}else if(e==="key");else if(e==="ref"){if(s){s(t)}}else if(!c&&e[0]==="o"&&e[1]==="n"){if(e[2]==="-"){e=e.slice(3)}else if(mt(St,i)){e=i.slice(2)}else{e=i[2]+e.slice(3)}if(n||s){const o=e.endsWith(L);e=e.replace(N,"");if(n){Ot.rel(t,e,n,o)}if(s){Ot.ael(t,e,s,o)}}}else{const i=v(s);if((c||i&&s!==null)&&!o){try{if(!t.tagName.includes("-")){const o=s==null?"":s;if(e==="list"){c=false}else if(n==null||t[e]!=o){t[e]=o}}else{t[e]=s}}catch(t){}}if(s==null||s===false){if(s!==false||t.getAttribute(e)===""){{t.removeAttribute(e)}}}else if((!c||l&4||o)&&!i){s=s===true?"":s;{t.setAttribute(e,s)}}}}};const A=/\s/;const F=t=>!t?[]:t.split(A);const L="Capture";const N=new RegExp(L+"$");const R=(t,e,n,s)=>{const o=e.p.nodeType===11&&e.p.host?e.p.host:e.p;const l=t&&t.o||r;const c=e.o||r;{for(s in l){if(!(s in c)){U(o,s,l[s],undefined,n,e.u)}}}for(s in c){U(o,s,l[s],c[s],n,e.u)}};const T=(t,o,l,c)=>{const i=o.l[l];let f=0;let r;let v;if(i.t!==null){r=i.p=jt.createTextNode(i.t)}else{if(!s){s=i.v==="svg"}r=i.p=jt.createElementNS(s?u:a,i.v);if(s&&i.v==="foreignObject"){s=false}{R(null,i,s)}if(d(e)&&r["s-si"]!==e){r.classList.add(r["s-si"]=e)}if(i.l){for(f=0;f<i.l.length;++f){v=T(t,i,f);if(v){r.appendChild(v)}}}{if(i.v==="svg"){s=false}else if(r.tagName==="foreignObject"){s=true}}}r["s-hn"]=n;return r};const H=(t,e,s,o,l,c)=>{let i=t;let f;if(i.shadowRoot&&i.tagName===n){i=i.shadowRoot}for(;l<=c;++l){if(o[l]){f=T(null,s,l);if(f){o[l].p=f;i.insertBefore(f,e)}}}};const W=(t,e,n)=>{for(let s=e;s<=n;++s){const e=t[s];if(e){const t=e.p;V(e);if(t){t.remove()}}}};const q=(t,e,n,s,o=false)=>{let l=0;let c=0;let i=0;let f=0;let r=e.length-1;let u=e[0];let a=e[r];let d=s.length-1;let v=s[0];let p=s[d];let y;let h;while(l<=r&&c<=d){if(u==null){u=e[++l]}else if(a==null){a=e[--r]}else if(v==null){v=s[++c]}else if(p==null){p=s[--d]}else if(D(u,v,o)){I(u,v,o);u=e[++l];v=s[++c]}else if(D(a,p,o)){I(a,p,o);a=e[--r];p=s[--d]}else if(D(u,p,o)){I(u,p,o);t.insertBefore(u.p,a.p.nextSibling);u=e[++l];p=s[--d]}else if(D(a,v,o)){I(a,v,o);t.insertBefore(a.p,u.p);a=e[--r];v=s[++c]}else{i=-1;{for(f=l;f<=r;++f){if(e[f]&&e[f].i!==null&&e[f].i===v.i){i=f;break}}}if(i>=0){h=e[i];if(h.v!==v.v){y=T(e&&e[c],n,i)}else{I(h,v,o);e[i]=undefined;y=h.p}v=s[++c]}else{y=T(e&&e[c],n,c);v=s[++c]}if(y){{u.p.parentNode.insertBefore(y,u.p)}}}}if(l>r){H(t,s[d+1]==null?null:s[d+1].p,n,s,c,d)}else if(c>d){W(e,l,r)}};const D=(t,e,n=false)=>{if(t.v===e.v){if(!n){return t.i===e.i}return true}return false};const I=(t,e,n=false)=>{const o=e.p=t.p;const l=t.l;const c=e.l;const i=e.v;const f=e.t;if(f===null){{s=i==="svg"?true:i==="foreignObject"?false:s}{if(i==="slot");else{R(t,e,s)}}if(l!==null&&c!==null){q(o,l,e,c,n)}else if(c!==null){if(t.t!==null){o.textContent=""}H(o,null,e,c,0,c.length-1)}else if(l!==null){W(l,0,l.length-1)}if(s&&i==="svg"){s=false}}else if(t.t!==f){o.data=f}};const V=t=>{{t.o&&t.o.ref&&t.o.ref(null);t.l&&t.l.map(V)}};const _=(t,s,o=false)=>{const l=t.$hostElement$;const c=t.j||h(null,null);const i=b(s)?s:y(null,null,s);n=l.tagName;if(o&&i.o){for(const t of Object.keys(i.o)){if(l.hasAttribute(t)&&!["key","ref","style","class"].includes(t)){i.o[t]=l[t]}}}i.v=null;i.u|=4;t.j=i;i.p=c.p=l.shadowRoot||l;{e=l["s-sc"]}I(c,i,o)};const z=(t,e)=>{if(e&&!t.O&&e["s-p"]){e["s-p"].push(new Promise((e=>t.O=e)))}};const B=(t,e)=>{{t.u|=16}if(t.u&4){t.u|=512;return}z(t,t.k);const n=()=>G(t,e);return Ft(n)};const G=(t,e)=>{const n=l("scheduleUpdate",t.$.S);const s=t.C;let o;n();return J(o,(()=>Q(t,s,e)))};const J=(t,e)=>K(t)?t.then(e):e();const K=t=>t instanceof Promise||t&&t.then&&typeof t.then==="function";const Q=async(t,e,n)=>{var s;const o=t.$hostElement$;const c=l("update",t.$.S);const i=o["s-rc"];if(n){P(t)}const f=l("render",t.$.S);{X(t,e,o,n)}if(i){i.map((t=>t()));o["s-rc"]=undefined}f();c();{const e=(s=o["s-p"])!==null&&s!==void 0?s:[];const n=()=>Y(t);if(e.length===0){n()}else{Promise.all(e).then(n);t.u|=4;e.length=0}}};const X=(t,e,n,s)=>{try{e=e.render();{t.u&=~16}{t.u|=2}{{{_(t,e,s)}}}}catch(e){bt(e,t.$hostElement$)}return null};const Y=t=>{const e=t.$.S;const n=t.$hostElement$;const s=l("postUpdate",e);const o=t.C;const c=t.k;if(!(t.u&64)){t.u|=64;{et(n)}{tt(o,"componentDidLoad")}s();{t.M(n);if(!c){Z()}}}else{s()}{t.P(n)}{if(t.O){t.O();t.O=undefined}if(t.u&512){At((()=>B(t,false)))}t.u&=~(4|512)}};const Z=e=>{{et(jt.documentElement)}At((()=>k(St,"appload",{detail:{namespace:t}})))};const tt=(t,e,n)=>{if(t&&t[e]){try{return t[e](n)}catch(t){bt(t)}}return undefined};const et=t=>t.classList.add("hydrated");const nt=(t,e)=>pt(t).U.get(e);const st=(t,e,n,s)=>{const o=pt(t);const l=o.$hostElement$;const c=o.U.get(e);const i=o.u;const f=o.C;n=S(n,s.A[e][0]);const r=Number.isNaN(c)&&Number.isNaN(n);const u=n!==c&&!r;if((!(i&8)||c===undefined)&&u){o.U.set(e,n);if(f){if(s.F&&i&128){const t=s.F[e];if(t){t.map((t=>{try{f[t](n,c,e)}catch(t){bt(t,l)}}))}}if((i&(2|16))===2){B(o,false)}}}};const ot=(t,e,n)=>{var s;const o=t.prototype;if(e.A){if(t.watchers){e.F=t.watchers}const l=Object.entries(e.A);l.map((([t,[s]])=>{if(s&31||n&2&&s&32){Object.defineProperty(o,t,{get(){return nt(this,t)},set(n){st(this,t,n,e)},configurable:true,enumerable:true})}else if(n&1&&s&64){Object.defineProperty(o,t,{value(...e){var n;const s=pt(this);return(n=s===null||s===void 0?void 0:s.L)===null||n===void 0?void 0:n.then((()=>{var n;return(n=s.C)===null||n===void 0?void 0:n[t](...e)}))}})}}));if(n&1){const n=new Map;o.attributeChangedCallback=function(t,s,l){Ot.jmp((()=>{var c;const i=n.get(t);if(this.hasOwnProperty(i)){l=this[i];delete this[i]}else if(o.hasOwnProperty(i)&&typeof this[i]==="number"&&this[i]==l){return}else if(i==null){const n=pt(this);const o=n===null||n===void 0?void 0:n.u;if(o&&!(o&8)&&o&128&&l!==s){const o=n.C;const i=(c=e.F)===null||c===void 0?void 0:c[t];i===null||i===void 0?void 0:i.forEach((e=>{if(o[e]!=null){o[e].call(o,l,s,t)}}))}return}this[i]=l===null&&typeof this[i]==="boolean"?false:l}))};t.observedAttributes=Array.from(new Set([...Object.keys((s=e.F)!==null&&s!==void 0?s:{}),...l.filter((([t,e])=>e[0]&15)).map((([t,e])=>{const s=e[1]||t;n.set(s,t);return s}))]))}}return t};const lt=async(t,e,n,s)=>{let o;if((e.u&32)===0){e.u|=32;{o=$t(n);if(o.then){const t=c();o=await o;t()}if(!o.isProxied){{n.F=o.watchers}ot(o,n,2);o.isProxied=true}const t=l("createInstance",n.S);{e.u|=8}try{new o(e)}catch(t){bt(t)}{e.u&=~8}{e.u|=128}t()}if(o.style){let t=o.style;const e=E(n);if(!gt.has(e)){const s=l("registerStyles",n.S);M(e,t,!!(n.u&1));s()}}}const i=e.k;const f=()=>B(e,true);if(i&&i["s-rc"]){i["s-rc"].push(f)}else{f()}};const ct=t=>{};const it=t=>{if((Ot.u&1)===0){const e=pt(t);const n=e.$;const s=l("connectedCallback",n.S);if(!(e.u&1)){e.u|=1;{let n=t;while(n=n.parentNode||n.host){if(n["s-p"]){z(e,e.k=n);break}}}if(n.A){Object.entries(n.A).map((([e,[n]])=>{if(n&31&&t.hasOwnProperty(e)){const n=t[e];delete t[e];t[e]=n}}))}{lt(t,e,n)}}else{if(e===null||e===void 0?void 0:e.C);else if(e===null||e===void 0?void 0:e.N){e.N.then((()=>ct()))}}s()}};const ft=t=>{};const rt=async t=>{if((Ot.u&1)===0){const e=pt(t);if(e===null||e===void 0?void 0:e.C);else if(e===null||e===void 0?void 0:e.N){e.N.then((()=>ft()))}}};const ut=(t,e={})=>{var n;const s=l();const o=[];const c=e.exclude||[];const r=St.customElements;const u=jt.head;const a=u.querySelector("meta[charset]");const d=jt.createElement("style");const v=[];let y;let h=true;Object.assign(Ot,e);Ot.R=new URL(e.resourcesUrl||"./",jt.baseURI).href;let m=false;t.map((t=>{t[1].map((e=>{var n;const s={u:e[0],S:e[1],A:e[2],T:e[3]};if(s.u&4){m=true}{s.A=e[2]}{s.F=(n=e[4])!==null&&n!==void 0?n:{}}const l=s.S;const i=class extends HTMLElement{constructor(t){super(t);t=this;ht(t,s);if(s.u&1){{{t.attachShadow({mode:"open"})}}}}connectedCallback(){if(y){clearTimeout(y);y=null}if(h){v.push(this)}else{Ot.jmp((()=>it(this)))}}disconnectedCallback(){Ot.jmp((()=>rt(this)))}componentOnReady(){return pt(this).N}};s.H=t[0];if(!c.includes(l)&&!r.get(l)){o.push(l);r.define(l,ot(i,s,1))}}))}));if(m){d.innerHTML+=f}{d.innerHTML+=o+i}if(d.innerHTML.length){d.setAttribute("data-styles","");const t=(n=Ot.m)!==null&&n!==void 0?n:p(jt);if(t!=null){d.setAttribute("nonce",t)}u.insertBefore(d,a?a.nextSibling:u.firstChild)}h=false;if(v.length){v.map((t=>t.connectedCallback()))}else{{Ot.jmp((()=>y=setTimeout(Z,30)))}}s()};const at=(t,e)=>e;const dt=t=>Ot.m=t;const vt=new WeakMap;const pt=t=>vt.get(t);const yt=(t,e)=>vt.set(e.C=t,e);const ht=(t,e)=>{const n={u:0,$hostElement$:t,$:e,U:new Map};{n.L=new Promise((t=>n.P=t))}{n.N=new Promise((t=>n.M=t));t["s-p"]=[];t["s-rc"]=[]}return vt.set(t,n)};const mt=(t,e)=>e in t;const bt=(t,e)=>(0,console.error)(t,e);const wt=new Map;const $t=(t,e,n)=>{const s=t.S.replace(/-/g,"_");const o=t.H;const l=wt.get(o);if(l){return l[s]}
/*!__STENCIL_STATIC_IMPORT_SWITCH__*/return import(`./${o}.entry.js${""}`).then((t=>{{wt.set(o,t)}return t[s]}),bt)};const gt=new Map;const St=typeof window!=="undefined"?window:{};const jt=St.document||{head:{}};const Ot={u:0,R:"",jmp:t=>t(),raf:t=>requestAnimationFrame(t),ael:(t,e,n,s)=>t.addEventListener(e,n,s),rel:(t,e,n,s)=>t.removeEventListener(e,n,s),ce:(t,e)=>new CustomEvent(t,e)};const kt=t=>Promise.resolve(t);const Ct=(()=>{try{new CSSStyleSheet;return typeof(new CSSStyleSheet).replaceSync==="function"}catch(t){}return false})();const Mt=[];const xt=[];const Pt=(t,e)=>n=>{t.push(n);if(!o){o=true;if(e&&Ot.u&4){At(Ut)}else{Ot.raf(Ut)}}};const Et=t=>{for(let e=0;e<t.length;e++){try{t[e](performance.now())}catch(t){bt(t)}}t.length=0};const Ut=()=>{Et(Mt);{Et(xt);if(o=Mt.length>0){Ot.raf(Ut)}}};const At=t=>kt().then(t);const Ft=Pt(xt,true);export{at as F,m as H,ut as b,O as c,y as h,kt as p,yt as r,dt as s};
//# sourceMappingURL=p-bc4d54f8.js.map