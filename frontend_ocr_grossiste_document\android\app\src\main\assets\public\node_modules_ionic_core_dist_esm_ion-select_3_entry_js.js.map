{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-select_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC+H;AACnD;AACkB;AACmB;AACgB;AAClF;AACmD;AAC/B;AACW;AACjB;AAChC;AACe;AACF;AACA;AACb;AAE7B,MAAMoC,YAAY,GAAG,ivPAAivP;AACtwP,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,mqeAAmqe;AACvre,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjBzC,qDAAgB,CAAC,IAAI,EAAEyC,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAGxC,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACyC,SAAS,GAAGzC,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC0C,UAAU,GAAG1C,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAAC2C,QAAQ,GAAG3C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC4C,OAAO,GAAG5C,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC6C,QAAQ,GAAG7C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC8C,OAAO,GAAI,WAAUC,SAAS,EAAG,EAAC;IACvC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,MAAM,GAAGD,EAAE,CAACC,MAAM;MACxB,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAAC,8BAA8B,CAAC;MAClE,IAAIF,MAAM,KAAK,IAAI,CAACG,EAAE,IAAIF,WAAW,KAAK,IAAI,EAAE;QAC5C,IAAI,CAACG,QAAQ,CAAC,CAAC;QACf,IAAI,CAACC,IAAI,CAACN,EAAE,CAAC;MACjB,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgBA,EAAE,CAACO,cAAc,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAChB,OAAO,CAACe,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,UAAU,GAAG,QAAQ;IAC1B,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,WAAW,GAAGD,SAAS;IAC5B,IAAI,CAACE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGH,SAAS;IACrB,IAAI,CAACI,SAAS,GAAG,OAAO;IACxB,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,KAAK,GAAGP,SAAS;IACtB,IAAI,CAACQ,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC5B,OAAO;IACxB,IAAI,CAAC6B,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAGZ,SAAS;IAC5B,IAAI,CAACa,YAAY,GAAGb,SAAS;IAC7B,IAAI,CAACc,UAAU,GAAGd,SAAS;IAC3B,IAAI,CAACe,YAAY,GAAGf,SAAS;IAC7B,IAAI,CAACgB,KAAK,GAAGhB,SAAS;IACtB,IAAI,CAACiB,KAAK,GAAGjB,SAAS;EAC1B;EACAkB,YAAYA,CAAA,EAAG;IACX,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,QAAQA,CAACH,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACzC,SAAS,CAACmB,IAAI,CAAC;MAAEsB;IAAM,CAAC,CAAC;EAClC;EACMI,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACtB,MAAM;QAAEjC;MAAG,CAAC,GAAGgC,KAAI;MACnBA,KAAI,CAACE,eAAe,GAAG/E,gEAAqB,CAAC6C,EAAE,EAAE,MAAMgC,KAAI,CAACG,aAAa,EAAE,MAAMH,KAAI,CAACI,SAAS,CAAC;MAChGJ,KAAI,CAACK,oBAAoB,CAAC,CAAC;MAC3BL,KAAI,CAACH,SAAS,CAAC,CAAC;MAChBG,KAAI,CAACM,SAAS,GAAGhE,6DAAe,CAAC0D,KAAI,CAAChC,EAAE,EAAE,mBAAmB,eAAAiC,6OAAA,CAAE,aAAY;QACvED,KAAI,CAACK,oBAAoB,CAAC,CAAC;QAC3B;AACZ;AACA;AACA;AACA;AACA;QACYpF,qDAAW,CAAC+E,KAAI,CAAC;MACrB,CAAC,EAAC;IAAC;EACP;EACAO,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC7C,mBAAmB,GAAGpC,uDAAiB,CAAC,IAAI,CAAC0C,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EACzE;EACAwC,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACX,SAAS,CAAC,CAAC;EACpB;EACAY,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACH,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACI,UAAU,CAAC,CAAC;MAC3B,IAAI,CAACJ,SAAS,GAAG5B,SAAS;IAC9B;IACA,IAAI,IAAI,CAACwB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACS,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACT,eAAe,GAAGxB,SAAS;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACUR,IAAIA,CAAC0C,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAZ,6OAAA;MACd,IAAIY,MAAI,CAACjC,QAAQ,IAAIiC,MAAI,CAACtC,UAAU,EAAE;QAClC,OAAOG,SAAS;MACpB;MACAmC,MAAI,CAACtC,UAAU,GAAG,IAAI;MACtB,MAAMuC,OAAO,GAAID,MAAI,CAACC,OAAO,SAASD,MAAI,CAACE,aAAa,CAACH,KAAK,CAAE;MAChEE,OAAO,CAACE,YAAY,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC9BJ,MAAI,CAACC,OAAO,GAAGpC,SAAS;QACxBmC,MAAI,CAACtC,UAAU,GAAG,KAAK;QACvBsC,MAAI,CAACzD,UAAU,CAACiB,IAAI,CAAC,CAAC;QACtBwC,MAAI,CAAC5C,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC;MACF,MAAM6C,OAAO,CAACI,OAAO,CAAC,CAAC;MACvB;MACA,IAAIL,MAAI,CAAC/B,SAAS,KAAK,SAAS,EAAE;QAC9B,MAAMqC,eAAe,GAAGN,MAAI,CAACO,SAAS,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC,CAAC4B,OAAO,CAACV,MAAI,CAAClB,KAAK,CAAC;QAC9E,IAAIwB,eAAe,GAAG,CAAC,CAAC,EAAE;UACtB,MAAMK,YAAY,GAAGV,OAAO,CAACW,aAAa,CAAE,sCAAqCN,eAAe,GAAG,CAAE,GAAE,CAAC;UACxG,IAAIK,YAAY,EAAE;YACdjG,uDAAmB,CAACiG,YAAY,CAAC;YACjC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;YACoB,MAAME,aAAa,GAAGF,YAAY,CAACC,aAAa,CAAC,yBAAyB,CAAC;YAC3E,IAAIC,aAAa,EAAE;cACfA,aAAa,CAACC,KAAK,CAAC,CAAC;YACzB;UACJ;QACJ,CAAC,MACI;UACD;AAChB;AACA;UACgB,MAAMC,kBAAkB,GAAGd,OAAO,CAACW,aAAa,CAAC,sEAAsE,CAAC;UACxH,IAAIG,kBAAkB,EAAE;YACpBrG,uDAAmB,CAACqG,kBAAkB,CAAC7D,OAAO,CAAC,UAAU,CAAC,CAAC;YAC3D;AACpB;AACA;YACoB6D,kBAAkB,CAACD,KAAK,CAAC,CAAC;UAC9B;QACJ;MACJ;MACA,OAAOb,OAAO;IAAC;EACnB;EACAC,aAAaA,CAACnD,EAAE,EAAE;IACd,IAAIiE,eAAe,GAAG,IAAI,CAAC/C,SAAS;IACpC,IAAI+C,eAAe,KAAK,cAAc,IAAI,IAAI,CAAC1C,QAAQ,EAAE;MACrD2C,OAAO,CAACC,IAAI,CAAE,+BAA8BF,eAAgB,mEAAkE,CAAC;MAC/HA,eAAe,GAAG,OAAO;IAC7B;IACA,IAAIA,eAAe,KAAK,SAAS,IAAI,CAACjE,EAAE,EAAE;MACtCkE,OAAO,CAACC,IAAI,CAAE,iCAAgCF,eAAgB,kEAAiE,CAAC;MAChIA,eAAe,GAAG,OAAO;IAC7B;IACA,IAAIA,eAAe,KAAK,cAAc,EAAE;MACpC,OAAO,IAAI,CAACG,eAAe,CAAC,CAAC;IACjC;IACA,IAAIH,eAAe,KAAK,SAAS,EAAE;MAC/B,OAAO,IAAI,CAACI,WAAW,CAACrE,EAAE,CAAC;IAC/B;IACA,OAAO,IAAI,CAACsE,SAAS,CAAC,CAAC;EAC3B;EACA7B,oBAAoBA,CAAA,EAAG;IACnB,MAAMS,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACA,MAAMM,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMzB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,QAAQ,IAAI,CAACb,SAAS;MAClB,KAAK,cAAc;QACfgC,OAAO,CAACqB,OAAO,GAAG,IAAI,CAACC,wBAAwB,CAAChB,SAAS,EAAEzB,KAAK,CAAC;QACjE;MACJ,KAAK,SAAS;QACV,MAAM0C,OAAO,GAAGvB,OAAO,CAACW,aAAa,CAAC,oBAAoB,CAAC;QAC3D,IAAIY,OAAO,EAAE;UACTA,OAAO,CAACC,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAACnB,SAAS,EAAEzB,KAAK,CAAC;QACjE;QACA;MACJ,KAAK,OAAO;QACR,MAAM6C,SAAS,GAAG,IAAI,CAACrD,QAAQ,GAAG,UAAU,GAAG,OAAO;QACtD2B,OAAO,CAAC2B,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACtB,SAAS,EAAEoB,SAAS,EAAE7C,KAAK,CAAC;QACpE;IACR;EACJ;EACAyC,wBAAwBA,CAACO,IAAI,EAAEC,WAAW,EAAE;IACxC,MAAMC,kBAAkB,GAAGF,IAAI,CAACtB,GAAG,CAAEyB,MAAM,IAAK;MAC5C,MAAMnD,KAAK,GAAGoD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAI,GAAEC,YAAa,IAAGR,WAAY,EAAC;MACjD,OAAO;QACHS,IAAI,EAAErI,kEAAgB,CAACwH,WAAW,EAAEjD,KAAK,EAAE,IAAI,CAAChB,WAAW,CAAC,GAAG,UAAU,GAAG,EAAE;QAC9E+E,IAAI,EAAEZ,MAAM,CAACa,WAAW;QACxBC,QAAQ,EAAEL,QAAQ;QAClBM,OAAO,EAAEA,CAAA,KAAM;UACX,IAAI,CAAC/D,QAAQ,CAACH,KAAK,CAAC;QACxB;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAkD,kBAAkB,CAACiB,IAAI,CAAC;MACpBJ,IAAI,EAAE,IAAI,CAAClF,UAAU;MACrBiF,IAAI,EAAE,QAAQ;MACdI,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAAC1G,SAAS,CAACkB,IAAI,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF,OAAOwE,kBAAkB;EAC7B;EACAH,iBAAiBA,CAACC,IAAI,EAAEH,SAAS,EAAEI,WAAW,EAAE;IAC5C,MAAMmB,WAAW,GAAGpB,IAAI,CAACtB,GAAG,CAAEyB,MAAM,IAAK;MACrC,MAAMnD,KAAK,GAAGoD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAI,GAAEC,YAAa,IAAGR,WAAY,EAAC;MACjD,OAAO;QACHgB,IAAI,EAAExB,SAAS;QACfoB,QAAQ,EAAEL,QAAQ;QAClBtE,KAAK,EAAE6D,MAAM,CAACa,WAAW,IAAI,EAAE;QAC/BhE,KAAK;QACLsE,OAAO,EAAE7I,kEAAgB,CAACwH,WAAW,EAAEjD,KAAK,EAAE,IAAI,CAAChB,WAAW,CAAC;QAC/DC,QAAQ,EAAEkE,MAAM,CAAClE;MACrB,CAAC;IACL,CAAC,CAAC;IACF,OAAOmF,WAAW;EACtB;EACAxB,oBAAoBA,CAACI,IAAI,EAAEC,WAAW,EAAE;IACpC,MAAMsB,cAAc,GAAGvB,IAAI,CAACtB,GAAG,CAAEyB,MAAM,IAAK;MACxC,MAAMnD,KAAK,GAAGoD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAI,GAAEC,YAAa,IAAGR,WAAY,EAAC;MACjD,OAAO;QACHU,IAAI,EAAEZ,MAAM,CAACa,WAAW,IAAI,EAAE;QAC9BC,QAAQ,EAAEL,QAAQ;QAClB5D,KAAK;QACLsE,OAAO,EAAE7I,kEAAgB,CAACwH,WAAW,EAAEjD,KAAK,EAAE,IAAI,CAAChB,WAAW,CAAC;QAC/DC,QAAQ,EAAEkE,MAAM,CAAClE,QAAQ;QACzBiF,OAAO,EAAGM,QAAQ,IAAK;UACnB,IAAI,CAACrE,QAAQ,CAACqE,QAAQ,CAAC;UACvB,IAAI,CAAC,IAAI,CAAChF,QAAQ,EAAE;YAChB,IAAI,CAACiF,KAAK,CAAC,CAAC;UAChB;QACJ;MACJ,CAAC;IACL,CAAC,CAAC;IACF,OAAOF,cAAc;EACzB;EACMjC,WAAWA,CAACrE,EAAE,EAAE;IAAA,IAAAyG,MAAA;IAAA,OAAApE,6OAAA;MAClB,MAAM;QAAEpB,IAAI;QAAEK;MAAe,CAAC,GAAGmF,MAAI;MACrC,MAAMtF,gBAAgB,GAAGsF,MAAI,CAACtF,gBAAgB;MAC9C,MAAMuF,IAAI,GAAG5H,6DAAU,CAAC2H,MAAI,CAAC;MAC7B,MAAME,YAAY,GAAGD,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI;MACjD,MAAMnF,QAAQ,GAAGkF,MAAI,CAAClF,QAAQ;MAC9B,MAAMQ,KAAK,GAAG0E,MAAI,CAAC1E,KAAK;MACxB,IAAIiB,KAAK,GAAGhD,EAAE;MACd,IAAI4G,IAAI,GAAG,MAAM;MACjB,MAAMC,yBAAyB,GAAGvF,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;MAC/F;AACR;AACA;AACA;AACA;MACQ,IAAIuF,yBAAyB,IAAKH,IAAI,KAAK,IAAI,IAAIzF,IAAI,KAAKH,SAAU,EAAE;QACpE8F,IAAI,GAAG,OAAO;QACd;AACZ;AACA;AACA;AACA;MACQ,CAAC,MACI;QACD5D,KAAK,GAAG8D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/G,EAAE,CAAC,EAAE;UAAEgH,MAAM,EAAE;YAC/CC,eAAe,EAAER,MAAI,CAACS;UAC1B;QAAE,CAAC,CAAC;MACZ;MACA,MAAMC,WAAW,GAAGL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEL,IAAI;QAClD1D,KAAK;QAAEoE,SAAS,EAAE,QAAQ;QAAER,IAAI;QAChCD;MAAa,CAAC,EAAExF,gBAAgB,CAAC,EAAE;QAAEkG,SAAS,EAAE,oBAAoB;QAAErB,QAAQ,EAAE,CAAC,gBAAgB,EAAE7E,gBAAgB,CAAC6E,QAAQ,CAAC;QAAEsB,cAAc,EAAE;UAC3IC,MAAM,EAAEpG,gBAAgB,CAACoG,MAAM;UAC/BC,SAAS,EAAErG,gBAAgB,CAACqG,SAAS;UACrCC,OAAO,EAAEtG,gBAAgB,CAACsG,OAAO;UACjClG,QAAQ;UACRQ,KAAK;UACL2C,OAAO,EAAE+B,MAAI,CAAC9B,oBAAoB,CAAC8B,MAAI,CAACjD,SAAS,EAAEzB,KAAK;QAC5D;MAAE,CAAC,CAAC;MACR,OAAOlE,oDAAiB,CAAC6J,MAAM,CAACP,WAAW,CAAC;IAAC;EACjD;EACM/C,eAAeA,CAAA,EAAG;IAAA,IAAAuD,MAAA;IAAA,OAAAtF,6OAAA;MACpB,MAAMqE,IAAI,GAAG5H,6DAAU,CAAC6I,MAAI,CAAC;MAC7B,MAAMxG,gBAAgB,GAAGwG,MAAI,CAACxG,gBAAgB;MAC9C,MAAMyG,eAAe,GAAGd,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEL;MAAK,CAAC,EAAEvF,gBAAgB,CAAC,EAAE;QAAEoD,OAAO,EAAEoD,MAAI,CAACnD,wBAAwB,CAACmD,MAAI,CAACnE,SAAS,EAAEmE,MAAI,CAAC5F,KAAK,CAAC;QAAEiE,QAAQ,EAAE,CAAC,qBAAqB,EAAE7E,gBAAgB,CAAC6E,QAAQ;MAAE,CAAC,CAAC;MACtN,OAAOjI,oDAAqB,CAAC2J,MAAM,CAACE,eAAe,CAAC;IAAC;EACzD;EACMtD,SAASA,CAAA,EAAG;IAAA,IAAAuD,MAAA;IAAA,OAAAxF,6OAAA;MACd,MAAMlB,gBAAgB,GAAG0G,MAAI,CAAC1G,gBAAgB;MAC9C,MAAMyD,SAAS,GAAGiD,MAAI,CAACtG,QAAQ,GAAG,UAAU,GAAG,OAAO;MACtD,MAAMmF,IAAI,GAAG5H,6DAAU,CAAC+I,MAAI,CAAC;MAC7B,MAAMC,SAAS,GAAGhB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEL;MAAK,CAAC,EAAEvF,gBAAgB,CAAC,EAAE;QAAEoG,MAAM,EAAEpG,gBAAgB,CAACoG,MAAM,GAAGpG,gBAAgB,CAACoG,MAAM,GAAGM,MAAI,CAACE,SAAS;QAAElD,MAAM,EAAEgD,MAAI,CAAC/C,iBAAiB,CAAC+C,MAAI,CAACrE,SAAS,EAAEoB,SAAS,EAAEiD,MAAI,CAAC9F,KAAK,CAAC;QAAEwC,OAAO,EAAE,CACjO;UACIuB,IAAI,EAAE+B,MAAI,CAACjH,UAAU;UACrBiF,IAAI,EAAE,QAAQ;UACdI,OAAO,EAAEA,CAAA,KAAM;YACX4B,MAAI,CAACtI,SAAS,CAACkB,IAAI,CAAC,CAAC;UACzB;QACJ,CAAC,EACD;UACIqF,IAAI,EAAE+B,MAAI,CAACpG,MAAM;UACjBwE,OAAO,EAAG+B,cAAc,IAAK;YACzBH,MAAI,CAAC3F,QAAQ,CAAC8F,cAAc,CAAC;UACjC;QACJ,CAAC,CACJ;QAAEhC,QAAQ,EAAE,CACT,cAAc,EACd7E,gBAAgB,CAAC6E,QAAQ,EACzB6B,MAAI,CAACtG,QAAQ,GAAG,uBAAuB,GAAG,qBAAqB;MACjE,CAAC,CAAC;MACR,OAAOtD,oDAAe,CAACyJ,MAAM,CAACI,SAAS,CAAC;IAAC;EAC7C;EACA;AACJ;AACA;EACItB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACtD,OAAO,EAAE;MACf,OAAO+E,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,OAAO,IAAI,CAAChF,OAAO,CAACiF,OAAO,CAAC,CAAC;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,KAAK,EAAE;EAChC;EACA,IAAI7E,SAASA,CAAA,EAAG;IACZ,OAAO6B,KAAK,CAACC,IAAI,CAAC,IAAI,CAAClF,EAAE,CAACkI,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIP,SAASA,CAAA,EAAG;IACZ,MAAM;MAAE1G;IAAM,CAAC,GAAG,IAAI;IACtB,IAAIA,KAAK,KAAKP,SAAS,EAAE;MACrB,OAAOO,KAAK;IAChB;IACA,MAAM;MAAEmB;IAAU,CAAC,GAAG,IAAI;IAC1B,IAAIA,SAAS,KAAK,IAAI,EAAE;MACpB,OAAOA,SAAS,CAACuD,WAAW;IAChC;IACA;EACJ;EACAsC,OAAOA,CAAA,EAAG;IACN,MAAM1G,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,IAAIA,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,EAAE,EAAE;MAC7C,OAAOA,YAAY;IACvB;IACA,OAAO4G,YAAY,CAAC,IAAI,CAAC/E,SAAS,EAAE,IAAI,CAACzB,KAAK,EAAE,IAAI,CAAChB,WAAW,CAAC;EACrE;EACAV,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACmI,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACzE,KAAK,CAAC,CAAC;IACxB;EACJ;EACA9B,SAASA,CAAA,EAAG;IACR,MAAM;MAAEjB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAMyH,KAAK,GAAG;MACV,sBAAsB,EAAEzH;IAC5B,CAAC;IACD,IAAI,CAACrB,QAAQ,CAACc,IAAI,CAACgI,KAAK,CAAC;EAC7B;EACAC,WAAWA,CAAA,EAAG;IACV,MAAM;MAAErH;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQtE,qDAAC,CAAC,KAAK,EAAE;MAAE4L,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACC;MACvC,CAAC;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAAExH,KAAK,KAAKP,SAAS,GAAG/D,qDAAC,CAAC,MAAM,EAAE;MAAEyE,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAGzE,qDAAC,CAAC,KAAK,EAAE;MAAE4L,KAAK,EAAE;IAAa,CAAC,EAAEtH,KAAK,CAAC,CAAC;EAC1H;EACAyH,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACzG,eAAe,MAAM,IAAI,IAAIyG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;EACI,IAAIxG,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpC,EAAE,CAACyD,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI+E,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACvH,KAAK,KAAKP,SAAS,IAAI,IAAI,CAAC0B,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACIyG,oBAAoBA,CAAA,EAAG;IACnB,MAAMvC,IAAI,GAAG5H,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMoK,cAAc,GAAGxC,IAAI,KAAK,IAAI,IAAI,IAAI,CAACzF,IAAI,KAAK,SAAS;IAC/D,IAAIiI,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACHnM,qDAAC,CAAC,KAAK,EAAE;QAAE4L,KAAK,EAAE;MAA2B,CAAC,EAAE5L,qDAAC,CAAC,KAAK,EAAE;QAAE4L,KAAK,EAAE;MAAuB,CAAC,CAAC,EAAE5L,qDAAC,CAAC,KAAK,EAAE;QAAE4L,KAAK,EAAE;UACvG,sBAAsB,EAAE,IAAI;UAC5B,6BAA6B,EAAE,CAAC,IAAI,CAACC;QACzC;MAAE,CAAC,EAAE7L,qDAAC,CAAC,KAAK,EAAE;QAAE4L,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEQ,GAAG,EAAG/I,EAAE,IAAM,IAAI,CAACmC,aAAa,GAAGnC;MAAI,CAAC,EAAE,IAAI,CAACiB,KAAK,CAAC,CAAC,EAAEtE,qDAAC,CAAC,KAAK,EAAE;QAAE4L,KAAK,EAAE;MAAqB,CAAC,CAAC,CAAC,EACpK,IAAI,CAACD,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIU,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAE1H;IAAY,CAAC,GAAG,IAAI;IAC5B,MAAM2H,YAAY,GAAG,IAAI,CAAChB,OAAO,CAAC,CAAC;IACnC,IAAIiB,mBAAmB,GAAG,KAAK;IAC/B,IAAIC,UAAU,GAAGF,YAAY;IAC7B,IAAIE,UAAU,KAAK,EAAE,IAAI7H,WAAW,KAAKZ,SAAS,EAAE;MAChDyI,UAAU,GAAG7H,WAAW;MACxB4H,mBAAmB,GAAG,IAAI;IAC9B;IACA,MAAME,iBAAiB,GAAG;MACtB,aAAa,EAAE,IAAI;MACnB,oBAAoB,EAAEF;IAC1B,CAAC;IACD,MAAMG,QAAQ,GAAGH,mBAAmB,GAAG,aAAa,GAAG,MAAM;IAC7D,OAAQvM,qDAAC,CAAC,KAAK,EAAE;MAAE,aAAa,EAAE,MAAM;MAAE4L,KAAK,EAAEa,iBAAiB;MAAEX,IAAI,EAAEY;IAAS,CAAC,EAAEF,UAAU,CAAC;EACrG;EACA;AACJ;AACA;AACA;EACIG,gBAAgBA,CAAA,EAAG;IACf,MAAMhD,IAAI,GAAG5H,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAE6B,UAAU;MAAEiB,UAAU;MAAEC;IAAa,CAAC,GAAG,IAAI;IACrD,IAAI8H,IAAI;IACR,IAAIhJ,UAAU,IAAIkB,YAAY,KAAKf,SAAS,EAAE;MAC1C6I,IAAI,GAAG9H,YAAY;IACvB,CAAC,MACI;MACD,MAAM+H,WAAW,GAAGlD,IAAI,KAAK,KAAK,GAAG/H,iDAAa,GAAGE,iDAAc;MACnE8K,IAAI,GAAG/H,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGgI,WAAW;IAClF;IACA,OAAO7M,qDAAC,CAAC,UAAU,EAAE;MAAE4L,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE,MAAM;MAAEc,IAAI,EAAEA;IAAK,CAAC,CAAC;EACnG;EACA,IAAIE,SAASA,CAAA,EAAG;IACZ,IAAId,EAAE;IACN,MAAM;MAAErH,WAAW;MAAE5B;IAAoB,CAAC,GAAG,IAAI;IACjD,MAAMuJ,YAAY,GAAG,IAAI,CAAChB,OAAO,CAAC,CAAC;IACnC;IACA,MAAMyB,YAAY,GAAG,CAACf,EAAE,GAAGjJ,mBAAmB,CAAC,YAAY,CAAC,MAAM,IAAI,IAAIiJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAChB,SAAS;IAC7G;AACR;AACA;AACA;AACA;IACQ,IAAIgC,aAAa,GAAGV,YAAY;IAChC,IAAIU,aAAa,KAAK,EAAE,IAAIrI,WAAW,KAAKZ,SAAS,EAAE;MACnDiJ,aAAa,GAAGrI,WAAW;IAC/B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIoI,YAAY,KAAKhJ,SAAS,EAAE;MAC5BiJ,aAAa,GAAGA,aAAa,KAAK,EAAE,GAAGD,YAAY,GAAI,GAAEA,YAAa,KAAIC,aAAc,EAAC;IAC7F;IACA,OAAOA,aAAa;EACxB;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEhJ,QAAQ;MAAEpB,OAAO;MAAEe;IAAW,CAAC,GAAG,IAAI;IAC9C,OAAQ5D,qDAAC,CAAC,QAAQ,EAAE;MAAEiE,QAAQ,EAAEA,QAAQ;MAAEiJ,EAAE,EAAErK,OAAO;MAAE,YAAY,EAAE,IAAI,CAACiK,SAAS;MAAE,eAAe,EAAE,QAAQ;MAAE,eAAe,EAAG,GAAElJ,UAAW,EAAC;MAAEH,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEyI,GAAG,EAAGX,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,CAAC;EAC/O;EACA0B,MAAMA,CAAA,EAAG;IACL,MAAM;MAAElJ,QAAQ;MAAEZ,EAAE;MAAEO,UAAU;MAAEkB,YAAY;MAAEP,cAAc;MAAEF,OAAO;MAAEM,WAAW;MAAET,IAAI;MAAEa,KAAK;MAAEN,IAAI;MAAEO;IAAM,CAAC,GAAG,IAAI;IACvH,MAAM2E,IAAI,GAAG5H,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM+H,yBAAyB,GAAGvF,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;IAC/F,MAAM6I,cAAc,GAAG,CAACtD,yBAAyB;IACjD,MAAMuD,GAAG,GAAGhM,mDAAK,CAACgC,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrC,MAAMiK,MAAM,GAAGhM,qDAAW,CAAC,UAAU,EAAE,IAAI,CAAC+B,EAAE,CAAC;IAC/C,MAAMkK,qBAAqB,GAAG5D,IAAI,KAAK,IAAI,IAAIzF,IAAI,KAAK,SAAS,IAAI,CAACoJ,MAAM;IAC5E,MAAMjC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAMmC,gBAAgB,GAAGnK,EAAE,CAACyD,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClFjG,uDAAiB,CAAC,IAAI,EAAEwC,EAAE,EAAEoB,IAAI,EAAEgJ,UAAU,CAACzI,KAAK,CAAC,EAAEf,QAAQ,CAAC;IAC9D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMyJ,gBAAgB,GAAGnJ,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAK8G,QAAQ,IAAIzH,UAAU,IAAI4J,gBAAgB,CAAE;IACxI,OAAQxN,qDAAC,CAACE,iDAAI,EAAE;MAAEyN,GAAG,EAAE,0CAA0C;MAAE3K,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE4I,KAAK,EAAErK,qDAAkB,CAAC,IAAI,CAACuC,KAAK,EAAE;QACxH,CAAC6F,IAAI,GAAG,IAAI;QACZ,SAAS,EAAE2D,MAAM;QACjB,eAAe,EAAEhM,qDAAW,CAAC,oBAAoB,EAAE+B,EAAE,CAAC;QACtD,iBAAiB,EAAEY,QAAQ;QAC3B,iBAAiB,EAAEL,UAAU;QAC7B,mBAAmB,EAAEkB,YAAY,KAAKf,SAAS;QAC/C,WAAW,EAAEsH,QAAQ;QACrB,gBAAgB,EAAEqC,gBAAgB;QAClC,iBAAiB,EAAE/I,WAAW,KAAKZ,SAAS;QAC5C,eAAe,EAAE,IAAI;QACrB,CAAE,UAASsJ,GAAI,EAAC,GAAG,IAAI;QACvB,CAAE,eAAcnJ,IAAK,EAAC,GAAGA,IAAI,KAAKH,SAAS;QAC3C,CAAE,kBAAiBM,OAAQ,EAAC,GAAG+I,cAAc;QAC7C,CAAE,gBAAerI,KAAM,EAAC,GAAGA,KAAK,KAAKhB,SAAS;QAC9C,CAAE,0BAAyBQ,cAAe,EAAC,GAAG;MAClD,CAAC;IAAE,CAAC,EAAEvE,qDAAC,CAAC,OAAO,EAAE;MAAE2N,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE,gBAAgB;MAAEsB,EAAE,EAAE;IAAe,CAAC,EAAE,IAAI,CAAChB,oBAAoB,CAAC,CAAC,EAAElM,qDAAC,CAAC,KAAK,EAAE;MAAE2N,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE;IAAuB,CAAC,EAAE5L,qDAAC,CAAC,MAAM,EAAE;MAAE2N,GAAG,EAAE,0CAA0C;MAAElJ,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEzE,qDAAC,CAAC,KAAK,EAAE;MAAE2N,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE,gBAAgB;MAAEQ,GAAG,EAAG/I,EAAE,IAAM,IAAI,CAAC8G,eAAe,GAAG9G,EAAG;MAAEyI,IAAI,EAAE;IAAY,CAAC,EAAE,IAAI,CAACO,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACY,aAAa,CAAC,CAAC,CAAC,EAAEjN,qDAAC,CAAC,MAAM,EAAE;MAAE2N,GAAG,EAAE,0CAA0C;MAAElJ,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE,CAACqF,yBAAyB,IAAI,IAAI,CAAC6C,gBAAgB,CAAC,CAAC,CAAC,EAAE7C,yBAAyB,IAAI,IAAI,CAAC6C,gBAAgB,CAAC,CAAC,EAAEY,qBAAqB,IAAIvN,qDAAC,CAAC,KAAK,EAAE;MAAE2N,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE;IAAmB,CAAC,CAAC,CAAC,CAAC;EACtzB;EACA,IAAIvI,EAAEA,CAAA,EAAG;IAAE,OAAOjD,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWwN,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,cAAc,CAAC;MAC5B,YAAY,EAAE,CAAC,cAAc,CAAC;MAC9B,aAAa,EAAE,CAAC,cAAc,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,MAAMxF,cAAc,GAAI/E,EAAE,IAAK;EAC3B,MAAM2B,KAAK,GAAG3B,EAAE,CAAC2B,KAAK;EACtB,OAAOA,KAAK,KAAKjB,SAAS,GAAGV,EAAE,CAAC2F,WAAW,IAAI,EAAE,GAAGhE,KAAK;AAC7D,CAAC;AACD,MAAMyI,UAAU,GAAIzI,KAAK,IAAK;EAC1B,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAOjB,SAAS;EACpB;EACA,IAAIuE,KAAK,CAACuF,OAAO,CAAC7I,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAAC2D,IAAI,CAAC,GAAG,CAAC;EAC1B;EACA,OAAO3D,KAAK,CAAC8I,QAAQ,CAAC,CAAC;AAC3B,CAAC;AACD,MAAMtC,YAAY,GAAGA,CAACuC,IAAI,EAAE/I,KAAK,EAAEhB,WAAW,KAAK;EAC/C,IAAIgB,KAAK,KAAKjB,SAAS,EAAE;IACrB,OAAO,EAAE;EACb;EACA,IAAIuE,KAAK,CAACuF,OAAO,CAAC7I,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CACP0B,GAAG,CAAEsH,CAAC,IAAKC,YAAY,CAACF,IAAI,EAAEC,CAAC,EAAEhK,WAAW,CAAC,CAAC,CAC9CyE,MAAM,CAAEyF,GAAG,IAAKA,GAAG,KAAK,IAAI,CAAC,CAC7BvF,IAAI,CAAC,IAAI,CAAC;EACnB,CAAC,MACI;IACD,OAAOsF,YAAY,CAACF,IAAI,EAAE/I,KAAK,EAAEhB,WAAW,CAAC,IAAI,EAAE;EACvD;AACJ,CAAC;AACD,MAAMiK,YAAY,GAAGA,CAACF,IAAI,EAAE/I,KAAK,EAAEhB,WAAW,KAAK;EAC/C,MAAMmK,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAEF,GAAG,IAAK;IACjC,OAAOxN,kEAAc,CAACsE,KAAK,EAAEoD,cAAc,CAAC8F,GAAG,CAAC,EAAElK,WAAW,CAAC;EAClE,CAAC,CAAC;EACF,OAAOmK,SAAS,GAAGA,SAAS,CAACnF,WAAW,GAAG,IAAI;AACnD,CAAC;AACD,IAAIlG,SAAS,GAAG,CAAC;AACjB,MAAM+F,YAAY,GAAG,yBAAyB;AAC9CzG,MAAM,CAACsJ,KAAK,GAAG;EACX2C,GAAG,EAAEpM,kBAAkB;EACvBqM,EAAE,EAAEnM;AACR,CAAC;AAED,MAAMoM,eAAe,GAAG,qBAAqB;AAC7C,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,YAAY,GAAG,MAAM;EACvBpM,WAAWA,CAACC,OAAO,EAAE;IACjBzC,qDAAgB,CAAC,IAAI,EAAEyC,OAAO,CAAC;IAC/B,IAAI,CAACO,OAAO,GAAI,cAAa6L,eAAe,EAAG,EAAC;IAChD,IAAI,CAACzK,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACe,KAAK,GAAGjB,SAAS;EAC1B;EACAoJ,MAAMA,CAAA,EAAG;IACL,OAAOnN,qDAAC,CAACE,iDAAI,EAAE;MAAEyN,GAAG,EAAE,0CAA0C;MAAE7E,IAAI,EAAE,QAAQ;MAAEoE,EAAE,EAAE,IAAI,CAACrK,OAAO;MAAE+I,KAAK,EAAE7J,6DAAU,CAAC,IAAI;IAAE,CAAC,CAAC;EAClI;EACA,IAAIsB,EAAEA,CAAA,EAAG;IAAE,OAAOjD,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,IAAIsO,eAAe,GAAG,CAAC;AACvBD,YAAY,CAAC/C,KAAK,GAAG8C,qBAAqB;AAE1C,MAAMG,mBAAmB,GAAG,iTAAiT;AAC7U,MAAMC,yBAAyB,GAAGD,mBAAmB;AAErD,MAAME,kBAAkB,GAAG,kqCAAkqC;AAC7rC,MAAMC,wBAAwB,GAAGD,kBAAkB;AAEnD,MAAME,aAAa,GAAG,MAAM;EACxB1M,WAAWA,CAACC,OAAO,EAAE;IACjBzC,qDAAgB,CAAC,IAAI,EAAEyC,OAAO,CAAC;IAC/B,IAAI,CAACkI,MAAM,GAAGzG,SAAS;IACvB,IAAI,CAAC0G,SAAS,GAAG1G,SAAS;IAC1B,IAAI,CAAC2G,OAAO,GAAG3G,SAAS;IACxB,IAAI,CAACS,QAAQ,GAAGT,SAAS;IACzB,IAAI,CAAC4D,OAAO,GAAG,EAAE;EACrB;EACAqH,mBAAmBA,CAAC/L,EAAE,EAAE;IACpB,MAAM;MAAE0E;IAAQ,CAAC,GAAG,IAAI;IACxB,OAAOA,OAAO,CAACyG,IAAI,CAAEzH,CAAC,IAAKA,CAAC,CAAC3B,KAAK,KAAK/B,EAAE,CAACC,MAAM,CAAC8B,KAAK,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;EACIiK,iBAAiBA,CAAChM,EAAE,EAAE;IAClB,MAAMkF,MAAM,GAAG,IAAI,CAAC6G,mBAAmB,CAAC/L,EAAE,CAAC;IAC3C,MAAMiM,MAAM,GAAG,IAAI,CAACC,SAAS,CAAClM,EAAE,CAAC;IACjC,IAAIkF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACe,OAAO,EAAE;MAChE9H,wDAAQ,CAAC+G,MAAM,CAACe,OAAO,EAAEgG,MAAM,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;EACIE,oBAAoBA,CAAA,EAAG;IACnB,MAAM1H,OAAO,GAAG,IAAI,CAACrE,EAAE,CAACD,OAAO,CAAC,aAAa,CAAC;IAC9C,IAAIsE,OAAO,EAAE;MACTA,OAAO,CAAC0D,OAAO,CAAC,CAAC;IACrB;EACJ;EACAiE,UAAUA,CAACpM,EAAE,EAAE;IACX,MAAM;MAAEuB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAM2D,MAAM,GAAG,IAAI,CAAC6G,mBAAmB,CAAC/L,EAAE,CAAC;IAC3C;IACA;IACA,IAAIuB,QAAQ,IAAI2D,MAAM,EAAE;MACpBA,MAAM,CAACmB,OAAO,GAAGrG,EAAE,CAACgH,MAAM,CAACX,OAAO;IACtC;EACJ;EACA6F,SAASA,CAAClM,EAAE,EAAE;IACV,MAAM;MAAEuB,QAAQ;MAAEmD;IAAQ,CAAC,GAAG,IAAI;IAClC,IAAInD,QAAQ,EAAE;MACV;MACA;MACA,OAAOmD,OAAO,CAACc,MAAM,CAAE9B,CAAC,IAAKA,CAAC,CAAC2C,OAAO,CAAC,CAAC5C,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC;IAC/D;IACA;IACA;IACA,MAAMmD,MAAM,GAAG,IAAI,CAAC6G,mBAAmB,CAAC/L,EAAE,CAAC;IAC3C,OAAOkF,MAAM,GAAGA,MAAM,CAACnD,KAAK,GAAGjB,SAAS;EAC5C;EACAuL,aAAaA,CAAC3H,OAAO,EAAE;IACnB,MAAM;MAAEnD;IAAS,CAAC,GAAG,IAAI;IACzB,QAAQA,QAAQ;MACZ,KAAK,IAAI;QACL,OAAO,IAAI,CAAC+K,qBAAqB,CAAC5H,OAAO,CAAC;MAC9C;QACI,OAAO,IAAI,CAAC6H,kBAAkB,CAAC7H,OAAO,CAAC;IAC/C;EACJ;EACA4H,qBAAqBA,CAAC5H,OAAO,EAAE;IAC3B,OAAOA,OAAO,CAACjB,GAAG,CAAEyB,MAAM,IAAMnI,qDAAC,CAAC,UAAU,EAAE;MAAE4L,KAAK,EAAE7B,MAAM,CAACC,MAAM,CAAC;QAC7D;QACA,uBAAuB,EAAE7B,MAAM,CAACmB;MACpC,CAAC,EAAE7H,qDAAW,CAAC0G,MAAM,CAACc,QAAQ,CAAC;IAAE,CAAC,EAAEjJ,qDAAC,CAAC,cAAc,EAAE;MAAEgF,KAAK,EAAEmD,MAAM,CAACnD,KAAK;MAAEf,QAAQ,EAAEkE,MAAM,CAAClE,QAAQ;MAAEqF,OAAO,EAAEnB,MAAM,CAACmB,OAAO;MAAEjF,OAAO,EAAE,OAAO;MAAEE,cAAc,EAAE,KAAK;MAAEkL,WAAW,EAAGxM,EAAE,IAAK;QAC3L,IAAI,CAACoM,UAAU,CAACpM,EAAE,CAAC;QACnB,IAAI,CAACgM,iBAAiB,CAAChM,EAAE,CAAC;QAC1B;QACA3C,qDAAW,CAAC,IAAI,CAAC;MACrB;IAAE,CAAC,EAAE6H,MAAM,CAACY,IAAI,CAAC,CAAE,CAAC;EAC5B;EACAyG,kBAAkBA,CAAC7H,OAAO,EAAE;IACxB,MAAM2B,OAAO,GAAG3B,OAAO,CAACc,MAAM,CAAE9B,CAAC,IAAKA,CAAC,CAAC2C,OAAO,CAAC,CAAC5C,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC,CAAC,CAAC,CAAC;IACvE,OAAQhF,qDAAC,CAAC,iBAAiB,EAAE;MAAEgF,KAAK,EAAEsE,OAAO;MAAEmG,WAAW,EAAGxM,EAAE,IAAK,IAAI,CAACgM,iBAAiB,CAAChM,EAAE;IAAE,CAAC,EAAE0E,OAAO,CAACjB,GAAG,CAAEyB,MAAM,IAAMnI,qDAAC,CAAC,UAAU,EAAE;MAAE4L,KAAK,EAAE7B,MAAM,CAACC,MAAM,CAAC;QACxJ;QACA,oBAAoB,EAAE7B,MAAM,CAACnD,KAAK,KAAKsE;MAC3C,CAAC,EAAE7H,qDAAW,CAAC0G,MAAM,CAACc,QAAQ,CAAC;IAAE,CAAC,EAAEjJ,qDAAC,CAAC,WAAW,EAAE;MAAEgF,KAAK,EAAEmD,MAAM,CAACnD,KAAK;MAAEf,QAAQ,EAAEkE,MAAM,CAAClE,QAAQ;MAAEjB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACoM,oBAAoB,CAAC,CAAC;MAAEM,OAAO,EAAGzM,EAAE,IAAK;QAC9J,IAAIA,EAAE,CAAC0K,GAAG,KAAK,GAAG,EAAE;UAChB;AACpB;AACA;AACA;AACA;UACoB,IAAI,CAACyB,oBAAoB,CAAC,CAAC;QAC/B;MACJ;IAAE,CAAC,EAAEjH,MAAM,CAACY,IAAI,CAAC,CAAE,CAAC,CAAC;EAC7B;EACAoE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE3C,MAAM;MAAEE,OAAO;MAAE/C,OAAO;MAAE8C;IAAU,CAAC,GAAG,IAAI;IACpD,MAAMkF,qBAAqB,GAAGlF,SAAS,KAAK1G,SAAS,IAAI2G,OAAO,KAAK3G,SAAS;IAC9E,OAAQ/D,qDAAC,CAACE,iDAAI,EAAE;MAAEyN,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE7J,6DAAU,CAAC,IAAI;IAAE,CAAC,EAAE/B,qDAAC,CAAC,UAAU,EAAE;MAAE2N,GAAG,EAAE;IAA2C,CAAC,EAAEnD,MAAM,KAAKzG,SAAS,IAAI/D,qDAAC,CAAC,iBAAiB,EAAE;MAAE2N,GAAG,EAAE;IAA2C,CAAC,EAAEnD,MAAM,CAAC,EAAEmF,qBAAqB,IAAK3P,qDAAC,CAAC,UAAU,EAAE;MAAE2N,GAAG,EAAE;IAA2C,CAAC,EAAE3N,qDAAC,CAAC,WAAW,EAAE;MAAE2N,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE;IAAgB,CAAC,EAAEnB,SAAS,KAAK1G,SAAS,IAAI/D,qDAAC,CAAC,IAAI,EAAE;MAAE2N,GAAG,EAAE;IAA2C,CAAC,EAAElD,SAAS,CAAC,EAAEC,OAAO,KAAK3G,SAAS,IAAI/D,qDAAC,CAAC,GAAG,EAAE;MAAE2N,GAAG,EAAE;IAA2C,CAAC,EAAEjD,OAAO,CAAC,CAAC,CAAE,EAAE,IAAI,CAAC4E,aAAa,CAAC3H,OAAO,CAAC,CAAC,CAAC;EAC5qB;EACA,IAAItE,EAAEA,CAAA,EAAG;IAAE,OAAOjD,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD2O,aAAa,CAACrD,KAAK,GAAG;EAClB2C,GAAG,EAAEO,yBAAyB;EAC9BN,EAAE,EAAEQ;AACR,CAAC;;;;;;;;;;;;;;;;;ACrxBD;AACA;AACA;AAC+C;AACE;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMtO,qBAAqB,GAAGA,CAAC6C,EAAE,EAAE4M,gBAAgB,EAAEC,YAAY,KAAK;EAClE,IAAIC,iBAAiB;EACrB,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAM5K,aAAa,GAAGyK,gBAAgB,CAAC,CAAC;IACxC;IACA;AACR;AACA;AACA;IACQzK,aAAa,KAAKzB,SAAS;IACvB;AACZ;AACA;AACA;AACA;IACYV,EAAE,CAACiB,KAAK,KAAKP,SAAS,IACtBmM,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE;MACzB,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAMjE,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAImE,uBAAuB,CAAC,CAAC,EAAE;MAC3B;AACZ;AACA;AACA;AACA;AACA;MACYJ,uDAAG,CAAC,MAAM;QACNK,aAAa,CAAC,CAAC;MACnB,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMA,aAAa,GAAGA,CAAA,KAAM;IACxB,MAAM7K,aAAa,GAAGyK,gBAAgB,CAAC,CAAC;IACxC,IAAIzK,aAAa,KAAKzB,SAAS,EAAE;MAC7B;IACJ;IACA,IAAI,CAACqM,uBAAuB,CAAC,CAAC,EAAE;MAC5B5K,aAAa,CAACkG,KAAK,CAAC4E,cAAc,CAAC,OAAO,CAAC;MAC3C;IACJ;IACA,MAAMC,KAAK,GAAGL,YAAY,CAAC,CAAC,CAACM,WAAW;IACxC;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQD,KAAK,KAAK,CAAC,IACP/K,aAAa,CAACiL,YAAY,KAAK,IAAI,IACnCV,iDAAG,KAAKhM,SAAS,IACjB,2EAA6B,EAAE;MAC/B;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIoM,iBAAiB,KAAKpM,SAAS,EAAE;QACjC;MACJ;MACA,MAAM2M,EAAE,GAAIP,iBAAiB,GAAG,IAAIQ,oBAAoB,CAAE1N,EAAE,IAAK;QAC7D;AAChB;AACA;AACA;QACgB,IAAIA,EAAE,CAAC,CAAC,CAAC,CAAC2N,iBAAiB,KAAK,CAAC,EAAE;UAC/BP,aAAa,CAAC,CAAC;UACfK,EAAE,CAAC3K,UAAU,CAAC,CAAC;UACfoK,iBAAiB,GAAGpM,SAAS;QACjC;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY;QAAE8M,SAAS,EAAE,IAAI;QAAEC,IAAI,EAAEzN;MAAG,CAAC,CAAE;MAC/BqN,EAAE,CAACK,OAAO,CAACvL,aAAa,CAAC;MACzB;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQA,aAAa,CAACkG,KAAK,CAACsF,WAAW,CAAC,OAAO,EAAG,GAAET,KAAK,GAAG,IAAK,IAAG,CAAC;EACjE,CAAC;EACD,MAAMvK,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAImK,iBAAiB,EAAE;MACnBA,iBAAiB,CAACpK,UAAU,CAAC,CAAC;MAC9BoK,iBAAiB,GAAGpM,SAAS;IACjC;EACJ,CAAC;EACD,OAAO;IACHkI,mBAAmB;IACnBjG;EACJ,CAAC;AACL,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-select_3.entry.js", "./node_modules/@ionic/core/dist/esm/notch-controller-55b09e11.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement, j as forceUpdate } from './index-c71c5417.js';\nimport { c as createNotchController } from './notch-controller-55b09e11.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-a96ff2ea.js';\nimport { h as inheritAttributes, f as focusVisibleElement, d as renderHiddenInput } from './helpers-da915de8.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, s as safeCall } from './overlays-0d212972.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { w as watchForOptions } from './watch-options-c2911ace.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-b9c0d1da.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-7f93f261.js';\nimport './framework-delegate-63d1a679.js';\nimport './gesture-controller-314a54f6.js';\nimport './index-9b0d46f4.js';\n\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host{--highlight-height:0px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonSelectIosStyle0 = selectIosCss;\n\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.ion-focused){--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}:host(.select-fill-solid) .select-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.ion-focused){--border-width:var(--highlight-height);--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-start{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius)}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-end{border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.ion-focused) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.ion-focused) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.select-highlight{inset-inline-start:0}:host(.select-expanded) .select-highlight,:host(.ion-focused) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}:host(.in-item) .select-highlight{inset-inline-start:0}:host(.select-expanded:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.ion-focused) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonSelectMdStyle0 = selectMdCss;\n\nconst Select = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inputId = `ion-sel-${selectIds++}`;\n        this.inheritedAttributes = {};\n        this.onClick = (ev) => {\n            const target = ev.target;\n            const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n            if (target === this.el || closestSlot === null) {\n                this.setFocus();\n                this.open(ev);\n            }\n            else {\n                /**\n                 * Prevent clicks to the start/end slots from opening the select.\n                 * We ensure the target isn't this element in case the select is slotted\n                 * in, for example, an item. This would prevent the select from ever\n                 * being opened since the element itself has slot=\"start\"/\"end\".\n                 *\n                 * Clicking a slotted element also causes a click\n                 * on the <label> element (since it wraps the slots).\n                 * Clicking <label> dispatches another click event on\n                 * the native form control that then bubbles up to this\n                 * listener. This additional event targets the host\n                 * element, so the select overlay is opened.\n                 *\n                 * When the slotted elements are clicked (and therefore\n                 * the ancestor <label> element) we want to prevent the label\n                 * from dispatching another click event.\n                 *\n                 * Do not call stopPropagation() because this will cause\n                 * click handlers on the slotted elements to never fire in React.\n                 * When developers do onClick in React a native \"click\" listener\n                 * is added on the root element, not the slotted element. When that\n                 * native click listener fires, React then dispatches the synthetic\n                 * click event on the slotted element. However, if stopPropagation\n                 * is called then the native click event will never bubble up\n                 * to the root element.\n                 */\n                ev.preventDefault();\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.isExpanded = false;\n        this.cancelText = 'Cancel';\n        this.color = undefined;\n        this.compareWith = undefined;\n        this.disabled = false;\n        this.fill = undefined;\n        this.interface = 'alert';\n        this.interfaceOptions = {};\n        this.justify = 'space-between';\n        this.label = undefined;\n        this.labelPlacement = 'start';\n        this.multiple = false;\n        this.name = this.inputId;\n        this.okText = 'OK';\n        this.placeholder = undefined;\n        this.selectedText = undefined;\n        this.toggleIcon = undefined;\n        this.expandedIcon = undefined;\n        this.shape = undefined;\n        this.value = undefined;\n    }\n    styleChanged() {\n        this.emitStyle();\n    }\n    setValue(value) {\n        this.value = value;\n        this.ionChange.emit({ value });\n    }\n    async connectedCallback() {\n        const { el } = this;\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.updateOverlayOptions();\n        this.emitStyle();\n        this.mutationO = watchForOptions(this.el, 'ion-select-option', async () => {\n            this.updateOverlayOptions();\n            /**\n             * We need to re-render the component\n             * because one of the new ion-select-option\n             * elements may match the value. In this case,\n             * the rendered selected text should be updated.\n             */\n            forceUpdate(this);\n        });\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    componentDidLoad() {\n        /**\n         * If any of the conditions that trigger the styleChanged callback\n         * are met on component load, it is possible the event emitted\n         * prior to a parent web component registering an event listener.\n         *\n         * To ensure the parent web component receives the event, we\n         * emit the style event again after the component has loaded.\n         *\n         * This is often seen in Angular with the `dist` output target.\n         */\n        this.emitStyle();\n    }\n    disconnectedCallback() {\n        if (this.mutationO) {\n            this.mutationO.disconnect();\n            this.mutationO = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    /**\n     * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n     * depending on the `interface` property on the `ion-select`.\n     *\n     * @param event The user interface event that called the open.\n     */\n    async open(event) {\n        if (this.disabled || this.isExpanded) {\n            return undefined;\n        }\n        this.isExpanded = true;\n        const overlay = (this.overlay = await this.createOverlay(event));\n        overlay.onDidDismiss().then(() => {\n            this.overlay = undefined;\n            this.isExpanded = false;\n            this.ionDismiss.emit();\n            this.setFocus();\n        });\n        await overlay.present();\n        // focus selected option for popovers\n        if (this.interface === 'popover') {\n            const indexOfSelected = this.childOpts.map((o) => o.value).indexOf(this.value);\n            if (indexOfSelected > -1) {\n                const selectedItem = overlay.querySelector(`.select-interface-option:nth-child(${indexOfSelected + 1})`);\n                if (selectedItem) {\n                    focusVisibleElement(selectedItem);\n                    /**\n                     * Browsers such as Firefox do not\n                     * correctly delegate focus when manually\n                     * focusing an element with delegatesFocus.\n                     * We work around this by manually focusing\n                     * the interactive element.\n                     * ion-radio and ion-checkbox are the only\n                     * elements that ion-select-popover uses, so\n                     * we only need to worry about those two components\n                     * when focusing.\n                     */\n                    const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n                    if (interactiveEl) {\n                        interactiveEl.focus();\n                    }\n                }\n            }\n            else {\n                /**\n                 * If no value is set then focus the first enabled option.\n                 */\n                const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n                if (firstEnabledOption) {\n                    focusVisibleElement(firstEnabledOption.closest('ion-item'));\n                    /**\n                     * Focus the option for the same reason as we do above.\n                     */\n                    firstEnabledOption.focus();\n                }\n            }\n        }\n        return overlay;\n    }\n    createOverlay(ev) {\n        let selectInterface = this.interface;\n        if (selectInterface === 'action-sheet' && this.multiple) {\n            console.warn(`Select interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'popover' && !ev) {\n            console.warn(`Select interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'action-sheet') {\n            return this.openActionSheet();\n        }\n        if (selectInterface === 'popover') {\n            return this.openPopover(ev);\n        }\n        return this.openAlert();\n    }\n    updateOverlayOptions() {\n        const overlay = this.overlay;\n        if (!overlay) {\n            return;\n        }\n        const childOpts = this.childOpts;\n        const value = this.value;\n        switch (this.interface) {\n            case 'action-sheet':\n                overlay.buttons = this.createActionSheetButtons(childOpts, value);\n                break;\n            case 'popover':\n                const popover = overlay.querySelector('ion-select-popover');\n                if (popover) {\n                    popover.options = this.createPopoverOptions(childOpts, value);\n                }\n                break;\n            case 'alert':\n                const inputType = this.multiple ? 'checkbox' : 'radio';\n                overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n                break;\n        }\n    }\n    createActionSheetButtons(data, selectValue) {\n        const actionSheetButtons = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n                text: option.textContent,\n                cssClass: optClass,\n                handler: () => {\n                    this.setValue(value);\n                },\n            };\n        });\n        // Add \"cancel\" button\n        actionSheetButtons.push({\n            text: this.cancelText,\n            role: 'cancel',\n            handler: () => {\n                this.ionCancel.emit();\n            },\n        });\n        return actionSheetButtons;\n    }\n    createAlertInputs(data, inputType, selectValue) {\n        const alertInputs = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                type: inputType,\n                cssClass: optClass,\n                label: option.textContent || '',\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n            };\n        });\n        return alertInputs;\n    }\n    createPopoverOptions(data, selectValue) {\n        const popoverOptions = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                text: option.textContent || '',\n                cssClass: optClass,\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n                handler: (selected) => {\n                    this.setValue(selected);\n                    if (!this.multiple) {\n                        this.close();\n                    }\n                },\n            };\n        });\n        return popoverOptions;\n    }\n    async openPopover(ev) {\n        const { fill, labelPlacement } = this;\n        const interfaceOptions = this.interfaceOptions;\n        const mode = getIonMode(this);\n        const showBackdrop = mode === 'md' ? false : true;\n        const multiple = this.multiple;\n        const value = this.value;\n        let event = ev;\n        let size = 'auto';\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        /**\n         * The popover should take up the full width\n         * when using a fill in MD mode or if the\n         * label is floating/stacked.\n         */\n        if (hasFloatingOrStackedLabel || (mode === 'md' && fill !== undefined)) {\n            size = 'cover';\n            /**\n             * Otherwise the popover\n             * should be positioned relative\n             * to the native element.\n             */\n        }\n        else {\n            event = Object.assign(Object.assign({}, ev), { detail: {\n                    ionShadowTarget: this.nativeWrapperEl,\n                } });\n        }\n        const popoverOpts = Object.assign(Object.assign({ mode,\n            event, alignment: 'center', size,\n            showBackdrop }, interfaceOptions), { component: 'ion-select-popover', cssClass: ['select-popover', interfaceOptions.cssClass], componentProps: {\n                header: interfaceOptions.header,\n                subHeader: interfaceOptions.subHeader,\n                message: interfaceOptions.message,\n                multiple,\n                value,\n                options: this.createPopoverOptions(this.childOpts, value),\n            } });\n        return popoverController.create(popoverOpts);\n    }\n    async openActionSheet() {\n        const mode = getIonMode(this);\n        const interfaceOptions = this.interfaceOptions;\n        const actionSheetOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { buttons: this.createActionSheetButtons(this.childOpts, this.value), cssClass: ['select-action-sheet', interfaceOptions.cssClass] });\n        return actionSheetController.create(actionSheetOpts);\n    }\n    async openAlert() {\n        const interfaceOptions = this.interfaceOptions;\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        const mode = getIonMode(this);\n        const alertOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { header: interfaceOptions.header ? interfaceOptions.header : this.labelText, inputs: this.createAlertInputs(this.childOpts, inputType, this.value), buttons: [\n                {\n                    text: this.cancelText,\n                    role: 'cancel',\n                    handler: () => {\n                        this.ionCancel.emit();\n                    },\n                },\n                {\n                    text: this.okText,\n                    handler: (selectedValues) => {\n                        this.setValue(selectedValues);\n                    },\n                },\n            ], cssClass: [\n                'select-alert',\n                interfaceOptions.cssClass,\n                this.multiple ? 'multiple-select-alert' : 'single-select-alert',\n            ] });\n        return alertController.create(alertOpts);\n    }\n    /**\n     * Close the select interface.\n     */\n    close() {\n        if (!this.overlay) {\n            return Promise.resolve(false);\n        }\n        return this.overlay.dismiss();\n    }\n    hasValue() {\n        return this.getText() !== '';\n    }\n    get childOpts() {\n        return Array.from(this.el.querySelectorAll('ion-select-option'));\n    }\n    /**\n     * Returns any plaintext associated with\n     * the label (either prop or slot).\n     * Note: This will not return any custom\n     * HTML. Use the `hasLabel` getter if you\n     * want to know if any slotted label content\n     * was passed.\n     */\n    get labelText() {\n        const { label } = this;\n        if (label !== undefined) {\n            return label;\n        }\n        const { labelSlot } = this;\n        if (labelSlot !== null) {\n            return labelSlot.textContent;\n        }\n        return;\n    }\n    getText() {\n        const selectedText = this.selectedText;\n        if (selectedText != null && selectedText !== '') {\n            return selectedText;\n        }\n        return generateText(this.childOpts, this.value, this.compareWith);\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    emitStyle() {\n        const { disabled } = this;\n        const style = {\n            'interactive-disabled': disabled,\n        };\n        this.ionStyle.emit(style);\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            }, part: \"label\" }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container\n     * when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the select and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"select-outline-container\" }, h(\"div\", { class: \"select-outline-start\" }), h(\"div\", { class: {\n                        'select-outline-notch': true,\n                        'select-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"select-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    /**\n     * Renders either the placeholder\n     * or the selected values based on\n     * the state of the select.\n     */\n    renderSelectText() {\n        const { placeholder } = this;\n        const displayValue = this.getText();\n        let addPlaceholderClass = false;\n        let selectText = displayValue;\n        if (selectText === '' && placeholder !== undefined) {\n            selectText = placeholder;\n            addPlaceholderClass = true;\n        }\n        const selectTextClasses = {\n            'select-text': true,\n            'select-placeholder': addPlaceholderClass,\n        };\n        const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n        return (h(\"div\", { \"aria-hidden\": \"true\", class: selectTextClasses, part: textPart }, selectText));\n    }\n    /**\n     * Renders the chevron icon\n     * next to the select text.\n     */\n    renderSelectIcon() {\n        const mode = getIonMode(this);\n        const { isExpanded, toggleIcon, expandedIcon } = this;\n        let icon;\n        if (isExpanded && expandedIcon !== undefined) {\n            icon = expandedIcon;\n        }\n        else {\n            const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n            icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n        }\n        return h(\"ion-icon\", { class: \"select-icon\", part: \"icon\", \"aria-hidden\": \"true\", icon: icon });\n    }\n    get ariaLabel() {\n        var _a;\n        const { placeholder, inheritedAttributes } = this;\n        const displayValue = this.getText();\n        // The aria label should be preferred over visible text if both are specified\n        const definedLabel = (_a = inheritedAttributes['aria-label']) !== null && _a !== void 0 ? _a : this.labelText;\n        /**\n         * If developer has specified a placeholder\n         * and there is nothing selected, the selectText\n         * should have the placeholder value.\n         */\n        let renderedLabel = displayValue;\n        if (renderedLabel === '' && placeholder !== undefined) {\n            renderedLabel = placeholder;\n        }\n        /**\n         * If there is a developer-defined label,\n         * then we need to concatenate the developer label\n         * string with the current current value.\n         * The label for the control should be read\n         * before the values of the control.\n         */\n        if (definedLabel !== undefined) {\n            renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n        }\n        return renderedLabel;\n    }\n    renderListbox() {\n        const { disabled, inputId, isExpanded } = this;\n        return (h(\"button\", { disabled: disabled, id: inputId, \"aria-label\": this.ariaLabel, \"aria-haspopup\": \"dialog\", \"aria-expanded\": `${isExpanded}`, onFocus: this.onFocus, onBlur: this.onBlur, ref: (focusEl) => (this.focusEl = focusEl) }));\n    }\n    render() {\n        const { disabled, el, isExpanded, expandedIcon, labelPlacement, justify, placeholder, fill, shape, name, value } = this;\n        const mode = getIonMode(this);\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        const justifyEnabled = !hasFloatingOrStackedLabel;\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        renderHiddenInput(true, el, name, parseValue(value), disabled);\n        /**\n         * If the label is stacked, it should always sit above the select.\n         * For floating labels, the label should move above the select if\n         * the select has a value, is open, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the select is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots));\n        return (h(Host, { key: 'e14586e887c0b73457b9f1e8f0585dfc7d590051', onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': inItem,\n                'in-item-color': hostContext('ion-item.ion-color', el),\n                'select-disabled': disabled,\n                'select-expanded': isExpanded,\n                'has-expanded-icon': expandedIcon !== undefined,\n                'has-value': hasValue,\n                'label-floating': labelShouldFloat,\n                'has-placeholder': placeholder !== undefined,\n                'ion-focusable': true,\n                [`select-${rtl}`]: true,\n                [`select-fill-${fill}`]: fill !== undefined,\n                [`select-justify-${justify}`]: justifyEnabled,\n                [`select-shape-${shape}`]: shape !== undefined,\n                [`select-label-placement-${labelPlacement}`]: true,\n            }) }, h(\"label\", { key: 'b44929542c809f2dca25afc588701762a083edd1', class: \"select-wrapper\", id: \"select-label\" }, this.renderLabelContainer(), h(\"div\", { key: '195e3fad9cea0363ff00bf257ec2975fe5aa6887', class: \"select-wrapper-inner\" }, h(\"slot\", { key: '9da7161a69b92d6fd124a478348d3fa420092f26', name: \"start\" }), h(\"div\", { key: '49c589c164d167de0ba5741a0c80fba58ea0bec6', class: \"native-wrapper\", ref: (el) => (this.nativeWrapperEl = el), part: \"container\" }, this.renderSelectText(), this.renderListbox()), h(\"slot\", { key: 'de1536d910fceb8e2dbdda13abcf88ee969888b8', name: \"end\" }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", { key: 'e821b7b32cd1996eaa6d471df9b531ad0d491098', class: \"select-highlight\" }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"styleChanged\"],\n        \"isExpanded\": [\"styleChanged\"],\n        \"placeholder\": [\"styleChanged\"],\n        \"value\": [\"styleChanged\"]\n    }; }\n};\nconst getOptionValue = (el) => {\n    const value = el.value;\n    return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = (value) => {\n    if (value == null) {\n        return undefined;\n    }\n    if (Array.isArray(value)) {\n        return value.join(',');\n    }\n    return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n    if (value === undefined) {\n        return '';\n    }\n    if (Array.isArray(value)) {\n        return value\n            .map((v) => textForValue(opts, v, compareWith))\n            .filter((opt) => opt !== null)\n            .join(', ');\n    }\n    else {\n        return textForValue(opts, value, compareWith) || '';\n    }\n};\nconst textForValue = (opts, value, compareWith) => {\n    const selectOpt = opts.find((opt) => {\n        return compareOptions(value, getOptionValue(opt), compareWith);\n    });\n    return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n    ios: IonSelectIosStyle0,\n    md: IonSelectMdStyle0\n};\n\nconst selectOptionCss = \":host{display:none}\";\nconst IonSelectOptionStyle0 = selectOptionCss;\n\nconst SelectOption = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inputId = `ion-selopt-${selectOptionIds++}`;\n        this.disabled = false;\n        this.value = undefined;\n    }\n    render() {\n        return h(Host, { key: 'ba5a9c695c53fe0802af11a49f4305a9b8f71773', role: \"option\", id: this.inputId, class: getIonMode(this) });\n    }\n    get el() { return getElement(this); }\n};\nlet selectOptionIds = 0;\nSelectOption.style = IonSelectOptionStyle0;\n\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\nconst IonSelectPopoverIosStyle0 = selectPopoverIosCss;\n\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){display:none}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\nconst IonSelectPopoverMdStyle0 = selectPopoverMdCss;\n\nconst SelectPopover = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.header = undefined;\n        this.subHeader = undefined;\n        this.message = undefined;\n        this.multiple = undefined;\n        this.options = [];\n    }\n    findOptionFromEvent(ev) {\n        const { options } = this;\n        return options.find((o) => o.value === ev.target.value);\n    }\n    /**\n     * When an option is selected we need to get the value(s)\n     * of the selected option(s) and return it in the option\n     * handler\n     */\n    callOptionHandler(ev) {\n        const option = this.findOptionFromEvent(ev);\n        const values = this.getValues(ev);\n        if (option === null || option === void 0 ? void 0 : option.handler) {\n            safeCall(option.handler, values);\n        }\n    }\n    /**\n     * Dismisses the host popover that the `ion-select-popover`\n     * is rendered within.\n     */\n    dismissParentPopover() {\n        const popover = this.el.closest('ion-popover');\n        if (popover) {\n            popover.dismiss();\n        }\n    }\n    setChecked(ev) {\n        const { multiple } = this;\n        const option = this.findOptionFromEvent(ev);\n        // this is a popover with checkboxes (multiple value select)\n        // we need to set the checked value for this option\n        if (multiple && option) {\n            option.checked = ev.detail.checked;\n        }\n    }\n    getValues(ev) {\n        const { multiple, options } = this;\n        if (multiple) {\n            // this is a popover with checkboxes (multiple value select)\n            // return an array of all the checked values\n            return options.filter((o) => o.checked).map((o) => o.value);\n        }\n        // this is a popover with radio buttons (single value select)\n        // return the value that was clicked, otherwise undefined\n        const option = this.findOptionFromEvent(ev);\n        return option ? option.value : undefined;\n    }\n    renderOptions(options) {\n        const { multiple } = this;\n        switch (multiple) {\n            case true:\n                return this.renderCheckboxOptions(options);\n            default:\n                return this.renderRadioOptions(options);\n        }\n    }\n    renderCheckboxOptions(options) {\n        return options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-checkbox-checked': option.checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-checkbox\", { value: option.value, disabled: option.disabled, checked: option.checked, justify: \"start\", labelPlacement: \"end\", onIonChange: (ev) => {\n                this.setChecked(ev);\n                this.callOptionHandler(ev);\n                // TODO FW-4784\n                forceUpdate(this);\n            } }, option.text))));\n    }\n    renderRadioOptions(options) {\n        const checked = options.filter((o) => o.checked).map((o) => o.value)[0];\n        return (h(\"ion-radio-group\", { value: checked, onIonChange: (ev) => this.callOptionHandler(ev) }, options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-radio-checked': option.value === checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-radio\", { value: option.value, disabled: option.disabled, onClick: () => this.dismissParentPopover(), onKeyUp: (ev) => {\n                if (ev.key === ' ') {\n                    /**\n                     * Selecting a radio option with keyboard navigation,\n                     * either through the Enter or Space keys, should\n                     * dismiss the popover.\n                     */\n                    this.dismissParentPopover();\n                }\n            } }, option.text))))));\n    }\n    render() {\n        const { header, message, options, subHeader } = this;\n        const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n        return (h(Host, { key: '302553a2eec4d1442751b8af28b7c9bd3487fd5d', class: getIonMode(this) }, h(\"ion-list\", { key: '39ae8579e6fe3bae2c7504147268ad5c82fd27e6' }, header !== undefined && h(\"ion-list-header\", { key: 'e0e6686380d188f46c593e1bb25287dcf08c75c2' }, header), hasSubHeaderOrMessage && (h(\"ion-item\", { key: '8a2d8652db269593c0ba7d767277e12c2b06144d' }, h(\"ion-label\", { key: 'a30cc0ecf95d5bdd6421ee1683922c1b853e98ea', class: \"ion-text-wrap\" }, subHeader !== undefined && h(\"h3\", { key: 'c298459ca450123808a08d65660825b2c26d00e5' }, subHeader), message !== undefined && h(\"p\", { key: 'ed895fbaec020e809021138401341b6fd7675035' }, message)))), this.renderOptions(options))));\n    }\n    get el() { return getElement(this); }\n};\nSelectPopover.style = {\n    ios: IonSelectPopoverIosStyle0,\n    md: IonSelectPopoverMdStyle0\n};\n\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { r as raf } from './helpers-da915de8.js';\n\n/**\n * A utility to calculate the size of an outline notch\n * width relative to the content passed. This is used in\n * components such as `ion-select` with `fill=\"outline\"`\n * where we need to pass slotted HTML content. This is not\n * needed when rendering plaintext content because we can\n * render the plaintext again hidden with `opacity: 0` inside\n * of the notch. As a result we can rely on the intrinsic size\n * of the element to correctly compute the notch width. We\n * cannot do this with slotted content because we cannot project\n * it into 2 places at once.\n *\n * @internal\n * @param el: The host element\n * @param getNotchSpacerEl: A function that returns a reference to the notch spacer element inside of the component template.\n * @param getLabelSlot: A function that returns a reference to the slotted content.\n */\nconst createNotchController = (el, getNotchSpacerEl, getLabelSlot) => {\n    let notchVisibilityIO;\n    const needsExplicitNotchWidth = () => {\n        const notchSpacerEl = getNotchSpacerEl();\n        if (\n        /**\n         * If the notch is not being used\n         * then we do not need to set the notch width.\n         */\n        notchSpacerEl === undefined ||\n            /**\n             * If either the label property is being\n             * used or the label slot is not defined,\n             * then we do not need to estimate the notch width.\n             */\n            el.label !== undefined ||\n            getLabelSlot() === null) {\n            return false;\n        }\n        return true;\n    };\n    const calculateNotchWidth = () => {\n        if (needsExplicitNotchWidth()) {\n            /**\n             * Run this the frame after\n             * the browser has re-painted the host element.\n             * Otherwise, the label element may have a width\n             * of 0 and the IntersectionObserver will be used.\n             */\n            raf(() => {\n                setNotchWidth();\n            });\n        }\n    };\n    /**\n     * When using a label prop we can render\n     * the label value inside of the notch and\n     * let the browser calculate the size of the notch.\n     * However, we cannot render the label slot in multiple\n     * places so we need to manually calculate the notch dimension\n     * based on the size of the slotted content.\n     *\n     * This function should only be used to set the notch width\n     * on slotted label content. The notch width for label prop\n     * content is automatically calculated based on the\n     * intrinsic size of the label text.\n     */\n    const setNotchWidth = () => {\n        const notchSpacerEl = getNotchSpacerEl();\n        if (notchSpacerEl === undefined) {\n            return;\n        }\n        if (!needsExplicitNotchWidth()) {\n            notchSpacerEl.style.removeProperty('width');\n            return;\n        }\n        const width = getLabelSlot().scrollWidth;\n        if (\n        /**\n         * If the computed width of the label is 0\n         * and notchSpacerEl's offsetParent is null\n         * then that means the element is hidden.\n         * As a result, we need to wait for the element\n         * to become visible before setting the notch width.\n         *\n         * We do not check el.offsetParent because\n         * that can be null if the host element has\n         * position: fixed applied to it.\n         * notchSpacerEl does not have position: fixed.\n         */\n        width === 0 &&\n            notchSpacerEl.offsetParent === null &&\n            win !== undefined &&\n            'IntersectionObserver' in win) {\n            /**\n             * If there is an IO already attached\n             * then that will update the notch\n             * once the element becomes visible.\n             * As a result, there is no need to create\n             * another one.\n             */\n            if (notchVisibilityIO !== undefined) {\n                return;\n            }\n            const io = (notchVisibilityIO = new IntersectionObserver((ev) => {\n                /**\n                 * If the element is visible then we\n                 * can try setting the notch width again.\n                 */\n                if (ev[0].intersectionRatio === 1) {\n                    setNotchWidth();\n                    io.disconnect();\n                    notchVisibilityIO = undefined;\n                }\n            }, \n            /**\n             * Set the root to be the host element\n             * This causes the IO callback\n             * to be fired in WebKit as soon as the element\n             * is visible. If we used the default root value\n             * then WebKit would only fire the IO callback\n             * after any animations (such as a modal transition)\n             * finished, and there would potentially be a flicker.\n             */\n            { threshold: 0.01, root: el }));\n            io.observe(notchSpacerEl);\n            return;\n        }\n        /**\n         * If the element is visible then we can set the notch width.\n         * The notch is only visible when the label is scaled,\n         * which is why we multiply the width by 0.75 as this is\n         * the same amount the label element is scaled by in the host CSS.\n         * (See $form-control-label-stacked-scale in ionic.globals.scss).\n         */\n        notchSpacerEl.style.setProperty('width', `${width * 0.75}px`);\n    };\n    const destroy = () => {\n        if (notchVisibilityIO) {\n            notchVisibilityIO.disconnect();\n            notchVisibilityIO = undefined;\n        }\n    };\n    return {\n        calculateNotchWidth,\n        destroy,\n    };\n};\n\nexport { createNotchController as c };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "j", "forceUpdate", "c", "createNotchController", "isOptionSelected", "compareOptions", "inheritAttributes", "focusVisibleElement", "renderHiddenInput", "popoverController", "b", "actionSheetController", "a", "alertController", "s", "safeCall", "isRTL", "hostContext", "createColorClasses", "g", "getClassMap", "w", "watchForOptions", "chevronExpand", "q", "caretDownSharp", "getIonMode", "selectIosCss", "IonSelectIosStyle0", "selectMdCss", "IonSelectMdStyle0", "Select", "constructor", "hostRef", "ionChange", "ionCancel", "ion<PERSON><PERSON><PERSON>", "ionFocus", "ionBlur", "ionStyle", "inputId", "selectIds", "inheritedAttributes", "onClick", "ev", "target", "closestSlot", "closest", "el", "setFocus", "open", "preventDefault", "onFocus", "emit", "onBlur", "isExpanded", "cancelText", "color", "undefined", "compareWith", "disabled", "fill", "interface", "interfaceOptions", "justify", "label", "labelPlacement", "multiple", "name", "okText", "placeholder", "selectedText", "toggleIcon", "expandedIcon", "shape", "value", "styleChanged", "emitStyle", "setValue", "connectedCallback", "_this", "_asyncToGenerator", "notchController", "notchSpacerEl", "labelSlot", "updateOverlayOptions", "mutationO", "componentWillLoad", "componentDidLoad", "disconnectedCallback", "disconnect", "destroy", "event", "_this2", "overlay", "createOverlay", "onDid<PERSON><PERSON><PERSON>", "then", "present", "indexOfSelected", "childOpts", "map", "o", "indexOf", "selectedItem", "querySelector", "interactiveEl", "focus", "firstEnabledOption", "selectInterface", "console", "warn", "openActionSheet", "openPopover", "openAlert", "buttons", "createActionSheetButtons", "popover", "options", "createPopoverOptions", "inputType", "inputs", "createAlertInputs", "data", "selectValue", "actionSheetButtons", "option", "getOptionValue", "copyClasses", "Array", "from", "classList", "filter", "cls", "join", "optClass", "OPTION_CLASS", "role", "text", "textContent", "cssClass", "handler", "push", "alertInputs", "type", "checked", "popoverOptions", "selected", "close", "_this3", "mode", "showBackdrop", "size", "hasFloatingOrStackedLabel", "Object", "assign", "detail", "ionShadowTarget", "nativeWrapperEl", "popoverOpts", "alignment", "component", "componentProps", "header", "subHeader", "message", "create", "_this4", "actionSheetOpts", "_this5", "alertOpts", "labelText", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "dismiss", "hasValue", "getText", "querySelectorAll", "generateText", "focusEl", "style", "renderLabel", "class", "<PERSON><PERSON><PERSON><PERSON>", "part", "componentDidRender", "_a", "calculateNotchWidth", "renderLabelContainer", "hasOutlineFill", "ref", "renderSelectText", "displayValue", "addPlaceholderClass", "selectText", "selectTextClasses", "textPart", "renderSelectIcon", "icon", "defaultIcon", "aria<PERSON><PERSON><PERSON>", "defined<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "renderListbox", "id", "render", "justifyEnabled", "rtl", "inItem", "should<PERSON>ender<PERSON>ighlight", "hasStartEndSlots", "parseValue", "labelShouldFloat", "key", "watchers", "isArray", "toString", "opts", "v", "textForValue", "opt", "selectOpt", "find", "ios", "md", "selectOptionCss", "IonSelectOptionStyle0", "SelectOption", "selectOptionIds", "selectPopoverIosCss", "IonSelectPopoverIosStyle0", "selectPopoverMdCss", "IonSelectPopoverMdStyle0", "SelectPopover", "findOptionFromEvent", "callOptionHandler", "values", "getV<PERSON>ues", "dismissParentPopover", "setChecked", "renderOptions", "renderCheckboxOptions", "renderRadioOptions", "onIonChange", "onKeyUp", "hasSubHeaderOrMessage", "ion_select", "ion_select_option", "ion_select_popover", "win", "raf", "getNotchSpacerEl", "getLabelSlot", "notchVisibilityIO", "needsExplicitNotchWidth", "set<PERSON><PERSON>chWidth", "removeProperty", "width", "scrollWidth", "offsetParent", "io", "IntersectionObserver", "intersectionRatio", "threshold", "root", "observe", "setProperty"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}