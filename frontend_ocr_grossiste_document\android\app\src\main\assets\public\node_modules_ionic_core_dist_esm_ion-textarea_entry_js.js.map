{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-textarea_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC+I;AACnE;AAC0D;AACnC;AACnB;AACnB;AAChC;AACA;AAE7B,MAAM2B,cAAc,GAAG,s5cAAs5c;AAC76c,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,aAAa,GAAG,irwBAAirwB;AACvswB,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjBhC,qDAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAG/B,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACgC,QAAQ,GAAGhC,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACiC,OAAO,GAAGjC,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACkC,QAAQ,GAAGlC,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACmC,OAAO,GAAI,gBAAeC,WAAW,EAAG,EAAC;IAC9C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,KAAK,GAAGD,EAAE,CAACE,MAAM;MACvB,IAAID,KAAK,EAAE;QACP,IAAI,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,IAAI,EAAE;MAClC;MACA,IAAI,CAACC,eAAe,CAACJ,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACK,QAAQ,GAAIL,EAAE,IAAK;MACpB,IAAI,CAACM,eAAe,CAACN,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACO,OAAO,GAAIP,EAAE,IAAK;MACnB,IAAI,CAACQ,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACN,KAAK;MAC9B,IAAI,CAACT,QAAQ,CAACgB,IAAI,CAACV,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,CAACW,MAAM,GAAIX,EAAE,IAAK;MAClB,IAAI,CAACQ,QAAQ,GAAG,KAAK;MACrB,IAAI,IAAI,CAACC,YAAY,KAAK,IAAI,CAACN,KAAK,EAAE;QAClC;AAChB;AACA;AACA;QACgB,IAAI,CAACG,eAAe,CAACN,EAAE,CAAC;MAC5B;MACA,IAAI,CAACH,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACJ,OAAO,CAACiB,IAAI,CAACV,EAAE,CAAC;IACzB,CAAC;IACD,IAAI,CAACY,SAAS,GAAIZ,EAAE,IAAK;MACrB,IAAI,CAACa,gBAAgB,CAACb,EAAE,CAAC;IAC7B,CAAC;IACD,IAAI,CAACQ,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACM,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,cAAc,GAAG,MAAM;IAC5B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,QAAQ,GAAGJ,SAAS;IACzB,IAAI,CAACK,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGN,SAAS;IACrB,IAAI,CAACO,SAAS,GAAGP,SAAS;IAC1B,IAAI,CAACQ,YAAY,GAAGR,SAAS;IAC7B,IAAI,CAACS,SAAS,GAAGT,SAAS;IAC1B,IAAI,CAACU,SAAS,GAAGV,SAAS;IAC1B,IAAI,CAACW,IAAI,GAAG,IAAI,CAAC/B,OAAO;IACxB,IAAI,CAACgC,WAAW,GAAGZ,SAAS;IAC5B,IAAI,CAACa,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,IAAI,GAAGhB,SAAS;IACrB,IAAI,CAACiB,IAAI,GAAGjB,SAAS;IACrB,IAAI,CAACkB,IAAI,GAAGlB,SAAS;IACrB,IAAI,CAACmB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC/B,KAAK,GAAG,EAAE;IACf,IAAI,CAACgC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,gBAAgB,GAAGrB,SAAS;IACjC,IAAI,CAACsB,SAAS,GAAGtB,SAAS;IAC1B,IAAI,CAACuB,UAAU,GAAGvB,SAAS;IAC3B,IAAI,CAACwB,KAAK,GAAGxB,SAAS;IACtB,IAAI,CAACyB,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,KAAK,GAAG1B,SAAS;EAC1B;EACA2B,eAAeA,CAAA,EAAG;IACd,MAAM;MAAElD,QAAQ;MAAE2B,QAAQ;MAAEwB;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACnD,QAAQ,GAAG2B,QAAQ,KAAKJ,SAAS,GAAG4B,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGnD,QAAQ,GAAGnB,uDAAa,CAACmB,QAAQ,EAAE2B,QAAQ,CAAC;EACvK;EACA;AACJ;AACA;EACIyB,YAAYA,CAAA,EAAG;IACX,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAM1C,KAAK,GAAG,IAAI,CAAC2C,QAAQ,CAAC,CAAC;IAC7B,IAAID,WAAW,IAAIA,WAAW,CAAC1C,KAAK,KAAKA,KAAK,EAAE;MAC5C0C,WAAW,CAAC1C,KAAK,GAAGA,KAAK;IAC7B;IACA,IAAI,CAAC4C,WAAW,CAAC,CAAC;EACtB;EACAC,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEC;IAAG,CAAC,GAAG,IAAI;IACnB,IAAI,CAACC,sBAAsB,GAAGzE,2DAA4B,CAACwE,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,MAAMhF,qDAAW,CAAC,IAAI,CAAC,CAAC;IAClH,IAAI,CAACkF,eAAe,GAAGhF,gEAAqB,CAAC8E,EAAE,EAAE,MAAM,IAAI,CAACG,aAAa,EAAE,MAAM,IAAI,CAACC,SAAS,CAAC;IAChG,IAAI,CAACX,eAAe,CAAC,CAAC;IACtB;MACIY,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,iBAAiB,EAAE;QACtDC,MAAM,EAAER;MACZ,CAAC,CAAC,CAAC;IACP;EACJ;EACAS,oBAAoBA,CAAA,EAAG;IACnB;MACIJ,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;QACxDC,MAAM,EAAE,IAAI,CAACR;MACjB,CAAC,CAAC,CAAC;IACP;IACA,IAAI,IAAI,CAACC,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAACS,OAAO,CAAC,CAAC;MACrC,IAAI,CAACT,sBAAsB,GAAGnC,SAAS;IAC3C;IACA,IAAI,IAAI,CAACoC,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACQ,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACR,eAAe,GAAGpC,SAAS;IACpC;EACJ;EACA6C,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC9D,mBAAmB,GAAG+D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExF,uDAAqB,CAAC,IAAI,CAAC2E,EAAE,CAAC,CAAC,EAAE1E,uDAAiB,CAAC,IAAI,CAAC0E,EAAE,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;EACpK;EACAc,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACpB,gBAAgB,GAAG,IAAI,CAACnD,QAAQ;IACrC,IAAI,CAACuD,WAAW,CAAC,CAAC;EACtB;EACAiB,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACd,eAAe,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;AACA;EACUC,QAAQA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACb,IAAID,KAAI,CAACvB,WAAW,EAAE;QAClBuB,KAAI,CAACvB,WAAW,CAACyB,KAAK,CAAC,CAAC;MAC5B;IAAC;EACL;EACA;AACJ;AACA;EACUC,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAH,6OAAA;MACpB;AACR;AACA;AACA;MACQ,IAAI,CAACG,MAAI,CAAC3B,WAAW,EAAE;QACnB,MAAM,IAAI4B,OAAO,CAAEC,OAAO,IAAKlG,uDAAgB,CAACgG,MAAI,CAACvB,EAAE,EAAEyB,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACF,MAAI,CAAC3B,WAAW,CAAC;IAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIvC,eAAeA,CAACqE,KAAK,EAAE;IACnB,MAAM;MAAExE;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMyE,QAAQ,GAAGzE,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAAC0E,QAAQ,CAAC,CAAC;IACzD;IACA,IAAI,CAACpE,YAAY,GAAGmE,QAAQ;IAC5B,IAAI,CAACrF,SAAS,CAACmB,IAAI,CAAC;MAAEP,KAAK,EAAEyE,QAAQ;MAAED;IAAM,CAAC,CAAC;EACnD;EACA;AACJ;AACA;EACIvE,eAAeA,CAACuE,KAAK,EAAE;IACnB,MAAM;MAAExE;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAACX,QAAQ,CAACkB,IAAI,CAAC;MAAEP,KAAK;MAAEwE;IAAM,CAAC,CAAC;EACxC;EACA5B,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACF,WAAW,IAAI,IAAI,CAACX,QAAQ,EAAE;MACnCxE,qDAAS,CAAC,MAAM;QACZ,IAAIuG,EAAE;QACN,IAAI,IAAI,CAACa,eAAe,EAAE;UACtB;UACA;UACA,IAAI,CAACA,eAAe,CAACC,OAAO,CAACC,eAAe,GAAG,CAACf,EAAE,GAAG,IAAI,CAAC9D,KAAK,MAAM,IAAI,IAAI8D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;QACxG;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;EACIpD,gBAAgBA,CAACb,EAAE,EAAE;IACjB,IAAI,CAAC,IAAI,CAACkB,WAAW,EAAE;MACnB;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM+D,YAAY,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC;IAC/D,MAAMC,iBAAiB,GAAGD,YAAY,CAACE,QAAQ,CAACnF,EAAE,CAACoF,GAAG,CAAC;IACvD;AACR;AACA;AACA;IACQ,IAAI,CAAC,IAAI,CAACvF,sBAAsB,IAAI,IAAI,CAACwF,QAAQ,CAAC,CAAC,IAAI,CAACH,iBAAiB,EAAE;MACvE,IAAI,CAAC/E,KAAK,GAAG,EAAE;MACf,IAAI,CAACC,eAAe,CAACJ,EAAE,CAAC;IAC5B;IACA;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACkF,iBAAiB,EAAE;MACpB,IAAI,CAACrF,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACAwF,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACvC,QAAQ,CAAC,CAAC,KAAK,EAAE;EACjC;EACAA,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC3C,KAAK,IAAI,EAAE;EAC3B;EACAmF,WAAWA,CAAA,EAAG;IACV,MAAM;MAAE/C;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQ5E,qDAAC,CAAC,KAAK,EAAE;MAAE4H,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACC;MACvC;IAAE,CAAC,EAAEjD,KAAK,KAAKxB,SAAS,GAAGpD,qDAAC,CAAC,MAAM,EAAE;MAAE+D,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAG/D,qDAAC,CAAC,KAAK,EAAE;MAAE4H,KAAK,EAAE;IAAa,CAAC,EAAEhD,KAAK,CAAC,CAAC;EAC3G;EACA;AACJ;AACA;AACA;EACI,IAAIc,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACJ,EAAE,CAACwC,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAID,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjD,KAAK,KAAKxB,SAAS,IAAI,IAAI,CAACsC,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;EACIqC,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,IAAI,GAAG5G,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM6G,cAAc,GAAGD,IAAI,KAAK,IAAI,IAAI,IAAI,CAACtE,IAAI,KAAK,SAAS;IAC/D,IAAIuE,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACHjI,qDAAC,CAAC,KAAK,EAAE;QAAE4H,KAAK,EAAE;MAA6B,CAAC,EAAE5H,qDAAC,CAAC,KAAK,EAAE;QAAE4H,KAAK,EAAE;MAAyB,CAAC,CAAC,EAAE5H,qDAAC,CAAC,KAAK,EAAE;QAAE4H,KAAK,EAAE;UAC3G,wBAAwB,EAAE,IAAI;UAC9B,+BAA+B,EAAE,CAAC,IAAI,CAACC;QAC3C;MAAE,CAAC,EAAE7H,qDAAC,CAAC,KAAK,EAAE;QAAE4H,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEM,GAAG,EAAG5C,EAAE,IAAM,IAAI,CAACG,aAAa,GAAGH;MAAI,CAAC,EAAE,IAAI,CAACV,KAAK,CAAC,CAAC,EAAE5E,qDAAC,CAAC,KAAK,EAAE;QAAE4H,KAAK,EAAE;MAAuB,CAAC,CAAC,CAAC,EACtK,IAAI,CAACD,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;EACIQ,cAAcA,CAAA,EAAG;IACb,MAAM;MAAExD,UAAU;MAAED;IAAU,CAAC,GAAG,IAAI;IACtC,OAAO,CAAC1E,qDAAC,CAAC,KAAK,EAAE;MAAE4H,KAAK,EAAE;IAAc,CAAC,EAAEjD,UAAU,CAAC,EAAE3E,qDAAC,CAAC,KAAK,EAAE;MAAE4H,KAAK,EAAE;IAAa,CAAC,EAAElD,SAAS,CAAC,CAAC;EACzG;EACA0D,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAE5D,OAAO;MAAEX,SAAS;MAAEY,gBAAgB;MAAEjC;IAAM,CAAC,GAAG,IAAI;IAC5D,IAAIgC,OAAO,KAAK,IAAI,IAAIX,SAAS,KAAKT,SAAS,EAAE;MAC7C;IACJ;IACA,OAAOpD,qDAAC,CAAC,KAAK,EAAE;MAAE4H,KAAK,EAAE;IAAU,CAAC,EAAE5G,2DAAc,CAACwB,KAAK,EAAEqB,SAAS,EAAEY,gBAAgB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;EACI4D,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAE7D,OAAO;MAAEG,UAAU;MAAED,SAAS;MAAEb;IAAU,CAAC,GAAG,IAAI;IAC1D;AACR;AACA;AACA;IACQ,MAAMyE,WAAW,GAAG,CAAC,CAAC3D,UAAU,IAAI,CAAC,CAACD,SAAS;IAC/C,MAAM6D,UAAU,GAAG/D,OAAO,KAAK,IAAI,IAAIX,SAAS,KAAKT,SAAS;IAC9D,IAAI,CAACkF,WAAW,IAAI,CAACC,UAAU,EAAE;MAC7B;IACJ;IACA,OAAQvI,qDAAC,CAAC,KAAK,EAAE;MAAE4H,KAAK,EAAE;IAAkB,CAAC,EAAE,IAAI,CAACO,cAAc,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC;EAC/F;EACAI,MAAMA,CAAA,EAAG;IACL,MAAM;MAAExG,OAAO;MAAEyB,QAAQ;MAAEC,IAAI;MAAEoB,KAAK;MAAED,cAAc;MAAES,EAAE;MAAEzC;IAAS,CAAC,GAAG,IAAI;IAC7E,MAAMmF,IAAI,GAAG5G,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMoB,KAAK,GAAG,IAAI,CAAC2C,QAAQ,CAAC,CAAC;IAC7B,MAAMsD,MAAM,GAAGxH,qDAAW,CAAC,UAAU,EAAE,IAAI,CAACqE,EAAE,CAAC;IAC/C,MAAMoD,qBAAqB,GAAGV,IAAI,KAAK,IAAI,IAAItE,IAAI,KAAK,SAAS,IAAI,CAAC+E,MAAM;IAC5E,MAAMf,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAMiB,gBAAgB,GAAGrD,EAAE,CAACwC,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMc,gBAAgB,GAAG/D,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAK6C,QAAQ,IAAI7E,QAAQ,IAAI8F,gBAAgB,CAAE;IACtI,OAAQ3I,qDAAC,CAACE,iDAAI,EAAE;MAAEuH,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE1G,qDAAkB,CAAC,IAAI,CAACiC,KAAK,EAAE;QACjG,CAAC6E,IAAI,GAAG,IAAI;QACZ,WAAW,EAAEN,QAAQ;QACrB,WAAW,EAAE7E,QAAQ;QACrB,gBAAgB,EAAE+F,gBAAgB;QAClC,CAAE,iBAAgBlF,IAAK,EAAC,GAAGA,IAAI,KAAKN,SAAS;QAC7C,CAAE,kBAAiB0B,KAAM,EAAC,GAAGA,KAAK,KAAK1B,SAAS;QAChD,CAAE,4BAA2ByB,cAAe,EAAC,GAAG,IAAI;QACpD,mBAAmB,EAAEpB;MACzB,CAAC;IAAE,CAAC,EAAEzD,qDAAC,CAAC,OAAO,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE,kBAAkB;MAAEiB,OAAO,EAAE7G;IAAQ,CAAC,EAAE,IAAI,CAAC+F,oBAAoB,CAAC,CAAC,EAAE/H,qDAAC,CAAC,KAAK,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE;IAAyB,CAAC,EAAE5H,qDAAC,CAAC,KAAK,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE;IAAqB,CAAC,EAAE5H,qDAAC,CAAC,MAAM,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAE1D,IAAI,EAAE;IAAQ,CAAC,CAAC,CAAC,EAAE/D,qDAAC,CAAC,KAAK,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE,gBAAgB;MAAEM,GAAG,EAAG5C,EAAE,IAAM,IAAI,CAAC6B,eAAe,GAAG7B;IAAI,CAAC,EAAEtF,qDAAC,CAAC,UAAU,EAAEkG,MAAM,CAACC,MAAM,CAAC;MAAEsB,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE,iBAAiB;MAAEM,GAAG,EAAG5C,EAAE,IAAM,IAAI,CAACJ,WAAW,GAAGI,EAAG;MAAEwD,EAAE,EAAE9G,OAAO;MAAEyB,QAAQ,EAAEA,QAAQ;MAAEsF,cAAc,EAAE,IAAI,CAAC1F,cAAc;MAAE2F,SAAS,EAAE,IAAI,CAAC1F,SAAS;MAAE2F,YAAY,EAAE,IAAI,CAACrF,YAAY;MAAEsF,SAAS,EAAE,IAAI,CAACvF,SAAS;MAAEwF,SAAS,EAAE,IAAI,CAACrF,SAAS;MAAEsF,SAAS,EAAE,IAAI,CAACvF,SAAS;MAAEE,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,EAAE;MAAEqF,QAAQ,EAAE,IAAI,CAACpF,QAAQ;MAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEC,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAElC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEM,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEM,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEJ,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEK,SAAS,EAAE,IAAI,CAACA;IAAU,CAAC,EAAE,IAAI,CAACd,mBAAmB,CAAC,EAAEK,KAAK,CAAC,CAAC,EAAExC,qDAAC,CAAC,KAAK,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE;IAAmB,CAAC,EAAE5H,qDAAC,CAAC,MAAM,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAE1D,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC,EAAE2E,qBAAqB,IAAI1I,qDAAC,CAAC,KAAK,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE;IAAqB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACS,mBAAmB,CAAC,CAAC,CAAC;EACnhD;EACA,IAAI/C,EAAEA,CAAA,EAAG;IAAE,OAAOlF,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWkJ,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAIrH,WAAW,GAAG,CAAC;AACnBR,QAAQ,CAAC8H,KAAK,GAAG;EACbC,GAAG,EAAElI,oBAAoB;EACzBmI,EAAE,EAAEjI;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-textarea.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, h, f as Host, i as getElement, j as forceUpdate } from './index-c71c5417.js';\nimport { c as createNotchController } from './notch-controller-55b09e11.js';\nimport { e as debounceEvent, i as inheritAriaAttributes, h as inheritAttributes, c as componentOnReady } from './helpers-da915de8.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-09c71bc7.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b9c0d1da.js';\nimport './index-a5d50daf.js';\nimport './index-9b0d46f4.js';\n\nconst textareaIosCss = \".sc-ion-textarea-ios-h{--background:initial;--color:initial;--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0;--padding-end:0;--padding-bottom:8px;--padding-start:0;--border-radius:0;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}.textarea-label-placement-floating.sc-ion-textarea-ios-h,.textarea-label-placement-stacked.sc-ion-textarea-ios-h{--padding-top:0px;min-height:56px}[cols].sc-ion-textarea-ios-h:not([auto-grow]){width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.ion-color.sc-ion-textarea-ios-h{--highlight-color-focused:var(--ion-color-base);background:initial}ion-item.sc-ion-textarea-ios-h,ion-item .sc-ion-textarea-ios-h{-ms-flex-item-align:baseline;align-self:baseline}ion-item[slot=start].sc-ion-textarea-ios-h,ion-item [slot=start].sc-ion-textarea-ios-h,ion-item[slot=end].sc-ion-textarea-ios-h,ion-item [slot=end].sc-ion-textarea-ios-h{width:auto}.native-textarea.sc-ion-textarea-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;white-space:pre-wrap;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box;resize:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}.native-textarea.sc-ion-textarea-ios::-webkit-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::-moz-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios:-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios{color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.cloned-input.sc-ion-textarea-ios{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-textarea-ios{inset-inline-start:0}.cloned-input.sc-ion-textarea-ios:disabled{opacity:1}[auto-grow].sc-ion-textarea-ios-h .cloned-input.sc-ion-textarea-ios{height:100%}[auto-grow].sc-ion-textarea-ios-h .native-textarea.sc-ion-textarea-ios{overflow:hidden}.textarea-wrapper.sc-ion-textarea-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:0px;padding-bottom:0px;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:start;align-items:flex-start;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-textarea-ios{position:relative;width:100%;height:100%}.has-focus.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{caret-color:var(--highlight-color)}.native-wrapper.sc-ion-textarea-ios textarea.sc-ion-textarea-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom)}.native-wrapper.sc-ion-textarea-ios{display:grid;min-width:inherit;max-width:inherit;min-height:inherit;max-height:inherit;grid-auto-rows:100%}.native-wrapper.sc-ion-textarea-ios::after{white-space:pre-wrap;content:attr(data-replicated-value) \\\" \\\";visibility:hidden}.native-wrapper.sc-ion-textarea-ios::after{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.textarea-wrapper-inner.sc-ion-textarea-ios{display:-ms-flexbox;display:flex;width:100%;min-height:inherit}.ion-touched.ion-invalid.sc-ion-textarea-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-textarea-ios-h{--highlight-color:var(--highlight-color-valid)}.textarea-bottom.sc-ion-textarea-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-textarea-ios-h,.ion-touched.ion-invalid.sc-ion-textarea-ios-h{--border-color:var(--highlight-color)}.textarea-bottom.sc-ion-textarea-ios .error-text.sc-ion-textarea-ios{display:none;color:var(--highlight-color-invalid)}.textarea-bottom.sc-ion-textarea-ios .helper-text.sc-ion-textarea-ios{display:block;color:var(--ion-color-step-550, var(--ion-text-color-step-450, #737373))}.ion-touched.ion-invalid.sc-ion-textarea-ios-h .textarea-bottom.sc-ion-textarea-ios .error-text.sc-ion-textarea-ios{display:block}.ion-touched.ion-invalid.sc-ion-textarea-ios-h .textarea-bottom.sc-ion-textarea-ios .helper-text.sc-ion-textarea-ios{display:none}.textarea-bottom.sc-ion-textarea-ios .counter.sc-ion-textarea-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, var(--ion-text-color-step-450, #737373));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.label-text-wrapper.sc-ion-textarea-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-textarea-ios,.sc-ion-textarea-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-textarea-ios,.textarea-outline-notch-hidden.sc-ion-textarea-ios{display:none}.textarea-wrapper.sc-ion-textarea-ios textarea.sc-ion-textarea-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.textarea-label-placement-start.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:row;flex-direction:row}.textarea-label-placement-start.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-end.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.textarea-label-placement-end.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-ios-h .label-text.sc-ion-textarea-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.textarea-label-placement-stacked.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:left top;transform-origin:left top;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;max-width:100%;z-index:2}[dir=rtl].sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-label-placement-stacked.sc-ion-textarea-ios-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-label-placement-stacked.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-ios-h .native-wrapper.sc-ion-textarea-ios::after,.textarea-label-placement-floating[auto-grow].sc-ion-textarea-ios-h .native-wrapper.sc-ion-textarea-ios::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:8px;margin-bottom:0px}.sc-ion-textarea-ios-h.textarea-label-placement-stacked.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-stacked .sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-stacked.sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-stacked .sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-floating.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-floating .sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-floating.sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-floating .sc-ion-textarea-ios-s>[slot=end]{margin-top:8px}.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{opacity:0}.has-focus.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.has-value.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{opacity:1}.label-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.start-slot-wrapper.sc-ion-textarea-ios,.end-slot-wrapper.sc-ion-textarea-ios{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0;-ms-flex-item-align:start;align-self:start}.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-s>[slot=end]{margin-top:0}.sc-ion-textarea-ios-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-textarea-ios-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-textarea-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--padding-top:10px;--padding-end:0px;--padding-bottom:8px;--padding-start:0px;--highlight-height:0px;font-size:inherit}.textarea-disabled.sc-ion-textarea-ios-h{opacity:0.3}.sc-ion-textarea-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-textarea-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonTextareaIosStyle0 = textareaIosCss;\n\nconst textareaMdCss = \".sc-ion-textarea-md-h{--background:initial;--color:initial;--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0;--padding-end:0;--padding-bottom:8px;--padding-start:0;--border-radius:0;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}.textarea-label-placement-floating.sc-ion-textarea-md-h,.textarea-label-placement-stacked.sc-ion-textarea-md-h{--padding-top:0px;min-height:56px}[cols].sc-ion-textarea-md-h:not([auto-grow]){width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.ion-color.sc-ion-textarea-md-h{--highlight-color-focused:var(--ion-color-base);background:initial}ion-item.sc-ion-textarea-md-h,ion-item .sc-ion-textarea-md-h{-ms-flex-item-align:baseline;align-self:baseline}ion-item[slot=start].sc-ion-textarea-md-h,ion-item [slot=start].sc-ion-textarea-md-h,ion-item[slot=end].sc-ion-textarea-md-h,ion-item [slot=end].sc-ion-textarea-md-h{width:auto}.native-textarea.sc-ion-textarea-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;white-space:pre-wrap;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box;resize:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}.native-textarea.sc-ion-textarea-md::-webkit-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::-moz-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md:-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md{color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.cloned-input.sc-ion-textarea-md{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-textarea-md{inset-inline-start:0}.cloned-input.sc-ion-textarea-md:disabled{opacity:1}[auto-grow].sc-ion-textarea-md-h .cloned-input.sc-ion-textarea-md{height:100%}[auto-grow].sc-ion-textarea-md-h .native-textarea.sc-ion-textarea-md{overflow:hidden}.textarea-wrapper.sc-ion-textarea-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:0px;padding-bottom:0px;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:start;align-items:flex-start;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-textarea-md{position:relative;width:100%;height:100%}.has-focus.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{caret-color:var(--highlight-color)}.native-wrapper.sc-ion-textarea-md textarea.sc-ion-textarea-md{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom)}.native-wrapper.sc-ion-textarea-md{display:grid;min-width:inherit;max-width:inherit;min-height:inherit;max-height:inherit;grid-auto-rows:100%}.native-wrapper.sc-ion-textarea-md::after{white-space:pre-wrap;content:attr(data-replicated-value) \\\" \\\";visibility:hidden}.native-wrapper.sc-ion-textarea-md::after{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.textarea-wrapper-inner.sc-ion-textarea-md{display:-ms-flexbox;display:flex;width:100%;min-height:inherit}.ion-touched.ion-invalid.sc-ion-textarea-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-textarea-md-h{--highlight-color:var(--highlight-color-valid)}.textarea-bottom.sc-ion-textarea-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-textarea-md-h,.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}.textarea-bottom.sc-ion-textarea-md .error-text.sc-ion-textarea-md{display:none;color:var(--highlight-color-invalid)}.textarea-bottom.sc-ion-textarea-md .helper-text.sc-ion-textarea-md{display:block;color:var(--ion-color-step-550, var(--ion-text-color-step-450, #737373))}.ion-touched.ion-invalid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md .error-text.sc-ion-textarea-md{display:block}.ion-touched.ion-invalid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md .helper-text.sc-ion-textarea-md{display:none}.textarea-bottom.sc-ion-textarea-md .counter.sc-ion-textarea-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, var(--ion-text-color-step-450, #737373));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.label-text-wrapper.sc-ion-textarea-md{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-textarea-md,.sc-ion-textarea-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-textarea-md,.textarea-outline-notch-hidden.sc-ion-textarea-md{display:none}.textarea-wrapper.sc-ion-textarea-md textarea.sc-ion-textarea-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.textarea-label-placement-start.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:row;flex-direction:row}.textarea-label-placement-start.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-end.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.textarea-label-placement-end.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-md-h .label-text.sc-ion-textarea-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.textarea-label-placement-stacked.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:left top;transform-origin:left top;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;max-width:100%;z-index:2}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-label-placement-stacked.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-label-placement-stacked.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after,.textarea-label-placement-floating[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:8px;margin-bottom:0px}.sc-ion-textarea-md-h.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=end]{margin-top:8px}.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{opacity:0}.has-focus.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.has-value.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{opacity:1}.label-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.start-slot-wrapper.sc-ion-textarea-md,.end-slot-wrapper.sc-ion-textarea-md{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0;-ms-flex-item-align:start;align-self:start}.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-s>[slot=end]{margin-top:0}.sc-ion-textarea-md-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-textarea-md-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.textarea-fill-solid.sc-ion-textarea-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.textarea-fill-solid.ion-valid.sc-ion-textarea-md-h,.textarea-fill-solid.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md{border-top:none}@media (any-hover: hover){.textarea-fill-solid.sc-ion-textarea-md-h:hover{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.textarea-fill-solid.has-focus.sc-ion-textarea-md-h{--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}.label-floating.textarea-fill-solid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{max-width:calc(100% / 0.75)}.textarea-fill-outline.sc-ion-textarea-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.textarea-fill-outline.textarea-shape-round.sc-ion-textarea-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.textarea-fill-outline.ion-valid.sc-ion-textarea-md-h,.textarea-fill-outline.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.textarea-fill-outline.sc-ion-textarea-md-h:hover{--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.textarea-fill-outline.has-focus.sc-ion-textarea-md-h{--border-width:var(--highlight-height);--border-color:var(--highlight-color)}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md{border-top:none}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-bottom:none}.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-fill-outline.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{position:relative}.label-floating.textarea-fill-outline.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc(\\n    (100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75\\n  )}.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after,.textarea-fill-outline.textarea-label-placement-floating[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:12px;margin-bottom:0px}.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=end]{margin-top:12px}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-container.sc-ion-textarea-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{pointer-events:none}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.textarea-fill-outline.sc-ion-textarea-md-h .notch-spacer.sc-ion-textarea-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px;-ms-flex-positive:1;flex-grow:1}.label-floating.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md{border-top:none}.sc-ion-textarea-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--padding-top:18px;--padding-end:0px;--padding-bottom:8px;--padding-start:0px;--highlight-height:2px;font-size:inherit}.textarea-bottom.sc-ion-textarea-md .counter.sc-ion-textarea-md{letter-spacing:0.0333333333em}.textarea-label-placement-floating.has-focus.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-stacked.has-focus.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{color:var(--highlight-color)}.has-focus.textarea-label-placement-floating.ion-valid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.ion-touched.ion-invalid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.has-focus.textarea-label-placement-stacked.ion-valid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-stacked.ion-touched.ion-invalid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{color:var(--highlight-color)}.textarea-disabled.sc-ion-textarea-md-h{opacity:0.38}.textarea-highlight.sc-ion-textarea-md{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.textarea-highlight.sc-ion-textarea-md{inset-inline-start:0}.has-focus.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{bottom:0}.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{inset-inline-start:0}.textarea-shape-round.sc-ion-textarea-md-h{--border-radius:16px}.sc-ion-textarea-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-textarea-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonTextareaMdStyle0 = textareaMdCss;\n\nconst Textarea = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.inputId = `ion-textarea-${textareaIds++}`;\n        /**\n         * `true` if the textarea was cleared as a result of the user typing\n         * with `clearOnEdit` enabled.\n         *\n         * Resets when the textarea loses focus.\n         */\n        this.didTextareaClearOnEdit = false;\n        this.inheritedAttributes = {};\n        // `Event` type is used instead of `InputEvent`\n        // since the types from Stencil are not derived\n        // from the element (e.g. textarea and input\n        // should be InputEvent, but all other elements\n        // should be Event).\n        this.onInput = (ev) => {\n            const input = ev.target;\n            if (input) {\n                this.value = input.value || '';\n            }\n            this.emitInputChange(ev);\n        };\n        this.onChange = (ev) => {\n            this.emitValueChange(ev);\n        };\n        this.onFocus = (ev) => {\n            this.hasFocus = true;\n            this.focusedValue = this.value;\n            this.ionFocus.emit(ev);\n        };\n        this.onBlur = (ev) => {\n            this.hasFocus = false;\n            if (this.focusedValue !== this.value) {\n                /**\n                 * Emits the `ionChange` event when the textarea value\n                 * is different than the value when the textarea was focused.\n                 */\n                this.emitValueChange(ev);\n            }\n            this.didTextareaClearOnEdit = false;\n            this.ionBlur.emit(ev);\n        };\n        this.onKeyDown = (ev) => {\n            this.checkClearOnEdit(ev);\n        };\n        this.hasFocus = false;\n        this.color = undefined;\n        this.autocapitalize = 'none';\n        this.autofocus = false;\n        this.clearOnEdit = false;\n        this.debounce = undefined;\n        this.disabled = false;\n        this.fill = undefined;\n        this.inputmode = undefined;\n        this.enterkeyhint = undefined;\n        this.maxlength = undefined;\n        this.minlength = undefined;\n        this.name = this.inputId;\n        this.placeholder = undefined;\n        this.readonly = false;\n        this.required = false;\n        this.spellcheck = false;\n        this.cols = undefined;\n        this.rows = undefined;\n        this.wrap = undefined;\n        this.autoGrow = false;\n        this.value = '';\n        this.counter = false;\n        this.counterFormatter = undefined;\n        this.errorText = undefined;\n        this.helperText = undefined;\n        this.label = undefined;\n        this.labelPlacement = 'start';\n        this.shape = undefined;\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    /**\n     * Update the native input element when the value changes\n     */\n    valueChanged() {\n        const nativeInput = this.nativeInput;\n        const value = this.getValue();\n        if (nativeInput && nativeInput.value !== value) {\n            nativeInput.value = value;\n        }\n        this.runAutoGrow();\n    }\n    connectedCallback() {\n        const { el } = this;\n        this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.debounceChanged();\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n                detail: el,\n            }));\n        }\n    }\n    disconnectedCallback() {\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n                detail: this.el,\n            }));\n        }\n        if (this.slotMutationController) {\n            this.slotMutationController.destroy();\n            this.slotMutationController = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['data-form-type', 'title', 'tabindex']));\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        this.runAutoGrow();\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    /**\n     * Sets focus on the native `textarea` in `ion-textarea`. Use this method instead of the global\n     * `textarea.focus()`.\n     *\n     * See [managing focus](/docs/developing/managing-focus) for more information.\n     */\n    async setFocus() {\n        if (this.nativeInput) {\n            this.nativeInput.focus();\n        }\n    }\n    /**\n     * Returns the native `<textarea>` element used under the hood.\n     */\n    async getInputElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.nativeInput) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.nativeInput);\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        // Emitting a value change should update the internal state for tracking the focused value\n        this.focusedValue = newValue;\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     */\n    emitInputChange(event) {\n        const { value } = this;\n        this.ionInput.emit({ value, event });\n    }\n    runAutoGrow() {\n        if (this.nativeInput && this.autoGrow) {\n            writeTask(() => {\n                var _a;\n                if (this.textareaWrapper) {\n                    // Replicated value is an attribute to be used in the stylesheet\n                    // to set the inner contents of a pseudo element.\n                    this.textareaWrapper.dataset.replicatedValue = (_a = this.value) !== null && _a !== void 0 ? _a : '';\n                }\n            });\n        }\n    }\n    /**\n     * Check if we need to clear the text input if clearOnEdit is enabled\n     */\n    checkClearOnEdit(ev) {\n        if (!this.clearOnEdit) {\n            return;\n        }\n        /**\n         * The following keys do not modify the\n         * contents of the input. As a result, pressing\n         * them should not edit the textarea.\n         *\n         * We can't check to see if the value of the textarea\n         * was changed because we call checkClearOnEdit\n         * in a keydown listener, and the key has not yet\n         * been added to the textarea.\n         *\n         * Unlike ion-input, the \"Enter\" key does modify the\n         * textarea by adding a new line, so \"Enter\" is not\n         * included in the IGNORED_KEYS array.\n         */\n        const IGNORED_KEYS = ['Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n        const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n        /**\n         * Clear the textarea if the control has not been previously cleared\n         * during focus.\n         */\n        if (!this.didTextareaClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n            this.value = '';\n            this.emitInputChange(ev);\n        }\n        /**\n         * Pressing an IGNORED_KEYS first and\n         * then an allowed key will cause the input to not\n         * be cleared.\n         */\n        if (!pressedIgnoredKey) {\n            this.didTextareaClearOnEdit = true;\n        }\n    }\n    hasValue() {\n        return this.getValue() !== '';\n    }\n    getValue() {\n        return this.value || '';\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            } }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the textarea and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"textarea-outline-container\" }, h(\"div\", { class: \"textarea-outline-start\" }), h(\"div\", { class: {\n                        'textarea-outline-notch': true,\n                        'textarea-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"textarea-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    /**\n     * Renders the helper text or error text values\n     */\n    renderHintText() {\n        const { helperText, errorText } = this;\n        return [h(\"div\", { class: \"helper-text\" }, helperText), h(\"div\", { class: \"error-text\" }, errorText)];\n    }\n    renderCounter() {\n        const { counter, maxlength, counterFormatter, value } = this;\n        if (counter !== true || maxlength === undefined) {\n            return;\n        }\n        return h(\"div\", { class: \"counter\" }, getCounterText(value, maxlength, counterFormatter));\n    }\n    /**\n     * Responsible for rendering helper text,\n     * error text, and counter. This element should only\n     * be rendered if hint text is set or counter is enabled.\n     */\n    renderBottomContent() {\n        const { counter, helperText, errorText, maxlength } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        const hasCounter = counter === true && maxlength !== undefined;\n        if (!hasHintText && !hasCounter) {\n            return;\n        }\n        return (h(\"div\", { class: \"textarea-bottom\" }, this.renderHintText(), this.renderCounter()));\n    }\n    render() {\n        const { inputId, disabled, fill, shape, labelPlacement, el, hasFocus } = this;\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        /**\n         * If the label is stacked, it should always sit above the textarea.\n         * For floating labels, the label should move above the textarea if\n         * the textarea has a value, is focused, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the textarea is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots));\n        return (h(Host, { key: '37595a18d77dea1a337ac1c672e5f08a4128111d', class: createColorClasses(this.color, {\n                [mode]: true,\n                'has-value': hasValue,\n                'has-focus': hasFocus,\n                'label-floating': labelShouldFloat,\n                [`textarea-fill-${fill}`]: fill !== undefined,\n                [`textarea-shape-${shape}`]: shape !== undefined,\n                [`textarea-label-placement-${labelPlacement}`]: true,\n                'textarea-disabled': disabled,\n            }) }, h(\"label\", { key: '67342758743e5a40448a32ff695876d39778621f', class: \"textarea-wrapper\", htmlFor: inputId }, this.renderLabelContainer(), h(\"div\", { key: 'a994be8264bf5652811cf816d79a04178954e83f', class: \"textarea-wrapper-inner\" }, h(\"div\", { key: 'e09c93ebcd5b3d227d51e682ca23dfc7fd7027ad', class: \"start-slot-wrapper\" }, h(\"slot\", { key: 'd39758f21f19ae70aea21e9a9a7b7c20991fe593', name: \"start\" })), h(\"div\", { key: '6a4e10e53de4bb235ee30def4c9ae5bdee888816', class: \"native-wrapper\", ref: (el) => (this.textareaWrapper = el) }, h(\"textarea\", Object.assign({ key: '9e254e551f124d972e9bc6b09ef0f2bb55890be5', class: \"native-textarea\", ref: (el) => (this.nativeInput = el), id: inputId, disabled: disabled, autoCapitalize: this.autocapitalize, autoFocus: this.autofocus, enterKeyHint: this.enterkeyhint, inputMode: this.inputmode, minLength: this.minlength, maxLength: this.maxlength, name: this.name, placeholder: this.placeholder || '', readOnly: this.readonly, required: this.required, spellcheck: this.spellcheck, cols: this.cols, rows: this.rows, wrap: this.wrap, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, onKeyDown: this.onKeyDown }, this.inheritedAttributes), value)), h(\"div\", { key: 'a66aa2d2bc4778a0bec56a8b9ec9052a832eb3b2', class: \"end-slot-wrapper\" }, h(\"slot\", { key: '8e6a90b4475de32e23afc593da4108610dcca663', name: \"end\" }))), shouldRenderHighlight && h(\"div\", { key: '6da03205a8daff45581b20f0af3938634a9d5f8c', class: \"textarea-highlight\" })), this.renderBottomContent()));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"debounce\": [\"debounceChanged\"],\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet textareaIds = 0;\nTextarea.style = {\n    ios: IonTextareaIosStyle0,\n    md: IonTextareaMdStyle0\n};\n\nexport { Textarea as ion_textarea };\n"], "names": ["r", "registerInstance", "d", "createEvent", "w", "writeTask", "h", "f", "Host", "i", "getElement", "j", "forceUpdate", "c", "createNotchController", "e", "debounceEvent", "inheritAriaAttributes", "inheritAttributes", "componentOnReady", "createSlotMutationController", "g", "getCounterText", "hostContext", "createColorClasses", "b", "getIonMode", "textareaIosCss", "IonTextareaIosStyle0", "textareaMdCss", "IonTextareaMdStyle0", "Textarea", "constructor", "hostRef", "ionChange", "ionInput", "ionBlur", "ionFocus", "inputId", "textareaIds", "didTextareaClearOnEdit", "inheritedAttributes", "onInput", "ev", "input", "target", "value", "emitInputChange", "onChange", "emitValueChange", "onFocus", "hasFocus", "focusedValue", "emit", "onBlur", "onKeyDown", "checkClearOnEdit", "color", "undefined", "autocapitalize", "autofocus", "clearOnEdit", "debounce", "disabled", "fill", "inputmode", "enterkeyhint", "maxlength", "minlength", "name", "placeholder", "readonly", "required", "spellcheck", "cols", "rows", "wrap", "autoGrow", "counter", "counterFormatter", "errorText", "helperText", "label", "labelPlacement", "shape", "debounce<PERSON><PERSON>ed", "originalIonInput", "valueChanged", "nativeInput", "getValue", "runAutoGrow", "connectedCallback", "el", "slotMutationController", "notchController", "notchSpacerEl", "labelSlot", "document", "dispatchEvent", "CustomEvent", "detail", "disconnectedCallback", "destroy", "componentWillLoad", "Object", "assign", "componentDidLoad", "componentDidRender", "_a", "calculateNotchWidth", "setFocus", "_this", "_asyncToGenerator", "focus", "getInputElement", "_this2", "Promise", "resolve", "event", "newValue", "toString", "textareaWrapper", "dataset", "replicatedValue", "IGNORED_KEYS", "pressedIgnoredKey", "includes", "key", "hasValue", "renderLabel", "class", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "renderLabelContainer", "mode", "hasOutlineFill", "ref", "renderHintText", "renderCounter", "renderBottomContent", "hasHintText", "<PERSON><PERSON><PERSON><PERSON>", "render", "inItem", "should<PERSON>ender<PERSON>ighlight", "hasStartEndSlots", "labelShouldFloat", "htmlFor", "id", "autoCapitalize", "autoFocus", "enterKeyHint", "inputMode", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "readOnly", "watchers", "style", "ios", "md", "ion_textarea"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}