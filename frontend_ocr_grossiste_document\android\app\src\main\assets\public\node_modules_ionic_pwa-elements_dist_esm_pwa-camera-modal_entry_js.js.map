{"version": 3, "file": "node_modules_ionic_pwa-elements_dist_esm_pwa-camera-modal_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAiF;AAEjF,MAAMK,cAAc,GAAG,4aAA4a;AAEnc,MAAMC,cAAc,GAAG,MAAM;EAC3BC,WAAWA,CAACC,OAAO,EAAE;IACnBP,qDAAgB,CAAC,IAAI,EAAEO,OAAO,CAAC;IAC/B,IAAI,CAACC,OAAO,GAAGN,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACO,aAAa,GAAGP,qDAAW,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IAC1D,IAAI,CAACQ,UAAU,GAAG,MAAM;IACxB,IAAI,CAACC,UAAU,GAAG,KAAK;EACzB;EACMC,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACd,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,2BAA2B,CAAC;MAClEF,MAAM,CAACL,UAAU,GAAGG,KAAI,CAACH,UAAU;MACnCK,MAAM,CAACJ,UAAU,GAAGE,KAAI,CAACF,UAAU;MACnCI,MAAM,CAACG,gBAAgB,CAAC,SAAS;QAAA,IAAAC,IAAA,GAAAL,6OAAA,CAAE,WAAOM,CAAC,EAAK;UAC9C,IAAI,CAACP,KAAI,CAACQ,MAAM,EAAE;YAChB;UACF;UACA,MAAMC,KAAK,GAAGF,CAAC,CAACG,MAAM;UACtBV,KAAI,CAACL,OAAO,CAACgB,IAAI,CAACF,KAAK,CAAC;QAC1B,CAAC;QAAA,iBAAAG,EAAA;UAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;MACFZ,MAAM,CAACG,gBAAgB,CAAC,eAAe;QAAA,IAAAU,KAAA,GAAAd,6OAAA,CAAE,WAAOM,CAAC,EAAK;UACpDP,KAAI,CAACJ,aAAa,CAACe,IAAI,CAACJ,CAAC,CAAC;QAC5B,CAAC;QAAA,iBAAAS,GAAA;UAAA,OAAAD,KAAA,CAAAF,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;MACFX,QAAQ,CAACc,IAAI,CAACC,MAAM,CAAChB,MAAM,CAAC;MAC5BF,KAAI,CAACQ,MAAM,GAAGN,MAAM;IAAC;EACvB;EACMiB,OAAOA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAnB,6OAAA;MACd,IAAI,CAACmB,MAAI,CAACZ,MAAM,EAAE;QAChB;MACF;MACAY,MAAI,CAACZ,MAAM,IAAIY,MAAI,CAACZ,MAAM,CAACa,UAAU,CAACC,WAAW,CAACF,MAAI,CAACZ,MAAM,CAAC;MAC9DY,MAAI,CAACZ,MAAM,GAAG,IAAI;IAAC;EACrB;EACAe,MAAMA,CAAA,EAAG;IACP,OAAQjC,qDAAC,CAAC,KAAK,EAAE,IAAI,CAAC;EACxB;AACF,CAAC;AACDE,cAAc,CAACgC,KAAK,GAAGjC,cAAc", "sources": ["./node_modules/@ionic/pwa-elements/dist/esm/pwa-camera-modal.entry.js"], "sourcesContent": ["import { r as registerInstance, c as createEvent, h } from './index-1c5c47b4.js';\n\nconst cameraModalCss = \":host{z-index:1000;position:fixed;top:0;left:0;width:100%;height:100%;display:-ms-flexbox;display:flex;contain:strict}.wrapper{-ms-flex:1;flex:1;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;background-color:rgba(0, 0, 0, 0.15)}.content{-webkit-box-shadow:0px 0px 5px rgba(0, 0, 0, 0.2);box-shadow:0px 0px 5px rgba(0, 0, 0, 0.2);width:600px;height:600px}\";\n\nconst PWACameraModal = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onPhoto = createEvent(this, \"onPhoto\", 7);\n    this.noDeviceError = createEvent(this, \"noDeviceError\", 7);\n    this.facingMode = 'user';\n    this.hidePicker = false;\n  }\n  async present() {\n    const camera = document.createElement('pwa-camera-modal-instance');\n    camera.facingMode = this.facingMode;\n    camera.hidePicker = this.hidePicker;\n    camera.addEventListener('onPhoto', async (e) => {\n      if (!this._modal) {\n        return;\n      }\n      const photo = e.detail;\n      this.onPhoto.emit(photo);\n    });\n    camera.addEventListener('noDeviceError', async (e) => {\n      this.noDeviceError.emit(e);\n    });\n    document.body.append(camera);\n    this._modal = camera;\n  }\n  async dismiss() {\n    if (!this._modal) {\n      return;\n    }\n    this._modal && this._modal.parentNode.removeChild(this._modal);\n    this._modal = null;\n  }\n  render() {\n    return (h(\"div\", null));\n  }\n};\nPWACameraModal.style = cameraModalCss;\n\nexport { PWACameraModal as pwa_camera_modal };\n"], "names": ["r", "registerInstance", "c", "createEvent", "h", "cameraModalCss", "PWACameraModal", "constructor", "hostRef", "onPhoto", "noDeviceError", "facingMode", "hidePicker", "present", "_this", "_asyncToGenerator", "camera", "document", "createElement", "addEventListener", "_ref", "e", "_modal", "photo", "detail", "emit", "_x", "apply", "arguments", "_ref2", "_x2", "body", "append", "dismiss", "_this2", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "render", "style", "pwa_camera_modal"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}