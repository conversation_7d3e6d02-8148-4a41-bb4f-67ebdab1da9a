{"version": 3, "file": "node_modules_ionic_pwa-elements_dist_esm_pwa-action-sheet_entry_js.js", "mappings": ";;;;;;;;;;;;;;AAAkG;AAElG,MAAMO,cAAc,GAAG,wwDAAwwD;AAE/xD,MAAMC,cAAc,GAAG,MAAM;EAC3BC,WAAWA,CAACC,OAAO,EAAE;IACnBT,qDAAgB,CAAC,IAAI,EAAES,OAAO,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAGR,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAACS,MAAM,GAAGC,SAAS;IACvB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,IAAI,GAAG,KAAK;EACnB;EACAC,gBAAgBA,CAAA,EAAG;IACjBC,qBAAqB,CAAC,MAAM;MAC1B,IAAI,CAACF,IAAI,GAAG,IAAI;IAClB,CAAC,CAAC;EACJ;EACAG,OAAOA,CAAA,EAAG;IACR,IAAI,IAAI,CAACL,UAAU,EAAE;MACnB,IAAI,CAACM,KAAK,CAAC,CAAC;IACd;EACF;EACAA,KAAKA,CAAA,EAAG;IACN,IAAI,CAACJ,IAAI,GAAG,KAAK;IACjBK,UAAU,CAAC,MAAM;MACf,IAAI,CAACC,EAAE,CAACC,UAAU,CAACC,WAAW,CAAC,IAAI,CAACF,EAAE,CAAC;IACzC,CAAC,EAAE,GAAG,CAAC;EACT;EACAG,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACtBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAI,CAACjB,WAAW,CAACkB,IAAI,CAACF,CAAC,CAAC;IACxB,IAAI,CAACP,KAAK,CAAC,CAAC;EACd;EACAU,MAAMA,CAAA,EAAG;IACP,OAAQ1B,qDAAC,CAAC,KAAK,EAAE;MAAE2B,KAAK,EAAG,UAAS,IAAI,CAACf,IAAI,GAAG,OAAO,GAAG,EAAG,EAAC;MAAEgB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACb,OAAO,CAAC;IAAE,CAAC,EAAEf,qDAAC,CAAC,KAAK,EAAE;MAAE2B,KAAK,EAAE;IAAU,CAAC,EAAE3B,qDAAC,CAAC,KAAK,EAAE;MAAE2B,KAAK,EAAE;IAAQ,CAAC,EAAE,IAAI,CAACnB,MAAM,CAAC,EAAE,IAAI,CAACG,OAAO,CAACkB,GAAG,CAAC,CAACC,MAAM,EAAEP,CAAC,KAAKvB,qDAAC,CAAC,KAAK,EAAE;MAAE2B,KAAK,EAAE,qBAAqB;MAAEC,OAAO,EAAGN,CAAC,IAAK,IAAI,CAACD,iBAAiB,CAACC,CAAC,EAAEC,CAAC;IAAE,CAAC,EAAEvB,qDAAC,CAAC,KAAK,EAAE;MAAE2B,KAAK,EAAE;IAAsB,CAAC,EAAEG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAClW;EACA,IAAIb,EAAEA,CAAA,EAAG;IAAE,OAAOhB,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACDE,cAAc,CAAC4B,KAAK,GAAG7B,cAAc", "sources": ["./node_modules/@ionic/pwa-elements/dist/esm/pwa-action-sheet.entry.js"], "sourcesContent": ["import { r as registerInstance, c as createEvent, h, g as getElement } from './index-1c5c47b4.js';\n\nconst actionSheetCss = \":host{z-index:1000;position:fixed;top:0;left:0;width:100%;height:100%;display:-ms-flexbox;display:flex;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;font-family:-apple-system, BlinkMacSystemFont, \\\"Helvetica Neue\\\", \\\"Roboto\\\", sans-serif}.wrapper{-ms-flex:1;flex:1;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;background-color:rgba(0, 0, 0, 0);-webkit-transition:400ms background-color cubic-bezier(.36,.66,.04,1);transition:400ms background-color cubic-bezier(.36,.66,.04,1)}.wrapper.open{background-color:rgba(0, 0, 0, 0.32)}.title{color:#999;height:23px;line-height:23px;padding-bottom:17px;-webkit-padding-end:16px;padding-inline-end:16px;-webkit-padding-start:16px;padding-inline-start:16px;padding-left:16px;padding-right:16px;padding-top:20px}.content{width:568px;-ms-flex-item-align:end;align-self:flex-end;background-color:#fff;-webkit-transition:400ms -webkit-transform cubic-bezier(.36,.66,.04,1);transition:400ms -webkit-transform cubic-bezier(.36,.66,.04,1);transition:400ms transform cubic-bezier(.36,.66,.04,1);transition:400ms transform cubic-bezier(.36,.66,.04,1), 400ms -webkit-transform cubic-bezier(.36,.66,.04,1);-webkit-transform:translateY(100%);transform:translateY(100%)}.wrapper.open .content{-webkit-transform:translateY(0%);transform:translateY(0%)}@media only screen and (max-width: 568px){.content{width:100%}}.action-sheet-option{cursor:pointer;height:52px;line-height:52px}.action-sheet-button{color:rgb(38, 38, 38);font-size:16px;-webkit-padding-end:16px;padding-inline-end:16px;-webkit-padding-start:16px;padding-inline-start:16px;padding-left:16px;padding-right:16px;padding-top:0px}.action-sheet-button:hover{background-color:#F6F6F6}\";\n\nconst PWAActionSheet = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onSelection = createEvent(this, \"onSelection\", 7);\n    this.header = undefined;\n    this.cancelable = true;\n    this.options = [];\n    this.open = false;\n  }\n  componentDidLoad() {\n    requestAnimationFrame(() => {\n      this.open = true;\n    });\n  }\n  dismiss() {\n    if (this.cancelable) {\n      this.close();\n    }\n  }\n  close() {\n    this.open = false;\n    setTimeout(() => {\n      this.el.parentNode.removeChild(this.el);\n    }, 500);\n  }\n  handleOptionClick(e, i) {\n    e.stopPropagation();\n    this.onSelection.emit(i);\n    this.close();\n  }\n  render() {\n    return (h(\"div\", { class: `wrapper${this.open ? ' open' : ''}`, onClick: () => this.dismiss() }, h(\"div\", { class: \"content\" }, h(\"div\", { class: \"title\" }, this.header), this.options.map((option, i) => h(\"div\", { class: \"action-sheet-option\", onClick: (e) => this.handleOptionClick(e, i) }, h(\"div\", { class: \"action-sheet-button\" }, option.title))))));\n  }\n  get el() { return getElement(this); }\n};\nPWAActionSheet.style = actionSheetCss;\n\nexport { PWAActionSheet as pwa_action_sheet };\n"], "names": ["r", "registerInstance", "c", "createEvent", "h", "g", "getElement", "actionSheetCss", "PWAActionSheet", "constructor", "hostRef", "onSelection", "header", "undefined", "cancelable", "options", "open", "componentDidLoad", "requestAnimationFrame", "dismiss", "close", "setTimeout", "el", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "handleOptionClick", "e", "i", "stopPropagation", "emit", "render", "class", "onClick", "map", "option", "title", "style", "pwa_action_sheet"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}