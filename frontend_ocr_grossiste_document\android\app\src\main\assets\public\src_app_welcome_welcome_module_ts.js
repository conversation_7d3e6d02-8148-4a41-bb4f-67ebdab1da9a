"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_welcome_welcome_module_ts"],{

/***/ 76134:
/*!***************************************************!*\
  !*** ./src/app/welcome/welcome-routing.module.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WelcomePageRoutingModule: () => (/* binding */ WelcomePageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _welcome_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./welcome.page */ 62744);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _WelcomePageRoutingModule;




const routes = [{
  path: '',
  component: _welcome_page__WEBPACK_IMPORTED_MODULE_0__.WelcomePage
}];
class WelcomePageRoutingModule {}
_WelcomePageRoutingModule = WelcomePageRoutingModule;
_WelcomePageRoutingModule.ɵfac = function WelcomePageRoutingModule_Factory(t) {
  return new (t || _WelcomePageRoutingModule)();
};
_WelcomePageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _WelcomePageRoutingModule
});
_WelcomePageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](WelcomePageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 18255:
/*!*******************************************!*\
  !*** ./src/app/welcome/welcome.module.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WelcomePageModule: () => (/* binding */ WelcomePageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _welcome_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./welcome-routing.module */ 76134);
/* harmony import */ var _welcome_page__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./welcome.page */ 62744);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
var _WelcomePageModule;






class WelcomePageModule {}
_WelcomePageModule = WelcomePageModule;
_WelcomePageModule.ɵfac = function WelcomePageModule_Factory(t) {
  return new (t || _WelcomePageModule)();
};
_WelcomePageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
  type: _WelcomePageModule
});
_WelcomePageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonicModule, _welcome_routing_module__WEBPACK_IMPORTED_MODULE_0__.WelcomePageRoutingModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](WelcomePageModule, {
    declarations: [_welcome_page__WEBPACK_IMPORTED_MODULE_1__.WelcomePage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonicModule, _welcome_routing_module__WEBPACK_IMPORTED_MODULE_0__.WelcomePageRoutingModule]
  });
})();

/***/ }),

/***/ 62744:
/*!*****************************************!*\
  !*** ./src/app/welcome/welcome.page.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WelcomePage: () => (/* binding */ WelcomePage)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ionic/angular */ 21507);
var _WelcomePage;


class WelcomePage {
  constructor(navCtrl) {
    this.navCtrl = navCtrl;
  }
  ngOnInit() {
    console.log("Welcome Page");
  }
  selectPlatform(platform) {
    // Store the selected platform in localStorage
    localStorage.setItem('src_app', platform);
    console.log('Platform selected:', platform);
    // Navigate to login page
    this.navCtrl.navigateRoot('/login');
  }
}
_WelcomePage = WelcomePage;
_WelcomePage.ɵfac = function WelcomePage_Factory(t) {
  return new (t || _WelcomePage)(_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_1__.NavController));
};
_WelcomePage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
  type: _WelcomePage,
  selectors: [["app-welcome"]],
  decls: 19,
  vars: 0,
  consts: [[1, "welcome-wrapper"], ["size", "12", 1, "slide-content"], ["src", "/assets/icon-welcome.svg", 1, "slide-image"], [1, "content-slide"], [1, "platform-selection"], ["fill", "clear", 1, "platform-button", "winplus-button", 3, "click"], ["src", "/assets/onboarding_images/winpluspharm.svg", "alt", "WinPlusPharm", 1, "platform-logo"], ["fill", "clear", 1, "platform-button", "pharmalien-button", 3, "click"], ["src", "/assets/onboarding_images/winpharm.svg", "alt", "Pharmalien", 1, "platform-logo"]],
  template: function WelcomePage_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](0, "ion-header");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](1, "ion-content")(2, "div", 0)(3, "ion-row")(4, "ion-col", 1);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](5, "img", 2);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](6, "div", 3)(7, "h2");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](8, "Scannez ,r\u00E9cup\u00E9rez, automatisez !");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "p");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](10, "Vos bons de livraison enregistr\u00E9s automatiquement en un instant.");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](11, "h3");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](12, "Choisissez votre plateforme");
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](13, "ion-footer")(14, "div", 4)(15, "ion-button", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function WelcomePage_Template_ion_button_click_15_listener() {
        return ctx.selectPlatform("winpluspharma");
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](16, "img", 6);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](17, "ion-button", 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function WelcomePage_Template_ion_button_click_17_listener() {
        return ctx.selectPlatform("pharmalien");
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](18, "img", 8);
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
    }
  },
  dependencies: [_ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonCol, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonFooter, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_2__.IonRow],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\nion-content[_ngcontent-%COMP%]::part(scroll) {\n  overflow-y: auto;\n}\n\nion-button[_ngcontent-%COMP%]::part(native) {\n  --padding-top: 20px !important;\n  --padding-bottom: 20px !important;\n}\n\n.platform-selection[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  padding: 20px 20px 40px 20px;\n  gap: 20px;\n}\n\n.platform-button[_ngcontent-%COMP%] {\n  flex: 1;\n  height: 80px;\n  --border-radius: 12px;\n  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  margin: 0;\n}\n\n.platform-button[_ngcontent-%COMP%]::part(native) {\n  padding: 0;\n  background: white;\n  border: 2px solid #e0e0e0;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n}\n\n.platform-button[_ngcontent-%COMP%]:hover::part(native) {\n  border-color: #007bff;\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.2);\n}\n\n.platform-logo[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 60px;\n  object-fit: contain;\n  padding: 10px;\n}\n\n.welcome-wrapper[_ngcontent-%COMP%] {\n  background: url(\"/assets/bg-welcome.png\") no-repeat center center fixed;\n  background-size: cover;\n  height: 100%;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  position: relative;\n  width: 100%;\n  padding: 10px;\n  display: flex;\n  justify-content: center;\n  flex-direction: column;\n  align-items: center;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #2f4fcd;\n}\n\nion-button[_ngcontent-%COMP%] {\n  margin: 10px;\n}\n\nion-button.welcome-button[_ngcontent-%COMP%] {\n  --background: #1f41bb;\n  --background-activated: #1f41bb;\n  --border-radius: 8px;\n  width: 65%;\n  text-align: center;\n  box-shadow: 0px 16px 20px rgb(203, 214, 255); \n\n  color: #fff;\n  font-size: 20px;\n  font-weight: bold;\n}\n\nion-row[_ngcontent-%COMP%] {\n  height: 100%;\n  height: 85vh;\n}\n\n  ion-row ion-col {\n  padding-bottom: 0 !important;\n}\n\n  ion-col {\n  display: flex !important;\n  flex-direction: column;\n  justify-content: space-evenly;\n  align-items: center;\n}\n\n  img {\n  width: auto;\n  max-width: 100%;\n  height: auto;\n  max-height: 100%;\n}\n\n  .content-slide {\n  text-align: left;\n  padding: 0 20px;\n}\n\n  .content-slide h2 {\n  font-family: \"Poppins\", \"Inter\", sans-serif !important;\n  font-weight: bold;\n  font-style: normal;\n  font-size: 30px;\n  color: #1f41bb;\n}\n\n  .slide-content h3 {\n  font-family: \"Poppins\", \"Inter\", sans-serif !important;\n  font-weight: bold;\n  font-style: normal;\n  font-size: 24px;\n  color: #1f41bb;\n  text-align: center;\n  margin-bottom: 0;\n}\n\n  .content-slide p {\n  padding-right: 20px;\n  margin-top: 40px;\n  letter-spacing: 1.1px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ })

}]);
//# sourceMappingURL=src_app_welcome_welcome_module_ts.js.map