{"version": 3, "file": "node_modules_ionic_core_dist_esm_input-shims-0314bbe5_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACyD;AACmD;AACa;AAC7C;AAC/C;AACI;AAEjC,MAAMoB,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC9B,MAAMC,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,EAAEC,cAAc,EAAEC,cAAc,GAAG,CAAC,EAAEC,mBAAmB,GAAG,KAAK,KAAK;EAC7G,IAAIP,QAAQ,CAACQ,GAAG,CAACL,WAAW,CAAC,KAAKE,cAAc,EAAE;IAC9C;EACJ;EACA,IAAIA,cAAc,EAAE;IAChBI,QAAQ,CAACN,WAAW,EAAEC,OAAO,EAAEE,cAAc,EAAEC,mBAAmB,CAAC;EACvE,CAAC,MACI;IACDG,WAAW,CAACP,WAAW,EAAEC,OAAO,CAAC;EACrC;AACJ,CAAC;AACD,MAAMO,SAAS,GAAIC,KAAK,IAAK;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOA,KAAK,KAAKA,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,aAAa;AACtD,CAAC;AACD,MAAML,QAAQ,GAAGA,CAACN,WAAW,EAAEC,OAAO,EAAEE,cAAc,EAAEC,mBAAmB,GAAG,KAAK,KAAK;EACpF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMQ,QAAQ,GAAGX,OAAO,CAACY,UAAU;EACnC;EACA,MAAMC,QAAQ,GAAGb,OAAO,CAACc,SAAS,CAAC,KAAK,CAAC;EACzCD,QAAQ,CAACE,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;EACtCH,QAAQ,CAACI,QAAQ,GAAG,CAAC,CAAC;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAId,mBAAmB,EAAE;IACrBU,QAAQ,CAACK,QAAQ,GAAG,IAAI;EAC5B;EACAP,QAAQ,CAACQ,WAAW,CAACN,QAAQ,CAAC;EAC9BjB,QAAQ,CAACwB,GAAG,CAACrB,WAAW,EAAEc,QAAQ,CAAC;EACnC,MAAMlC,GAAG,GAAGoB,WAAW,CAACsB,aAAa;EACrC,MAAMC,EAAE,GAAG3C,GAAG,CAAC4C,GAAG,KAAK,KAAK,GAAG,IAAI,GAAG,CAAC,IAAI;EAC3CxB,WAAW,CAACyB,KAAK,CAACC,aAAa,GAAG,MAAM;EACxCzB,OAAO,CAACwB,KAAK,CAACE,SAAS,GAAI,eAAcJ,EAAG,MAAKpB,cAAe,gBAAe;AACnF,CAAC;AACD,MAAMI,WAAW,GAAGA,CAACP,WAAW,EAAEC,OAAO,KAAK;EAC1C,MAAM2B,KAAK,GAAG/B,QAAQ,CAACgC,GAAG,CAAC7B,WAAW,CAAC;EACvC,IAAI4B,KAAK,EAAE;IACP/B,QAAQ,CAACiC,MAAM,CAAC9B,WAAW,CAAC;IAC5B4B,KAAK,CAACG,MAAM,CAAC,CAAC;EAClB;EACA/B,WAAW,CAACyB,KAAK,CAACC,aAAa,GAAG,EAAE;EACpCzB,OAAO,CAACwB,KAAK,CAACE,SAAS,GAAG,EAAE;AAChC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMK,qBAAqB,GAAG,EAAE;AAEhC,MAAMC,uBAAuB,GAAGA,CAACjC,WAAW,EAAEC,OAAO,EAAEiC,QAAQ,KAAK;EAChE,IAAI,CAACA,QAAQ,IAAI,CAACjC,OAAO,EAAE;IACvB,OAAO,MAAM;MACT;IACJ,CAAC;EACL;EACA,MAAMkC,eAAe,GAAIC,eAAe,IAAK;IACzC,IAAI5B,SAAS,CAACP,OAAO,CAAC,EAAE;MACpBF,aAAa,CAACC,WAAW,EAAEC,OAAO,EAAEmC,eAAe,CAAC;IACxD;EACJ,CAAC;EACD,MAAMC,MAAM,GAAGA,CAAA,KAAMtC,aAAa,CAACC,WAAW,EAAEC,OAAO,EAAE,KAAK,CAAC;EAC/D,MAAMqC,SAAS,GAAGA,CAAA,KAAMH,eAAe,CAAC,IAAI,CAAC;EAC7C,MAAMI,SAAS,GAAGA,CAAA,KAAMJ,eAAe,CAAC,KAAK,CAAC;EAC9C/C,uDAAgB,CAAC8C,QAAQ,EAAE,gBAAgB,EAAEI,SAAS,CAAC;EACvDlD,uDAAgB,CAAC8C,QAAQ,EAAE,cAAc,EAAEK,SAAS,CAAC;EACrDtC,OAAO,CAACb,gBAAgB,CAAC,MAAM,EAAEiD,MAAM,CAAC;EACxC,OAAO,MAAM;IACT/C,uDAAmB,CAAC4C,QAAQ,EAAE,gBAAgB,EAAEI,SAAS,CAAC;IAC1DhD,uDAAmB,CAAC4C,QAAQ,EAAE,cAAc,EAAEK,SAAS,CAAC;IACxDtC,OAAO,CAACX,mBAAmB,CAAC,MAAM,EAAE+C,MAAM,CAAC;EAC/C,CAAC;AACL,CAAC;AAED,MAAMG,aAAa,GAAG,+CAA+C;AACrE,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAC9B,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIC,SAAS,GAAG,KAAK;EACrB,MAAM/D,GAAG,GAAGgE,QAAQ;EACpB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACnBF,SAAS,GAAG,IAAI;EACpB,CAAC;EACD,MAAMG,SAAS,GAAGA,CAAA,KAAM;IACpBJ,OAAO,GAAG,IAAI;EAClB,CAAC;EACD,MAAMK,UAAU,GAAIC,EAAE,IAAK;IACvB;IACA,IAAIL,SAAS,EAAE;MACXA,SAAS,GAAG,KAAK;MACjB;IACJ;IACA,MAAMM,MAAM,GAAGrE,GAAG,CAAC+B,aAAa;IAChC,IAAI,CAACsC,MAAM,EAAE;MACT;IACJ;IACA;IACA,IAAIA,MAAM,CAACC,OAAO,CAACV,aAAa,CAAC,EAAE;MAC/B;IACJ;IACA;IACA,MAAMW,MAAM,GAAGH,EAAE,CAACI,MAAM;IACxB,IAAID,MAAM,KAAKF,MAAM,EAAE;MACnB;IACJ;IACA,IAAIE,MAAM,CAACD,OAAO,CAACV,aAAa,CAAC,IAAIW,MAAM,CAACE,OAAO,CAACb,aAAa,CAAC,EAAE;MAChE;IACJ;IACAE,OAAO,GAAG,KAAK;IACf;IACAY,UAAU,CAAC,MAAM;MACb,IAAI,CAACZ,OAAO,EAAE;QACVO,MAAM,CAACM,IAAI,CAAC,CAAC;MACjB;IACJ,CAAC,EAAE,EAAE,CAAC;EACV,CAAC;EACDnE,uDAAgB,CAACR,GAAG,EAAE,gBAAgB,EAAEiE,QAAQ,CAAC;EACjDjE,GAAG,CAACQ,gBAAgB,CAAC,SAAS,EAAE0D,SAAS,EAAE,IAAI,CAAC;EAChDlE,GAAG,CAACQ,gBAAgB,CAAC,UAAU,EAAE2D,UAAU,EAAE,KAAK,CAAC;EACnD,OAAO,MAAM;IACTzD,uDAAmB,CAACV,GAAG,EAAE,gBAAgB,EAAEiE,QAAQ,EAAE,IAAI,CAAC;IAC1DjE,GAAG,CAACU,mBAAmB,CAAC,SAAS,EAAEwD,SAAS,EAAE,IAAI,CAAC;IACnDlE,GAAG,CAACU,mBAAmB,CAAC,UAAU,EAAEyD,UAAU,EAAE,KAAK,CAAC;EAC1D,CAAC;AACL,CAAC;AAED,MAAMS,mBAAmB,GAAG,GAAG;AAC/B,MAAMC,aAAa,GAAGA,CAACzD,WAAW,EAAE0D,SAAS,EAAEC,cAAc,EAAEC,cAAc,KAAK;EAC9E,IAAIC,EAAE;EACN,MAAMC,MAAM,GAAG,CAACD,EAAE,GAAG7D,WAAW,CAACqD,OAAO,CAAC,qBAAqB,CAAC,MAAM,IAAI,IAAIQ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG7D,WAAW;EAC7G,OAAO+D,cAAc,CAACD,MAAM,CAACE,qBAAqB,CAAC,CAAC,EAAEN,SAAS,CAACM,qBAAqB,CAAC,CAAC,EAAEL,cAAc,EAAEC,cAAc,CAAC;AAC5H,CAAC;AACD,MAAMG,cAAc,GAAGA,CAACE,SAAS,EAAEC,WAAW,EAAEP,cAAc,EAAEC,cAAc,KAAK;EAC/E;EACA,MAAMO,QAAQ,GAAGF,SAAS,CAACG,GAAG;EAC9B,MAAMC,WAAW,GAAGJ,SAAS,CAACK,MAAM;EACpC;EACA,MAAMC,cAAc,GAAGL,WAAW,CAACE,GAAG;EACtC,MAAMI,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAACR,WAAW,CAACI,MAAM,EAAEV,cAAc,GAAGD,cAAc,CAAC;EACvF;EACA,MAAMgB,WAAW,GAAGJ,cAAc,GAAG,EAAE;EACvC,MAAMK,cAAc,GAAGJ,iBAAiB,GAAGxC,qBAAqB;EAChE;EACA,MAAM6C,gBAAgB,GAAGD,cAAc,GAAGP,WAAW;EACrD,MAAMS,aAAa,GAAGH,WAAW,GAAGR,QAAQ;EAC5C;EACA,MAAMY,mBAAmB,GAAGN,IAAI,CAACO,KAAK,CAACH,gBAAgB,GAAG,CAAC,GAAG,CAACA,gBAAgB,GAAGC,aAAa,GAAG,CAAC,GAAG,CAACA,aAAa,GAAG,CAAC,CAAC;EACzH;EACA;EACA,MAAMG,YAAY,GAAGR,IAAI,CAACC,GAAG,CAACK,mBAAmB,EAAEZ,QAAQ,GAAGI,cAAc,CAAC;EAC7E,MAAMW,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAACF,YAAY,CAAC;EACvC,MAAMG,QAAQ,GAAGF,QAAQ,GAAG1B,mBAAmB;EAC/C,MAAM6B,cAAc,GAAGZ,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACa,GAAG,CAAC,GAAG,EAAEF,QAAQ,CAAC,CAAC;EAC7D,OAAO;IACHH,YAAY;IACZI,cAAc;IACdE,aAAa,EAAE5B,cAAc;IAC7B6B,UAAU,EAAE,EAAErB,QAAQ,GAAGQ,WAAW,CAAC,GAAG;EAC5C,CAAC;AACL,CAAC;AAED,MAAMc,iBAAiB,GAAG,kBAAkB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGA,CAAChC,SAAS,EAAEiC,aAAa,EAAEC,aAAa,KAAK;EAClE,MAAMC,KAAK,GAAGnC,SAAS,CAAC+B,iBAAiB,CAAC;EAC1C,IAAII,KAAK,EAAE;IACPC,YAAY,CAACD,KAAK,CAAC;EACvB;EACA,IAAIF,aAAa,GAAG,CAAC,EAAE;IACnBjC,SAAS,CAACjC,KAAK,CAACsE,WAAW,CAAC,mBAAmB,EAAG,GAAEJ,aAAc,IAAG,CAAC;EAC1E,CAAC,MACI;IACDjC,SAAS,CAAC+B,iBAAiB,CAAC,GAAGnC,UAAU,CAAC,MAAM;MAC5CI,SAAS,CAACjC,KAAK,CAACsE,WAAW,CAAC,mBAAmB,EAAE,KAAK,CAAC;MACvD,IAAIH,aAAa,EAAE;QACfA,aAAa,CAAC,CAAC;MACnB;IACJ,CAAC,EAAE,GAAG,CAAC;EACX;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,6BAA6B,GAAGA,CAAC/F,OAAO,EAAEyD,SAAS,EAAEuC,YAAY,KAAK;EACxE,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,IAAIxC,SAAS,EAAE;MACXgC,gBAAgB,CAAChC,SAAS,EAAE,CAAC,EAAEuC,YAAY,CAAC;IAChD;EACJ,CAAC;EACDhG,OAAO,CAACb,gBAAgB,CAAC,UAAU,EAAE8G,kBAAkB,EAAE;IAAEC,IAAI,EAAE;EAAK,CAAC,CAAC;AAC5E,CAAC;AAED,IAAIC,cAAc,GAAG,CAAC;AACtB,MAAMC,kBAAkB,GAAG,+BAA+B;AAC1D,MAAMC,kBAAkB,GAAGA,CAACtG,WAAW,EAAEC,OAAO,EAAEyD,SAAS,EAAE6C,QAAQ,EAAE5C,cAAc,EAAE6C,mBAAmB,EAAEC,cAAc,EAAEC,kBAAkB,GAAG,KAAK,KAAK;EACvJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,gBAAgB,GAAGH,mBAAmB,KAAKC,cAAc,KAAKG,SAAS,IAAIH,cAAc,CAACI,IAAI,KAAKnH,oDAAc,CAACoH,IAAI,CAAC;EAC7H;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,oCAAoC,GAAG,KAAK;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMnD,cAAc,GAAGlF,iDAAG,KAAKkI,SAAS,GAAGlI,iDAAG,CAACsI,WAAW,GAAG,CAAC;EAC9D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,YAAY,GAAIjE,EAAE,IAAK;IACzB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI+D,oCAAoC,KAAK,KAAK,EAAE;MAChDA,oCAAoC,GAAG,IAAI;MAC3C;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQG,UAAU,CAAClH,WAAW,EAAEC,OAAO,EAAEyD,SAAS,EAAE6C,QAAQ,EAAEvD,EAAE,CAACmE,MAAM,CAACxD,cAAc,EAAEgD,gBAAgB,EAAED,kBAAkB,EAAE9C,cAAc,EAAE,KAAK,CAAC;EAChJ,CAAC;EACD;AACJ;AACA;EACI,MAAMwD,QAAQ,GAAGA,CAAA,KAAM;IACnBL,oCAAoC,GAAG,KAAK;IAC5CrI,iDAAG,KAAK,IAAI,IAAIA,iDAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iDAAG,CAACY,mBAAmB,CAAC,oBAAoB,EAAE2H,YAAY,CAAC;IACrGjH,WAAW,CAACV,mBAAmB,CAAC,UAAU,EAAE8H,QAAQ,CAAC;EACzD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI,MAAMC,OAAO;IAAA,IAAAC,IAAA,GAAAC,6OAAA,CAAG,aAAY;MACxB;AACR;AACA;AACA;AACA;AACA;MACQ,IAAItH,OAAO,CAACuH,YAAY,CAACnB,kBAAkB,CAAC,EAAE;QAC1CpG,OAAO,CAACwH,eAAe,CAACpB,kBAAkB,CAAC;QAC3C;MACJ;MACAa,UAAU,CAAClH,WAAW,EAAEC,OAAO,EAAEyD,SAAS,EAAE6C,QAAQ,EAAE5C,cAAc,EAAEgD,gBAAgB,EAAED,kBAAkB,EAAE9C,cAAc,CAAC;MAC3HlF,iDAAG,KAAK,IAAI,IAAIA,iDAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iDAAG,CAACU,gBAAgB,CAAC,oBAAoB,EAAE6H,YAAY,CAAC;MAClGjH,WAAW,CAACZ,gBAAgB,CAAC,UAAU,EAAEgI,QAAQ,CAAC;IACtD,CAAC;IAAA,gBAdKC,OAAOA,CAAA;MAAA,OAAAC,IAAA,CAAAI,KAAA,OAAAC,SAAA;IAAA;EAAA,GAcZ;EACD3H,WAAW,CAACZ,gBAAgB,CAAC,SAAS,EAAEiI,OAAO,CAAC;EAChD,OAAO,MAAM;IACTrH,WAAW,CAACV,mBAAmB,CAAC,SAAS,EAAE+H,OAAO,CAAC;IACnD3I,iDAAG,KAAK,IAAI,IAAIA,iDAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iDAAG,CAACY,mBAAmB,CAAC,oBAAoB,EAAE2H,YAAY,CAAC;IACrGjH,WAAW,CAACV,mBAAmB,CAAC,UAAU,EAAE8H,QAAQ,CAAC;EACzD,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMQ,cAAc,GAAIC,EAAE,IAAK;EAC3B;AACJ;AACA;AACA;AACA;EACI,IAAIjF,QAAQ,CAACjC,aAAa,KAAKkH,EAAE,EAAE;IAC/B;EACJ;EACAA,EAAE,CAACC,YAAY,CAACzB,kBAAkB,EAAE,MAAM,CAAC;EAC3CwB,EAAE,CAACE,KAAK,CAAC,CAAC;AACd,CAAC;AACD,MAAMb,UAAU;EAAA,IAAAc,KAAA,GAAAT,6OAAA,CAAG,WAAOvH,WAAW,EAAEC,OAAO,EAAEyD,SAAS,EAAE6C,QAAQ,EAAE5C,cAAc,EAAE6C,mBAAmB,EAAEE,kBAAkB,GAAG,KAAK,EAAE9C,cAAc,GAAG,CAAC,EAAEqE,aAAa,GAAG,IAAI,EAAK;IAC/K,IAAI,CAACvE,SAAS,IAAI,CAAC6C,QAAQ,EAAE;MACzB;IACJ;IACA,MAAM2B,UAAU,GAAGzE,aAAa,CAACzD,WAAW,EAAG0D,SAAS,IAAI6C,QAAQ,EAAG5C,cAAc,EAAEC,cAAc,CAAC;IACtG,IAAIF,SAAS,IAAIe,IAAI,CAACU,GAAG,CAAC+C,UAAU,CAACjD,YAAY,CAAC,GAAG,CAAC,EAAE;MACpD;MACA;MACA2C,cAAc,CAAC3H,OAAO,CAAC;MACvB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,IAAIuG,mBAAmB,IAAI9C,SAAS,KAAK,IAAI,EAAE;QAC3CgC,gBAAgB,CAAChC,SAAS,EAAE0C,cAAc,CAAC;QAC3CJ,6BAA6B,CAAC/F,OAAO,EAAEyD,SAAS,EAAE,MAAO0C,cAAc,GAAG,CAAE,CAAC;MACjF;MACA;IACJ;IACA;IACA;IACA;IACArG,aAAa,CAACC,WAAW,EAAEC,OAAO,EAAE,IAAI,EAAEiI,UAAU,CAAC1C,UAAU,EAAEkB,kBAAkB,CAAC;IACpFkB,cAAc,CAAC3H,OAAO,CAAC;IACvB;AACJ;AACA;AACA;AACA;IACIT,uDAAG,CAAC,MAAMQ,WAAW,CAACmI,KAAK,CAAC,CAAC,CAAC;IAC9B;AACJ;AACA;AACA;AACA;AACA;IACI,IAAI3B,mBAAmB,IAAI9C,SAAS,EAAE;MAClC0C,cAAc,GAAG8B,UAAU,CAAC3C,aAAa;MACzCG,gBAAgB,CAAChC,SAAS,EAAE0C,cAAc,CAAC;IAC/C;IACA,IAAI,OAAOgC,MAAM,KAAK,WAAW,EAAE;MAC/B,IAAIC,oBAAoB;MACxB,MAAMC,cAAa;QAAA,IAAAC,KAAA,GAAAhB,6OAAA,CAAG,aAAY;UAC9B;UACA,IAAIc,oBAAoB,KAAKzB,SAAS,EAAE;YACpCd,YAAY,CAACuC,oBAAoB,CAAC;UACtC;UACAD,MAAM,CAAC9I,mBAAmB,CAAC,oBAAoB,EAAEkJ,2BAA2B,CAAC;UAC7EJ,MAAM,CAAC9I,mBAAmB,CAAC,oBAAoB,EAAEgJ,cAAa,CAAC;UAC/D;UACA,IAAI5E,SAAS,EAAE;YACX,MAAM1E,qDAAa,CAAC0E,SAAS,EAAE,CAAC,EAAEwE,UAAU,CAACjD,YAAY,EAAEiD,UAAU,CAAC7C,cAAc,CAAC;UACzF;UACA;UACA;UACAtF,aAAa,CAACC,WAAW,EAAEC,OAAO,EAAE,KAAK,EAAEiI,UAAU,CAAC1C,UAAU,CAAC;UACjE;UACAoC,cAAc,CAAC3H,OAAO,CAAC;UACvB;AACZ;AACA;AACA;AACA;UACY,IAAIuG,mBAAmB,EAAE;YACrBR,6BAA6B,CAAC/F,OAAO,EAAEyD,SAAS,EAAE,MAAO0C,cAAc,GAAG,CAAE,CAAC;UACjF;QACJ,CAAC;QAAA,gBAxBKkC,aAAaA,CAAA;UAAA,OAAAC,KAAA,CAAAb,KAAA,OAAAC,SAAA;QAAA;MAAA,GAwBlB;MACD,MAAMa,2BAA2B,GAAGA,CAAA,KAAM;QACtCJ,MAAM,CAAC9I,mBAAmB,CAAC,oBAAoB,EAAEkJ,2BAA2B,CAAC;QAC7EJ,MAAM,CAAChJ,gBAAgB,CAAC,oBAAoB,EAAEkJ,cAAa,CAAC;MAChE,CAAC;MACD,IAAI5E,SAAS,EAAE;QACX,MAAMxB,QAAQ,SAASpD,qDAAgB,CAAC4E,SAAS,CAAC;QAClD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACY,MAAM+E,iBAAiB,GAAGvG,QAAQ,CAACwG,YAAY,GAAGxG,QAAQ,CAACyG,YAAY;QACvE,IAAIV,aAAa,IAAIC,UAAU,CAACjD,YAAY,GAAGwD,iBAAiB,GAAGvG,QAAQ,CAAC0G,SAAS,EAAE;UACnF;AAChB;AACA;AACA;AACA;UACgB,IAAI3I,OAAO,CAAC4I,IAAI,KAAK,UAAU,EAAE;YAC7B;YACAX,UAAU,CAACjD,YAAY,IAAIjD,qBAAqB;YAChDoG,MAAM,CAAChJ,gBAAgB,CAAC,oBAAoB,EAAEoJ,2BAA2B,CAAC;UAC9E,CAAC,MACI;YACDJ,MAAM,CAAChJ,gBAAgB,CAAC,oBAAoB,EAAEkJ,cAAa,CAAC;UAChE;UACA;AAChB;AACA;AACA;AACA;AACA;UACgBD,oBAAoB,GAAG/E,UAAU,CAACgF,cAAa,EAAE,IAAI,CAAC;UACtD;QACJ;MACJ;MACAA,cAAa,CAAC,CAAC;IACnB;EACJ,CAAC;EAAA,gBArHKpB,UAAUA,CAAA4B,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAnB,KAAA,CAAAN,KAAA,OAAAC,SAAA;EAAA;AAAA,GAqHf;AAED,MAAMyB,cAAc,GAAG,IAAI;AAC3B,MAAMC,eAAe;EAAA,IAAAC,KAAA,GAAA/B,6OAAA,CAAG,WAAOgC,MAAM,EAAEC,QAAQ,EAAK;IAChD;AACJ;AACA;AACA;IACI,IAAI5K,iDAAG,KAAKgI,SAAS,EAAE;MACnB;IACJ;IACA,MAAM6C,KAAK,GAAGD,QAAQ,KAAK,KAAK;IAChC,MAAME,SAAS,GAAGF,QAAQ,KAAK,SAAS;IACxC;AACJ;AACA;AACA;AACA;IACI,MAAM7F,cAAc,GAAG4F,MAAM,CAACI,SAAS,CAAC,gBAAgB,EAAE,GAAG,CAAC;IAC9D,MAAMC,YAAY,GAAGL,MAAM,CAACM,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC;IAC5D,MAAMvH,SAAS,GAAGiH,MAAM,CAACM,UAAU,CAAC,mBAAmB,EAAEJ,KAAK,CAAC;IAC/D;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,MAAMK,aAAa,GAAGP,MAAM,CAACM,UAAU,CAAC,eAAe,EAAE,KAAK,CAAC;IAC/D,MAAMtE,aAAa,GAAGgE,MAAM,CAACM,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC;IAC9D,MAAME,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACrL,iDAAG,CAACsL,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;IAC1E,MAAMC,YAAY,GAAG,IAAIrK,OAAO,CAAC,CAAC;IAClC,MAAMsK,eAAe,GAAG,IAAItK,OAAO,CAAC,CAAC;IACrC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,MAAMuK,kBAAkB,SAASzK,oDAAQ,CAAC0K,aAAa,CAAC,CAAC;IACzD,MAAMC,aAAa;MAAA,IAAAC,KAAA,GAAAjD,6OAAA,CAAG,WAAOvH,WAAW,EAAK;QACzC,MAAM,IAAIyK,OAAO,CAAEC,OAAO,IAAKjL,uDAAgB,CAACO,WAAW,EAAE0K,OAAO,CAAC,CAAC;QACtE,MAAMC,SAAS,GAAG3K,WAAW,CAAC4K,UAAU,IAAI5K,WAAW;QACvD,MAAMC,OAAO,GAAG0K,SAAS,CAACE,aAAa,CAAC,OAAO,CAAC,IAAIF,SAAS,CAACE,aAAa,CAAC,UAAU,CAAC;QACvF,MAAM3I,QAAQ,GAAGhD,qDAAqB,CAACc,WAAW,CAAC;QACnD,MAAMuG,QAAQ,GAAG,CAACrE,QAAQ,GAAGlC,WAAW,CAACqD,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI;QACrE,IAAI,CAACpD,OAAO,EAAE;UACV;QACJ;QACA,IAAI,CAAC,CAACiC,QAAQ,IAAII,SAAS,IAAI,CAAC6H,YAAY,CAAC9J,GAAG,CAACL,WAAW,CAAC,EAAE;UAC3D,MAAM8K,IAAI,GAAG7I,uBAAuB,CAACjC,WAAW,EAAEC,OAAO,EAAEiC,QAAQ,CAAC;UACpEiI,YAAY,CAAC9I,GAAG,CAACrB,WAAW,EAAE8K,IAAI,CAAC;QACvC;QACA;AACR;AACA;AACA;AACA;AACA;QACQ,MAAMC,WAAW,GAAG9K,OAAO,CAAC4I,IAAI,KAAK,MAAM,IAAI5I,OAAO,CAAC4I,IAAI,KAAK,gBAAgB;QAChF,IAAI,CAACkC,WAAW,KACX,CAAC,CAAC7I,QAAQ,IAAI,CAAC,CAACqE,QAAQ,CAAC,IAC1BqD,YAAY,IACZ,CAACQ,eAAe,CAAC/J,GAAG,CAACL,WAAW,CAAC,EAAE;UACnC,MAAM8K,IAAI,GAAGxE,kBAAkB,CAACtG,WAAW,EAAEC,OAAO,EAAEiC,QAAQ,EAAEqE,QAAQ,EAAE5C,cAAc,EAAE4B,aAAa,EAAE8E,kBAAkB,EAAEX,SAAS,CAAC;UACvIU,eAAe,CAAC/I,GAAG,CAACrB,WAAW,EAAE8K,IAAI,CAAC;QAC1C;MACJ,CAAC;MAAA,gBA3BKP,aAAaA,CAAAS,GAAA;QAAA,OAAAR,KAAA,CAAA9C,KAAA,OAAAC,SAAA;MAAA;IAAA,GA2BlB;IACD,MAAMsD,eAAe,GAAIjL,WAAW,IAAK;MACrC,IAAIsC,SAAS,EAAE;QACX,MAAM4I,EAAE,GAAGf,YAAY,CAACtI,GAAG,CAAC7B,WAAW,CAAC;QACxC,IAAIkL,EAAE,EAAE;UACJA,EAAE,CAAC,CAAC;QACR;QACAf,YAAY,CAACrI,MAAM,CAAC9B,WAAW,CAAC;MACpC;MACA,IAAI4J,YAAY,EAAE;QACd,MAAMsB,EAAE,GAAGd,eAAe,CAACvI,GAAG,CAAC7B,WAAW,CAAC;QAC3C,IAAIkL,EAAE,EAAE;UACJA,EAAE,CAAC,CAAC;QACR;QACAd,eAAe,CAACtI,MAAM,CAAC9B,WAAW,CAAC;MACvC;IACJ,CAAC;IACD,IAAI8J,aAAa,IAAIV,cAAc,EAAE;MACjC3G,mBAAmB,CAAC,CAAC;IACzB;IACA;IACA;IACA;IACA,KAAK,MAAMhC,KAAK,IAAIsJ,MAAM,EAAE;MACxBQ,aAAa,CAAC9J,KAAK,CAAC;IACxB;IACA7B,iDAAG,CAACQ,gBAAgB,CAAC,iBAAiB,EAAG4D,EAAE,IAAK;MAC5CuH,aAAa,CAACvH,EAAE,CAACmE,MAAM,CAAC;IAC5B,CAAC,CAAC;IACFvI,iDAAG,CAACQ,gBAAgB,CAAC,mBAAmB,EAAG4D,EAAE,IAAK;MAC9CiI,eAAe,CAACjI,EAAE,CAACmE,MAAM,CAAC;IAC9B,CAAC,CAAC;EACN,CAAC;EAAA,gBAlGKkC,eAAeA,CAAA8B,GAAA,EAAAC,GAAA;IAAA,OAAA9B,KAAA,CAAA5B,KAAA,OAAAC,SAAA;EAAA;AAAA,GAkGpB", "sources": ["./node_modules/@ionic/core/dist/esm/input-shims-0314bbe5.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win, d as doc } from './index-a5d50daf.js';\nimport { g as getScrollElement, c as scrollByPoint, f as findClosestIonContent } from './index-5cc724f3.js';\nimport { a as addEventListener, b as removeEventListener, r as raf, c as componentOnReady } from './helpers-da915de8.js';\nimport { a as KeyboardResize, K as Keyboard } from './keyboard-73175e24.js';\nimport './index-9b0d46f4.js';\nimport './capacitor-59395cbd.js';\n\nconst cloneMap = new WeakMap();\nconst relocateInput = (componentEl, inputEl, shouldRelocate, inputRelativeY = 0, disabledClonedInput = false) => {\n    if (cloneMap.has(componentEl) === shouldRelocate) {\n        return;\n    }\n    if (shouldRelocate) {\n        addClone(componentEl, inputEl, inputRelativeY, disabledClonedInput);\n    }\n    else {\n        removeClone(componentEl, inputEl);\n    }\n};\nconst isFocused = (input) => {\n    /**\n     * https://developer.mozilla.org/en-US/docs/Web/API/Node/getRootNode\n     * Calling getRootNode on an element in standard web page will return HTMLDocument.\n     * Calling getRootNode on an element inside of the Shadow DOM will return the associated ShadowRoot.\n     * Calling getRootNode on an element that is not attached to a document/shadow tree will return\n     * the root of the DOM tree it belongs to.\n     * isFocused is used for the hide-caret utility which only considers input/textarea elements\n     * that are present in the DOM, so we don't set types for that final case since it does not apply.\n     */\n    return input === input.getRootNode().activeElement;\n};\nconst addClone = (componentEl, inputEl, inputRelativeY, disabledClonedInput = false) => {\n    // this allows for the actual input to receive the focus from\n    // the user's touch event, but before it receives focus, it\n    // moves the actual input to a location that will not screw\n    // up the app's layout, and does not allow the native browser\n    // to attempt to scroll the input into place (messing up headers/footers)\n    // the cloned input fills the area of where native input should be\n    // while the native input fakes out the browser by relocating itself\n    // before it receives the actual focus event\n    // We hide the focused input (with the visible caret) invisible by making it scale(0),\n    const parentEl = inputEl.parentNode;\n    // DOM WRITES\n    const clonedEl = inputEl.cloneNode(false);\n    clonedEl.classList.add('cloned-input');\n    clonedEl.tabIndex = -1;\n    /**\n     * Making the cloned input disabled prevents\n     * Chrome for Android from still scrolling\n     * the entire page since this cloned input\n     * will briefly be hidden by the keyboard\n     * even though it is not focused.\n     *\n     * This is not needed on iOS. While this\n     * does not cause functional issues on iOS,\n     * the input still appears slightly dimmed even\n     * if we set opacity: 1.\n     */\n    if (disabledClonedInput) {\n        clonedEl.disabled = true;\n    }\n    parentEl.appendChild(clonedEl);\n    cloneMap.set(componentEl, clonedEl);\n    const doc = componentEl.ownerDocument;\n    const tx = doc.dir === 'rtl' ? 9999 : -9999;\n    componentEl.style.pointerEvents = 'none';\n    inputEl.style.transform = `translate3d(${tx}px,${inputRelativeY}px,0) scale(0)`;\n};\nconst removeClone = (componentEl, inputEl) => {\n    const clone = cloneMap.get(componentEl);\n    if (clone) {\n        cloneMap.delete(componentEl);\n        clone.remove();\n    }\n    componentEl.style.pointerEvents = '';\n    inputEl.style.transform = '';\n};\n/**\n * Factoring in 50px gives us some room\n * in case the keyboard shows password/autofill bars\n * asynchronously.\n */\nconst SCROLL_AMOUNT_PADDING = 50;\n\nconst enableHideCaretOnScroll = (componentEl, inputEl, scrollEl) => {\n    if (!scrollEl || !inputEl) {\n        return () => {\n            return;\n        };\n    }\n    const scrollHideCaret = (shouldHideCaret) => {\n        if (isFocused(inputEl)) {\n            relocateInput(componentEl, inputEl, shouldHideCaret);\n        }\n    };\n    const onBlur = () => relocateInput(componentEl, inputEl, false);\n    const hideCaret = () => scrollHideCaret(true);\n    const showCaret = () => scrollHideCaret(false);\n    addEventListener(scrollEl, 'ionScrollStart', hideCaret);\n    addEventListener(scrollEl, 'ionScrollEnd', showCaret);\n    inputEl.addEventListener('blur', onBlur);\n    return () => {\n        removeEventListener(scrollEl, 'ionScrollStart', hideCaret);\n        removeEventListener(scrollEl, 'ionScrollEnd', showCaret);\n        inputEl.removeEventListener('blur', onBlur);\n    };\n};\n\nconst SKIP_SELECTOR = 'input, textarea, [no-blur], [contenteditable]';\nconst enableInputBlurring = () => {\n    let focused = true;\n    let didScroll = false;\n    const doc = document;\n    const onScroll = () => {\n        didScroll = true;\n    };\n    const onFocusin = () => {\n        focused = true;\n    };\n    const onTouchend = (ev) => {\n        // if app did scroll return early\n        if (didScroll) {\n            didScroll = false;\n            return;\n        }\n        const active = doc.activeElement;\n        if (!active) {\n            return;\n        }\n        // only blur if the active element is a text-input or a textarea\n        if (active.matches(SKIP_SELECTOR)) {\n            return;\n        }\n        // if the selected target is the active element, do not blur\n        const tapped = ev.target;\n        if (tapped === active) {\n            return;\n        }\n        if (tapped.matches(SKIP_SELECTOR) || tapped.closest(SKIP_SELECTOR)) {\n            return;\n        }\n        focused = false;\n        // TODO FW-2796: find a better way, why 50ms?\n        setTimeout(() => {\n            if (!focused) {\n                active.blur();\n            }\n        }, 50);\n    };\n    addEventListener(doc, 'ionScrollStart', onScroll);\n    doc.addEventListener('focusin', onFocusin, true);\n    doc.addEventListener('touchend', onTouchend, false);\n    return () => {\n        removeEventListener(doc, 'ionScrollStart', onScroll, true);\n        doc.removeEventListener('focusin', onFocusin, true);\n        doc.removeEventListener('touchend', onTouchend, false);\n    };\n};\n\nconst SCROLL_ASSIST_SPEED = 0.3;\nconst getScrollData = (componentEl, contentEl, keyboardHeight, platformHeight) => {\n    var _a;\n    const itemEl = (_a = componentEl.closest('ion-item,[ion-item]')) !== null && _a !== void 0 ? _a : componentEl;\n    return calcScrollData(itemEl.getBoundingClientRect(), contentEl.getBoundingClientRect(), keyboardHeight, platformHeight);\n};\nconst calcScrollData = (inputRect, contentRect, keyboardHeight, platformHeight) => {\n    // compute input's Y values relative to the body\n    const inputTop = inputRect.top;\n    const inputBottom = inputRect.bottom;\n    // compute visible area\n    const visibleAreaTop = contentRect.top;\n    const visibleAreaBottom = Math.min(contentRect.bottom, platformHeight - keyboardHeight);\n    // compute safe area\n    const safeAreaTop = visibleAreaTop + 15;\n    const safeAreaBottom = visibleAreaBottom - SCROLL_AMOUNT_PADDING;\n    // figure out if each edge of the input is within the safe area\n    const distanceToBottom = safeAreaBottom - inputBottom;\n    const distanceToTop = safeAreaTop - inputTop;\n    // desiredScrollAmount is the negated distance to the safe area according to our calculations.\n    const desiredScrollAmount = Math.round(distanceToBottom < 0 ? -distanceToBottom : distanceToTop > 0 ? -distanceToTop : 0);\n    // our calculations make some assumptions that aren't always true, like the keyboard being closed when an input\n    // gets focus, so make sure we don't scroll the input above the visible area\n    const scrollAmount = Math.min(desiredScrollAmount, inputTop - visibleAreaTop);\n    const distance = Math.abs(scrollAmount);\n    const duration = distance / SCROLL_ASSIST_SPEED;\n    const scrollDuration = Math.min(400, Math.max(150, duration));\n    return {\n        scrollAmount,\n        scrollDuration,\n        scrollPadding: keyboardHeight,\n        inputSafeY: -(inputTop - safeAreaTop) + 4,\n    };\n};\n\nconst PADDING_TIMER_KEY = '$ionPaddingTimer';\n/**\n * Scroll padding adds additional padding to the bottom\n * of ion-content so that there is enough scroll space\n * for an input to be scrolled above the keyboard. This\n * is needed in environments where the webview does not\n * resize when the keyboard opens.\n *\n * Example: If an input at the bottom of ion-content is\n * focused, there is no additional scrolling space below\n * it, so the input cannot be scrolled above the keyboard.\n * Scroll padding fixes this by adding padding equal to the\n * height of the keyboard to the bottom of the content.\n *\n * Common environments where this is needed:\n * - Mobile Safari: The keyboard overlays the content\n * - Capacitor/Cordova on iOS: The keyboard overlays the content\n * when the KeyboardResize mode is set to 'none'.\n */\nconst setScrollPadding = (contentEl, paddingAmount, clearCallback) => {\n    const timer = contentEl[PADDING_TIMER_KEY];\n    if (timer) {\n        clearTimeout(timer);\n    }\n    if (paddingAmount > 0) {\n        contentEl.style.setProperty('--keyboard-offset', `${paddingAmount}px`);\n    }\n    else {\n        contentEl[PADDING_TIMER_KEY] = setTimeout(() => {\n            contentEl.style.setProperty('--keyboard-offset', '0px');\n            if (clearCallback) {\n                clearCallback();\n            }\n        }, 120);\n    }\n};\n/**\n * When an input is about to be focused,\n * set a timeout to clear any scroll padding\n * on the content. Note: The clearing\n * is done on a timeout so that if users\n * are moving focus from one input to the next\n * then re-adding scroll padding to the new\n * input with cancel the timeout to clear the\n * scroll padding.\n */\nconst setClearScrollPaddingListener = (inputEl, contentEl, doneCallback) => {\n    const clearScrollPadding = () => {\n        if (contentEl) {\n            setScrollPadding(contentEl, 0, doneCallback);\n        }\n    };\n    inputEl.addEventListener('focusout', clearScrollPadding, { once: true });\n};\n\nlet currentPadding = 0;\nconst SKIP_SCROLL_ASSIST = 'data-ionic-skip-scroll-assist';\nconst enableScrollAssist = (componentEl, inputEl, contentEl, footerEl, keyboardHeight, enableScrollPadding, keyboardResize, disableClonedInput = false) => {\n    /**\n     * Scroll padding should only be added if:\n     * 1. The global scrollPadding config option\n     * is set to true.\n     * 2. The native keyboard resize mode is either \"none\"\n     * (keyboard overlays webview) or undefined (resize\n     * information unavailable)\n     * Resize info is available on Capacitor 4+\n     */\n    const addScrollPadding = enableScrollPadding && (keyboardResize === undefined || keyboardResize.mode === KeyboardResize.None);\n    /**\n     * This tracks whether or not the keyboard has been\n     * presented for a single focused text field. Note\n     * that it does not track if the keyboard is open\n     * in general such as if the keyboard is open for\n     * a different focused text field.\n     */\n    let hasKeyboardBeenPresentedForTextField = false;\n    /**\n     * When adding scroll padding we need to know\n     * how much of the viewport the keyboard obscures.\n     * We do this by subtracting the keyboard height\n     * from the platform height.\n     *\n     * If we compute this value when switching between\n     * inputs then the webview may already be resized.\n     * At this point, `win.innerHeight` has already accounted\n     * for the keyboard meaning we would then subtract\n     * the keyboard height again. This will result in the input\n     * being scrolled more than it needs to.\n     */\n    const platformHeight = win !== undefined ? win.innerHeight : 0;\n    /**\n     * Scroll assist is run when a text field\n     * is focused. However, it may need to\n     * re-run when the keyboard size changes\n     * such that the text field is now hidden\n     * underneath the keyboard.\n     * This function re-runs scroll assist\n     * when that happens.\n     *\n     * One limitation of this is on a web browser\n     * where native keyboard APIs do not have cross-browser\n     * support. `ionKeyboardDidShow` relies on the Visual Viewport API.\n     * This means that if the keyboard changes but does not change\n     * geometry, then scroll assist will not re-run even if\n     * the user has scrolled the text field under the keyboard.\n     * This is not a problem when running in Cordova/Capacitor\n     * because `ionKeyboardDidShow` uses the native events\n     * which fire every time the keyboard changes.\n     */\n    const keyboardShow = (ev) => {\n        /**\n         * If the keyboard has not yet been presented\n         * for this text field then the text field has just\n         * received focus. In that case, the focusin listener\n         * will run scroll assist.\n         */\n        if (hasKeyboardBeenPresentedForTextField === false) {\n            hasKeyboardBeenPresentedForTextField = true;\n            return;\n        }\n        /**\n         * Otherwise, the keyboard has already been presented\n         * for the focused text field.\n         * This means that the keyboard likely changed\n         * geometry, and we need to re-run scroll assist.\n         * This can happen when the user rotates their device\n         * or when they switch keyboards.\n         *\n         * Make sure we pass in the computed keyboard height\n         * rather than the estimated keyboard height.\n         *\n         * Since the keyboard is already open then we do not\n         * need to wait for the webview to resize, so we pass\n         * \"waitForResize: false\".\n         */\n        jsSetFocus(componentEl, inputEl, contentEl, footerEl, ev.detail.keyboardHeight, addScrollPadding, disableClonedInput, platformHeight, false);\n    };\n    /**\n     * Reset the internal state when the text field loses focus.\n     */\n    const focusOut = () => {\n        hasKeyboardBeenPresentedForTextField = false;\n        win === null || win === void 0 ? void 0 : win.removeEventListener('ionKeyboardDidShow', keyboardShow);\n        componentEl.removeEventListener('focusout', focusOut);\n    };\n    /**\n     * When the input is about to receive\n     * focus, we need to move it to prevent\n     * mobile Safari from adjusting the viewport.\n     */\n    const focusIn = async () => {\n        /**\n         * Scroll assist should not run again\n         * on inputs that have been manually\n         * focused inside of the scroll assist\n         * implementation.\n         */\n        if (inputEl.hasAttribute(SKIP_SCROLL_ASSIST)) {\n            inputEl.removeAttribute(SKIP_SCROLL_ASSIST);\n            return;\n        }\n        jsSetFocus(componentEl, inputEl, contentEl, footerEl, keyboardHeight, addScrollPadding, disableClonedInput, platformHeight);\n        win === null || win === void 0 ? void 0 : win.addEventListener('ionKeyboardDidShow', keyboardShow);\n        componentEl.addEventListener('focusout', focusOut);\n    };\n    componentEl.addEventListener('focusin', focusIn);\n    return () => {\n        componentEl.removeEventListener('focusin', focusIn);\n        win === null || win === void 0 ? void 0 : win.removeEventListener('ionKeyboardDidShow', keyboardShow);\n        componentEl.removeEventListener('focusout', focusOut);\n    };\n};\n/**\n * Use this function when you want to manually\n * focus an input but not have scroll assist run again.\n */\nconst setManualFocus = (el) => {\n    /**\n     * If element is already focused then\n     * a new focusin event will not be dispatched\n     * to remove the SKIL_SCROLL_ASSIST attribute.\n     */\n    if (document.activeElement === el) {\n        return;\n    }\n    el.setAttribute(SKIP_SCROLL_ASSIST, 'true');\n    el.focus();\n};\nconst jsSetFocus = async (componentEl, inputEl, contentEl, footerEl, keyboardHeight, enableScrollPadding, disableClonedInput = false, platformHeight = 0, waitForResize = true) => {\n    if (!contentEl && !footerEl) {\n        return;\n    }\n    const scrollData = getScrollData(componentEl, (contentEl || footerEl), keyboardHeight, platformHeight);\n    if (contentEl && Math.abs(scrollData.scrollAmount) < 4) {\n        // the text input is in a safe position that doesn't\n        // require it to be scrolled into view, just set focus now\n        setManualFocus(inputEl);\n        /**\n         * Even though the input does not need\n         * scroll assist, we should preserve the\n         * the scroll padding as users could be moving\n         * focus from an input that needs scroll padding\n         * to an input that does not need scroll padding.\n         * If we remove the scroll padding now, users will\n         * see the page jump.\n         */\n        if (enableScrollPadding && contentEl !== null) {\n            setScrollPadding(contentEl, currentPadding);\n            setClearScrollPaddingListener(inputEl, contentEl, () => (currentPadding = 0));\n        }\n        return;\n    }\n    // temporarily move the focus to the focus holder so the browser\n    // doesn't freak out while it's trying to get the input in place\n    // at this point the native text input still does not have focus\n    relocateInput(componentEl, inputEl, true, scrollData.inputSafeY, disableClonedInput);\n    setManualFocus(inputEl);\n    /**\n     * Relocating/Focusing input causes the\n     * click event to be cancelled, so\n     * manually fire one here.\n     */\n    raf(() => componentEl.click());\n    /**\n     * If enabled, we can add scroll padding to\n     * the bottom of the content so that scroll assist\n     * has enough room to scroll the input above\n     * the keyboard.\n     */\n    if (enableScrollPadding && contentEl) {\n        currentPadding = scrollData.scrollPadding;\n        setScrollPadding(contentEl, currentPadding);\n    }\n    if (typeof window !== 'undefined') {\n        let scrollContentTimeout;\n        const scrollContent = async () => {\n            // clean up listeners and timeouts\n            if (scrollContentTimeout !== undefined) {\n                clearTimeout(scrollContentTimeout);\n            }\n            window.removeEventListener('ionKeyboardDidShow', doubleKeyboardEventListener);\n            window.removeEventListener('ionKeyboardDidShow', scrollContent);\n            // scroll the input into place\n            if (contentEl) {\n                await scrollByPoint(contentEl, 0, scrollData.scrollAmount, scrollData.scrollDuration);\n            }\n            // the scroll view is in the correct position now\n            // give the native text input focus\n            relocateInput(componentEl, inputEl, false, scrollData.inputSafeY);\n            // ensure this is the focused input\n            setManualFocus(inputEl);\n            /**\n             * When the input is about to be blurred\n             * we should set a timeout to remove\n             * any scroll padding.\n             */\n            if (enableScrollPadding) {\n                setClearScrollPaddingListener(inputEl, contentEl, () => (currentPadding = 0));\n            }\n        };\n        const doubleKeyboardEventListener = () => {\n            window.removeEventListener('ionKeyboardDidShow', doubleKeyboardEventListener);\n            window.addEventListener('ionKeyboardDidShow', scrollContent);\n        };\n        if (contentEl) {\n            const scrollEl = await getScrollElement(contentEl);\n            /**\n             * scrollData will only consider the amount we need\n             * to scroll in order to properly bring the input\n             * into view. It will not consider the amount\n             * we can scroll in the content element.\n             * As a result, scrollData may request a greater\n             * scroll position than is currently available\n             * in the DOM. If this is the case, we need to\n             * wait for the webview to resize/the keyboard\n             * to show in order for additional scroll\n             * bandwidth to become available.\n             */\n            const totalScrollAmount = scrollEl.scrollHeight - scrollEl.clientHeight;\n            if (waitForResize && scrollData.scrollAmount > totalScrollAmount - scrollEl.scrollTop) {\n                /**\n                 * On iOS devices, the system will show a \"Passwords\" bar above the keyboard\n                 * after the initial keyboard is shown. This prevents the webview from resizing\n                 * until the \"Passwords\" bar is shown, so we need to wait for that to happen first.\n                 */\n                if (inputEl.type === 'password') {\n                    // Add 50px to account for the \"Passwords\" bar\n                    scrollData.scrollAmount += SCROLL_AMOUNT_PADDING;\n                    window.addEventListener('ionKeyboardDidShow', doubleKeyboardEventListener);\n                }\n                else {\n                    window.addEventListener('ionKeyboardDidShow', scrollContent);\n                }\n                /**\n                 * This should only fire in 2 instances:\n                 * 1. The app is very slow.\n                 * 2. The app is running in a browser on an old OS\n                 * that does not support Ionic Keyboard Events\n                 */\n                scrollContentTimeout = setTimeout(scrollContent, 1000);\n                return;\n            }\n        }\n        scrollContent();\n    }\n};\n\nconst INPUT_BLURRING = true;\nconst startInputShims = async (config, platform) => {\n    /**\n     * If doc is undefined then we are in an SSR environment\n     * where input shims do not apply.\n     */\n    if (doc === undefined) {\n        return;\n    }\n    const isIOS = platform === 'ios';\n    const isAndroid = platform === 'android';\n    /**\n     * Hide Caret and Input Blurring are needed on iOS.\n     * Scroll Assist and Scroll Padding are needed on iOS and Android\n     * with Chrome web browser (not Chrome webview).\n     */\n    const keyboardHeight = config.getNumber('keyboardHeight', 290);\n    const scrollAssist = config.getBoolean('scrollAssist', true);\n    const hideCaret = config.getBoolean('hideCaretOnScroll', isIOS);\n    /**\n     * The team is evaluating if inputBlurring is still needed. As a result\n     * this feature is disabled by default as of Ionic 8.0. Developers are\n     * able to re-enable it temporarily. The team may remove this utility\n     * if it is determined that doing so would not bring any adverse side effects.\n     * TODO FW-6014 remove input blurring utility (including implementation)\n     */\n    const inputBlurring = config.getBoolean('inputBlurring', false);\n    const scrollPadding = config.getBoolean('scrollPadding', true);\n    const inputs = Array.from(doc.querySelectorAll('ion-input, ion-textarea'));\n    const hideCaretMap = new WeakMap();\n    const scrollAssistMap = new WeakMap();\n    /**\n     * Grab the native keyboard resize configuration\n     * and pass it to scroll assist. Scroll assist requires\n     * that we adjust the input right before the input\n     * is about to be focused. If we called `Keyboard.getResizeMode`\n     * on focusin in scroll assist, we could potentially adjust the\n     * input too late since this call is async.\n     */\n    const keyboardResizeMode = await Keyboard.getResizeMode();\n    const registerInput = async (componentEl) => {\n        await new Promise((resolve) => componentOnReady(componentEl, resolve));\n        const inputRoot = componentEl.shadowRoot || componentEl;\n        const inputEl = inputRoot.querySelector('input') || inputRoot.querySelector('textarea');\n        const scrollEl = findClosestIonContent(componentEl);\n        const footerEl = !scrollEl ? componentEl.closest('ion-footer') : null;\n        if (!inputEl) {\n            return;\n        }\n        if (!!scrollEl && hideCaret && !hideCaretMap.has(componentEl)) {\n            const rmFn = enableHideCaretOnScroll(componentEl, inputEl, scrollEl);\n            hideCaretMap.set(componentEl, rmFn);\n        }\n        /**\n         * date/datetime-locale inputs on mobile devices show date picker\n         * overlays instead of keyboards. As a result, scroll assist is\n         * not needed. This also works around a bug in iOS <16 where\n         * scroll assist causes the browser to lock up. See FW-1997.\n         */\n        const isDateInput = inputEl.type === 'date' || inputEl.type === 'datetime-local';\n        if (!isDateInput &&\n            (!!scrollEl || !!footerEl) &&\n            scrollAssist &&\n            !scrollAssistMap.has(componentEl)) {\n            const rmFn = enableScrollAssist(componentEl, inputEl, scrollEl, footerEl, keyboardHeight, scrollPadding, keyboardResizeMode, isAndroid);\n            scrollAssistMap.set(componentEl, rmFn);\n        }\n    };\n    const unregisterInput = (componentEl) => {\n        if (hideCaret) {\n            const fn = hideCaretMap.get(componentEl);\n            if (fn) {\n                fn();\n            }\n            hideCaretMap.delete(componentEl);\n        }\n        if (scrollAssist) {\n            const fn = scrollAssistMap.get(componentEl);\n            if (fn) {\n                fn();\n            }\n            scrollAssistMap.delete(componentEl);\n        }\n    };\n    if (inputBlurring && INPUT_BLURRING) {\n        enableInputBlurring();\n    }\n    // Input might be already loaded in the DOM before ion-device-hacks did.\n    // At this point we need to look for all of the inputs not registered yet\n    // and register them.\n    for (const input of inputs) {\n        registerInput(input);\n    }\n    doc.addEventListener('ionInputDidLoad', (ev) => {\n        registerInput(ev.detail);\n    });\n    doc.addEventListener('ionInputDidUnload', (ev) => {\n        unregisterInput(ev.detail);\n    });\n};\n\nexport { startInputShims };\n"], "names": ["w", "win", "d", "doc", "g", "getScrollElement", "c", "scrollByPoint", "f", "findClosestIonContent", "a", "addEventListener", "b", "removeEventListener", "r", "raf", "componentOnReady", "KeyboardResize", "K", "Keyboard", "cloneMap", "WeakMap", "relocateInput", "componentEl", "inputEl", "shouldRelocate", "inputRelativeY", "disabledClonedInput", "has", "addClone", "removeClone", "isFocused", "input", "getRootNode", "activeElement", "parentEl", "parentNode", "clonedEl", "cloneNode", "classList", "add", "tabIndex", "disabled", "append<PERSON><PERSON><PERSON>", "set", "ownerDocument", "tx", "dir", "style", "pointerEvents", "transform", "clone", "get", "delete", "remove", "SCROLL_AMOUNT_PADDING", "enableHideCaretOnScroll", "scrollEl", "scrollHideCaret", "shouldHideCaret", "onBlur", "hideCaret", "showCaret", "SKIP_SELECTOR", "enableInputBlurring", "focused", "didScroll", "document", "onScroll", "onFocusin", "onTouchend", "ev", "active", "matches", "tapped", "target", "closest", "setTimeout", "blur", "SCROLL_ASSIST_SPEED", "getScrollData", "contentEl", "keyboardHeight", "platformHeight", "_a", "itemEl", "calcScrollData", "getBoundingClientRect", "inputRect", "contentRect", "inputTop", "top", "inputBottom", "bottom", "visibleAreaTop", "visibleAreaBottom", "Math", "min", "safeAreaTop", "safeAreaBottom", "distanceToBottom", "distanceToTop", "desiredScrollAmount", "round", "scrollAmount", "distance", "abs", "duration", "scrollDuration", "max", "scrollPadding", "inputSafeY", "PADDING_TIMER_KEY", "setScrollPadding", "paddingAmount", "clearCallback", "timer", "clearTimeout", "setProperty", "setClearScrollPaddingListener", "doneCallback", "clearScrollPadding", "once", "currentPadding", "SKIP_SCROLL_ASSIST", "enableScrollAssist", "footer<PERSON>l", "enableScrollPadding", "keyboardResize", "disableClonedInput", "addScrollPadding", "undefined", "mode", "None", "hasKeyboardBeenPresentedForTextField", "innerHeight", "keyboardShow", "jsSetFocus", "detail", "focusOut", "focusIn", "_ref", "_asyncToGenerator", "hasAttribute", "removeAttribute", "apply", "arguments", "setManualF<PERSON><PERSON>", "el", "setAttribute", "focus", "_ref2", "waitForResize", "scrollData", "click", "window", "scrollContentTimeout", "scrollContent", "_ref3", "doubleKeyboardEventListener", "totalScrollAmount", "scrollHeight", "clientHeight", "scrollTop", "type", "_x", "_x2", "_x3", "_x4", "_x5", "_x6", "INPUT_BLURRING", "startInputShims", "_ref4", "config", "platform", "isIOS", "isAndroid", "getNumber", "scrollAssist", "getBoolean", "inputBlurring", "inputs", "Array", "from", "querySelectorAll", "hideCaretMap", "scrollAssistMap", "keyboardResizeMode", "getResizeMode", "registerInput", "_ref5", "Promise", "resolve", "inputRoot", "shadowRoot", "querySelector", "rmFn", "isDateInput", "_x9", "unregisterInput", "fn", "_x7", "_x8"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}