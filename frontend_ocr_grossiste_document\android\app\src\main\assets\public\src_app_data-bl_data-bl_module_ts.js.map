{"version": 3, "file": "src_app_data-bl_data-bl_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAEX;;;AAE5C,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,qDAAUA;CACtB,CACF;AAMK,MAAOI,uBAAuB;2BAAvBA,uBAAuB;;mBAAvBA,wBAAuB;AAAA;;QAAvBA;AAAuB;;YAHxBL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,uBAAuB;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFxBT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAEsB;AAEvB;AACW,CAAC;;AAalD,MAAOc,gBAAgB;oBAAhBA,gBAAgB;;mBAAhBA,iBAAgB;AAAA;;QAAhBA;AAAgB;;YATzBJ,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,4EAAuB,EACvBQ,+DAAY;AAAA;;sHAKHC,gBAAgB;IAAAC,YAAA,GAFZd,qDAAU;IAAAM,OAAA,GAPvBG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,4EAAuB,EACvBQ,+DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;ACjB+E;AAGX;AACzB;;;;;;;;;;;IC0BjDQ,4DAHN,uBAA4D,cACjD,kBACkC,mBACZ;IACzBA,uDAAA,cAA+C;IAG3CA,4DAFJ,cAAiC,sBACd,wBACI;IAAAA,oDAAA,GAAiB;IACtCA,0DADsC,EAAoB,EACxC;IAGhBA,4DADF,uBAAkB,YACV;IAAAA,oDAAA,IAAgB;IAAAA,0DAAA,EAAO;IAC7BA,4DAAA,YAAM;IAAAA,oDAAA,IAA2B;IAM7CA,0DAN6C,EAAO,EACvB,EACf,EACG,EACH,EACF,EACG;;;;IAdFA,uDAAA,GAAmB;IAAnBA,wDAAA,QAAAO,QAAA,CAAAC,KAAA,EAAAR,2DAAA,CAAmB;IAGDA,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAO,QAAA,CAAAI,KAAA,CAAiB;IAI9BX,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAO,QAAA,CAAAK,IAAA,CAAgB;IAChBZ,uDAAA,GAA2B;IAA3BA,gEAAA,UAAAO,QAAA,CAAAO,UAAA,KAA2B;;;;;IA6BjCd,4DALV,mBAA2D,uBACvC,cACK,cACV,cACE,mBACmC;IACxCA,uDAAA,oBAOY;IAGlBA,0DAFI,EAAW,EACH,EACF;IAINA,4DAFJ,cAAS,cACE,mBACmC;IACxCA,uDAAA,qBAQY;IAEhBA,0DADE,EAAW,EACH;IAERA,4DADF,eAAS,oBACmD;IACxDA,uDAAA,qBAQY;IAGlBA,0DAFI,EAAW,EACH,EACF;IAINA,4DAFJ,eAAS,eACE,oBACmC;IACxCA,uDAAA,qBAQY;IAEhBA,0DADE,EAAW,EACH;IAERA,4DADF,eAAS,oBACmC;IACxCA,uDAAA,qBAQY;IAGlBA,0DAFI,EAAW,EACH,EACF;IAINA,4DAFJ,eAAS,eACE,oBACmC;IACxCA,uDAAA,qBAQY;IAMxBA,0DALU,EAAW,EACH,EACF,EACN,EACW,EACN;;;;IAlFDA,uDAAA,GAAiC;IAAjCA,mEAAA,UAAAgB,UAAA,CAAAC,WAAA,CAAiC;IAFjCjB,wDAAA,oBAAmB;IAkBnBA,uDAAA,GAA8B;IAA9BA,mEAAA,UAAAgB,UAAA,CAAAE,QAAA,CAA8B;IAH9BlB,wDAAA,oBAAmB;IAgBnBA,uDAAA,GAAgC;IAAhCA,mEAAA,UAAAgB,UAAA,CAAAG,UAAA,CAAgC;IAHhCnB,wDAAA,oBAAmB;IAmBnBA,uDAAA,GAAyB;IAAzBA,mEAAA,UAAAgB,UAAA,CAAAI,GAAA,CAAyB;IAHzBpB,wDAAA,oBAAmB;IAgBnBA,uDAAA,GAAyB;IAAzBA,mEAAA,UAAAgB,UAAA,CAAAK,GAAA,CAAyB;IAHzBrB,wDAAA,oBAAmB;IAmBnBA,uDAAA,GAA2B;IAA3BA,mEAAA,UAAAgB,UAAA,CAAAM,KAAA,CAA2B;IAH3BtB,wDAAA,oBAAmB;;;ADrIjC,MAAOpB,UAAU;EAcrB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA2C,YAAA;IAtEA,KAAAC,oBAAoB,GAAU,EAAE;IAChC,KAAAC,mBAAmB,GAAW,CAAC;IAC/B,KAAAC,gBAAgB,GAAW,CAAC;IAE5B,KAAAC,UAAU,GAAyB,EAAE;IACrC,KAAAC,OAAO,GAAGjC,qDAAM,CAACC,yDAAa,CAAC;IAC/B,KAAAiC,aAAa,GAAGlC,qDAAM,CAACI,mEAAa,CAAC;IACrC,KAAA+B,iBAAiB,GAAGnC,qDAAM,CAACE,6DAAiB,CAAC;IAC7C,KAAAkC,eAAe,GAAGpC,qDAAM,CAACG,2DAAe,CAAC;EA8D1B;EAEfkC,QAAQA,CAAA;IAAA,IAAAC,eAAA,EAAAC,kBAAA;IACN,IAAI,CAACP,UAAU,GAAG,IAAI,CAACE,aAAa,CAACM,kBAAkB,EAAE;IACzD,IAAG,IAAI,CAACR,UAAU,CAACS,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACR,OAAO,CAACS,YAAY,CAAC,UAAU,CAAC;;IAEvC,CAAAJ,eAAA,OAAI,CAACK,SAAS,cAAAL,eAAA,eAAdA,eAAA,CAAgBM,aAAa,CAACC,MAAM,CAACC,MAAM,EAAE;IAC7C,CAAAP,kBAAA,OAAI,CAACQ,YAAY,cAAAR,kBAAA,eAAjBA,kBAAA,CAAmBK,aAAa,CAACC,MAAM,CAACC,MAAM,EAAE;IAChDE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACjB,UAAU,CAAC;EAChD;EAEAkB,eAAeA,CAAA;IACb,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,aAAaA,CAAA;IACXJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,IAAI,CAACE,WAAW,EAAE;EACpB;EACAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAACR,SAAS,IAAI,IAAI,CAACA,SAAS,CAACC,aAAa,IAAI,IAAI,CAACG,YAAY,IAAI,IAAI,CAACA,YAAY,CAACH,aAAa,EAAE;MAC1G,MAAMS,iBAAiB,GAAG,IAAI,CAACV,SAAS,CAACC,aAAa,CAACC,MAAM;MAC7D,MAAMS,oBAAoB,GAAG,IAAI,CAACP,YAAY,CAACH,aAAa,CAACC,MAAM;MACnE,IAAI,CAACU,kBAAkB,EAAE;;EAE7B;EAEAA,kBAAkBA,CAAA;IAEhB,IAAI,IAAI,CAACZ,SAAS,IAAI,IAAI,CAACA,SAAS,CAACC,aAAa,EAAE;MAAA,IAAAY,qBAAA;MAClD,MAAMH,iBAAiB,GAAG,IAAI,CAACV,SAAS,CAACC,aAAa,CAACC,MAAM;MAC7D,MAAMY,WAAW,GAAGJ,iBAAiB,CAACK,SAAS;MAC/C,IAAI,CAAC3B,gBAAgB,GAAG0B,WAAW,GAAG,CAAC,CAAC,CAAC;MACzC,IAAI,CAAC5B,oBAAoB,GAAG,EAAA2B,qBAAA,OAAI,CAACxB,UAAU,CAACyB,WAAW,CAAC,cAAAD,qBAAA,uBAA5BA,qBAAA,CAA8BG,QAAQ,KAAI,EAAE;MACxE;MACA,IAAI,CAACC,kBAAkB,EAAE;;EAE7B;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACb,YAAY,IAAI,IAAI,CAACA,YAAY,CAACH,aAAa,EAAE;MACxD,MAAMU,oBAAoB,GAAG,IAAI,CAACP,YAAY,CAACH,aAAa,CAACC,MAAM;MACnE,MAAMgB,kBAAkB,GAAGP,oBAAoB,CAACI,SAAS;MACzD,IAAI,CAAC5B,mBAAmB,GAAG+B,kBAAkB,GAAG,CAAC;;EAErD;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACf,YAAY,IAAI,IAAI,CAACA,YAAY,CAACH,aAAa,EAAE;MACxD,MAAMU,oBAAoB,GAAG,IAAI,CAACP,YAAY,CAACH,aAAa,CAACC,MAAM;MACnES,oBAAoB,CAACQ,SAAS,EAAE;MAChC,IAAI,CAACF,kBAAkB,EAAE;;EAE7B;EAEAG,SAASA,CAAA;IACP,IAAI,IAAI,CAAChB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACH,aAAa,EAAE;MACxD,MAAMU,oBAAoB,GAAG,IAAI,CAACP,YAAY,CAACH,aAAa,CAACC,MAAM;MACnES,oBAAoB,CAACS,SAAS,EAAE;MAChC,IAAI,CAACH,kBAAkB,EAAE;;EAE7B;EAGMI,KAAKA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACT,MAAMC,KAAK,SAASF,KAAI,CAAC7B,eAAe,CAACgC,MAAM,CAAC;QAC9CC,MAAM,EAAE,yCAAyC;QACjDC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,uCAAuC;UACjDC,OAAO,EAAEA,CAAA,KAAK;YACZ1B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEsB,IAAI,EAAE,YAAY;UAClBE,QAAQ,EAAE,uCAAuC;UACjDC,OAAO,EAAEA,CAAA,KAAK;YACZT,KAAI,CAAC/B,aAAa,CAACyC,aAAa,EAAE;YAClCC,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;YAC3CZ,KAAI,CAAChC,OAAO,CAAC6C,eAAe,CAAC,UAAU,CAAC;UAC1C;SACD;OAEJ,CAAC;MAEF,MAAMX,KAAK,CAACY,OAAO,EAAE;IAAC;EACxB;EAGMC,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAf,6OAAA;MACjB,MAAMC,KAAK,SAASc,MAAI,CAAC7C,eAAe,CAACgC,MAAM,CAAC;QAC9CC,MAAM,EAAE,0DAA0D;QAClEC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,uCAAuC;UACjDC,OAAO,EAAEA,CAAA,KAAK;YACZ1B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEsB,IAAI,EAAE,UAAU;UAChBE,QAAQ,EAAE,uCAAuC;UACjDC,OAAO,EAAEA,CAAA,KAAK;YACZO,MAAI,CAAChD,OAAO,CAACiD,YAAY,CAAC,cAAc,CAAC;UAC3C;SACD;OAEJ,CAAC;MAEF,MAAMf,KAAK,CAACY,OAAO,EAAE;IAAC;EACxB;;cA9LW9F,UAAU;;mBAAVA,WAAU;AAAA;;QAAVA,WAAU;EAAAkG,SAAA;EAAAC,SAAA,WAAAC,iBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;MCVnBjF,4DAFJ,iBAAY,kBACG,gBACA;MAAAA,oDAAA,kCAAsB;MAOrCA,0DAPqC,EAAY,EAMjC,EACH;MAGXA,4DADF,qBAAqC,YACT;MAAAA,oDAAA,YAAK;MAAAA,0DAAA,EAAK;MACpCA,4DAAA,6BAaC;MADCA,wDAAA,+BAAAoF,kEAAA;QAAApF,2DAAA,CAAAsF,GAAA;QAAA,OAAAtF,yDAAA,CAAqBkF,GAAA,CAAAnC,aAAA,EAAe;MAAA,EAAC;MAErC/C,wDAAA,IAAAyF,kCAAA,2BAA4D;MAmB9DzF,0DAAA,EAAmB;MAIfA,4DAFJ,mBAA+B,uBACZ,0BAC0C;MAAtBA,wDAAA,mBAAA0F,sDAAA;QAAA1F,2DAAA,CAAAsF,GAAA;QAAA,OAAAtF,yDAAA,CAASkF,GAAA,CAAAxB,SAAA,EAAW;MAAA,EAAC;MAAC1D,0DAAA,EAAkB;MAGzEA,4DADF,cAA4B,sBACV;MAAAA,oDAAA,IAAqE;MAAAA,0DAAA,EAAiB;MACtGA,4DAAA,yBAAmB;MAAAA,oDAAA,IAA2B;MAChDA,0DADgD,EAAoB,EAC9D;MACNA,4DAAA,0BAA0D;MAAtBA,wDAAA,mBAAA2F,sDAAA;QAAA3F,2DAAA,CAAAsF,GAAA;QAAA,OAAAtF,yDAAA,CAASkF,GAAA,CAAAzB,SAAA,EAAW;MAAA,EAAC;MAE3DzD,0DAF4D,EAAkB,EAE5D;MAElBA,uDAAA,cAAoB;MAEpBA,4DAAA,+BAAsD;MACpDA,wDAAA,KAAA4F,mCAAA,6BAA2D;MAiGjE5F,0DAHI,EAAmB,EACV,EAEC;MAKRA,4DAHN,kBAAY,mBACG,mBACE,sBACmE;MAA1BA,wDAAA,mBAAA6F,iDAAA;QAAA7F,2DAAA,CAAAsF,GAAA;QAAA,OAAAtF,yDAAA,CAASkF,GAAA,CAAAP,aAAA,EAAe;MAAA,EAAC;MAC3E3E,uDAAA,2BAAgD;MAClDA,0DAAA,EAAa;MACbA,4DAAA,sBAAyD;MAAlBA,wDAAA,mBAAA8F,iDAAA;QAAA9F,2DAAA,CAAAsF,GAAA;QAAA,OAAAtF,yDAAA,CAASkF,GAAA,CAAAvB,KAAA,EAAO;MAAA,EAAC;MACtD3D,uDAAA,2BAAkD;MAClDA,4DAAA,YAAM;MAAAA,oDAAA,eAAO;MACfA,0DADe,EAAO,EACT;MACbA,4DAAA,sBAAyE;MACvEA,uDAAA,2BAAmD;MAI3DA,0DAHM,EAAa,EACD,EACF,EACH;;;MAtJuBA,uDAAA,GAAa;MAAbA,wDAAA,YAAAkF,GAAA,CAAAvD,UAAA,CAAa;MA0BzB3B,uDAAA,GAAqE;MAArEA,gEAAA,aAAAkF,GAAA,CAAAzD,mBAAA,SAAAyD,GAAA,CAAA1D,oBAAA,CAAAY,MAAA,KAAqE;MAClEpC,uDAAA,GAA2B;MAA3BA,gEAAA,UAAAkF,GAAA,CAAAxD,gBAAA,KAA2B;MASd1B,uDAAA,GAAuB;MAAvBA,wDAAA,YAAAkF,GAAA,CAAA1D,oBAAA,CAAuB;MA6GZxB,uDAAA,IAA2B;MAA3BA,wDAAA,eAAAA,6DAAA,IAAAiG,GAAA,EAA2B", "sources": ["./src/app/data-bl/data-bl-routing.module.ts", "./src/app/data-bl/data-bl.module.ts", "./src/app/data-bl/data-bl.page.ts", "./src/app/data-bl/data-bl.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { DataBLPage } from './data-bl.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: DataBLPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class DataBLPageRoutingModule {}\r\n", "import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { DataBLPageRoutingModule } from './data-bl-routing.module';\r\n\r\nimport { DataBLPage } from './data-bl.page';\r\nimport { SharedModule } from '../shared/shared.module'; // Import SharedModule\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    DataBLPageRoutingModule,\r\n    SharedModule\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  declarations: [DataBLPage]\r\n})\r\nexport class DataBLPageModule {}\r\n", "import { Component, ViewChild, AfterViewInit, ElementRef,OnInit, inject } from '@angular/core';\r\nimport { ImageData } from 'src/models/ImageData';\r\nimport Swiper from 'swiper';\r\nimport { NavController, LoadingController, AlertController  } from '@ionic/angular';\r\nimport { SignalService } from '../services/signal.service';\r\nimport { TransformedDocData } from 'src/models/TransformedDocData';\r\n\r\n@Component({\r\n  selector: 'app-data-bl',\r\n  templateUrl: './data-bl.page.html',\r\n  styleUrls: ['./data-bl.page.scss'],\r\n})\r\nexport class DataBLPage implements AfterViewInit, OnInit {\r\n  @ViewChild('topSwiper', { static: true }) topSwiper: ElementRef | undefined;\r\n  @ViewChild('bottomSwiper', { static: true }) bottomSwiper: ElementRef | undefined;\r\n\r\n  currentSlideProducts: any[] = [];\r\n  currentProductIndex: number = 1;\r\n  currentPageIndex: number = 1;\r\n\r\n  slidesData: TransformedDocData[] = [];\r\n  navCtrl = inject(NavController);\r\n  signalService = inject(SignalService);\r\n  loadingController = inject(LoadingController);\r\n  alertController = inject(AlertController);\r\n\r\n  // slidesData = [\r\n  //   {\r\n  //     image: 'assets/doc1.jpg',\r\n  //     title: 'Scan 01:11:2023 01:57:06',\r\n  //     date: 'Aujourd\\'hui',\r\n  //     page_index: '1',\r\n  //     products: [\r\n  //       { designation: 'REPADINA 10OVUL OV', quantity: 2, expiryDate: '2023-12-01', ppv: 99.00, pph: 66.50, total: 133.00 },\r\n  //       { designation: 'APIXOL SPR AD 30M AE', quantity: 3, expiryDate: '2024-01-01', ppv: 153.40, pph: 101.00, total: 303.00 },\r\n  //       { designation: 'MAXICLAV 1G/125MG 24SACH SA', quantity: 4, expiryDate: '2024-04-01', ppv: 36.70, pph: 24.00, total: 96.00 },\r\n  //     ]\r\n  //   },\r\n  //   {\r\n  //     image: 'assets/doc2.png',\r\n  //     title: 'Scan 01:11:2023 02:57:06',\r\n  //     date: 'Aujourd\\'hui',\r\n  //     page_index: '2',\r\n  //     products: [\r\n  //       { designation: 'BIOMARTIAL 30 GELULE', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },\r\n  //       { designation: 'ACFOL 5MG BT 28CP', quantity: 10, expiryDate: '2024-02-01', ppv: 26.20, pph: 17.34, total: 173.39 },\r\n  //       { designation: 'BIOMARTIAL PLUS 30CP', quantity: 2, expiryDate: '2025-01-01', ppv: 133.00, pph: 93.10, total: 186.20 },\r\n  //       { designation: 'MENOPHYT BT/30CPS', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },\r\n  //     ]\r\n  //   },\r\n  //   {\r\n  //     image: 'assets/doc3.png',\r\n  //     title: 'Scan 01:11:2023 03:57:06',\r\n  //     date: 'Aujourd\\'hui',\r\n  //     page_index: '3',\r\n  //     products: [\r\n  //       { designation: 'CARBOFLORE BT/30GLLES', quantity: 2, expiryDate: '2023-12-01', ppv: 99.00, pph: 66.50, total: 133.00 },\r\n  //       { designation: 'DIGESTAL BT/30CPS', quantity: 3, expiryDate: '2024-01-02', ppv: 153.40, pph: 101.00, total: 303.00 },\r\n  //       { designation: 'FORTIVISION BT/30DG', quantity: 4, expiryDate: '2024-04-24', ppv: 36.70, pph: 24.00, total: 96.00 },\r\n  //     ]\r\n  //   },\r\n  //   {\r\n  //     image: 'assets/doc2.png',\r\n  //     title: 'Scan 01:11:2023 02:57:06',\r\n  //     date: 'Aujourd\\'hui',\r\n  //     page_index: '2',\r\n  //     products: [\r\n  //       { designation: 'LEVUPHTA 0.05% COLLYRE', quantity: 2, expiryDate: '2023-12-01', ppv: 99.00, pph: 66.50, total: 133.00 },\r\n  //       { designation: 'CETIRAL 10MG BT/15 CP', quantity: 3, expiryDate: '2024-01-01', ppv: 153.40, pph: 101.00, total: 303.00 },\r\n  //       { designation: 'PURCARB BTE/30 GELULES', quantity: 4, expiryDate: '2024-04-01', ppv: 36.70, pph: 24.00, total: 96.00 },\r\n  //     ]\r\n  //   },\r\n  //   {\r\n  //     image: 'assets/doc3.png',\r\n  //     title: 'Scan 01:11:2023 03:57:06',\r\n  //     date: 'Aujourd\\'hui',\r\n  //     page_index: '3',\r\n  //     products: [\r\n  //       { designation: 'CATAFLAM 50MG BT/10 CP', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },\r\n  //       { designation: 'ANAPRED 20MG BTE/20 CPS', quantity: 10, expiryDate: '2024-02-01', ppv: 26.20, pph: 17.34, total: 173.39 },\r\n  //       { designation: 'NEOFORTAN 160MG BT/10 CP EF', quantity: 2, expiryDate: '2025-01-01', ppv: 133.00, pph: 93.10, total: 186.20 },\r\n  //       { designation: 'CARDIX 6.25 MG BT/28 CP', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },\r\n  //     ]\r\n  //   },\r\n  // ];\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {\r\n    this.slidesData = this.signalService.getTransformedData();\r\n    if(this.slidesData.length === 0) {\r\n      this.navCtrl.navigateBack('/scan-bl');\r\n    }\r\n    this.topSwiper?.nativeElement.swiper.update();\r\n    this.bottomSwiper?.nativeElement.swiper.update();\r\n    console.log('Received data:', this.slidesData);\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.initSwipers();\r\n  }\r\n\r\n  onSlideChange(){\r\n    console.log(\"changedslidechange\");\r\n    this.initSwipers()\r\n  }\r\n  initSwipers() {\r\n    if (this.topSwiper && this.topSwiper.nativeElement && this.bottomSwiper && this.bottomSwiper.nativeElement) {\r\n      const topSwiperInstance = this.topSwiper.nativeElement.swiper;\r\n      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;\r\n      this.updateBottomSwiper();\r\n    }\r\n  }\r\n\r\n  updateBottomSwiper() {\r\n\r\n    if (this.topSwiper && this.topSwiper.nativeElement) {\r\n      const topSwiperInstance = this.topSwiper.nativeElement.swiper;\r\n      const activeIndex = topSwiperInstance.realIndex;\r\n      this.currentPageIndex = activeIndex + 1; // Update the current page index\r\n      this.currentSlideProducts = this.slidesData[activeIndex]?.products || [];\r\n      // this.currentSlideProducts = this.slidesData[activeIndex].products;\r\n      this.updateProductIndex();\r\n    }\r\n  }\r\n\r\n  updateProductIndex() {\r\n    if (this.bottomSwiper && this.bottomSwiper.nativeElement) {\r\n      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;\r\n      const activeProductIndex = bottomSwiperInstance.realIndex;\r\n      this.currentProductIndex = activeProductIndex + 1;\r\n    }\r\n  }\r\n\r\n  slideNext() {\r\n    if (this.bottomSwiper && this.bottomSwiper.nativeElement) {\r\n      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;\r\n      bottomSwiperInstance.slideNext();\r\n      this.updateProductIndex();\r\n    }\r\n  }\r\n\r\n  slidePrev() {\r\n    if (this.bottomSwiper && this.bottomSwiper.nativeElement) {\r\n      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;\r\n      bottomSwiperInstance.slidePrev();\r\n      this.updateProductIndex();\r\n    }\r\n  }\r\n\r\n\r\n  async NewBL() {\r\n    const alert = await this.alertController.create({\r\n      header: 'Voulez-vous créer un nouveau document ?',\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button-rename-doc cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Nouveau BL',\r\n          cssClass: 'custom-alert-button-rename-doc rename',\r\n          handler: () => {\r\n            this.signalService.removeAllData();\r\n            localStorage.removeItem('selectedSupplier');\r\n            this.navCtrl.navigateForward('/scan-bl');\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n\r\n  async EditCurrentBL(){\r\n    const alert = await this.alertController.create({\r\n      header: 'Voulez-vous vraiment modifier les pages de ce document ?',\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button-rename-doc cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Modifier',\r\n          cssClass: 'custom-alert-button-rename-doc rename',\r\n          handler: () => {\r\n            this.navCtrl.navigateRoot('/process-doc');\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n}\r\n", "<ion-header>\r\n  <ion-toolbar>\r\n    <ion-title>Extraction des données</ion-title>\r\n    <!-- <ion-buttons slot=\"end\">\r\n      <ion-button>\r\n        <ion-icon slot=\"icon-only\" name=\"search-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons> -->\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content class=\"data-bl-wrapper\">\r\n  <h2 class=\"section-title\">Pages</h2>\r\n  <swiper-container\r\n    effect=\"coverflow\"\r\n    grab-cursor=\"true\"\r\n    centered-slides=\"true\"\r\n    slides-per-view=\"auto\"\r\n    coverflow-effect-rotate=\"50\"\r\n    coverflow-effect-stretch=\"0\"\r\n    coverflow-effect-depth=\"100\"\r\n    coverflow-effect-modifier=\"1\"\r\n    coverflow-effect-slide-shadows=\"true\"\r\n    class=\"swiper top-swiper\"\r\n    #topSwiper\r\n    (swiperslidechange)=\"onSlideChange()\"\r\n  >\r\n    <swiper-slide *ngFor=\"let slide of slidesData\" class=\"test\">\r\n      <ion-row>\r\n        <ion-col size=\"12\" class=\"slide-content\">\r\n          <ion-card class=\"card-doc\">\r\n            <img [src]=\"slide.image\" class=\"slide-image\" />\r\n            <div class=\"content-global-card\">\r\n              <ion-card-header>\r\n                <ion-card-subtitle>{{ slide.title }}</ion-card-subtitle>\r\n              </ion-card-header>\r\n\r\n              <ion-card-content>\r\n                <span>{{ slide.date }}</span>\r\n                <span>Page {{ slide.page_index }}</span>\r\n              </ion-card-content>\r\n            </div>\r\n          </ion-card>\r\n        </ion-col>\r\n      </ion-row>\r\n    </swiper-slide>\r\n  </swiper-container>\r\n\r\n  <ion-card class=\"card-data-bl\">\r\n    <ion-card-header>\r\n      <app-custom-icon name=\"arrow-left\" (click)=\"slidePrev()\"></app-custom-icon>\r\n      \r\n      <div class=\"content-header\">\r\n        <ion-card-title>Produit {{ currentProductIndex }} / {{ currentSlideProducts.length }}</ion-card-title>\r\n        <ion-card-subtitle>Page {{ currentPageIndex }}</ion-card-subtitle>\r\n      </div>\r\n      <app-custom-icon name=\"arrow-right\" (click)=\"slideNext()\"></app-custom-icon>\r\n\r\n    </ion-card-header>\r\n\r\n    <hr class=\"hr-card\">\r\n\r\n    <swiper-container class=\"bottom-swiper\" #bottomSwiper>\r\n      <swiper-slide *ngFor=\"let product of currentSlideProducts\">\r\n        <ion-card-content>\r\n          <div class=\"data-bl\">\r\n            <ion-row>\r\n              <ion-col>\r\n                <ion-item lines=\"none\" class=\"input-item\">\r\n                  <ion-input\r\n                    label=\"Désignation\"\r\n                    labelPlacement=\"stacked\"\r\n                    [clearInput]=\"true\"\r\n                    placeholder=\"Enter la Désignation du produit ...\"\r\n                    value=\"{{ product.designation }}\"\r\n                  >\r\n                  </ion-input>\r\n                </ion-item>\r\n              </ion-col>\r\n            </ion-row>\r\n\r\n            <ion-row>\r\n              <ion-col>\r\n                <ion-item lines=\"none\" class=\"input-item\">\r\n                  <ion-input\r\n                    label=\"Quantité Livré\"\r\n                    labelPlacement=\"stacked\"\r\n                    [clearInput]=\"true\"\r\n                    placeholder=\"Enter la Quantité livré\"\r\n                    type=\"number\"\r\n                    value=\"{{ product.quantity }}\"\r\n                  >\r\n                  </ion-input>\r\n                </ion-item>\r\n              </ion-col>\r\n              <ion-col>\r\n                <ion-item lines=\"none\" class=\"input-item input-item-date\">\r\n                  <ion-input\r\n                    label=\"Date de péremption\"\r\n                    labelPlacement=\"stacked\"\r\n                    [clearInput]=\"true\"\r\n                    placeholder=\"Enter la Date de péremption\"\r\n                    type=\"date\"\r\n                    value=\"{{ product.expiryDate }}\"\r\n                  >\r\n                  </ion-input>\r\n                </ion-item>\r\n              </ion-col>\r\n            </ion-row>\r\n\r\n            <ion-row>\r\n              <ion-col>\r\n                <ion-item lines=\"none\" class=\"input-item\">\r\n                  <ion-input\r\n                    label=\"Prix (PPV)\"\r\n                    labelPlacement=\"stacked\"\r\n                    [clearInput]=\"true\"\r\n                    placeholder=\"Enter le Prix (PPV)\"\r\n                    type=\"number\"\r\n                    value=\"{{ product.ppv }}\"\r\n                  >\r\n                  </ion-input>\r\n                </ion-item>\r\n              </ion-col>\r\n              <ion-col>\r\n                <ion-item lines=\"none\" class=\"input-item\">\r\n                  <ion-input\r\n                    label=\"Prix (PPH)\"\r\n                    labelPlacement=\"stacked\"\r\n                    [clearInput]=\"true\"\r\n                    placeholder=\"Enter la Prix (PPH)\"\r\n                    type=\"number\"\r\n                    value=\"{{ product.pph }}\"\r\n                  >\r\n                  </ion-input>\r\n                </ion-item>\r\n              </ion-col>\r\n            </ion-row>\r\n\r\n            <ion-row>\r\n              <ion-col>\r\n                <ion-item lines=\"none\" class=\"input-item\">\r\n                  <ion-input\r\n                    label=\"Total (TTC)\"\r\n                    labelPlacement=\"stacked\"\r\n                    [clearInput]=\"true\"\r\n                    placeholder=\"Enter le Total (TTC)\"\r\n                    type=\"number\"\r\n                    value=\"{{ product.total }}\"\r\n                  >\r\n                  </ion-input>\r\n                </ion-item>\r\n              </ion-col>\r\n            </ion-row>\r\n          </div>\r\n        </ion-card-content>\r\n      </swiper-slide>\r\n    </swiper-container>\r\n  </ion-card>\r\n\r\n</ion-content>\r\n\r\n<ion-footer>\r\n  <ion-toolbar>\r\n    <ion-buttons>\r\n      <ion-button class=\"menu-button active\" size=\"small\" (click)=\"EditCurrentBL()\">\r\n        <app-custom-icon name=\"files\"></app-custom-icon>\r\n      </ion-button>\r\n      <ion-button class=\"menu-button-middle\" (click)=\"NewBL()\">\r\n        <app-custom-icon name=\"extract\"></app-custom-icon>\r\n        <span>NOUVEAU</span>\r\n      </ion-button>\r\n      <ion-button class=\"menu-button\" size=\"small\" [routerLink]=\"['/profile']\">\r\n        <app-custom-icon name=\"settings\"></app-custom-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-footer>\r\n"], "names": ["RouterModule", "DataBLPage", "routes", "path", "component", "DataBLPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SharedModule", "DataBLPageModule", "declarations", "inject", "NavController", "LoadingController", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SignalService", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "slide_r2", "image", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "title", "date", "ɵɵtextInterpolate1", "page_index", "ɵɵpropertyInterpolate", "product_r3", "designation", "quantity", "expiryDate", "ppv", "pph", "total", "constructor", "currentSlideProducts", "currentProductIndex", "currentPageIndex", "slidesData", "navCtrl", "signalService", "loadingController", "alertController", "ngOnInit", "_this$topSwiper", "_this$bottomSwiper", "getTransformedData", "length", "navigateBack", "topSwiper", "nativeElement", "swiper", "update", "bottomSwiper", "console", "log", "ngAfterViewInit", "initSwipers", "onSlideChange", "topSwiperInstance", "bottomSwiperInstance", "updateBottomSwiper", "_this$slidesData$acti", "activeIndex", "realIndex", "products", "updateProductIndex", "activeProductIndex", "slideNext", "slidePrev", "NewBL", "_this", "_asyncToGenerator", "alert", "create", "header", "buttons", "text", "role", "cssClass", "handler", "removeAllData", "localStorage", "removeItem", "navigateForward", "present", "EditCurrentBL", "_this2", "navigateRoot", "selectors", "viewQuery", "DataBLPage_Query", "rf", "ctx", "ɵɵlistener", "DataBLPage_Template_swiper_container_swiperslidechange_7_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵtemplate", "DataBLPage_swiper_slide_9_Template", "DataBLPage_Template_app_custom_icon_click_12_listener", "DataBLPage_Template_app_custom_icon_click_18_listener", "DataBLPage_swiper_slide_22_Template", "DataBLPage_Template_ion_button_click_26_listener", "DataBLPage_Template_ion_button_click_28_listener", "ɵɵtextInterpolate2", "ɵɵpureFunction0", "_c2"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}