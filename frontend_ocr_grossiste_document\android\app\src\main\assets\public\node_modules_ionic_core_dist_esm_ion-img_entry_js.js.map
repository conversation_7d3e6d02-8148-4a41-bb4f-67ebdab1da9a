{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-img_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAC9C;AACF;AAE7D,MAAMY,MAAM,GAAG,oMAAoM;AACnN,MAAMC,YAAY,GAAGD,MAAM;AAE3B,MAAME,GAAG,GAAG,MAAM;EACdC,WAAWA,CAACC,OAAO,EAAE;IACjBf,qDAAgB,CAAC,IAAI,EAAEe,OAAO,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAGd,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACe,aAAa,GAAGf,qDAAW,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IAC1D,IAAI,CAACgB,QAAQ,GAAGhB,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACiB,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACH,aAAa,CAACI,IAAI,CAAC,CAAC;IAC7B,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACJ,QAAQ,CAACG,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACE,OAAO,GAAGC,SAAS;IACxB,IAAI,CAACC,SAAS,GAAGD,SAAS;IAC1B,IAAI,CAACE,GAAG,GAAGF,SAAS;IACpB,IAAI,CAACG,GAAG,GAAGH,SAAS;EACxB;EACAI,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,KAAK,CAAC,CAAC;EAChB;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACX,mBAAmB,GAAGX,uDAAiB,CAAC,IAAI,CAACuB,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC;EACxE;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACH,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACF,GAAG,KAAKH,SAAS,EAAE;MACxB;IACJ;IACA,IAAI,OAAOS,MAAM,KAAK,WAAW,IAC7B,sBAAsB,IAAIA,MAAM,IAChC,2BAA2B,IAAIA,MAAM,IACrC,gBAAgB,IAAIA,MAAM,CAACC,yBAAyB,CAACC,SAAS,EAAE;MAChE,IAAI,CAACC,QAAQ,CAAC,CAAC;MACf,IAAI,CAACC,EAAE,GAAG,IAAIC,oBAAoB,CAAEC,IAAI,IAAK;QACzC;AAChB;AACA;AACA;AACA;QACgB,IAAIA,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAACC,cAAc,EAAE;UACtC,IAAI,CAACC,IAAI,CAAC,CAAC;UACX,IAAI,CAACN,QAAQ,CAAC,CAAC;QACnB;MACJ,CAAC,CAAC;MACF,IAAI,CAACC,EAAE,CAACM,OAAO,CAAC,IAAI,CAACZ,EAAE,CAAC;IAC5B,CAAC,MACI;MACD;MACAa,UAAU,CAAC,MAAM,IAAI,CAACF,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;IACtC;EACJ;EACAA,IAAIA,CAAA,EAAG;IACH,IAAI,CAACjB,SAAS,GAAG,IAAI,CAACH,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAG,IAAI,CAACI,GAAG;IACvB,IAAI,CAACX,cAAc,CAACK,IAAI,CAAC,CAAC;EAC9B;EACAe,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,EAAE,EAAE;MACT,IAAI,CAACA,EAAE,CAACQ,UAAU,CAAC,CAAC;MACpB,IAAI,CAACR,EAAE,GAAGb,SAAS;IACvB;EACJ;EACAsB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEvB,OAAO;MAAEG,GAAG;MAAEN,MAAM;MAAEK,SAAS;MAAEN;IAAoB,CAAC,GAAG,IAAI;IACrE,MAAM;MAAE4B;IAAU,CAAC,GAAG5B,mBAAmB;IACzC,OAAQhB,qDAAC,CAACE,iDAAI,EAAE;MAAE2C,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEvC,4DAAU,CAAC,IAAI;IAAE,CAAC,EAAEP,qDAAC,CAAC,KAAK,EAAE;MAAE6C,GAAG,EAAE,0CAA0C;MAAEE,QAAQ,EAAE,OAAO;MAAEvB,GAAG,EAAEJ,OAAO;MAAEG,GAAG,EAAEA,GAAG;MAAEN,MAAM,EAAEA,MAAM;MAAEE,OAAO,EAAEG,SAAS;MAAE0B,IAAI,EAAE,OAAO;MAAEJ,SAAS,EAAEK,WAAW,CAACL,SAAS;IAAE,CAAC,CAAC,CAAC;EACjS;EACA,IAAIhB,EAAEA,CAAA,EAAG;IAAE,OAAOxB,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW8C,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,KAAK,EAAE,CAAC,YAAY;IACxB,CAAC;EAAE;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,WAAW,GAAIL,SAAS,IAAK;EAC/B,QAAQA,SAAS;IACb,KAAK,MAAM;MACP,OAAO,IAAI;IACf,KAAK,OAAO;MACR,OAAO,KAAK;IAChB;MACI,OAAOvB,SAAS;EACxB;AACJ,CAAC;AACDX,GAAG,CAACyC,KAAK,GAAG1C,YAAY", "sources": ["./node_modules/@ionic/core/dist/esm/ion-img.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-c71c5417.js';\nimport { h as inheritAttributes } from './helpers-da915de8.js';\nimport { b as getIonMode } from './ionic-global-b9c0d1da.js';\n\nconst imgCss = \":host{display:block;-o-object-fit:contain;object-fit:contain}img{display:block;width:100%;height:100%;-o-object-fit:inherit;object-fit:inherit;-o-object-position:inherit;object-position:inherit}\";\nconst IonImgStyle0 = imgCss;\n\nconst Img = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionImgWillLoad = createEvent(this, \"ionImgWillLoad\", 7);\n        this.ionImgDidLoad = createEvent(this, \"ionImgDidLoad\", 7);\n        this.ionError = createEvent(this, \"ionError\", 7);\n        this.inheritedAttributes = {};\n        this.onLoad = () => {\n            this.ionImgDidLoad.emit();\n        };\n        this.onError = () => {\n            this.ionError.emit();\n        };\n        this.loadSrc = undefined;\n        this.loadError = undefined;\n        this.alt = undefined;\n        this.src = undefined;\n    }\n    srcChanged() {\n        this.addIO();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['draggable']);\n    }\n    componentDidLoad() {\n        this.addIO();\n    }\n    addIO() {\n        if (this.src === undefined) {\n            return;\n        }\n        if (typeof window !== 'undefined' &&\n            'IntersectionObserver' in window &&\n            'IntersectionObserverEntry' in window &&\n            'isIntersecting' in window.IntersectionObserverEntry.prototype) {\n            this.removeIO();\n            this.io = new IntersectionObserver((data) => {\n                /**\n                 * On slower devices, it is possible for an intersection observer entry to contain multiple\n                 * objects in the array. This happens when quickly scrolling an image into view and then out of\n                 * view. In this case, the last object represents the current state of the component.\n                 */\n                if (data[data.length - 1].isIntersecting) {\n                    this.load();\n                    this.removeIO();\n                }\n            });\n            this.io.observe(this.el);\n        }\n        else {\n            // fall back to setTimeout for Safari and IE\n            setTimeout(() => this.load(), 200);\n        }\n    }\n    load() {\n        this.loadError = this.onError;\n        this.loadSrc = this.src;\n        this.ionImgWillLoad.emit();\n    }\n    removeIO() {\n        if (this.io) {\n            this.io.disconnect();\n            this.io = undefined;\n        }\n    }\n    render() {\n        const { loadSrc, alt, onLoad, loadError, inheritedAttributes } = this;\n        const { draggable } = inheritedAttributes;\n        return (h(Host, { key: '14d24d65ec8e5522192ca58035264971b1ab883b', class: getIonMode(this) }, h(\"img\", { key: '345ba155a5fdce5e66c397a599b7333d37d9cb1d', decoding: \"async\", src: loadSrc, alt: alt, onLoad: onLoad, onError: loadError, part: \"image\", draggable: isDraggable(draggable) })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"src\": [\"srcChanged\"]\n    }; }\n};\n/**\n * Enumerated strings must be set as booleans\n * as Stencil will not render 'false' in the DOM.\n * The need to explicitly render draggable=\"true\"\n * as only certain elements are draggable by default.\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/draggable.\n */\nconst isDraggable = (draggable) => {\n    switch (draggable) {\n        case 'true':\n            return true;\n        case 'false':\n            return false;\n        default:\n            return undefined;\n    }\n};\nImg.style = IonImgStyle0;\n\nexport { Img as ion_img };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "inheritAttributes", "b", "getIonMode", "imgCss", "IonImgStyle0", "Img", "constructor", "hostRef", "ionImgWillLoad", "ionImgDidLoad", "ionError", "inheritedAttributes", "onLoad", "emit", "onError", "loadSrc", "undefined", "loadError", "alt", "src", "srcChanged", "addIO", "componentWillLoad", "el", "componentDidLoad", "window", "IntersectionObserverEntry", "prototype", "removeIO", "io", "IntersectionObserver", "data", "length", "isIntersecting", "load", "observe", "setTimeout", "disconnect", "render", "draggable", "key", "class", "decoding", "part", "isDraggable", "watchers", "style", "ion_img"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}