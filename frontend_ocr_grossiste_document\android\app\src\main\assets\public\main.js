(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["main"],{

/***/ 94114:
/*!***************************************!*\
  !*** ./src/app/app-routing.module.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppRoutingModule: () => (/* binding */ AppRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _interceptors_auth_guard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interceptors/auth.guard */ 35934);
/* harmony import */ var _interceptors_onboardin_guard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./interceptors/onboardin.guard */ 52972);
/* harmony import */ var _interceptors_public_guard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./interceptors/public.guard */ 48105);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
var _AppRoutingModule;






const routes = [{
  path: 'home',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_welcome_welcome_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./welcome/welcome.module */ 18255)).then(m => m.WelcomePageModule)
}, {
  path: '',
  redirectTo: 'onboarding',
  pathMatch: 'full'
}, {
  path: 'onboarding',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_onboarding_onboarding_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./onboarding/onboarding.module */ 97543)).then(m => m.OnboardingPageModule),
  canActivate: [_interceptors_onboardin_guard__WEBPACK_IMPORTED_MODULE_1__.OnboardingGuard, _interceptors_public_guard__WEBPACK_IMPORTED_MODULE_2__.PublicGuard]
}, {
  path: 'welcome',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_welcome_welcome_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./welcome/welcome.module */ 18255)).then(m => m.WelcomePageModule),
  canActivate: [_interceptors_public_guard__WEBPACK_IMPORTED_MODULE_2__.PublicGuard]
}, {
  path: 'login',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_login_login_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./login/login.module */ 91307)).then(m => m.LoginPageModule),
  canActivate: [_interceptors_public_guard__WEBPACK_IMPORTED_MODULE_2__.PublicGuard]
}, {
  path: 'guide',
  loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_shared_shared_module_ts"), __webpack_require__.e("src_app_guide_guide_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./guide/guide.module */ 62763)).then(m => m.GuidePageModule),
  canActivate: [_interceptors_auth_guard__WEBPACK_IMPORTED_MODULE_0__.AuthGuard]
}, {
  path: 'scan-bl',
  loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_shared_shared_module_ts"), __webpack_require__.e("default-src_app_services_scanner_service_ts-src_app_services_websocket_service_ts-node_module-2d6712"), __webpack_require__.e("common"), __webpack_require__.e("src_app_scan-bl_scan-bl_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./scan-bl/scan-bl.module */ 28927)).then(m => m.ScanBLPageModule),
  canActivate: [_interceptors_auth_guard__WEBPACK_IMPORTED_MODULE_0__.AuthGuard]
}, {
  path: 'doc-list',
  loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_shared_shared_module_ts"), __webpack_require__.e("common"), __webpack_require__.e("src_app_doc-list_doc-list_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./doc-list/doc-list.module */ 35703)).then(m => m.DocListPageModule),
  canActivate: [_interceptors_auth_guard__WEBPACK_IMPORTED_MODULE_0__.AuthGuard]
}, {
  path: 'data-bl',
  loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_shared_shared_module_ts"), __webpack_require__.e("common"), __webpack_require__.e("src_app_data-bl_data-bl_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./data-bl/data-bl.module */ 80715)).then(m => m.DataBLPageModule),
  canActivate: [_interceptors_auth_guard__WEBPACK_IMPORTED_MODULE_0__.AuthGuard]
}, {
  path: 'crop-doc',
  loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_shared_shared_module_ts"), __webpack_require__.e("common"), __webpack_require__.e("src_app_crop-doc_crop-doc_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./crop-doc/crop-doc.module */ 63495)).then(m => m.CropDocPageModule),
  canActivate: [_interceptors_auth_guard__WEBPACK_IMPORTED_MODULE_0__.AuthGuard]
}, {
  path: 'process-doc',
  loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_shared_shared_module_ts"), __webpack_require__.e("common"), __webpack_require__.e("src_app_process-doc_process-doc_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./process-doc/process-doc.module */ 62787)).then(m => m.ProcessDocPageModule),
  canActivate: [_interceptors_auth_guard__WEBPACK_IMPORTED_MODULE_0__.AuthGuard]
}, {
  path: 'network-error',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_network-error_network-error_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./network-error/network-error.module */ 87731)).then(m => m.NetworkErrorPageModule)
}, {
  path: 'request-error',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_request-error_request-error_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./request-error/request-error.module */ 49007)).then(m => m.RequestErrorPageModule)
}, {
  path: 'realtime-contours',
  loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_shared_shared_module_ts"), __webpack_require__.e("src_app_realtime-contours_realtime-contours_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./realtime-contours/realtime-contours.module */ 21343)).then(m => m.RealtimeContoursPageModule),
  canActivate: [_interceptors_auth_guard__WEBPACK_IMPORTED_MODULE_0__.AuthGuard]
}, {
  path: 'data-bl-success',
  loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_shared_shared_module_ts"), __webpack_require__.e("common"), __webpack_require__.e("src_app_data-bl-success_data-bl-success_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./data-bl-success/data-bl-success.module */ 53775)).then(m => m.DataBlSuccessPageModule)
}, {
  path: 'profile',
  loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_profile_profile_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./profile/profile.module */ 4219)).then(m => m.ProfilePageModule)
}, {
  path: 'medicament-ocr',
  loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_shared_shared_module_ts"), __webpack_require__.e("default-src_app_services_scanner_service_ts-src_app_services_websocket_service_ts-node_module-2d6712"), __webpack_require__.e("src_app_medicament-ocr_medicament-ocr_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./medicament-ocr/medicament-ocr.module */ 86985)).then(m => m.MedicamentOcrPageModule)
}];
class AppRoutingModule {}
_AppRoutingModule = AppRoutingModule;
_AppRoutingModule.ɵfac = function AppRoutingModule_Factory(t) {
  return new (t || _AppRoutingModule)();
};
_AppRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
  type: _AppRoutingModule
});
_AppRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterModule.forRoot(routes, {
    preloadingStrategy: _angular_router__WEBPACK_IMPORTED_MODULE_4__.PreloadAllModules
  }), _angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](AppRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterModule]
  });
})();

/***/ }),

/***/ 20092:
/*!**********************************!*\
  !*** ./src/app/app.component.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppComponent: () => (/* binding */ AppComponent)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var swiper_element_bundle__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swiper/element/bundle */ 10493);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../environments/environment */ 45312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _services_storage_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./services/storage.service */ 57291);
/* harmony import */ var _services_api_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./services/api.service */ 3366);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);

var _AppComponent;


 // Import the environment






const _c0 = a0 => ({
  "web-environment": a0
});
(0,swiper_element_bundle__WEBPACK_IMPORTED_MODULE_1__.register)();
class AppComponent {
  constructor(storageService, router, apiService, platform) {
    this.storageService = storageService;
    this.router = router;
    this.apiService = apiService;
    this.platform = platform;
    // constructor(private networkService: NetworkService, private navCtrl: NavController) {
    //   this.networkService.getNetworkStatus().subscribe((connected: boolean) => {
    //     if (!connected) {
    //       this.navCtrl.navigateRoot('/network-error');
    //     }
    //   });
    // }
    this.environment = _environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment;
    this.initializeApp();
  }
  initializeApp() {
    this.platform.ready().then(() => {
      this.forceLightMode();
      this.checkEnvironmentAndClearStorage();
    });
  }
  forceLightMode() {
    // Remove dark mode from body
    document.body.classList.remove('dark');
    document.documentElement.classList.remove('dark');
    // Add light mode
    document.body.classList.add('light');
    document.documentElement.classList.add('light');
    // Set attribute
    document.body.setAttribute('data-theme', 'light');
    // Force color scheme
    const meta = document.createElement('meta');
    meta.name = 'color-scheme';
    meta.content = 'light';
    document.head.appendChild(meta);
  }
  ngOnInit() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      // Ensure light mode is applied
      _this.forceLightMode();
      // Initialize storage
      yield _this.storageService.init();
      // Check if the user has seen onboarding
      const hasSeenOnboarding = yield _this.storageService.get('hasSeenOnboarding');
      if (!hasSeenOnboarding) {
        // Navigate to the onboarding screen if not seen before
        _this.router.navigate(['/onboarding']);
      }
      // Handle route redirection for logged-in users
      _this.router.events.subscribe(event => {
        if (event instanceof _angular_router__WEBPACK_IMPORTED_MODULE_5__.NavigationStart) {
          if (!_this.apiService.isLoggedIn() && !_this.isPublicRoute(event.url)) {
            _this.router.navigate(['/welcome']);
          }
        }
      });
    })();
  }
  isPublicRoute(url) {
    const publicRoutes = ['/login', '/onboarding', '/welcome', '/network-error', '/request-error'];
    return publicRoutes.includes(url);
  }
  checkEnvironmentAndClearStorage() {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.production) {
        console.log('Non-production environment detected. Clearing storage...');
        yield _this2.storageService.clear(); // Clear all storage
      }
    })();
  }
}
_AppComponent = AppComponent;
_AppComponent.ɵfac = function AppComponent_Factory(t) {
  return new (t || _AppComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_storage_service__WEBPACK_IMPORTED_MODULE_3__.StorageService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_api_service__WEBPACK_IMPORTED_MODULE_4__.ApiService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_7__.Platform));
};
_AppComponent.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
  type: _AppComponent,
  selectors: [["app-root"]],
  decls: 2,
  vars: 3,
  consts: [[3, "ngClass"]],
  template: function AppComponent_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "ion-app", 0);
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "ion-router-outlet");
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](1, _c0, ctx.environment.platform === "web"));
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonApp, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonRouterOutlet],
  styles: [".web-environment[_ngcontent-%COMP%] {\n  max-width: 920px;\n  margin: auto;\n  overflow: hidden;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vLi4vLi4vV29yayUyMF9fQWJkZXJyYWhtYW5lX291aG5hL09DUl9ET0NVTUVOVF9HUk9TU0lTVEUvRnJvbnRlbmQlMjBvY3IlMjBncm9zc2lzdGUlMjBkb2N1bWVudC9mcm9udGVuZF9vY3JfZ3Jvc3Npc3RlX2RvY3VtZW50L3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7QUNDSiIsInNvdXJjZXNDb250ZW50IjpbIi53ZWItZW52aXJvbm1lbnR7XHJcbiAgICBtYXgtd2lkdGg6IDkyMHB4O1xyXG4gICAgbWFyZ2luOiBhdXRvO1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufSIsIi53ZWItZW52aXJvbm1lbnQge1xuICBtYXgtd2lkdGg6IDkyMHB4O1xuICBtYXJnaW46IGF1dG87XG4gIG92ZXJmbG93OiBoaWRkZW47XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */"]
});

/***/ }),

/***/ 50635:
/*!*******************************!*\
  !*** ./src/app/app.module.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppModule: () => (/* binding */ AppModule)
/* harmony export */ });
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/platform-browser */ 80436);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _app_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app.component */ 20092);
/* harmony import */ var _app_routing_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app-routing.module */ 94114);
/* harmony import */ var _ionic_pwa_elements_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ionic/pwa-elements/loader */ 90466);
/* harmony import */ var _ionic_storage_angular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ionic/storage-angular */ 26817);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common/http */ 46443);
/* harmony import */ var _interceptors_http_error_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./interceptors/http-error.service */ 80401);
/* harmony import */ var _services_network_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./services/network.service */ 32404);
/* harmony import */ var _interceptors_auth_interceptor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./interceptors/auth.interceptor */ 20472);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
var _AppModule;
















class AppModule {
  constructor(sanitizer) {
    this.sanitizer = sanitizer;
    (0,_ionic_pwa_elements_loader__WEBPACK_IMPORTED_MODULE_2__.defineCustomElements)(window);
    // register();
  }
}
_AppModule = AppModule;
_AppModule.ɵfac = function AppModule_Factory(t) {
  return new (t || _AppModule)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵinject"](_angular_platform_browser__WEBPACK_IMPORTED_MODULE_7__.DomSanitizer));
};
_AppModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineNgModule"]({
  type: _AppModule,
  bootstrap: [_app_component__WEBPACK_IMPORTED_MODULE_0__.AppComponent]
});
_AppModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjector"]({
  providers: [{
    provide: _angular_router__WEBPACK_IMPORTED_MODULE_8__.RouteReuseStrategy,
    useClass: _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonicRouteStrategy
  }, {
    provide: _angular_common_http__WEBPACK_IMPORTED_MODULE_10__.HTTP_INTERCEPTORS,
    useClass: _interceptors_auth_interceptor__WEBPACK_IMPORTED_MODULE_5__.AuthInterceptor,
    multi: true
  }, {
    provide: _angular_common_http__WEBPACK_IMPORTED_MODULE_10__.HTTP_INTERCEPTORS,
    useClass: _interceptors_http_error_service__WEBPACK_IMPORTED_MODULE_3__.HttpErrorInterceptor,
    multi: true
  }, _services_network_service__WEBPACK_IMPORTED_MODULE_4__.NetworkService, {
    provide: 'DARK_MODE',
    useValue: false
  }],
  imports: [_angular_platform_browser__WEBPACK_IMPORTED_MODULE_7__.BrowserModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonicModule.forRoot({
    mode: 'ios',
    innerHTMLTemplatesEnabled: true
  }), _app_routing_module__WEBPACK_IMPORTED_MODULE_1__.AppRoutingModule, _ionic_storage_angular__WEBPACK_IMPORTED_MODULE_12__.IonicStorageModule.forRoot(), _angular_common_http__WEBPACK_IMPORTED_MODULE_10__.HttpClientModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsetNgModuleScope"](AppModule, {
    declarations: [_app_component__WEBPACK_IMPORTED_MODULE_0__.AppComponent],
    imports: [_angular_platform_browser__WEBPACK_IMPORTED_MODULE_7__.BrowserModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_11__.IonicModule, _app_routing_module__WEBPACK_IMPORTED_MODULE_1__.AppRoutingModule, _ionic_storage_angular__WEBPACK_IMPORTED_MODULE_12__.IonicStorageModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_10__.HttpClientModule]
  });
})();

/***/ }),

/***/ 35934:
/*!********************************************!*\
  !*** ./src/app/interceptors/auth.guard.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard)
/* harmony export */ });
/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jwt-decode */ 34751);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);
var _AuthGuard;




class AuthGuard {
  constructor(router) {
    this.router = router;
  }
  canActivate() {
    const tokenUser = localStorage.getItem('tokenUser');
    const tokenTenant = localStorage.getItem('tokenTenant');
    const tokenLocal = localStorage.getItem('token');
    const platform = localStorage.getItem('src_app');
    // Check if platform is selected
    if (!platform) {
      this.router.navigate(['/welcome']);
      return false;
    }
    if (platform === 'pharmalien') {
      // Pharmalien platform: only needs tokenUser and tokenLocal
      if (tokenUser && tokenLocal) {
        try {
          const isTokenUserValid = this.checkTokenExpiration(tokenUser);
          const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);
          if (isTokenUserValid && isTokenLocalValid) {
            return true;
          }
        } catch (error) {
          console.error('Token validation error:', error);
        }
      }
    } else if (platform === 'winpluspharma') {
      // WinPlus platform: needs all three tokens
      if (tokenUser && tokenTenant && tokenLocal) {
        try {
          const isTokenUserValid = this.checkTokenExpiration(tokenUser);
          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant);
          const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);
          if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {
            return true;
          }
        } catch (error) {
          console.error('Token validation error:', error);
        }
      }
    }
    // If any required token is missing or expired, redirect to login
    this.router.navigate(['/login']);
    return false;
  }
  checkTokenExpiration(token) {
    const decodedToken = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_0__.jwtDecode)(token);
    const expiration = moment__WEBPACK_IMPORTED_MODULE_1__((decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.exp) * 1000);
    return moment__WEBPACK_IMPORTED_MODULE_1__(new Date()) < expiration;
  }
}
_AuthGuard = AuthGuard;
_AuthGuard.ɵfac = function AuthGuard_Factory(t) {
  return new (t || _AuthGuard)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router));
};
_AuthGuard.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
  token: _AuthGuard,
  factory: _AuthGuard.ɵfac,
  providedIn: 'root'
});

/***/ }),

/***/ 20472:
/*!**************************************************!*\
  !*** ./src/app/interceptors/auth.interceptor.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthInterceptor: () => (/* binding */ AuthInterceptor)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 77919);
/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jwt-decode */ 34751);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs/operators */ 61318);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _services_api_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/api.service */ 3366);
var _AuthInterceptor;





class AuthInterceptor {
  constructor(apiService) {
    this.apiService = apiService;
  }
  intercept(req, next) {
    const tokenUser = localStorage.getItem('tokenUser');
    const tokenTenant = localStorage.getItem('tokenTenant');
    const tokenLocal = localStorage.getItem('token');
    const platform = localStorage.getItem('src_app');
    if (platform === 'pharmalien') {
      // Pharmalien platform: only needs tokenUser and tokenLocal
      if (tokenUser && tokenLocal) {
        if (!this.apiService.isLoggedIn()) {
          this.logout();
          return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.throwError)(() => new Error('Token has expired'));
        }
        const cloned = req.clone({
          headers: req.headers.set('Authorization', `Bearer ${tokenLocal}`).set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`)
        });
        return next.handle(cloned).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => {
          if (error.status === 401 || error.status === 403) {
            this.logout();
          }
          return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.throwError)(() => error);
        }));
      }
    } else if (platform === 'winpluspharma') {
      // WinPlus platform: needs all three tokens
      if (tokenUser && tokenTenant && tokenLocal) {
        if (!this.apiService.isLoggedIn()) {
          this.logout();
          return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.throwError)(() => new Error('Token has expired'));
        }
        const cloned = req.clone({
          headers: req.headers.set('Authorization', `Bearer ${tokenLocal}`).set('AuthorizationTenant', `BearerTenant ${JSON.parse(tokenTenant).accessToken}`).set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`)
        });
        return next.handle(cloned).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.catchError)(error => {
          if (error.status === 401 || error.status === 403) {
            this.logout();
          }
          return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.throwError)(() => error);
        }));
      }
    }
    return next.handle(req);
  }
  isTokenExpired(token) {
    const decodedToken = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_0__.jwtDecode)(token);
    const expirationDate = new Date(decodedToken.exp * 1000);
    return expirationDate < new Date();
  }
  logout() {
    localStorage.removeItem('tokenUser');
    localStorage.removeItem('tokenTenant');
    localStorage.removeItem('token');
    localStorage.removeItem('credentials');
    localStorage.removeItem('ocrMode');
    // Keep src_app to remember platform choice
    // Redirect the user to the login page or show a logout message
  }
}
_AuthInterceptor = AuthInterceptor;
_AuthInterceptor.ɵfac = function AuthInterceptor_Factory(t) {
  return new (t || _AuthInterceptor)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵinject"](_services_api_service__WEBPACK_IMPORTED_MODULE_1__.ApiService));
};
_AuthInterceptor.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineInjectable"]({
  token: _AuthInterceptor,
  factory: _AuthInterceptor.ɵfac
});

/***/ }),

/***/ 80401:
/*!****************************************************!*\
  !*** ./src/app/interceptors/http-error.service.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HttpErrorInterceptor: () => (/* binding */ HttpErrorInterceptor)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 95429);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ 77919);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs/operators */ 36647);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs/operators */ 61318);
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sweetalert2 */ 37581);
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _services_network_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/network.service */ 32404);
var _HttpErrorInterceptor;






class HttpErrorInterceptor {
  constructor(navCtrl, networkService) {
    this.navCtrl = navCtrl;
    this.networkService = networkService;
  }
  intercept(request, next) {
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.from)(this.networkService.isConnected()).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.switchMap)(isConnected => {
      if (!isConnected) {
        // Redirect to network error page if no internet connection
        this.navCtrl.navigateRoot('/network-error');
        // Return an empty observable since we don't want to proceed with the request
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)({
          status: 0,
          message: 'No Internet Connection'
        });
      }
      // Proceed with the request
      return next.handle(request).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.catchError)(error => {
        if (error.status === 401) {
          // Redirect to login page if unauthorized
          sweetalert2__WEBPACK_IMPORTED_MODULE_0___default().fire({
            title: 'Non Autorisé',
            text: 'Vous devez vous connecter pour accéder à cette page.',
            icon: 'error',
            confirmButtonText: 'Login'
          }).then(() => {
            this.navCtrl.navigateRoot('/login');
          });
        }
        // this.navCtrl.navigateRoot('/request-error');
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(error);
      }));
    }));
  }
}
_HttpErrorInterceptor = HttpErrorInterceptor;
_HttpErrorInterceptor.ɵfac = function HttpErrorInterceptor_Factory(t) {
  return new (t || _HttpErrorInterceptor)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵinject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_7__.NavController), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵinject"](_services_network_service__WEBPACK_IMPORTED_MODULE_1__.NetworkService));
};
_HttpErrorInterceptor.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjectable"]({
  token: _HttpErrorInterceptor,
  factory: _HttpErrorInterceptor.ɵfac
});

/***/ }),

/***/ 52972:
/*!*************************************************!*\
  !*** ./src/app/interceptors/onboardin.guard.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OnboardingGuard: () => (/* binding */ OnboardingGuard)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _services_storage_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/storage.service */ 57291);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);

var _OnboardingGuard;



class OnboardingGuard {
  constructor(storageService, router) {
    this.storageService = storageService;
    this.router = router;
  }
  canActivate() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const hasSeenOnboarding = yield _this.storageService.get('hasSeenOnboarding');
      if (hasSeenOnboarding) {
        _this.router.navigate(['/welcome']);
        return false;
      }
      return true;
    })();
  }
}
_OnboardingGuard = OnboardingGuard;
_OnboardingGuard.ɵfac = function OnboardingGuard_Factory(t) {
  return new (t || _OnboardingGuard)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_services_storage_service__WEBPACK_IMPORTED_MODULE_1__.StorageService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router));
};
_OnboardingGuard.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
  token: _OnboardingGuard,
  factory: _OnboardingGuard.ɵfac,
  providedIn: 'root'
});

/***/ }),

/***/ 48105:
/*!**********************************************!*\
  !*** ./src/app/interceptors/public.guard.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PublicGuard: () => (/* binding */ PublicGuard)
/* harmony export */ });
/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jwt-decode */ 34751);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);
var _PublicGuard;




class PublicGuard {
  constructor(router) {
    this.router = router;
  }
  canActivate() {
    const tokenUser = localStorage.getItem('tokenUser');
    const tokenTenant = localStorage.getItem('tokenTenant');
    const tokenLocal = localStorage.getItem('token');
    if (tokenUser && tokenTenant && tokenLocal) {
      try {
        // Check if tokens are valid
        const isTokenUserValid = this.checkTokenExpiration(tokenUser);
        const isTokenTenantValid = this.checkTokenExpiration(tokenTenant);
        const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);
        if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {
          // If user is already authenticated, redirect to scan-bl
          this.router.navigate(['/scan-bl']);
          return false;
        }
      } catch (error) {
        console.error('Token validation error:', error);
      }
    }
    // Allow access to public route if not authenticated
    return true;
  }
  checkTokenExpiration(token) {
    const decodedToken = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_0__.jwtDecode)(token);
    const expiration = moment__WEBPACK_IMPORTED_MODULE_1__((decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.exp) * 1000);
    return moment__WEBPACK_IMPORTED_MODULE_1__(new Date()) < expiration;
  }
}
_PublicGuard = PublicGuard;
_PublicGuard.ɵfac = function PublicGuard_Factory(t) {
  return new (t || _PublicGuard)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router));
};
_PublicGuard.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
  token: _PublicGuard,
  factory: _PublicGuard.ɵfac,
  providedIn: 'root'
});

/***/ }),

/***/ 3366:
/*!*****************************************!*\
  !*** ./src/app/services/api.service.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ApiService: () => (/* binding */ ApiService)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common/http */ 46443);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 77919);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../environments/environment */ 45312);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs/operators */ 98764);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs/operators */ 61318);
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sweetalert2 */ 37581);
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jwt-decode */ 34751);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ionic/angular */ 21507);

var _ApiService;









var OcrMode;
(function (OcrMode) {
  OcrMode["STANDARD"] = "STANDARD";
  OcrMode["MINDEE_ADVANCED"] = "MINDEE_ADVANCED";
})(OcrMode || (OcrMode = {}));
class ApiService {
  constructor(http, alertController) {
    this.http = http;
    this.alertController = alertController;
    this.baseUrl = _environments_environment__WEBPACK_IMPORTED_MODULE_1__.environment.apiUrl; // Ensure your environment file has the correct base URL
    // Set common HTTP options
    this.httpOptions = {
      headers: new _angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpHeaders({
        'Content-Type': 'application/json'
      })
    };
  }
  // Generate a unique job ID
  generateJobId() {
    return 'job_' + Math.random().toString(36).substr(2, 9);
  }
  logRequest(url, method, body) {
    console.log(`Request: ${method} ${url}`, body);
  }
  logResponse(response) {
    console.log('Response:', response);
  }
  handleError(error) {
    console.error('API Error:', error);
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.throwError)(error);
  }
  // Fetch root endpoint
  getRoot() {
    return this.http.get(`${this.baseUrl}/`);
  }
  // Tenant Login
  tenantLogin(request) {
    const url = `${this.baseUrl}/tenant_login`;
    this.logRequest(url, 'POST', request);
    return this.http.post(url, request, this.httpOptions).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.tap)(this.logResponse), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.catchError)(this.handleError));
  }
  // User Login
  userLogin(request, tenantToken) {
    const url = `${this.baseUrl}/login`;
    const headers = new _angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpHeaders({
      'Content-Type': 'application/json',
      'AuthorizationTenant': `BearerTenant ${tenantToken}`
    });
    const bodyWithToken = {
      ...request,
      tenant_token: tenantToken
    };
    this.logRequest(url, 'POST', bodyWithToken);
    return this.http.post(url, bodyWithToken, {
      headers
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.tap)(this.logResponse), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.catchError)(this.handleError));
  }
  // Pharmalien Login
  pharmalienLogin(request) {
    const url = `${this.baseUrl}/pharmalien_login`;
    this.logRequest(url, 'POST', request);
    return this.http.post(url, request, this.httpOptions).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.tap)(this.logResponse), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.catchError)(this.handleError));
  }
  // Logout function
  logout() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      return new Promise( /*#__PURE__*/function () {
        var _ref = (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* (resolve) {
          const alert = yield _this.alertController.create({
            header: 'Déconnexion',
            message: `Confirmer la déconnexion ?`,
            buttons: [{
              text: 'Annuler',
              role: 'cancel',
              cssClass: 'custom-alert-button cancel',
              handler: () => {
                console.log('Confirm Cancel');
                resolve(); // Resolve even if canceled
              }
            }, {
              text: 'Oui',
              cssClass: 'custom-alert-button danger',
              handler: () => {
                localStorage.removeItem('tokenUser');
                localStorage.removeItem('tokenTenant');
                localStorage.removeItem('token');
                localStorage.removeItem('ocrMode');
                localStorage.removeItem('forceSupplierGlobal');
                localStorage.removeItem('selectedSupplier');
                resolve(); // Resolve after logout
              }
            }]
          });
          yield alert.present();
        });
        return function (_x) {
          return _ref.apply(this, arguments);
        };
      }());
    })();
  }
  // Smart Crop API
  smartCrop(image, jobId, isScanner) {
    console.log('API Base URL:', this.baseUrl);
    const formData = new FormData();
    formData.append('image', image);
    formData.append('job_id', jobId); // Include the job ID
    formData.append('isScanner', isScanner.toString()); // Include the isScanner flag
    const url = `${this.baseUrl}/smart_crop/`;
    this.logRequest(url, 'POST', formData);
    return this.http.post(url, formData).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.tap)(this.logResponse), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.catchError)(this.handleError));
  }
  // Magic Pro Filter API
  magicProFilter(image, modelName, randomId, jobId) {
    const formData = new FormData();
    formData.append('image', image);
    formData.append('model_name', modelName);
    formData.append('job_id', jobId !== null && jobId !== void 0 ? jobId : ""); // Include the job ID
    if (randomId) {
      formData.append('random_id', randomId);
    }
    return this.http.post(`${this.baseUrl}/magic_pro_filter/`, formData);
  }
  // Identify Supplier API
  identifySupplier(image, modelName, randomId, jobId) {
    const formData = new FormData();
    formData.append('image', image);
    formData.append('model_name', modelName);
    formData.append('job_id', jobId !== null && jobId !== void 0 ? jobId : ""); // Include the job ID
    if (randomId) {
      formData.append('random_id', randomId);
    }
    return this.http.post(`${this.baseUrl}/identify_supplier/`, formData);
  }
  // Process Image Supp API
  processImageSupp(request, jobId) {
    // const jobId = this.generateJobId();  // Get the job ID
    const modifiedRequest = {
      ...request,
      job_id: jobId
    }; // Include the job ID in the request
    return this.http.post(`${this.baseUrl}/process_image_supp/`, modifiedRequest, this.httpOptions);
  }
  // Process OCR Multi API
  processOcrMulti(images, jobId, random_id, ocrMode = OcrMode.STANDARD) {
    const modifiedImages = images.map(image => ({
      ...image,
      job_id: jobId
    }));
    // Get src_app from localStorage, default to 'winpluspharma'
    const srcApp = localStorage.getItem('src_app') || 'winpluspharma';
    // Create the request body with images, ocrMode, and src_app
    const requestBody = {
      images: modifiedImages,
      ocr_mode: ocrMode.toLowerCase(),
      src_app: srcApp // Include platform source
    };
    return this.http.post(`${this.baseUrl}/process_ocr_multi/`, requestBody, this.httpOptions);
  }
  // Update BL Status API to EN_COURS
  updateBLStatus(blId, status, id_BL_origine, date_BL_origine, supplier_name) {
    return this.http.put(`${this.baseUrl}/winplus/updateStatus/${blId}`, {
      status,
      id_BL_origine,
      date_BL_origine,
      supplier_name
    });
  }
  // Get All Suppliers list
  getAllSuppliers() {
    return this.http.get(`${this.baseUrl}/suppliers`);
  }
  showErrorAlert(message) {
    const messageDisplayed = message !== "" ? message : "Il y a eu une erreur de compréhension de la requête. Veuillez réessayer plus tard.";
    sweetalert2__WEBPACK_IMPORTED_MODULE_2___default().fire({
      icon: 'error',
      title: 'Format de l\'image incorrecte !',
      html: messageDisplayed,
      footer: '<a href="/guide">Comment capturer une image de qualité ?</a>',
      showConfirmButton: false,
      showCloseButton: true,
      customClass: {
        closeButton: 'custom-close-button',
        popup: 'custom-popup',
        footer: 'custom-footer' // Custom class for the footer
      }
    });
    // localStorage.removeItem('ocrMode');
    // localStorage.removeItem('forceSupplierGlobal');
    localStorage.removeItem('selectedSupplier');
  }
  isLoggedIn() {
    const token = localStorage.getItem('token');
    const tokenUser = localStorage.getItem('tokenUser');
    const tokenTenant = localStorage.getItem('tokenTenant');
    const platform = localStorage.getItem('src_app');
    // Check required tokens based on platform
    if (platform === 'pharmalien') {
      // Pharmalien only needs token and tokenUser
      if (!token || !tokenUser) {
        return false;
      }
    } else {
      // WinPlus needs all three tokens
      if (!token || !tokenUser || !tokenTenant) {
        return false;
      }
    }
    try {
      const decodedToken = this.decodeToken(token);
      if (!this.isTokenValid(decodedToken)) {
        return false;
      }
    } catch (e) {
      console.error('Token decoding failed', e);
      return false;
    }
    return true;
  }
  decodeToken(token) {
    return (0,jwt_decode__WEBPACK_IMPORTED_MODULE_3__.jwtDecode)(token);
  }
  isTokenValid(decodedToken) {
    // Check if token has expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decodedToken.exp < currentTime) {
      return false;
    }
    return true;
  }
  // ## -- Medicament API -- ##
  /**
   * Get medicament information and suggestions from OCR
   * @param image Image file to analyze
   * @param jobId Job ID for tracking progress
   * @returns Promise with medicament suggestions
   */
  getMedicamentInfo(image, jobId) {
    console.log('API Base URL:', this.baseUrl);
    const formData = new FormData();
    formData.append('image', image);
    formData.append('job_id', jobId); // Include the job ID
    const url = `${this.baseUrl}/medicament_ocr_tap/`;
    console.log('Making request to:', url);
    return this.http.post(url, formData);
  }
}
_ApiService = ApiService;
_ApiService.ɵfac = function ApiService_Factory(t) {
  return new (t || _ApiService)(_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpClient), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵinject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_9__.AlertController));
};
_ApiService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjectable"]({
  token: _ApiService,
  factory: _ApiService.ɵfac,
  providedIn: 'root'
});

/***/ }),

/***/ 32404:
/*!*********************************************!*\
  !*** ./src/app/services/network.service.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NetworkService: () => (/* binding */ NetworkService)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _capacitor_network__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @capacitor/network */ 5477);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 75797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs */ 63617);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ 18537);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 70271);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs */ 43942);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 37580);

var _NetworkService;




class NetworkService {
  constructor() {
    this.networkStatus = new rxjs__WEBPACK_IMPORTED_MODULE_2__.BehaviorSubject(true);
    this.isOnline$ = (0,rxjs__WEBPACK_IMPORTED_MODULE_3__.merge)((0,rxjs__WEBPACK_IMPORTED_MODULE_4__.fromEvent)(window, 'online').pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_5__.map)(() => true)), (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.fromEvent)(window, 'offline').pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_5__.map)(() => false)), new rxjs__WEBPACK_IMPORTED_MODULE_6__.Observable(sub => {
      sub.next(navigator.onLine);
      sub.complete();
    }));
    this.initializeNetworkListener();
  }
  initializeNetworkListener() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const status = yield _capacitor_network__WEBPACK_IMPORTED_MODULE_1__.Network.getStatus();
      _this.networkStatus.next(status.connected);
      console.log('Initial Network Status:', status.connected);
      _capacitor_network__WEBPACK_IMPORTED_MODULE_1__.Network.addListener('networkStatusChange', status => {
        console.log('Network status changed:', status.connected);
        _this.networkStatus.next(status.connected);
      });
    })();
  }
  getNetworkStatus() {
    return this.networkStatus.asObservable();
  }
  isConnected() {
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const status = yield _capacitor_network__WEBPACK_IMPORTED_MODULE_1__.Network.getStatus();
      return status.connected;
    })();
  }
}
_NetworkService = NetworkService;
_NetworkService.ɵfac = function NetworkService_Factory(t) {
  return new (t || _NetworkService)();
};
_NetworkService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineInjectable"]({
  token: _NetworkService,
  factory: _NetworkService.ɵfac,
  providedIn: 'root'
});

/***/ }),

/***/ 57291:
/*!*********************************************!*\
  !*** ./src/app/services/storage.service.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StorageService: () => (/* binding */ StorageService)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_storage_angular__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ionic/storage-angular */ 60850);

var _StorageService;


class StorageService {
  constructor(storage) {
    this.storage = storage;
  }
  init() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      yield _this.storage.create();
    })();
  }
  set(key, value) {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      yield _this2.storage.set(key, value);
    })();
  }
  get(key) {
    var _this3 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const value = yield _this3.storage.get(key);
      return value;
    })();
  }
  clear() {
    var _this4 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      console.log('Clearing all storage...');
      yield _this4.storage.clear(); // Clear all stored data
    })();
  }
}
_StorageService = StorageService;
_StorageService.ɵfac = function StorageService_Factory(t) {
  return new (t || _StorageService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_ionic_storage_angular__WEBPACK_IMPORTED_MODULE_2__.Storage));
};
_StorageService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
  token: _StorageService,
  factory: _StorageService.ɵfac,
  providedIn: 'root'
});

/***/ }),

/***/ 45312:
/*!*****************************************!*\
  !*** ./src/environments/environment.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   environment: () => (/* binding */ environment)
/* harmony export */ });
// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.
const environment = {
  production: true,
  platform: 'mobile',
  // webSocketUrl: 'ws://192.168.101.176:8085/ws',
  // apiUrl: 'http://192.168.101.176:8085',  // Adjust to your FastAPI local URL
  // webSockeRealTimetUrl: 'ws://192.168.101.176:8085/websocket/realtime_processing',
  // webSocketUrl: 'ws://192.168.101.176:8088/ws',
  // apiUrl: 'http://192.168.101.176:8088',  // Adjust to your FastAPI local URL
  // webSockeRealTimetUrl: 'ws://192.168.101.176:8088/websocket/realtime_processing'
  webSocketUrl: 'wss://winproduit.sophatel.com:8001/ws',
  apiUrl: 'https://winproduit.sophatel.com:8001',
  webSockeRealTimetUrl: 'wss://winproduit.sophatel.com:8001/websocket/realtime_processing'
  // webSocketUrl: 'wss://windoc-api.sophatel.com/ws',
  // apiUrl: 'https://windoc-api.sophatel.com',  // Adjust to your FastAPI local URL
  // webSockeRealTimetUrl: 'wss://windoc-api.sophatel.com/websocket/realtime_processing'
  // webSocketUrl: 'wss://winproduit.sophatel.com:8000/ws',
  // apiUrl: 'https://winproduit.sophatel.com:8000',  // Adjust to your FastAPI local URL
  // webSockeRealTimetUrl: 'wss://winproduit.sophatel.com:8000/websocket/realtime_processing'
  // webSocketUrl: 'wss://ocr-api.abdohero.com/ws', // Secure WebSocket URL
  // apiUrl: 'https://ocr-api.abdohero.com',  // Adjust to your FastAPI server URL
  // webSockeRealTimetUrl: 'wss://ocr-api.abdohero.com/websocket/realtime_processing'
};
/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.

/***/ }),

/***/ 84429:
/*!*********************!*\
  !*** ./src/main.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/platform-browser */ 80436);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _app_app_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/app.module */ 50635);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./environments/environment */ 45312);
/* harmony import */ var swiper_element_bundle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/element/bundle */ 10493);





if (_environments_environment__WEBPACK_IMPORTED_MODULE_1__.environment.production) {
  (0,_angular_core__WEBPACK_IMPORTED_MODULE_3__.enableProdMode)();
}
(0,swiper_element_bundle__WEBPACK_IMPORTED_MODULE_2__.register)();
_angular_platform_browser__WEBPACK_IMPORTED_MODULE_4__.platformBrowser().bootstrapModule(_app_app_module__WEBPACK_IMPORTED_MODULE_0__.AppModule).catch(err => console.log(err));

/***/ }),

/***/ 88996:
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ lazy ^\.\/.*\.entry\.js$ include: \.entry\.js$ exclude: \.system\.entry\.js$ namespace object ***!
  \******************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ion-accordion_2.entry.js": [
		37518,
		"common",
		"node_modules_ionic_core_dist_esm_ion-accordion_2_entry_js"
	],
	"./ion-action-sheet.entry.js": [
		41981,
		"common",
		"node_modules_ionic_core_dist_esm_ion-action-sheet_entry_js"
	],
	"./ion-alert.entry.js": [
		71603,
		"common",
		"node_modules_ionic_core_dist_esm_ion-alert_entry_js"
	],
	"./ion-app_8.entry.js": [
		82273,
		"common",
		"node_modules_ionic_core_dist_esm_ion-app_8_entry_js"
	],
	"./ion-avatar_3.entry.js": [
		19642,
		"node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js"
	],
	"./ion-back-button.entry.js": [
		32095,
		"common",
		"node_modules_ionic_core_dist_esm_ion-back-button_entry_js"
	],
	"./ion-backdrop.entry.js": [
		72335,
		"node_modules_ionic_core_dist_esm_ion-backdrop_entry_js"
	],
	"./ion-breadcrumb_2.entry.js": [
		78221,
		"common",
		"node_modules_ionic_core_dist_esm_ion-breadcrumb_2_entry_js"
	],
	"./ion-button_2.entry.js": [
		47184,
		"node_modules_ionic_core_dist_esm_ion-button_2_entry_js"
	],
	"./ion-card_5.entry.js": [
		38759,
		"node_modules_ionic_core_dist_esm_ion-card_5_entry_js"
	],
	"./ion-checkbox.entry.js": [
		24248,
		"node_modules_ionic_core_dist_esm_ion-checkbox_entry_js"
	],
	"./ion-chip.entry.js": [
		69863,
		"node_modules_ionic_core_dist_esm_ion-chip_entry_js"
	],
	"./ion-col_3.entry.js": [
		51769,
		"node_modules_ionic_core_dist_esm_ion-col_3_entry_js"
	],
	"./ion-datetime-button.entry.js": [
		2569,
		"default-node_modules_ionic_core_dist_esm_data-ae11fd43_js",
		"node_modules_ionic_core_dist_esm_ion-datetime-button_entry_js"
	],
	"./ion-datetime_3.entry.js": [
		76534,
		"default-node_modules_ionic_core_dist_esm_data-ae11fd43_js",
		"common",
		"node_modules_ionic_core_dist_esm_ion-datetime_3_entry_js"
	],
	"./ion-fab_3.entry.js": [
		25458,
		"common",
		"node_modules_ionic_core_dist_esm_ion-fab_3_entry_js"
	],
	"./ion-img.entry.js": [
		70654,
		"node_modules_ionic_core_dist_esm_ion-img_entry_js"
	],
	"./ion-infinite-scroll_2.entry.js": [
		36034,
		"common",
		"node_modules_ionic_core_dist_esm_ion-infinite-scroll_2_entry_js"
	],
	"./ion-input-password-toggle.entry.js": [
		5196,
		"common",
		"node_modules_ionic_core_dist_esm_ion-input-password-toggle_entry_js"
	],
	"./ion-input.entry.js": [
		20761,
		"default-node_modules_ionic_core_dist_esm_input_utils-09c71bc7_js-node_modules_ionic_core_dist-8b8a84",
		"common",
		"node_modules_ionic_core_dist_esm_ion-input_entry_js"
	],
	"./ion-item-option_3.entry.js": [
		6492,
		"common",
		"node_modules_ionic_core_dist_esm_ion-item-option_3_entry_js"
	],
	"./ion-item_8.entry.js": [
		29557,
		"common",
		"node_modules_ionic_core_dist_esm_ion-item_8_entry_js"
	],
	"./ion-loading.entry.js": [
		68353,
		"common",
		"node_modules_ionic_core_dist_esm_ion-loading_entry_js"
	],
	"./ion-menu_3.entry.js": [
		51024,
		"common",
		"node_modules_ionic_core_dist_esm_ion-menu_3_entry_js"
	],
	"./ion-modal.entry.js": [
		29160,
		"common",
		"node_modules_ionic_core_dist_esm_ion-modal_entry_js"
	],
	"./ion-nav_2.entry.js": [
		60393,
		"node_modules_ionic_core_dist_esm_ion-nav_2_entry_js"
	],
	"./ion-picker-column-option.entry.js": [
		68442,
		"node_modules_ionic_core_dist_esm_ion-picker-column-option_entry_js"
	],
	"./ion-picker-column.entry.js": [
		43110,
		"common",
		"node_modules_ionic_core_dist_esm_ion-picker-column_entry_js"
	],
	"./ion-picker.entry.js": [
		15575,
		"node_modules_ionic_core_dist_esm_ion-picker_entry_js"
	],
	"./ion-popover.entry.js": [
		16772,
		"common",
		"node_modules_ionic_core_dist_esm_ion-popover_entry_js"
	],
	"./ion-progress-bar.entry.js": [
		34810,
		"node_modules_ionic_core_dist_esm_ion-progress-bar_entry_js"
	],
	"./ion-radio_2.entry.js": [
		14639,
		"common",
		"node_modules_ionic_core_dist_esm_ion-radio_2_entry_js"
	],
	"./ion-range.entry.js": [
		90628,
		"common",
		"node_modules_ionic_core_dist_esm_ion-range_entry_js"
	],
	"./ion-refresher_2.entry.js": [
		10852,
		"common",
		"node_modules_ionic_core_dist_esm_ion-refresher_2_entry_js"
	],
	"./ion-reorder_2.entry.js": [
		61479,
		"common",
		"node_modules_ionic_core_dist_esm_ion-reorder_2_entry_js"
	],
	"./ion-ripple-effect.entry.js": [
		24065,
		"node_modules_ionic_core_dist_esm_ion-ripple-effect_entry_js"
	],
	"./ion-route_4.entry.js": [
		57971,
		"node_modules_ionic_core_dist_esm_ion-route_4_entry_js"
	],
	"./ion-searchbar.entry.js": [
		93184,
		"common",
		"node_modules_ionic_core_dist_esm_ion-searchbar_entry_js"
	],
	"./ion-segment_2.entry.js": [
		469,
		"common",
		"node_modules_ionic_core_dist_esm_ion-segment_2_entry_js"
	],
	"./ion-select_3.entry.js": [
		78471,
		"common",
		"node_modules_ionic_core_dist_esm_ion-select_3_entry_js"
	],
	"./ion-spinner.entry.js": [
		40388,
		"common",
		"node_modules_ionic_core_dist_esm_ion-spinner_entry_js"
	],
	"./ion-split-pane.entry.js": [
		42392,
		"node_modules_ionic_core_dist_esm_ion-split-pane_entry_js"
	],
	"./ion-tab-bar_2.entry.js": [
		36059,
		"common",
		"node_modules_ionic_core_dist_esm_ion-tab-bar_2_entry_js"
	],
	"./ion-tab_2.entry.js": [
		5427,
		"node_modules_ionic_core_dist_esm_ion-tab_2_entry_js"
	],
	"./ion-text.entry.js": [
		50198,
		"node_modules_ionic_core_dist_esm_ion-text_entry_js"
	],
	"./ion-textarea.entry.js": [
		1735,
		"default-node_modules_ionic_core_dist_esm_input_utils-09c71bc7_js-node_modules_ionic_core_dist-8b8a84",
		"node_modules_ionic_core_dist_esm_ion-textarea_entry_js"
	],
	"./ion-toast.entry.js": [
		7510,
		"common",
		"node_modules_ionic_core_dist_esm_ion-toast_entry_js"
	],
	"./ion-toggle.entry.js": [
		45297,
		"common",
		"node_modules_ionic_core_dist_esm_ion-toggle_entry_js"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = 88996;
module.exports = webpackAsyncContext;

/***/ }),

/***/ 95235:
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/@ionic/pwa-elements/dist/esm/ lazy ^\.\/.*\.entry\.js$ include: \.entry\.js$ exclude: \.system\.entry\.js$ namespace object ***!
  \**************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./pwa-action-sheet.entry.js": [
		75222,
		"node_modules_ionic_pwa-elements_dist_esm_pwa-action-sheet_entry_js"
	],
	"./pwa-camera-modal-instance.entry.js": [
		89253,
		"node_modules_ionic_pwa-elements_dist_esm_pwa-camera-modal-instance_entry_js"
	],
	"./pwa-camera-modal.entry.js": [
		69577,
		"node_modules_ionic_pwa-elements_dist_esm_pwa-camera-modal_entry_js"
	],
	"./pwa-camera.entry.js": [
		52217,
		"node_modules_ionic_pwa-elements_dist_esm_pwa-camera_entry_js"
	],
	"./pwa-toast.entry.js": [
		70071,
		"node_modules_ionic_pwa-elements_dist_esm_pwa-toast_entry_js"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = 95235;
module.exports = webpackAsyncContext;

/***/ }),

/***/ 54140:
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/@stencil/core/internal/client/ lazy ^\.\/.*\.entry\.js.*$ include: \.entry\.js$ exclude: \.system\.entry\.js$ strict namespace object ***!
  \************************************************************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyAsyncContext(req) {
	// Here Promise.resolve().then() is used instead of new Promise() to prevent
	// uncaught exception popping up in devtools
	return Promise.resolve().then(() => {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	});
}
webpackEmptyAsyncContext.keys = () => ([]);
webpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;
webpackEmptyAsyncContext.id = 54140;
module.exports = webpackEmptyAsyncContext;

/***/ }),

/***/ 35358:
/*!***************************************************!*\
  !*** ./node_modules/moment/locale/ sync ^\.\/.*$ ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./af": 85637,
	"./af.js": 85637,
	"./ar": 6777,
	"./ar-dz": 74508,
	"./ar-dz.js": 74508,
	"./ar-kw": 67504,
	"./ar-kw.js": 67504,
	"./ar-ly": 95373,
	"./ar-ly.js": 95373,
	"./ar-ma": 92412,
	"./ar-ma.js": 92412,
	"./ar-ps": 78823,
	"./ar-ps.js": 78823,
	"./ar-sa": 36670,
	"./ar-sa.js": 36670,
	"./ar-tn": 36448,
	"./ar-tn.js": 36448,
	"./ar.js": 6777,
	"./az": 23009,
	"./az.js": 23009,
	"./be": 28299,
	"./be.js": 28299,
	"./bg": 4685,
	"./bg.js": 4685,
	"./bm": 11171,
	"./bm.js": 11171,
	"./bn": 23590,
	"./bn-bd": 5841,
	"./bn-bd.js": 5841,
	"./bn.js": 23590,
	"./bo": 54309,
	"./bo.js": 54309,
	"./br": 54130,
	"./br.js": 54130,
	"./bs": 8033,
	"./bs.js": 8033,
	"./ca": 55294,
	"./ca.js": 55294,
	"./cs": 53028,
	"./cs.js": 53028,
	"./cv": 5807,
	"./cv.js": 5807,
	"./cy": 70342,
	"./cy.js": 70342,
	"./da": 38269,
	"./da.js": 38269,
	"./de": 11489,
	"./de-at": 42123,
	"./de-at.js": 42123,
	"./de-ch": 17757,
	"./de-ch.js": 17757,
	"./de.js": 11489,
	"./dv": 28152,
	"./dv.js": 28152,
	"./el": 7687,
	"./el.js": 7687,
	"./en-au": 46668,
	"./en-au.js": 46668,
	"./en-ca": 76798,
	"./en-ca.js": 76798,
	"./en-gb": 53615,
	"./en-gb.js": 53615,
	"./en-ie": 91364,
	"./en-ie.js": 91364,
	"./en-il": 79907,
	"./en-il.js": 79907,
	"./en-in": 70533,
	"./en-in.js": 70533,
	"./en-nz": 33190,
	"./en-nz.js": 33190,
	"./en-sg": 51096,
	"./en-sg.js": 51096,
	"./eo": 3962,
	"./eo.js": 3962,
	"./es": 37726,
	"./es-do": 65010,
	"./es-do.js": 65010,
	"./es-mx": 63654,
	"./es-mx.js": 63654,
	"./es-us": 59043,
	"./es-us.js": 59043,
	"./es.js": 37726,
	"./et": 25343,
	"./et.js": 25343,
	"./eu": 90728,
	"./eu.js": 90728,
	"./fa": 60787,
	"./fa.js": 60787,
	"./fi": 71771,
	"./fi.js": 71771,
	"./fil": 45335,
	"./fil.js": 45335,
	"./fo": 69761,
	"./fo.js": 69761,
	"./fr": 1670,
	"./fr-ca": 28991,
	"./fr-ca.js": 28991,
	"./fr-ch": 97280,
	"./fr-ch.js": 97280,
	"./fr.js": 1670,
	"./fy": 24203,
	"./fy.js": 24203,
	"./ga": 69858,
	"./ga.js": 69858,
	"./gd": 38605,
	"./gd.js": 38605,
	"./gl": 27365,
	"./gl.js": 27365,
	"./gom-deva": 33896,
	"./gom-deva.js": 33896,
	"./gom-latn": 95587,
	"./gom-latn.js": 95587,
	"./gu": 97950,
	"./gu.js": 97950,
	"./he": 92029,
	"./he.js": 92029,
	"./hi": 51897,
	"./hi.js": 51897,
	"./hr": 29816,
	"./hr.js": 29816,
	"./hu": 22253,
	"./hu.js": 22253,
	"./hy-am": 28196,
	"./hy-am.js": 28196,
	"./id": 51307,
	"./id.js": 51307,
	"./is": 95474,
	"./is.js": 95474,
	"./it": 23099,
	"./it-ch": 45807,
	"./it-ch.js": 45807,
	"./it.js": 23099,
	"./ja": 19127,
	"./ja.js": 19127,
	"./jv": 30182,
	"./jv.js": 30182,
	"./ka": 10758,
	"./ka.js": 10758,
	"./kk": 93444,
	"./kk.js": 93444,
	"./km": 72034,
	"./km.js": 72034,
	"./kn": 46223,
	"./kn.js": 46223,
	"./ko": 83064,
	"./ko.js": 83064,
	"./ku": 8714,
	"./ku-kmr": 10961,
	"./ku-kmr.js": 10961,
	"./ku.js": 8714,
	"./ky": 12062,
	"./ky.js": 12062,
	"./lb": 84796,
	"./lb.js": 84796,
	"./lo": 19279,
	"./lo.js": 19279,
	"./lt": 106,
	"./lt.js": 106,
	"./lv": 11840,
	"./lv.js": 11840,
	"./me": 42240,
	"./me.js": 42240,
	"./mi": 13588,
	"./mi.js": 13588,
	"./mk": 15518,
	"./mk.js": 15518,
	"./ml": 37823,
	"./ml.js": 37823,
	"./mn": 98657,
	"./mn.js": 98657,
	"./mr": 61285,
	"./mr.js": 61285,
	"./ms": 43014,
	"./ms-my": 86253,
	"./ms-my.js": 86253,
	"./ms.js": 43014,
	"./mt": 20167,
	"./mt.js": 20167,
	"./my": 47940,
	"./my.js": 47940,
	"./nb": 50014,
	"./nb.js": 50014,
	"./ne": 49023,
	"./ne.js": 49023,
	"./nl": 34208,
	"./nl-be": 71412,
	"./nl-be.js": 71412,
	"./nl.js": 34208,
	"./nn": 81354,
	"./nn.js": 81354,
	"./oc-lnc": 40870,
	"./oc-lnc.js": 40870,
	"./pa-in": 80389,
	"./pa-in.js": 80389,
	"./pl": 7342,
	"./pl.js": 7342,
	"./pt": 34774,
	"./pt-br": 73003,
	"./pt-br.js": 73003,
	"./pt.js": 34774,
	"./ro": 85333,
	"./ro.js": 85333,
	"./ru": 73451,
	"./ru.js": 73451,
	"./sd": 43921,
	"./sd.js": 43921,
	"./se": 59682,
	"./se.js": 59682,
	"./si": 80582,
	"./si.js": 80582,
	"./sk": 4348,
	"./sk.js": 4348,
	"./sl": 95337,
	"./sl.js": 95337,
	"./sq": 39358,
	"./sq.js": 39358,
	"./sr": 50683,
	"./sr-cyrl": 69382,
	"./sr-cyrl.js": 69382,
	"./sr.js": 50683,
	"./ss": 51156,
	"./ss.js": 51156,
	"./sv": 29855,
	"./sv.js": 29855,
	"./sw": 18536,
	"./sw.js": 18536,
	"./ta": 15373,
	"./ta.js": 15373,
	"./te": 37809,
	"./te.js": 37809,
	"./tet": 61297,
	"./tet.js": 61297,
	"./tg": 92527,
	"./tg.js": 92527,
	"./th": 85862,
	"./th.js": 85862,
	"./tk": 79331,
	"./tk.js": 79331,
	"./tl-ph": 44387,
	"./tl-ph.js": 44387,
	"./tlh": 3592,
	"./tlh.js": 3592,
	"./tr": 79732,
	"./tr.js": 79732,
	"./tzl": 99570,
	"./tzl.js": 99570,
	"./tzm": 83553,
	"./tzm-latn": 7699,
	"./tzm-latn.js": 7699,
	"./tzm.js": 83553,
	"./ug-cn": 25674,
	"./ug-cn.js": 25674,
	"./uk": 69974,
	"./uk.js": 69974,
	"./ur": 45773,
	"./ur.js": 45773,
	"./uz": 357,
	"./uz-latn": 77135,
	"./uz-latn.js": 77135,
	"./uz.js": 357,
	"./vi": 20043,
	"./vi.js": 20043,
	"./x-pseudo": 40767,
	"./x-pseudo.js": 40767,
	"./yo": 80150,
	"./yo.js": 80150,
	"./zh-cn": 21828,
	"./zh-cn.js": 21828,
	"./zh-hk": 86644,
	"./zh-hk.js": 86644,
	"./zh-mo": 79305,
	"./zh-mo.js": 79305,
	"./zh-tw": 31860,
	"./zh-tw.js": 31860
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 35358;

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendor"], () => (__webpack_exec__(84429)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=main.js.map