{"version": 3, "file": "src_app_onboarding_onboarding_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAEJ;;;AAEnD,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,4DAAcA;CAC1B,CACF;AAMK,MAAOI,2BAA2B;+BAA3BA,2BAA2B;;mBAA3BA,4BAA2B;AAAA;;QAA3BA;AAA2B;;YAH5BL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,2BAA2B;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAF5BT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAE6B;AAEvB;;AAY7C,MAAOa,oBAAoB;wBAApBA,oBAAoB;;mBAApBA,qBAAoB;AAAA;;QAApBA;AAAoB;;YAP7BH,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,mFAA2B;AAAA;;sHAIlBQ,oBAAoB;IAAAC,YAAA,GAFhBb,4DAAc;IAAAM,OAAA,GAL3BG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,mFAA2B;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IENnBU,4DAFJ,uBAA4D,cACjD,kBACkC;IACvCA,uDAAA,cAA+C;IAE7CA,4DADF,cAA2B,SACrB;IAAAA,oDAAA,GAAiB;IAAAA,0DAAA,EAAK;IAC1BA,4DAAA,QAAG;IAAAA,oDAAA,GAAuB;IAIlCA,0DAJkC,EAAI,EAC1B,EACE,EACF,EACG;;;;IAPJA,uDAAA,GAAmB;IAAnBA,wDAAA,QAAAO,QAAA,CAAAC,KAAA,EAAAR,2DAAA,CAAmB;IAElBA,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAO,QAAA,CAAAI,KAAA,CAAiB;IAClBX,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAO,QAAA,CAAAK,WAAA,CAAuB;;;ADHpC,MAAO1B,cAAc;EA+CzB2B,YAAoBC,cAA8B,EAAUC,OAAsB,EAAUC,MAAc;IAAtF,KAAAF,cAAc,GAAdA,cAAc;IAA0B,KAAAC,OAAO,GAAPA,OAAO;IAAyB,KAAAC,MAAM,GAANA,MAAM;IA5ClG,KAAAC,UAAU,GAAG;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACET,KAAK,EAAE,qCAAqC;MAC5CG,KAAK,EAAE,8BAA8B;MACrCC,WAAW,EAAE;KACd,EACD;MACEJ,KAAK,EAAE,qCAAqC;MAC5CG,KAAK,EAAE,wCAAwC;MAC/CC,WAAW,EAAE;KACd,EACD;MACEJ,KAAK,EAAE,2CAA2C;MAClDG,KAAK,EAAE,2CAA2C;MAClDC,WAAW,EAAE;;IAEf;IAAA,CACD;EAE4G;EAE7GM,eAAeA,CAAA;IACb,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,aAAa,EAAE;MAC5C,MAAMC,cAAc,GAAG,IAAI,CAACF,MAAM,CAACC,aAAa,CAACD,MAAM;;EAE3D;EAEAG,IAAIA,CAAA;IACF;IACA,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACT,OAAO,CAACU,YAAY,CAAC,UAAU,CAAC;EACvC;EAEAC,IAAIA,CAAA;IACF,IAAI,IAAI,CAACP,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,aAAa,EAAE;MAC5C,MAAMC,cAAc,GAAG,IAAI,CAACF,MAAM,CAACC,aAAa,CAACD,MAAM;MACvD,MAAMQ,YAAY,GAAGN,cAAc,CAACO,WAAW;MAC/C,MAAMC,WAAW,GAAGR,cAAc,CAACS,MAAM,CAACC,MAAM,GAAG,CAAC;MAEpD,IAAIJ,YAAY,KAAKE,WAAW,EAAE;QAChC;QACA;QAEA;QACA,IAAI,CAACN,kBAAkB,EAAE;OAE1B,MAAM;QACL;QACAF,cAAc,CAACW,SAAS,EAAE;;;EAGhC;EAEMT,kBAAkBA,CAAA;IAAA,IAAAU,KAAA;IAAA,OAAAC,6OAAA;MACtBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD;MACAH,KAAI,CAACjB,MAAM,CAACqB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAAA;EACtC;;kBAxFWnD,cAAc;;mBAAdA,eAAc,EAAAc,+DAAA,CAAAP,qEAAA,GAAAO,+DAAA,CAAAwC,yDAAA,GAAAxC,+DAAA,CAAA0C,mDAAA;AAAA;;QAAdxD,eAAc;EAAA0D,SAAA;EAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;;MCX3B/C,4DAAA,aAAyB;MACvBA,uDAAA,iBAEa;MAITA,4DAFJ,qBAAwB,aACU,6BACc;MAC1CA,wDAAA,IAAAkD,sCAAA,0BAA4D;MAelElD,0DAJI,EAAmB,EAGf,EACM;MAGNA,4DAFN,iBAAY,aACiB,oBACuC;MAAjBA,wDAAA,mBAAAoD,oDAAA;QAAApD,2DAAA,CAAAsD,GAAA;QAAA,OAAAtD,yDAAA,CAASgD,GAAA,CAAAtB,IAAA,EAAM;MAAA,EAAC;MAC3D1B,4DAAA,YAAM;MAAAA,oDAAA,eAAO;MAAAA,0DAAA,EAAO;MACpBA,uDAAA,mBAA6D;MAC/DA,0DAAA,EAAa;MACbA,4DAAA,qBAA8D;MAAjBA,wDAAA,mBAAAwD,qDAAA;QAAAxD,2DAAA,CAAAsD,GAAA;QAAA,OAAAtD,yDAAA,CAASgD,GAAA,CAAA1B,IAAA,EAAM;MAAA,EAAC;MAACtB,oDAAA,cAAM;MAG9EA,0DAH8E,EAAa,EAC7E,EACK,EACb;;;MAzBkCA,uDAAA,GAAa;MAAbA,wDAAA,YAAAgD,GAAA,CAAA/B,UAAA,CAAa", "sources": ["./src/app/onboarding/onboarding-routing.module.ts", "./src/app/onboarding/onboarding.module.ts", "./src/app/onboarding/onboarding.page.ts", "./src/app/onboarding/onboarding.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { OnboardingPage } from './onboarding.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: OnboardingPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class OnboardingPageRoutingModule {}\r\n", "import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { OnboardingPageRoutingModule } from './onboarding-routing.module';\r\n\r\nimport { OnboardingPage } from './onboarding.page';\r\n\r\n@NgModule({\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    OnboardingPageRoutingModule\r\n  ],\r\n  declarations: [OnboardingPage]\r\n})\r\nexport class OnboardingPageModule {}\r\n", "import { Component, ViewChild, AfterViewInit, ElementRef, ViewEncapsulation } from '@angular/core';\r\nimport { NavController } from '@ionic/angular';\r\nimport { StorageService } from '../services/storage.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-onboarding',\r\n  templateUrl: './onboarding.page.html',\r\n  styleUrls: ['./onboarding.page.scss'],\r\n  // encapsulation: ViewEncapsulation.ShadowDom\r\n})\r\nexport class OnboardingPage implements AfterViewInit {\r\n  @ViewChild('swiper', { static: true }) swiper: ElementRef | undefined;\r\n\r\n  slidesData = [\r\n    // {\r\n    //   image: 'assets/onboarding_images/slide1.svg',\r\n    //   title: 'Capturez vos documents facilement',\r\n    //   description: 'Simplifiez la gestion de vos Bons de Livraison avec notre technologie OCR avancée'\r\n    // },\r\n    // {\r\n    //   image: 'assets/onboarding_images/slide2.svg',\r\n    //   title: 'Scannez vos documents',\r\n    //   description: 'Jusqu\\'à 5 pages par Bon de Livraison. Assurez-vous que toutes les pages appartiennent au même Bon de Livraison pour une meilleure précision.'\r\n    // },\r\n    // {\r\n    //   image: 'assets/onboarding_images/slide3.svg',\r\n    //   title: 'Recadrez vos documents',\r\n    //   description: 'Utilisez notre outil de recadrage pour sélectionner uniquement les parties importantes du document avant d\\'extraire les données.'\r\n    // },\r\n    // {\r\n    //   image: 'assets/onboarding_images/slide4.svg',\r\n    //   title: 'Choisissez une source d\\'image',\r\n    //   description: 'Caméra ou Galerie. Prenez une photo directement ou sélectionnez une image existante depuis votre galerie.'\r\n    // },\r\n    // {\r\n    //   image: 'assets/onboarding_images/slide5.svg',\r\n    //   title: 'Identifiez le fournisseur',\r\n    //   description: 'Pour des résultats optimaux. Choisissez le fournisseur du document avant de soumettre le formulaire.'\r\n    // },\r\n    {\r\n      image: 'assets/onboarding_images/windoc.svg',\r\n      title: 'Scannez votre BL avec WinDoc',\r\n      description: \"Utilisez l'application WinDoc pour capturer une image claire de votre Bon de Livraison (BL). L'application détecte automatiquement les contours et améliore l'image pour une meilleure reconnaissance.\"\r\n    },\r\n    {\r\n      image: 'assets/onboarding_images/slide2.svg',\r\n      title: 'Extraction et vérification des données',\r\n      description: 'WinDoc extrait automatiquement les informations clés de votre BL, comme la désignation, la quantité et le PPV. Vérifiez et complétez les informations si nécessaire avant de soumettre.'\r\n    },\r\n    {\r\n      image: 'assets/onboarding_images/winpluspharm.svg',\r\n      title: 'Retrouvez vos résultats sur WinPlusPharma',\r\n      description: 'Une fois le BL soumis, retrouvez les résultats directement dans votre espace personnel sur WinPlusPharma.'\r\n    },\r\n    // Add other slides here...\r\n  ];\r\n\r\n  constructor(private storageService: StorageService, private navCtrl: NavController, private router: Router) {}\r\n\r\n  ngAfterViewInit() {\r\n    if (this.swiper && this.swiper.nativeElement) {\r\n      const swiperInstance = this.swiper.nativeElement.swiper;\r\n    }\r\n  }\r\n\r\n  skip() {\r\n    // this.navCtrl.navigateRoot('/scan-bl');\r\n    this.completeOnboarding();  \r\n  }\r\n\r\n  welcome(){\r\n    this.navCtrl.navigateRoot('/welcome'); \r\n  }\r\n\r\n  next() {\r\n    if (this.swiper && this.swiper.nativeElement) {\r\n      const swiperInstance = this.swiper.nativeElement.swiper;\r\n      const currentIndex = swiperInstance.activeIndex;\r\n      const totalSlides = swiperInstance.slides.length - 1;\r\n\r\n      if (currentIndex === totalSlides) {\r\n        // // Navigate to 'scan-bl' page if it's the last slide\r\n        // this.welcome()\r\n\r\n        // Call completeOnboarding on the last slide\r\n        this.completeOnboarding();\r\n\r\n      } else {\r\n        // Otherwise, navigate to the next slide\r\n        swiperInstance.slideNext();\r\n      }\r\n    }\r\n  }\r\n\r\n  async completeOnboarding() {\r\n    console.log('Setting hasSeenOnboarding to true');\r\n    // await this.storageService.set('hasSeenOnboarding', true);\r\n    this.router.navigate(['/welcome']); // Redirect to the main app\r\n  }\r\n}\r\n", "<div class=\"fix-wrapper\">\r\n  <ion-header>\r\n  \r\n  </ion-header>\r\n  \r\n  <ion-content fullscreen>\r\n    <div class=\"onboarding-wrapper\">\r\n      <swiper-container #swiper pagination=\"true\">\r\n        <swiper-slide *ngFor=\"let slide of slidesData\" class=\"test\">\r\n          <ion-row>\r\n            <ion-col size=\"12\" class=\"slide-content\">\r\n              <img [src]=\"slide.image\" class=\"slide-image\" />\r\n              <div class=\"content-slide\">\r\n                <h2>{{ slide.title }}</h2>\r\n                <p>{{ slide.description }}</p>\r\n              </div>\r\n            </ion-col>\r\n          </ion-row>\r\n        </swiper-slide>\r\n      </swiper-container>\r\n  \r\n  \r\n    </div>\r\n  </ion-content>\r\n    <ion-footer>\r\n        <div class=\"buttons_nav\">\r\n          <ion-button fill=\"solid\" class=\"next-button\" (click)=\"next()\">\r\n            <span>SUIVANT</span>\r\n            <ion-icon slot=\"end\" name=\"arrow-forward-outline\"></ion-icon>\r\n          </ion-button>\r\n          <ion-button fill=\"clear\" class=\"skip-button\" (click)=\"skip()\">PASSER</ion-button>\r\n        </div>\r\n      </ion-footer>\r\n</div>"], "names": ["RouterModule", "OnboardingPage", "routes", "path", "component", "OnboardingPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "OnboardingPageModule", "declarations", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "slide_r2", "image", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "title", "description", "constructor", "storageService", "navCtrl", "router", "slidesData", "ngAfterViewInit", "swiper", "nativeElement", "swiperInstance", "skip", "completeOnboarding", "welcome", "navigateRoot", "next", "currentIndex", "activeIndex", "totalSlides", "slides", "length", "slideNext", "_this", "_asyncToGenerator", "console", "log", "navigate", "ɵɵdirectiveInject", "StorageService", "i2", "NavController", "i3", "Router", "selectors", "viewQuery", "OnboardingPage_Query", "rf", "ctx", "ɵɵtemplate", "OnboardingPage_swiper_slide_6_Template", "ɵɵlistener", "OnboardingPage_Template_ion_button_click_9_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "OnboardingPage_Template_ion_button_click_13_listener"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}