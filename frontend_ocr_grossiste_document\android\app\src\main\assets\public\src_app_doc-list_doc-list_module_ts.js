"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_doc-list_doc-list_module_ts"],{

/***/ 42030:
/*!*****************************************************!*\
  !*** ./src/app/doc-list/doc-list-routing.module.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DocListPageRoutingModule: () => (/* binding */ DocListPageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _doc_list_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./doc-list.page */ 37584);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _DocListPageRoutingModule;




const routes = [{
  path: '',
  component: _doc_list_page__WEBPACK_IMPORTED_MODULE_0__.DocListPage
}];
class DocListPageRoutingModule {}
_DocListPageRoutingModule = DocListPageRoutingModule;
_DocListPageRoutingModule.ɵfac = function DocListPageRoutingModule_Factory(t) {
  return new (t || _DocListPageRoutingModule)();
};
_DocListPageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _DocListPageRoutingModule
});
_DocListPageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](DocListPageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 35703:
/*!*********************************************!*\
  !*** ./src/app/doc-list/doc-list.module.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DocListPageModule: () => (/* binding */ DocListPageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _doc_list_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./doc-list-routing.module */ 42030);
/* harmony import */ var _doc_list_page__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./doc-list.page */ 37584);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
var _DocListPageModule;





 // Import SharedModule

class DocListPageModule {}
_DocListPageModule = DocListPageModule;
_DocListPageModule.ɵfac = function DocListPageModule_Factory(t) {
  return new (t || _DocListPageModule)();
};
_DocListPageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
  type: _DocListPageModule
});
_DocListPageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _doc_list_routing_module__WEBPACK_IMPORTED_MODULE_0__.DocListPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](DocListPageModule, {
    declarations: [_doc_list_page__WEBPACK_IMPORTED_MODULE_1__.DocListPage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _doc_list_routing_module__WEBPACK_IMPORTED_MODULE_0__.DocListPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule]
  });
})();

/***/ }),

/***/ 37584:
/*!*******************************************!*\
  !*** ./src/app/doc-list/doc-list.page.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DocListPage: () => (/* binding */ DocListPage)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var swiper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swiper */ 89971);
/* harmony import */ var _services_signal_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/signal.service */ 56658);
/* harmony import */ var _services_api_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/api.service */ 3366);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var _services_websocket_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../services/websocket.service */ 30765);
/* harmony import */ var _services_network_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../services/network.service */ 32404);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../custom-icon/custom-icon.component */ 40816);
/* harmony import */ var _check_network_check_network_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../check-network/check-network.component */ 93648);
/* harmony import */ var _custom_alert_custom_alert_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../custom-alert/custom-alert.component */ 32374);

var _DocListPage;




 // Import ApiService










const _c0 = ["swiperContainer"];
const _c1 = a0 => ({
  "loading": a0
});
const _c2 = () => ["/process-doc"];
function DocListPage_ion_item_sliding_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](0, "ion-item-sliding", 16)(1, "ion-item")(2, "ion-thumbnail", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](3, "img", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](4, "ion-label")(5, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](7, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](9, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](11, "ion-item-options", 20)(12, "ion-item-option", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function DocListPage_ion_item_sliding_12_Template_ion_item_option_click_12_listener() {
      const i_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1).index;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx_r2.renameCard(i_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](13, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](14, "ion-icon", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](15, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](16, "Renommer");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](17, "ion-item-option", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function DocListPage_ion_item_sliding_12_Template_ion_item_option_click_17_listener() {
      const i_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1).index;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx_r2.confirmDeleteCard(i_r2));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](18, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](19, "ion-icon", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](20, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](21, "Supprimer");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const slide_r4 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("src", slide_r4.filtered_image, _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtextInterpolate"](slide_r4.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtextInterpolate"](slide_r4.date);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtextInterpolate1"]("Page ", slide_r4.page_index, "");
  }
}
var OcrMode;
(function (OcrMode) {
  OcrMode["STANDARD"] = "STANDARD";
  OcrMode["MINDEE_ADVANCED"] = "MINDEE_ADVANCED";
})(OcrMode || (OcrMode = {}));
class DocListPage {
  constructor(alertController, webSocketService, networkService) {
    this.alertController = alertController;
    this.webSocketService = webSocketService;
    this.networkService = networkService;
    this.slidesData = [];
    this.navCtrl = (0,_angular_core__WEBPACK_IMPORTED_MODULE_10__.inject)(_ionic_angular__WEBPACK_IMPORTED_MODULE_11__.NavController);
    this.signalService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_10__.inject)(_services_signal_service__WEBPACK_IMPORTED_MODULE_2__.SignalService);
    this.apiService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_10__.inject)(_services_api_service__WEBPACK_IMPORTED_MODULE_3__.ApiService);
    this.loadingController = (0,_angular_core__WEBPACK_IMPORTED_MODULE_10__.inject)(_ionic_angular__WEBPACK_IMPORTED_MODULE_12__.LoadingController);
    this.progress = 0;
    this.isLoading = false;
    this.isConnected = true; // Track network status
    // this.jobId = this.apiService.generateJobId(); // Generate job ID once
  }
  ngAfterViewInit() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this.initializeSwiper();
    })();
  }
  ngOnInit() {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (_this2.signalService.getData() == null || _this2.signalService.getData() == undefined || _this2.signalService.getData().length == 0) {
        _this2.navCtrl.navigateRoot('/scan-bl');
      } else {
        var _this2$swiperContaine;
        _this2.slidesData = _this2.signalService.getData();
        (_this2$swiperContaine = _this2.swiperContainer) === null || _this2$swiperContaine === void 0 || (_this2$swiperContaine = _this2$swiperContaine.nativeElement.swiper) === null || _this2$swiperContaine === void 0 || _this2$swiperContaine.update();
        console.log('Slides data:', _this2.slidesData);
      }
      // Subscribe to the network status
      _this2.networkService.getNetworkStatus().subscribe(connected => {
        _this2.isConnected = connected;
      });
      // // const jobId = this.apiService.generateJobId();  // Get the job ID
      // const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;
      // this.webSocketService.connect(websocketUrl, this.jobId);
      // this.webSocketService.onMessage(this.jobId).subscribe((message) => {
      //   // console.log('Received message:', message);
      //   if (message.progress !== undefined) {
      //     this.progress = message.progress;
      //   }
      //   console.log("progress __ :" , this.progress)
      // });
    })();
  }
  initializeSwiper() {
    var _this3 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      var _this3$swiperContaine;
      _this3.swiper = new swiper__WEBPACK_IMPORTED_MODULE_1__["default"]((_this3$swiperContaine = _this3.swiperContainer) === null || _this3$swiperContaine === void 0 ? void 0 : _this3$swiperContaine.nativeElement, {
        effect: 'cards',
        grabCursor: true,
        on: {
          init: () => {
            console.log('Swiper initialized', _this3.swiper);
          }
        }
      });
    })();
  }
  data_bl() {
    this.navCtrl.navigateRoot('/data-bl');
  }
  scan_bl() {
    this.navCtrl.navigateRoot('/scan-bl');
  }
  renameCard(index) {
    var _this4 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this4.alertController.create({
        header: 'Renommer le document',
        inputs: [{
          name: 'newTitle',
          type: 'text',
          value: _this4.slidesData[index].title,
          placeholder: 'New Title'
        }],
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button-rename-doc cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Renommer',
          cssClass: 'custom-alert-button-rename-doc rename',
          handler: data => {
            _this4.slidesData[index].title = data.newTitle;
          }
        }]
      });
      yield alert.present();
    })();
  }
  confirmDeleteCard(index) {
    var _this5 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this5.alertController.create({
        header: 'Supprimer le document',
        message: `Êtes-vous sûr de vouloir supprimer ce document ?`,
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            _this5.removeSlide(index);
            if (_this5.slidesData.length == 0) {
              localStorage.removeItem('selectedSupplier');
              _this5.navCtrl.navigateRoot('/scan-bl');
            }
          }
        }]
      });
      yield alert.present();
    })();
  }
  removeSlide(index) {
    var _this6 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      var _this6$swiperContaine, _bottomSwiperInstance;
      const bottomSwiperInstance = (_this6$swiperContaine = _this6.swiperContainer) === null || _this6$swiperContaine === void 0 ? void 0 : _this6$swiperContaine.nativeElement.swiper;
      bottomSwiperInstance.removeSlide(index);
      bottomSwiperInstance.update();
      (_bottomSwiperInstance = bottomSwiperInstance.slideTo(index - 1)) !== null && _bottomSwiperInstance !== void 0 ? _bottomSwiperInstance : index > 0;
      _this6.slidesData.splice(index, 1);
      console.log('Slide removed and swiper updated', bottomSwiperInstance);
    })();
  }
  removeDoc(index) {
    var _this7 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this7.alertController.create({
        header: 'Supprimer le document',
        message: `Êtes-vous sûr de vouloir supprimer ce document ?`,
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            var _this7$swiperContaine, _this7$swiperContaine2;
            _this7.signalService.removeData(index);
            _this7.slidesData = _this7.signalService.getData();
            (_this7$swiperContaine = _this7.swiperContainer) === null || _this7$swiperContaine === void 0 || (_this7$swiperContaine = _this7$swiperContaine.nativeElement.swiper) === null || _this7$swiperContaine === void 0 || _this7$swiperContaine.removeSlide(index);
            (_this7$swiperContaine2 = _this7.swiperContainer) === null || _this7$swiperContaine2 === void 0 || (_this7$swiperContaine2 = _this7$swiperContaine2.nativeElement.swiper) === null || _this7$swiperContaine2 === void 0 || _this7$swiperContaine2.update();
            if (_this7.slidesData.length == 0) {
              localStorage.removeItem('selectedSupplier');
              _this7.navCtrl.navigateRoot('/scan-bl');
            }
          }
        }]
      });
      yield alert.present();
    })();
  }
  removeAllDoc() {
    var _this8 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this8.alertController.create({
        header: 'Supprimer le document',
        message: `Êtes-vous sûr de vouloir supprimer Tous les documents ?`,
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            var _this8$swiperContaine, _this8$swiperContaine2;
            _this8.signalService.removeAllData();
            _this8.slidesData = [];
            (_this8$swiperContaine = _this8.swiperContainer) === null || _this8$swiperContaine === void 0 || (_this8$swiperContaine = _this8$swiperContaine.nativeElement.swiper) === null || _this8$swiperContaine === void 0 || _this8$swiperContaine.removeAllSlides();
            (_this8$swiperContaine2 = _this8.swiperContainer) === null || _this8$swiperContaine2 === void 0 || (_this8$swiperContaine2 = _this8$swiperContaine2.nativeElement.swiper) === null || _this8$swiperContaine2 === void 0 || _this8$swiperContaine2.update();
            localStorage.removeItem('selectedSupplier');
            // redirect to scan-bl
            _this8.navCtrl.navigateRoot('/scan-bl');
          }
        }]
      });
      yield alert.present();
    })();
  }
  processOcrMulti() {
    var _this9 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      var _this9$slidesData$0$r;
      _this9.jobId = _this9.apiService.generateJobId(); // Generate job ID
      const websocketUrl = `${src_environments_environment__WEBPACK_IMPORTED_MODULE_4__.environment.webSocketUrl}/${_this9.jobId}`;
      console.log('WebSocket URL doc-list:', websocketUrl);
      _this9.webSocketService.connect(websocketUrl, _this9.jobId);
      _this9.webSocketService.onMessage(_this9.jobId).subscribe(message => {
        if (message.progress !== undefined) {
          _this9.progress = message.progress;
          console.log("progress __ :", _this9.progress);
        }
      });
      // const loading = await this.presentLoading(); // Show loading spinner
      _this9.isLoading = true;
      const that = _this9;
      // check if data.supplier_name && data.random_id exists
      if (_this9.slidesData.some(data => data.supplier_name == undefined || data.random_id == undefined || data.supplier_name == '' || data.random_id == '')) {
        const errorMessage = `
      <h3>Le fournisseur ou l'image est incorrect</h3>
      <ul>
        <li>Verifier le founerisseur selectionné</li>
        <li>Verifier que l'image est un document</li>
      </ul>
      `;
        _this9.apiService.showErrorAlert(errorMessage);
        _this9.isLoading = false;
        return;
      }
      // Get random_id from the first image since all images have the same random_id
      const globalRandomId = ((_this9$slidesData$0$r = _this9.slidesData[0].random_id) !== null && _this9$slidesData$0$r !== void 0 ? _this9$slidesData$0$r : '').toString();
      const imageDataArray = _this9.slidesData.map(data => {
        var _data$cropped_image$s, _data$cropped_image, _data$cropped_image2, _data$supplier_name;
        // Determine model name
        let modelName;
        if (data.supplier_name) {
          modelName = data.supplier_name.toUpperCase();
          if (modelName === 'UNKNOWN') {
            modelName = localStorage.getItem('selectedSupplier') && localStorage.getItem('selectedSupplier') != 'undefined' && localStorage.getItem('selectedSupplier') != 'AUTRE' ? '' + localStorage.getItem('selectedSupplier') : 'GLOBAL';
          }
        } else {
          modelName = localStorage.getItem('selectedSupplier') && localStorage.getItem('selectedSupplier') != 'undefined' && localStorage.getItem('selectedSupplier') != 'AUTRE' ? '' + localStorage.getItem('selectedSupplier') : 'GLOBAL';
        }
        // get it from the local storage (force the supplier to be global for apply the Advanced OCR 'Mindee')
        let forceSupplierGlobal = localStorage.getItem('forceSupplierGlobal') == 'true' ? true : false;
        // modelName = 'UNKNOWN' // -- for test error handling
        // Determine image URL
        // const models = ['GPM', 'SOPHADIMS', 'COOPER']
        // const imageUrl = models.includes(data.supplier_name?.toLocaleUpperCase() ?? '')
        //   ? (data.cropped_image?.substring(data.cropped_image?.indexOf('smart_crop_output')) ?? '')
        //   : (data.filtered_image?.substring(data.filtered_image?.indexOf('magic_pro_filter_output')) ?? '');
        const imageUrl = (_data$cropped_image$s = (_data$cropped_image = data.cropped_image) === null || _data$cropped_image === void 0 ? void 0 : _data$cropped_image.substring((_data$cropped_image2 = data.cropped_image) === null || _data$cropped_image2 === void 0 ? void 0 : _data$cropped_image2.indexOf('smart_crop_output'))) !== null && _data$cropped_image$s !== void 0 ? _data$cropped_image$s : ''; // Force to use smart_crop_output image 
        console.log('supplier_name:', (_data$supplier_name = data.supplier_name) === null || _data$supplier_name === void 0 ? void 0 : _data$supplier_name.toLocaleUpperCase());
        console.log('Image URL:', imageUrl);
        return {
          image: imageUrl,
          // model_name: forceSupplierGlobal == true ? 'GLOBAL' : modelName, // force the supplier to be global for apply the Advanced OCR 'Mindee'
          model_name: modelName,
          random_id: data.random_id
        };
      });
      console.log('Image data array:', JSON.stringify(imageDataArray));
      // Get the current OCR mode from localStorage or use default
      const currentOcrMode = localStorage.getItem('ocrMode') || OcrMode.MINDEE_ADVANCED;
      _this9.apiService.processOcrMulti(imageDataArray, _this9.jobId, globalRandomId, currentOcrMode).subscribe(response => {
        console.log('OCR Multi response:', response);
        try {
          _this9.signalService.transformAndSetData(_this9.transformAndSetData(response['responses']));
          // loading.dismiss();
          _this9.isLoading = false;
          // this.navCtrl.navigateForward('/data-bl', { state: { data: this.signalService.getTransformedData() } }); // Redirect to data-bl page
          _this9.navCtrl.navigateForward('/data-bl-success', {
            state: {
              BL_id: response['ID_BL'],
              supplier_name: imageDataArray[0].model_name
            }
          }); // Redirect to data-bl-success page
          // Disconnect WebSocket after completion
          _this9.webSocketService.close(_this9.jobId);
        } catch (e) {
          const errorMessage = `
          <h3>Erreur de traitement du document</h3>
          <ul>
            <li>Verifier que l'image est un document</li>
            <li>Verifier le founerisseur selectionné</li>
            <li>Verifier le bon cadrage du document</li>
            <li>Verifier la qualité de l'image</li>
            <li>Supprimer les objets inutiles dans l'image </li>
          </ul>
          `;
          _this9.apiService.showErrorAlert(errorMessage);
          _this9.isLoading = false;
          _this9.progress = 0;
          _this9.navCtrl.navigateRoot('/request-error');
        }
      }, error => {
        // loading.dismiss();
        _this9.isLoading = false;
        const errorMessage = `
        <h3>Erreur de traitement du document</h3>
        <ul>
          <li>Verifier que l'image est un document</li>
          <li>Verifier le founerisseur selectionné</li>
          <li>Verifier le bon cadrage du document</li>
          <li>Verifier la qualité de l'image</li>
          <li>Supprimer les objets inutiles dans l'image </li>
        </ul>
        `;
        _this9.apiService.showErrorAlert(errorMessage);
        console.error('API error:', error);
        console.error(errorMessage);
        // // Disconnect WebSocket after completion
        _this9.webSocketService.close(_this9.jobId);
      });
      _this9.webSocketService.onMessage(_this9.jobId).subscribe(message => {
        if (message.progress !== undefined) {
          _this9.progress = message.progress;
        }
      });
    })();
  }
  presentLoading() {
    var _this10 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const loading = yield _this10.loadingController.create({
        message: 'Chargement...',
        spinner: 'circles'
        // duration: 30000 // Optional: specify a timeout for the loading spinner
      });
      yield loading.present();
      return loading;
    })();
  }
  transformAndSetData(responseData) {
    try {
      const transformedData = responseData.map((item, index) => {
        var _item$data;
        const general = (_item$data = item.data) === null || _item$data === void 0 ? void 0 : _item$data.general;
        const products = item.data.table.filter(product => product.designation || product.quantity || product.date_per || product.ppv || product.pph || product.total_ttc) // Filter out products with all fields null
        .map(product => ({
          designation: product.designation,
          quantity: product.quantity ? parseInt(product.quantity) : null,
          expiryDate: product.date_per || null,
          ppv: product.ppv ? parseFloat(product.ppv) : null,
          pph: product.pph ? parseFloat(product.pph) : null,
          total: product.total_ttc ? parseFloat(product.total_ttc) : null
        }));
        return {
          image: general.images_url_path.origin,
          title: `Scan ${this.signalService.formatDate()}`,
          date: this.signalService.getFormattedDate(general.date_export),
          page_index: index + 1,
          products
        };
      });
      return transformedData;
    } catch (e) {
      console.log('Error:', e);
      const errorMessage = `
      <h3>Erreur de traitement du document</h3>
      <ul>
        <li>Verifier que l'image est un document</li>
        <li>Verifier le founerisseur selectionné</li>
        <li>Verifier le bon cadrage du document</li>
        <li>Verifier la qualité de l'image</li>
        <li>Supprimer les objets inutiles dans l'image </li>
      </ul>
      `;
      this.apiService.showErrorAlert(errorMessage);
      this.isLoading = false;
      this.progress = 0;
      this.navCtrl.navigateRoot('/request-error');
      return [];
    }
  }
}
_DocListPage = DocListPage;
_DocListPage.ɵfac = function DocListPage_Factory(t) {
  return new (t || _DocListPage)(_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_12__.AlertController), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_services_websocket_service__WEBPACK_IMPORTED_MODULE_5__.WebSocketService), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_services_network_service__WEBPACK_IMPORTED_MODULE_6__.NetworkService));
};
_DocListPage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdefineComponent"]({
  type: _DocListPage,
  selectors: [["app-doc-list"]],
  viewQuery: function DocListPage_Query(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵviewQuery"](_c0, 7);
    }
    if (rf & 2) {
      let _t;
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵloadQuery"]()) && (ctx.swiperContainer = _t.first);
    }
  },
  decls: 26,
  vars: 16,
  consts: [[3, "ngClass"], ["slot", "end"], [3, "click"], ["name", "camera-plus", 1, "camera-plus"], [1, "doc-list-wrapper", 3, "ngClass"], [1, "section-title"], [1, "doc-list"], ["class", "document-card", 4, "ngFor", "ngForOf"], [1, "alert-progress", 3, "ngClass"], [3, "progress"], ["size", "small", 1, "menu-button", "active", 3, "routerLink"], ["name", "files"], [1, "menu-button-middle", 3, "click"], ["name", "extract"], ["size", "small", 1, "menu-button", 3, "click"], ["name", "delete"], [1, "document-card"], ["slot", "start"], [3, "src"], [1, "page-count"], ["side", "end"], [1, "renameCard", 3, "click"], [1, "content-item-option"], ["slot", "start", "name", "create-outline"], [1, "confirmDeleteCard", 3, "click"], ["slot", "start", "name", "trash"]],
  template: function DocListPage_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](0, "ion-header", 0)(1, "ion-toolbar")(2, "ion-title");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](3, "Liste des pages");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](4, "ion-buttons", 1)(5, "ion-button", 2);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function DocListPage_Template_ion_button_click_5_listener() {
        return ctx.scan_bl();
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](6, "app-custom-icon", 3);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](7, "ion-content", 4);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](8, "app-check-network");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](9, "h2", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](10, "Pages");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](11, "div", 6);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtemplate"](12, DocListPage_ion_item_sliding_12_Template, 22, 4, "ion-item-sliding", 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](13, "div", 8);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](14, "app-custom-alert", 9);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](15, "ion-footer", 0)(16, "ion-toolbar")(17, "ion-buttons")(18, "ion-button", 10);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](19, "app-custom-icon", 11);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](20, "ion-button", 12);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function DocListPage_Template_ion_button_click_20_listener() {
        return ctx.processOcrMulti();
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](21, "app-custom-icon", 13);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](22, "span");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](23, "EXTRAIRE");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](24, "ion-button", 14);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function DocListPage_Template_ion_button_click_24_listener() {
        return ctx.removeAllDoc();
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](25, "app-custom-icon", 15);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵpureFunction1"](7, _c1, ctx.isLoading));
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](7);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵpureFunction1"](9, _c1, ctx.isLoading));
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](5);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngForOf", ctx.slidesData);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵpureFunction1"](11, _c1, ctx.isLoading));
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("progress", ctx.progress);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵpureFunction1"](13, _c1, ctx.isLoading));
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](3);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵpureFunction0"](15, _c2));
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_13__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_13__.NgForOf, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonButtons, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonFooter, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonIcon, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonItem, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonItemOption, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonItemOptions, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonItemSliding, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonLabel, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonThumbnail, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonToolbar, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.RouterLinkDelegate, _angular_router__WEBPACK_IMPORTED_MODULE_14__.RouterLink, _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_7__.CustomIconComponent, _check_network_check_network_component__WEBPACK_IMPORTED_MODULE_8__.CheckNetworkComponent, _custom_alert_custom_alert_component__WEBPACK_IMPORTED_MODULE_9__.CustomAlertComponent],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\nion-content[_ngcontent-%COMP%]::part(scroll) {\n  scrollbar-width: none !important;\n  -ms-overflow-style: none !important;\n}\n\nion-content[_ngcontent-%COMP%] {\n  --offset-top: 0px !important;\n}\n\nion-header[_ngcontent-%COMP%] {\n  height: 70px;\n  --border: 0;\n  display: flex;\n  align-items: center;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --border: 0;\n  --border-width: 0;\n  display: flex;\n  justify-content: center;\n  align-items: flex-end;\n  flex-direction: row;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%] {\n  font-size: 26px;\n  font-weight: 700;\n  color: #2f4fcd;\n  text-align: left;\n  width: 100%;\n  padding-left: 2rem;\n}\n\n  ion-header ion-toolbar app-custom-icon .custom-icon {\n  width: 40px !important;\n  height: 40px !important;\n  margin-right: 5px !important;\n}\n\n.doc-list-wrapper[_ngcontent-%COMP%] {\n  background: url(\"/assets/bg-scan-bl.png\") no-repeat center center fixed;\n  background-size: cover;\n  height: 80%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  margin-top: 2rem;\n}\n\n  .scan-bl-wrapper .file-import-icon img {\n  width: 150px !important;\n  height: 150px !important;\n}\n\n  .scan-bl-wrapper .arrow-bottom-icon img {\n  width: 100px !important;\n  height: 100px !important;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\n  padding: 10px 50px 0 50px;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 22px;\n  font-weight: 700;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 12px;\n  text-align: justify;\n  padding: 5px 10px;\n}\n\n.scan-bl-content[_ngcontent-%COMP%] {\n  --background: #ffffff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n}\n\n.empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.document-icon[_ngcontent-%COMP%] {\n  font-size: 100px;\n  color: #c4c4c4;\n}\n\nion-label[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\nion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  font-weight: bold;\n  color: #404040;\n  font-size: 14px;\n}\n\nion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  color: #888888;\n  font-size: 14px;\n  margin-top: 10px;\n  margin-bottom: 10px;\n  opacity: 0.5;\n}\n\nh2[_ngcontent-%COMP%] {\n  color: #555555;\n  font-size: 18px;\n  margin-top: 20px;\n}\n\n.arrow-icon[_ngcontent-%COMP%] {\n  font-size: 30px;\n  color: #3b82f6;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  background-color: #e5e7eb;\n}\n\n.camera-button[_ngcontent-%COMP%] {\n  --background: #3b82f6;\n  --background-activated: #2563eb;\n  border-radius: 50%;\n  width: 60px;\n  height: 60px;\n  margin-top: -30px;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #3b82f6;\n}\n\nion-button[_ngcontent-%COMP%] {\n  --color: #3b82f6;\n}\n\nion-button[slot=icon-only][_ngcontent-%COMP%] {\n  --color: #3b82f6;\n}\n\nion-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  position: relative;\n  background-color: #dddbff;\n  height: 110px;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --border-width: 0;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%], ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-evenly;\n  align-items: center;\n  flex-direction: row;\n}\n\n  ion-button.menu-button app-custom-icon img {\n  width: 30px !important;\n  height: 30px !important;\n  color: #000;\n}\n\n  .menu-button.active app-custom-icon img {\n  color: #2f4fcd;\n}\n\n  .menu-button-middle {\n  background-color: #2f4fcd;\n  padding: 2px 12px;\n  border-radius: 14px;\n  width: 160px;\n  height: 60px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n  margin-bottom: 15px;\n}\n  .menu-button-middle app-custom-icon img {\n  width: 35px !important;\n  height: 35px !important;\n  color: #fff;\n}\n  .menu-button-middle span {\n  color: #fff;\n  font-weight: 500;\n  font-size: 16px;\n  padding-left: 10px;\n}\n\n.document-card[_ngcontent-%COMP%] {\n  margin: 14px;\n  border-radius: 16px;\n  box-shadow: none;\n  border: 1px solid #e5ebfd;\n  border-width: 0.55px;\n  width: auto;\n}\n\n.doc-list[_ngcontent-%COMP%] {\n  overflow-y: auto !important;\n  --overflow: auto !important;\n}\n\nion-thumbnail[_ngcontent-%COMP%] {\n  --border-radius: 2px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  height: 80px;\n}\n\n.page-count[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #888;\n  margin-left: auto;\n  padding-right: 8px;\n}\n\nion-item[_ngcontent-%COMP%]::part(native) {\n  border-style: auto !important;\n  --border-style: auto !important;\n}\n\n.section-title[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  margin-bottom: 0;\n  color: #4b4b4b;\n  font-size: 16px;\n  margin-left: 16px;\n  opacity: 0.5;\n}\n\nswiper-container[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%] {\n  padding: 0;\n  margin: 0;\n  border-radius: 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin-top: 10px;\n}\n\n.card-doc[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: flex-start;\n  width: 60%;\n  height: 100%;\n  box-shadow: none;\n  margin: 0;\n  padding: 0;\n  border: 2px solid #e5ebfd;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.card-doc[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 150px;\n  object-fit: cover;\n}\n\n.card-doc[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 10px 20px;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: flex-start;\n}\n.card-doc[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-subtitle[_ngcontent-%COMP%] {\n  width: 100%;\n  font-size: 14px;\n  text-align: left;\n  font-weight: 600;\n  color: #202020;\n}\n\n.content-global-card[_ngcontent-%COMP%] {\n  width: 100%;\n  background-color: #fff;\n  border-top: 1px solid #e5ebfd;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.card-doc[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  width: 100%;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-between;\n  padding-bottom: 10px;\n}\n.card-doc[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1n) {\n  color: #4b4b4b;\n  font-size: 14px;\n  opacity: 0.5;\n}\n.card-doc[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2n) {\n  color: #070707;\n  font-size: 14px;\n  font-weight: 500;\n  opacity: 1;\n}\n\nion-item-option[_ngcontent-%COMP%] {\n  border-radius: 12px;\n  width: 90px;\n  margin-left: 5px;\n}\n\n.content-item-option[_ngcontent-%COMP%] {\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n}\n.content-item-option[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-bottom: 6px;\n  font-size: 26px;\n  font-weight: bold;\n}\n.content-item-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  color: #fff;\n  font-size: 13px;\n  font-weight: 500;\n}\n\n.renameCard[_ngcontent-%COMP%] {\n  background-color: #cccbcb;\n}\n\n.confirmDeleteCard[_ngcontent-%COMP%] {\n  background-color: #FFA5A5;\n}\n\n\n\n.swiper[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n  swiper-slide .swiper-slide-shadow {\n  background: none !important;\n  --background: none !important;\n}\n\n.card-doc[_ngcontent-%COMP%] {\n  left: 0px !important;\n}\n\n  .custom-alert-button, custom-alert-button-rename-doc[_ngcontent-%COMP%] {\n  display: inline-block;\n  text-align: center;\n  font-size: 14px !important;\n  font-weight: bold;\n}\n\n  .custom-alert-button.cancel,   .custom-alert-button-rename-doc.cancel {\n  color: #2563eb;\n  width: 48%;\n}\n\n  .custom-alert-button-rename-doc.cancel {\n  font-size: 16px;\n}\n\n  .custom-alert-button.danger {\n  color: red;\n  width: 50%;\n}\n\n  .custom-alert-button-rename-doc.rename {\n  font-size: 16px;\n  color: #535353;\n}\n\n\n\n\n\n  .loading:not(.alert-progress) {\n  opacity: 0.5;\n  pointer-events: none; \n\n  --background: rgba(0, 0, 0, 0.1);\n}\n\n  ion-modal {\n  height: 23%;\n  width: 90%;\n  position: absolute;\n  top: 35%;\n  left: 5%;\n  --background:none;\n  --backdrop-opacity: var(--ion-backdrop-opacity, 0);\n}\n\n  .alert-progress {\n  position: absolute;\n  width: 100%;\n  top: 40%;\n  justify-content: center;\n  align-items: center;\n}\n\n  .alert-progress app-custom-alert {\n  width: 90%;\n}\n\n  .alert-progress {\n  display: none;\n}\n\n  .alert-progress.loading {\n  display: flex;\n}\n\n\n\n\n\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ }),

/***/ 30765:
/*!***********************************************!*\
  !*** ./src/app/services/websocket.service.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WebSocketService: () => (/* binding */ WebSocketService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 10819);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _WebSocketService;


class WebSocketService {
  constructor() {
    this.subjects = {};
  }
  connect(url, jobId) {
    this.socket = new WebSocket(url);
    this.socket.onopen = event => {
      console.log('WebSocket connection established:', event);
    };
    this.socket.onmessage = event => {
      const data = JSON.parse(event.data);
      if (data.job_id && this.subjects[data.job_id]) {
        this.subjects[data.job_id].next(data);
      } else if (this.subjects[jobId]) {
        this.subjects[jobId].next(data);
      }
    };
    this.socket.onerror = event => {
      console.error('WebSocket error observed:', event);
      this.reconnect(url, jobId);
    };
    this.socket.onclose = event => {
      console.log('WebSocket connection closed:', event);
      this.reconnect(url, jobId);
    };
  }
  reconnect(url, jobId) {
    setTimeout(() => {
      this.connect(url, jobId);
    }, 1000); // Retry connection after 1 second
  }
  send(data) {
    var _this$socket;
    if (((_this$socket = this.socket) === null || _this$socket === void 0 ? void 0 : _this$socket.readyState) === WebSocket.OPEN) {
      var _this$socket2;
      (_this$socket2 = this.socket) === null || _this$socket2 === void 0 || _this$socket2.send(JSON.stringify(data));
    } else {
      console.error('WebSocket connection is not open.');
    }
  }
  onMessage(jobId) {
    if (!this.subjects[jobId]) {
      this.subjects[jobId] = new rxjs__WEBPACK_IMPORTED_MODULE_0__.Subject();
    }
    return this.subjects[jobId].asObservable();
  }
  close(jobId) {
    if (this.socket) {
      this.socket.close();
      delete this.subjects[jobId];
    }
  }
}
_WebSocketService = WebSocketService;
_WebSocketService.ɵfac = function WebSocketService_Factory(t) {
  return new (t || _WebSocketService)();
};
_WebSocketService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
  token: _WebSocketService,
  factory: _WebSocketService.ɵfac,
  providedIn: 'root'
});

/***/ }),

/***/ 89971:
/*!****************************************!*\
  !*** ./node_modules/swiper/swiper.mjs ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Swiper: () => (/* reexport safe */ _shared_swiper_core_mjs__WEBPACK_IMPORTED_MODULE_0__.S),
/* harmony export */   "default": () => (/* reexport safe */ _shared_swiper_core_mjs__WEBPACK_IMPORTED_MODULE_0__.S)
/* harmony export */ });
/* harmony import */ var _shared_swiper_core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./shared/swiper-core.mjs */ 1385);
/**
 * Swiper 11.1.4
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2024 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: May 30, 2024
 */



/***/ })

}]);
//# sourceMappingURL=src_app_doc-list_doc-list_module_ts.js.map