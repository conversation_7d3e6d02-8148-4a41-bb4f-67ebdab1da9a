{"version": 3, "file": "node_modules_ionic_pwa-elements_dist_esm_pwa-camera_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAkG;;AAElG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIO,YAAY,GAAGC,MAAM,CAACD,YAAY;AACtC,IAAI,OAAOA,YAAY,KAAK,WAAW,EAAE;EACvCA,YAAY,GAAG,MAAM;IACnB;AACJ;AACA;AACA;AACA;IACIE,WAAWA,CAACC,gBAAgB,EAAE;MAC5B,IAAIA,gBAAgB,CAACC,IAAI,KAAK,OAAO,EACnC,MAAM,IAAIC,YAAY,CAAC,mBAAmB,CAAC;MAC7C,IAAI,CAACC,iBAAiB,GAAGH,gBAAgB;MACzC,IAAI,EAAE,YAAY,IAAI,IAAI,CAACG,iBAAiB,CAAC,EAAE;QAC7C;QACA,IAAI,CAACA,iBAAiB,CAACC,UAAU,GAAG,MAAM;MAC5C;MACA;MACA,IAAI,CAACC,cAAc,GAAG,IAAIC,WAAW,CAAC,CAACN,gBAAgB,CAAC,CAAC;MACzD,IAAI,CAACO,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MACnD,IAAI,CAACC,mBAAmB,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAI;QAChD,IAAI,CAACL,YAAY,CAACM,gBAAgB,CAAC,SAAS,EAAED,OAAO,CAAC;MACxD,CAAC,CAAC;MACF,IAAIE,gBAAgB,EAAE;QACpB,IAAI,CAACP,YAAY,CAACQ,SAAS,GAAG,IAAI,CAACV,cAAc,CAAC,CAAC;MACrD,CAAC,MACI;QACH,IAAI,CAACE,YAAY,CAACS,GAAG,GAAGC,GAAG,CAACC,eAAe,CAAC,IAAI,CAACb,cAAc,CAAC;MAClE;MACA,IAAI,CAACE,YAAY,CAACY,KAAK,GAAG,IAAI;MAC9B,IAAI,CAACZ,YAAY,CAACa,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;MACnD,IAAI,CAACb,YAAY,CAACc,IAAI,CAAC,CAAC;MACxB,IAAI,CAACC,aAAa,GAAGd,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MACrD;MACA,IAAI,CAACc,eAAe,GAAG,IAAI,CAACD,aAAa,CAACE,UAAU,CAAC,IAAI,CAAC;IAC5D;IACA;AACJ;AACA;AACA;IACI,IAAIxB,gBAAgBA,CAAA,EAAG;MACrB,OAAO,IAAI,CAACG,iBAAiB;IAC/B;IACA;AACJ;AACA;AACA;AACA;AACA;IACIsB,oBAAoBA,CAAA,EAAG;MACrB,OAAO,IAAId,OAAO,CAAC,SAASe,WAAWA,CAACd,OAAO,EAAEe,MAAM,EAAE;QACvD;QACA,MAAMC,kBAAkB,GAAG;UACzBC,OAAO,EAAE,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,GAAG,EAAE;QAC3B,CAAC;QACDnB,OAAO,CAAC;UACNoB,oBAAoB,EAAEJ,kBAAkB;UACxCK,YAAY,EAAE,MAAM;UACpBC,aAAa,EAAE,CAAC,MAAM,CAAC;UACvBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAER,kBAAkB;UAC/BS,UAAU,EAAET,kBAAkB;UAC9BU,GAAG,EAAEV,kBAAkB;UACvBW,eAAe,EAAE,KAAK;UACtBC,gBAAgB,EAAE,MAAM;UACxBC,IAAI,EAAEb;QACR,CAAC,CAAC;QACFD,MAAM,CAAC,IAAIzB,YAAY,CAAC,gBAAgB,CAAC,CAAC;MAC5C,CAAC,CAAC;IACJ;IACA;AACJ;AACA;AACA;AACA;IACIwC,UAAUA,CAACC,cAAc,GAAG,CAAC,CAAC,EAAE;MAC9B,OAAO,IAAIhC,OAAO,CAAC,SAASiC,UAAUA,CAACC,QAAQ,EAAEC,OAAO,EAAE;QACxD;MAAA,CACD,CAAC;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;IACIC,SAASA,CAAA,EAAG;MACV,MAAMC,IAAI,GAAG,IAAI;MACjB,OAAO,IAAIrC,OAAO,CAAC,SAASsC,UAAUA,CAACrC,OAAO,EAAEe,MAAM,EAAE;QACtD;QACA;QACA,IAAIqB,IAAI,CAAC7C,iBAAiB,CAACC,UAAU,KAAK,MAAM,EAAE;UAChD,OAAOuB,MAAM,CAAC,IAAIzB,YAAY,CAAC,mBAAmB,CAAC,CAAC;QACtD;QACA8C,IAAI,CAACtC,mBAAmB,CAACwC,IAAI,CAAC,MAAM;UAClC,IAAI;YACFF,IAAI,CAAC1B,aAAa,CAAC6B,KAAK,GAAGH,IAAI,CAACzC,YAAY,CAAC6C,UAAU;YACvDJ,IAAI,CAAC1B,aAAa,CAAC+B,MAAM,GAAGL,IAAI,CAACzC,YAAY,CAAC+C,WAAW;YACzDN,IAAI,CAACzB,eAAe,CAACgC,SAAS,CAACP,IAAI,CAACzC,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC;YACvDyC,IAAI,CAAC1B,aAAa,CAACkC,MAAM,CAAC5C,OAAO,CAAC;UACpC,CAAC,CACD,OAAO6C,KAAK,EAAE;YACZ9B,MAAM,CAAC,IAAIzB,YAAY,CAAC,cAAc,CAAC,CAAC;UAC1C;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;IACIwD,SAASA,CAAA,EAAG;MACV,MAAMV,IAAI,GAAG,IAAI;MACjB,OAAO,IAAIrC,OAAO,CAAC,SAASgD,UAAUA,CAAC/C,OAAO,EAAEe,MAAM,EAAE;QACtD;QACA;QACA,IAAIqB,IAAI,CAAC7C,iBAAiB,CAACC,UAAU,KAAK,MAAM,EAAE;UAChD,OAAOuB,MAAM,CAAC,IAAIzB,YAAY,CAAC,mBAAmB,CAAC,CAAC;QACtD;QACA8C,IAAI,CAACtC,mBAAmB,CAACwC,IAAI,CAAC,MAAM;UAClC,IAAI;YACFF,IAAI,CAAC1B,aAAa,CAAC6B,KAAK,GAAGH,IAAI,CAACzC,YAAY,CAAC6C,UAAU;YACvDJ,IAAI,CAAC1B,aAAa,CAAC+B,MAAM,GAAGL,IAAI,CAACzC,YAAY,CAAC+C,WAAW;YACzDN,IAAI,CAACzB,eAAe,CAACgC,SAAS,CAACP,IAAI,CAACzC,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC;YACvD;YACAK,OAAO,CAACd,MAAM,CAAC8D,iBAAiB,CAACZ,IAAI,CAAC1B,aAAa,CAAC,CAAC;UACvD,CAAC,CACD,OAAOmC,KAAK,EAAE;YACZ9B,MAAM,CAAC,IAAIzB,YAAY,CAAC,cAAc,CAAC,CAAC;UAC1C;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AACAJ,MAAM,CAACD,YAAY,GAAGA,YAAY;AAElC,MAAMgE,SAAS,GAAG,wvHAAwvH;AAE1wH,MAAMC,SAAS,GAAG,MAAM;EACtB/D,WAAWA,CAACgE,OAAO,EAAE;IAAA,IAAAC,KAAA;IACnBzE,qDAAgB,CAAC,IAAI,EAAEwE,OAAO,CAAC;IAC/B;IACA,IAAI,CAACE,kBAAkB,GAAG,KAAK;IAC/B;IACA,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,cAAc,GAAIC,EAAE,IAAK,CAC9B,CAAC;IACD,IAAI,CAACC,kBAAkB,GAAID,EAAE,IAAK;MAChCE,OAAO,CAACC,KAAK,CAAC,eAAe,CAAC;MAC9B,IAAI,CAACC,OAAO,CAAC,CAAC;IAChB,CAAC;IACD,IAAI,CAACC,iBAAiB,GAAIL,EAAE,IAAK;MAC/B,IAAI,CAACM,MAAM,CAAC,CAAC;IACf,CAAC;IACD,IAAI,CAACC,WAAW,GAAIP,EAAE,IAAK;MACzB,IAAI,CAACQ,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC,IAAI,CAAC;IAC5C,CAAC;IACD,IAAI,CAACC,gBAAgB,GAAIT,EAAE,IAAK;MAC9B,IAAI,CAACU,UAAU,CAAC,CAAC;IACnB,CAAC;IACD,IAAI,CAACC,iBAAiB,GAAIX,EAAE,IAAK;MAC/B,MAAMY,KAAK,GAAG,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,IAAIC,CAAC,GAAGH,KAAK,IAAIA,KAAK,CAACI,cAAc,CAAC,CAAC;MACvC,IAAI,CAACC,KAAK,GAAG,IAAI;MACjB,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAIH,CAAC,EAAE;QACL,IAAI,CAACI,UAAU,CAAC;UACdC,KAAK,EAAE;YACLC,UAAU,EAAEN,CAAC,CAACM;UAChB;QACF,CAAC,CAAC;MACJ,CAAC,MACI;QACH,IAAI,CAACF,UAAU,CAAC,CAAC;MACnB;IACF,CAAC;IACD,IAAI,CAACG,iBAAiB,GAAItB,EAAE,IAAK;MAC/B,IAAI,CAACQ,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC,IAAI,CAACS,KAAK,CAAC;IAClD,CAAC;IACD,IAAI,CAACM,qBAAqB;MAAA,IAAAC,IAAA,GAAAC,6OAAA,CAAG,WAAOC,CAAC,EAAK;QACxC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM;QACtB,MAAMC,IAAI,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;QAC3B,IAAI;UACF,MAAMC,WAAW,SAASrC,KAAI,CAACsC,cAAc,CAACH,IAAI,CAAC;UACnD3B,OAAO,CAACC,KAAK,CAAC,iBAAiB,EAAE4B,WAAW,CAAC;UAC7CrC,KAAI,CAACuC,gBAAgB,GAAGF,WAAW;QACrC,CAAC,CACD,OAAOL,CAAC,EAAE,CACV;QACAhC,KAAI,CAACc,WAAW,IAAId,KAAI,CAACc,WAAW,CAACqB,IAAI,CAAC;MAC5C,CAAC;MAAA,iBAAAK,EAAA;QAAA,OAAAV,IAAA,CAAAW,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACC,mBAAmB,GAAIX,CAAC,IAAK;MAChCxB,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEuB,CAAC,CAAC;IACpC,CAAC;IACD,IAAI,CAACL,UAAU,GAAG,MAAM;IACxB,IAAI,CAACb,WAAW,GAAG8B,SAAS;IAC5B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,mBAAmB,GAAGF,SAAS;IACpC,IAAI,CAACG,aAAa,GAAG,iBAAiB;IACtC,IAAI,CAACC,mBAAmB,GAAG,cAAc;IACzC,IAAI,CAACzB,KAAK,GAAGqB,SAAS;IACtB,IAAI,CAACpB,QAAQ,GAAGoB,SAAS;IACzB,IAAI,CAACK,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,WAAW,GAAG,IAAI;EACzB;EACMC,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAxB,6OAAA;MACvBwB,MAAI,CAACC,kBAAkB,GAAG;QACxB9B,KAAK,EAAE;UACLC,UAAU,EAAE4B,MAAI,CAAC5B;QACnB;MACF,CAAC;MACD;MACA,MAAM4B,MAAI,CAACE,YAAY,CAAC,CAAC;MACzB;MACA,MAAMF,MAAI,CAAC9B,UAAU,CAAC,CAAC;IAAC;EAC1B;EACAiC,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACnC,QAAQ,IAAIvE,GAAG,CAAC2G,eAAe,CAAC,IAAI,CAACpC,QAAQ,CAAC;EACrD;EACAqC,eAAeA,CAAA,EAAG;IAChB,OAAO,cAAc,IAAI/H,MAAM;EACjC;EACA;AACF;AACA;EACQ2H,YAAYA,CAAA,EAAG;IAAA,IAAAK,MAAA;IAAA,OAAA/B,6OAAA;MACnB,IAAI;QACF,MAAMgC,OAAO,SAASC,SAAS,CAACC,YAAY,CAACC,gBAAgB,CAAC,CAAC;QAC/D,MAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpI,IAAI,IAAI,YAAY,CAAC;QAChE6H,MAAI,CAACX,SAAS,GAAG,CAAC,CAACgB,YAAY,CAACG,MAAM;QACtCR,MAAI,CAAC7D,kBAAkB,GAAGkE,YAAY,CAACG,MAAM,GAAG,CAAC;MACnD,CAAC,CACD,OAAOtC,CAAC,EAAE;QACR8B,MAAI,CAACT,WAAW,GAAGrB,CAAC;MACtB;IAAC;EACH;EACMP,UAAUA,CAAC8C,WAAW,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAzC,6OAAA;MAC5B,IAAI,CAACwC,WAAW,EAAE;QAChBA,WAAW,GAAGC,MAAI,CAAChB,kBAAkB;MACvC;MACA,IAAI;QACF,MAAMrC,MAAM,SAAS6C,SAAS,CAACC,YAAY,CAACQ,YAAY,CAACC,MAAM,CAACC,MAAM,CAAC;UAAEjD,KAAK,EAAE,IAAI;UAAEkD,KAAK,EAAE;QAAM,CAAC,EAAEL,WAAW,CAAC,CAAC;QACnHC,MAAI,CAACK,UAAU,CAAC1D,MAAM,CAAC;MACzB,CAAC,CACD,OAAOa,CAAC,EAAE;QACRwC,MAAI,CAACnB,WAAW,GAAGrB,CAAC;QACpBwC,MAAI,CAAC1B,mBAAmB,IAAI0B,MAAI,CAAC1B,mBAAmB,CAACd,CAAC,CAAC;MACzD;IAAC;EACH;EACM6C,UAAUA,CAAC1D,MAAM,EAAE;IAAA,IAAA2D,MAAA;IAAA,OAAA/C,6OAAA;MACvB+C,MAAI,CAAC3D,MAAM,GAAGA,MAAM;MACpB2D,MAAI,CAACvI,YAAY,CAACQ,SAAS,GAAGoE,MAAM;MACpC,IAAI2D,MAAI,CAACjB,eAAe,CAAC,CAAC,EAAE;QAC1BiB,MAAI,CAACC,YAAY,GAAG,IAAIjJ,MAAM,CAACD,YAAY,CAACsF,MAAM,CAAC6D,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,MAAMF,MAAI,CAACG,qBAAqB,CAACH,MAAI,CAACC,YAAY,CAAC;MACrD,CAAC,MACI;QACHD,MAAI,CAACzB,WAAW,GAAG,kBAAkB;QACrCyB,MAAI,CAAChC,mBAAmB,IAAIgC,MAAI,CAAChC,mBAAmB,CAAC,CAAC;MACxD;MACA;MACArH,qDAAW,CAACqJ,MAAI,CAACI,EAAE,CAAC;IAAC;EACvB;EACMD,qBAAqBA,CAACF,YAAY,EAAE;IAAA,IAAAI,MAAA;IAAA,OAAApD,6OAAA;MACxC,MAAMV,CAAC,SAAS0D,YAAY,CAACtH,oBAAoB,CAAC,CAAC;MACnD,IAAI4D,CAAC,CAACnD,aAAa,IAAImD,CAAC,CAACnD,aAAa,CAACoG,MAAM,GAAG,CAAC,EAAE;QACjDa,MAAI,CAAChF,UAAU,GAAGkB,CAAC,CAACnD,aAAa,CAACkH,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC;QAC7C;QACA,IAAIF,MAAI,CAAC/E,SAAS,EAAE;UAClB+E,MAAI,CAAC/E,SAAS,GAAG+E,MAAI,CAAChF,UAAU,CAACgF,MAAI,CAAChF,UAAU,CAACmF,OAAO,CAACH,MAAI,CAAC/E,SAAS,CAAC,CAAC,IAAI,KAAK;UAClF+E,MAAI,CAACjC,UAAU,GAAGiC,MAAI,CAAChF,UAAU,CAACmF,OAAO,CAACH,MAAI,CAAC/E,SAAS,CAAC,IAAI,CAAC;QAChE,CAAC,MACI;UACH+E,MAAI,CAACjC,UAAU,GAAG,CAAC;QACrB;MACF;IAAC;EACH;EACAS,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACpH,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACQ,SAAS,GAAG,IAAI;IACpC;IACA,IAAI,CAACoE,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,SAAS,CAAC,CAAC,CAACmE,OAAO,CAACrE,KAAK,IAAIA,KAAK,CAACsE,IAAI,CAAC,CAAC,CAAC;EACvE;EACM9E,OAAOA,CAAA,EAAG;IAAA,IAAA+E,MAAA;IAAA,OAAA1D,6OAAA;MACd,IAAI0D,MAAI,CAAC5B,eAAe,CAAC,CAAC,EAAE;QAC1B,IAAI;UACF,MAAMtC,KAAK,SAASkE,MAAI,CAACV,YAAY,CAAChG,SAAS,CAAC;YAC9Cb,aAAa,EAAEuH,MAAI,CAACtF,UAAU,CAACmE,MAAM,GAAG,CAAC,GAAGmB,MAAI,CAACrF,SAAS,GAAGwC;UAC/D,CAAC,CAAC;UACF,MAAM6C,MAAI,CAACC,WAAW,CAAC,CAAC;UACxBD,MAAI,CAACE,YAAY,CAACpE,KAAK,CAAC;QAC1B,CAAC,CACD,OAAOS,CAAC,EAAE;UACRxB,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEuC,CAAC,CAAC;QAC3C;MACF;MACAyD,MAAI,CAAC9B,UAAU,CAAC,CAAC;IAAC;EACpB;EACMgC,YAAYA,CAACpE,KAAK,EAAE;IAAA,IAAAqE,MAAA;IAAA,OAAA7D,6OAAA;MACxB6D,MAAI,CAACrE,KAAK,GAAGA,KAAK;MAClB,MAAMc,WAAW,SAASuD,MAAI,CAACtD,cAAc,CAACf,KAAK,CAAC;MACpDf,OAAO,CAACC,KAAK,CAAC,iBAAiB,EAAE4B,WAAW,CAAC;MAC7CuD,MAAI,CAACrD,gBAAgB,GAAGF,WAAW;MACnC,IAAIA,WAAW,EAAE;QACf,QAAQA,WAAW;UACjB,KAAK,CAAC;UACN,KAAK,CAAC;YACJuD,MAAI,CAACxC,QAAQ,GAAG,CAAC;YACjB;UACF,KAAK,CAAC;UACN,KAAK,CAAC;YACJwC,MAAI,CAACxC,QAAQ,GAAG,GAAG;YACnB;UACF,KAAK,CAAC;UACN,KAAK,CAAC;YACJwC,MAAI,CAACxC,QAAQ,GAAG,EAAE;YAClB;UACF,KAAK,CAAC;UACN,KAAK,CAAC;YACJwC,MAAI,CAACxC,QAAQ,GAAG,GAAG;YACnB;QACJ;MACF;MACAwC,MAAI,CAACpE,QAAQ,GAAGvE,GAAG,CAACC,eAAe,CAACqE,KAAK,CAAC;IAAC;EAC7C;EACAe,cAAcA,CAACH,IAAI,EAAE;IACnB,OAAO,IAAIxF,OAAO,CAACC,OAAO,IAAI;MAC5B,MAAMiJ,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAK;QACzB,MAAMC,IAAI,GAAG,IAAIC,QAAQ,CAACF,KAAK,CAAC9D,MAAM,CAACiE,MAAM,CAAC;QAC9C,IAAIF,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,MAAM,EAAE;UACvC,OAAOxJ,OAAO,CAAC,CAAC,CAAC,CAAC;QACpB;QACA,MAAM0H,MAAM,GAAG2B,IAAI,CAACI,UAAU;QAC9B,IAAIC,MAAM,GAAG,CAAC;QACd,OAAOA,MAAM,GAAGhC,MAAM,EAAE;UACtB,MAAMiC,MAAM,GAAGN,IAAI,CAACG,SAAS,CAACE,MAAM,EAAE,KAAK,CAAC;UAC5CA,MAAM,IAAI,CAAC;UACX,IAAIC,MAAM,KAAK,MAAM,EAAE;YACrB,IAAIN,IAAI,CAACO,SAAS,CAACF,MAAM,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,UAAU,EAAE;cACrD,OAAO1J,OAAO,CAAC,CAAC,CAAC,CAAC;YACpB;YACA,MAAM6J,MAAM,GAAGR,IAAI,CAACG,SAAS,CAACE,MAAM,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,MAAM;YAC5DA,MAAM,IAAIL,IAAI,CAACO,SAAS,CAACF,MAAM,GAAG,CAAC,EAAEG,MAAM,CAAC;YAC5C,MAAMC,IAAI,GAAGT,IAAI,CAACG,SAAS,CAACE,MAAM,EAAEG,MAAM,CAAC;YAC3CH,MAAM,IAAI,CAAC;YACX,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,EAAEC,CAAC,EAAE,EAAE;cAC7B,IAAIV,IAAI,CAACG,SAAS,CAACE,MAAM,GAAIK,CAAC,GAAG,EAAG,EAAEF,MAAM,CAAC,KAAK,MAAM,EAAE;gBACxD,OAAO7J,OAAO,CAACqJ,IAAI,CAACG,SAAS,CAACE,MAAM,GAAIK,CAAC,GAAG,EAAG,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC;cAC/D;YACF;UACF,CAAC,MACI,IAAI,CAACF,MAAM,GAAG,MAAM,MAAM,MAAM,EAAE;YACrC;UACF,CAAC,MACI;YACHD,MAAM,IAAIL,IAAI,CAACG,SAAS,CAACE,MAAM,EAAE,KAAK,CAAC;UACzC;QACF;QACA,OAAO1J,OAAO,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC;MACDiJ,MAAM,CAACe,iBAAiB,CAACzE,IAAI,CAAC0E,KAAK,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC;IACpD,CAAC,CAAC;EACJ;EACAjG,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC+C,UAAU,CAAC,CAAC;IACjB,MAAMzC,KAAK,GAAG,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,IAAI,CAACF,KAAK,EAAE;MACV;IACF;IACA,IAAIG,CAAC,GAAGH,KAAK,CAACI,cAAc,CAAC,CAAC;IAC9B,IAAIK,UAAU,GAAGN,CAAC,CAACM,UAAU;IAC7B,IAAI,CAACA,UAAU,EAAE;MACf,IAAIN,CAAC,GAAGH,KAAK,CAAC4F,eAAe,CAAC,CAAC;MAC/B,IAAIzF,CAAC,CAACM,UAAU,EAAE;QAChBA,UAAU,GAAGN,CAAC,CAACM,UAAU,CAAC,CAAC,CAAC;MAC9B;IACF;IACA,IAAIA,UAAU,KAAK,aAAa,EAAE;MAChC,IAAI,CAACF,UAAU,CAAC;QACdC,KAAK,EAAE;UACLC,UAAU,EAAE;QACd;MACF,CAAC,CAAC;IACJ,CAAC,MACI;MACH,IAAI,CAACF,UAAU,CAAC;QACdC,KAAK,EAAE;UACLC,UAAU,EAAE;QACd;MACF,CAAC,CAAC;IACJ;EACF;EACAoF,YAAYA,CAACC,IAAI,EAAE;IACjBxG,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEuG,IAAI,CAAC;IACvC,IAAI,CAAC5G,SAAS,GAAG4G,IAAI;EACvB;EACAhG,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACb,UAAU,CAACmE,MAAM,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACpB,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC/C,UAAU,CAACmE,MAAM;MAChE,IAAI,CAACyC,YAAY,CAAC,IAAI,CAAC5G,UAAU,CAAC,IAAI,CAAC+C,UAAU,CAAC,CAAC;IACrD;EACF;EACMwC,WAAWA,CAAA,EAAG;IAAA,IAAAuB,MAAA;IAAA,OAAAlF,6OAAA;MAClB,OAAO,IAAIpF,OAAO,CAAC,CAACC,OAAO,EAAEkC,OAAO,KAAK;QACvCmI,MAAI,CAAChE,kBAAkB,GAAG,IAAI;QAC9BiE,UAAU,CAAC,MAAM;UACfD,MAAI,CAAChE,kBAAkB,GAAG,KAAK;UAC/BrG,OAAO,CAAC,CAAC;QACX,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC;IAAC;EACL;EACAuK,QAAQA,CAAA,EAAG;IACT,OAAQ,4uBAA2uB;EACrvB;EACAC,UAAUA,CAAA,EAAG;IACX,OAAQ1L,qDAAC,CAAC,KAAK,EAAE;MAAE2L,KAAK,EAAE,4BAA4B;MAAElI,KAAK,EAAE,KAAK;MAAEE,MAAM,EAAE,KAAK;MAAEiI,OAAO,EAAE;IAAc,CAAC,EAAE5L,qDAAC,CAAC,MAAM,EAAE;MAAE2I,CAAC,EAAE;IAAia,CAAC,CAAC,EAAE3I,qDAAC,CAAC,MAAM,EAAE;MAAE2I,CAAC,EAAE;IAA8G,CAAC,CAAC,CAAC;EACrqB;EACAkD,WAAWA,CAAA,EAAG;IACZ,OAAQ,ymBAAwmB;EAClnB;EACAC,iBAAiBA,CAAA,EAAG;IAClB,OAAQ,opCAAmpC;EAC7pC;EACAC,UAAUA,CAAA,EAAG;IACX,OAAQ,oyBAAmyB;EAC7yB;EACAC,YAAYA,CAAA,EAAG;IACb,OAAQ,sqBAAqqB;EAC/qB;EACAC,WAAWA,CAAA,EAAG;IACZ,OAAQ,yiBAAwiB;EACljB;EACAC,aAAaA,CAAA,EAAG;IACd,OAAQ,ssBAAqsB;EAC/sB;EACAC,MAAMA,CAAA,EAAG;IACP;IACA,MAAMC,YAAY,GAAG,CAAC,CAAC;IACvB,OAAQpM,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE;IAAiB,CAAC,EAAErM,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE;IAAgB,CAAC,EAAErM,qDAAC,CAAC,SAAS,EAAE;MAAEqM,KAAK,EAAE;IAAQ,CAAC,EAAErM,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE,YAAY;MAAEC,OAAO,EAAEhG,CAAC,IAAI,IAAI,CAACnB,WAAW,CAACmB,CAAC;IAAE,CAAC,EAAEtG,qDAAC,CAAC,KAAK,EAAE;MAAEsB,GAAG,EAAE,IAAI,CAACmK,QAAQ,CAAC;IAAE,CAAC,CAAC,CAAC,EAAEzL,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE,YAAY;MAAEC,OAAO,EAAEhG,CAAC,IAAI,IAAI,CAACjB,gBAAgB,CAACiB,CAAC;IAAE,CAAC,EAAE,IAAI,CAAC7B,UAAU,CAACmE,MAAM,GAAG,CAAC,IAAK5I,qDAAC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC0E,SAAS,IAAI,KAAK,GAAG1E,qDAAC,CAAC,KAAK,EAAE;MAAEsB,GAAG,EAAE,IAAI,CAAC0K,YAAY,CAAC;IAAE,CAAC,CAAC,GAAG,EAAE,EAAE,IAAI,CAACtH,SAAS,IAAI,MAAM,GAAG1E,qDAAC,CAAC,KAAK,EAAE;MAAEsB,GAAG,EAAE,IAAI,CAAC4K,aAAa,CAAC;IAAE,CAAC,CAAC,GAAG,EAAE,EAAE,IAAI,CAACxH,SAAS,IAAI,OAAO,GAAG1E,qDAAC,CAAC,KAAK,EAAE;MAAEsB,GAAG,EAAE,IAAI,CAAC2K,WAAW,CAAC;IAAE,CAAC,CAAC,GAAG,EAAE,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAACxE,SAAS,KAAK,KAAK,IAAI,CAAC,CAAC,IAAI,CAACE,WAAW,KAAM3H,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE;IAAY,CAAC,EAAErM,qDAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAACqH,aAAa,CAAC,EAAErH,qDAAC,CAAC,OAAO,EAAE;MAAEuM,OAAO,EAAE;IAA6B,CAAC,EAAE,IAAI,CAACjF,mBAAmB,CAAC,EAAEtH,qDAAC,CAAC,OAAO,EAAE;MAAEwM,IAAI,EAAE,MAAM;MAAEC,EAAE,EAAE,4BAA4B;MAAEC,QAAQ,EAAE,IAAI,CAACvG,qBAAqB;MAAEwG,MAAM,EAAE,SAAS;MAAEN,KAAK,EAAE;IAAqB,CAAC,CAAC,CAAE,EAAE,IAAI,CAACvG,QAAQ,GAAI9F,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE;IAAS,CAAC,EAAErM,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE,cAAc;MAAEO,KAAK,EAAE5D,MAAM,CAACC,MAAM,CAAC;QAAE4D,eAAe,EAAG,OAAM,IAAI,CAAC/G,QAAS;MAAG,CAAC,EAAEsG,YAAY;IAAE,CAAC,CAAC,CAAC,GAAKpM,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE;IAAe,CAAC,EAAE,IAAI,CAAC9E,kBAAkB,IAAKvH,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE;IAAkB,CAAC,CAAE,EAAE,IAAI,CAAClE,eAAe,CAAC,CAAC,GAAInI,qDAAC,CAAC,OAAO,EAAE;MAAE8M,GAAG,EAAGtD,EAAE,IAAK,IAAI,CAAC3I,YAAY,GAAG2I,EAAE;MAAEuD,gBAAgB,EAAE,IAAI,CAAC9F,mBAAmB;MAAE+F,QAAQ,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAK,CAAC,CAAC,GAAKjN,qDAAC,CAAC,QAAQ,EAAE;MAAE8M,GAAG,EAAGtD,EAAE,IAAK,IAAI,CAAC5H,aAAa,GAAG4H,EAAE;MAAE/F,KAAK,EAAE,MAAM;MAAEE,MAAM,EAAE;IAAO,CAAC,CAAE,EAAE3D,qDAAC,CAAC,QAAQ,EAAE;MAAEqM,KAAK,EAAE,wBAAwB;MAAES,GAAG,EAAExG,CAAC,IAAI,IAAI,CAAC4G,eAAe,GAAG5G,CAAC;MAAE7C,KAAK,EAAE,MAAM;MAAEE,MAAM,EAAE;IAAO,CAAC,CAAC,CAAE,EAAE,IAAI,CAAC8D,SAAS,IAAKzH,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE;IAAgB,CAAC,EAAE,CAAC,IAAI,CAACxG,KAAK,GAAI,CAC5lD,CAAC,IAAI,CAACsB,UAAU,IAAKnH,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE,YAAY;MAAEC,OAAO,EAAE,IAAI,CAAC3H;IAAe,CAAC,EAAE3E,qDAAC,CAAC,OAAO,EAAE;MAAEuM,OAAO,EAAE;IAA0B,CAAC,EAAE,IAAI,CAACb,UAAU,CAAC,CAAC,CAAC,EAAE1L,qDAAC,CAAC,OAAO,EAAE;MAAEwM,IAAI,EAAE,MAAM;MAAEC,EAAE,EAAE,yBAAyB;MAAEC,QAAQ,EAAE,IAAI,CAACvG,qBAAqB;MAAEwG,MAAM,EAAE,SAAS;MAAEN,KAAK,EAAE;IAAoB,CAAC,CAAC,CAAE,EAC9SrM,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE,SAAS;MAAEC,OAAO,EAAE,IAAI,CAACzH;IAAmB,CAAC,EAAE7E,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE;IAAiB,CAAC,CAAC,CAAC,EACvGrM,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE,QAAQ;MAAEC,OAAO,EAAE,IAAI,CAACrH;IAAkB,CAAC,EAAEjF,qDAAC,CAAC,KAAK,EAAE;MAAEsB,GAAG,EAAE,IAAI,CAACwK,iBAAiB,CAAC;IAAE,CAAC,CAAC,CAAC,CAC5G,GAAK9L,qDAAC,CAAC,SAAS,EAAE;MAAEqM,KAAK,EAAE;IAAQ,CAAC,EAAErM,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE,oBAAoB;MAAEC,OAAO,EAAEhG,CAAC,IAAI,IAAI,CAACf,iBAAiB,CAACe,CAAC;IAAE,CAAC,EAAEtG,qDAAC,CAAC,KAAK,EAAE;MAAEsB,GAAG,EAAE,IAAI,CAACyK,UAAU,CAAC;IAAE,CAAC,CAAC,CAAC,EAAE/L,qDAAC,CAAC,KAAK,EAAE;MAAEqM,KAAK,EAAE,iBAAiB;MAAEC,OAAO,EAAEhG,CAAC,IAAI,IAAI,CAACJ,iBAAiB,CAACI,CAAC;IAAE,CAAC,EAAEtG,qDAAC,CAAC,KAAK,EAAE;MAAEsB,GAAG,EAAE,IAAI,CAACuK,WAAW,CAAC;IAAE,CAAC,CAAC,CAAC,CAAE,CAAE,CAAC;EAC9R;EACA,WAAWsB,UAAUA,CAAA,EAAG;IAAE,OAAO,CAAC,OAAO,CAAC;EAAE;EAC5C,IAAI3D,EAAEA,CAAA,EAAG;IAAE,OAAOtJ,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACDkE,SAAS,CAACwI,KAAK,GAAGzI,SAAS", "sources": ["./node_modules/@ionic/pwa-elements/dist/esm/pwa-camera.entry.js"], "sourcesContent": ["import { r as registerInstance, f as forceUpdate, h, g as getElement } from './index-1c5c47b4.js';\n\n/**\n * MediaStream ImageCapture polyfill\n *\n * @license\n * Copyright 2018 Google Inc.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nlet ImageCapture = window.ImageCapture;\nif (typeof ImageCapture === 'undefined') {\n  ImageCapture = class {\n    /**\n     * TODO https://www.w3.org/TR/image-capture/#constructors\n     *\n     * @param {MediaStreamTrack} videoStreamTrack - A MediaStreamTrack of the 'video' kind\n     */\n    constructor(videoStreamTrack) {\n      if (videoStreamTrack.kind !== 'video')\n        throw new DOMException('NotSupportedError');\n      this._videoStreamTrack = videoStreamTrack;\n      if (!('readyState' in this._videoStreamTrack)) {\n        // Polyfill for Firefox\n        this._videoStreamTrack.readyState = 'live';\n      }\n      // MediaStream constructor not available until Chrome 55 - https://www.chromestatus.com/feature/5912172546752512\n      this._previewStream = new MediaStream([videoStreamTrack]);\n      this.videoElement = document.createElement('video');\n      this.videoElementPlaying = new Promise(resolve => {\n        this.videoElement.addEventListener('playing', resolve);\n      });\n      if (HTMLMediaElement) {\n        this.videoElement.srcObject = this._previewStream; // Safari 11 doesn't allow use of createObjectURL for MediaStream\n      }\n      else {\n        this.videoElement.src = URL.createObjectURL(this._previewStream);\n      }\n      this.videoElement.muted = true;\n      this.videoElement.setAttribute('playsinline', ''); // Required by Safari on iOS 11. See https://webkit.org/blog/6784\n      this.videoElement.play();\n      this.canvasElement = document.createElement('canvas');\n      // TODO Firefox has https://developer.mozilla.org/en-US/docs/Web/API/OffscreenCanvas\n      this.canvas2dContext = this.canvasElement.getContext('2d');\n    }\n    /**\n     * https://w3c.github.io/mediacapture-image/index.html#dom-imagecapture-videostreamtrack\n     * @return {MediaStreamTrack} The MediaStreamTrack passed into the constructor\n     */\n    get videoStreamTrack() {\n      return this._videoStreamTrack;\n    }\n    /**\n     * Implements https://www.w3.org/TR/image-capture/#dom-imagecapture-getphotocapabilities\n     * @return {Promise<PhotoCapabilities>} Fulfilled promise with\n     * [PhotoCapabilities](https://www.w3.org/TR/image-capture/#idl-def-photocapabilities)\n     * object on success, rejected promise on failure\n     */\n    getPhotoCapabilities() {\n      return new Promise(function executorGPC(resolve, reject) {\n        // TODO see https://github.com/w3c/mediacapture-image/issues/97\n        const MediaSettingsRange = {\n          current: 0, min: 0, max: 0,\n        };\n        resolve({\n          exposureCompensation: MediaSettingsRange,\n          exposureMode: 'none',\n          fillLightMode: ['none'],\n          focusMode: 'none',\n          imageHeight: MediaSettingsRange,\n          imageWidth: MediaSettingsRange,\n          iso: MediaSettingsRange,\n          redEyeReduction: false,\n          whiteBalanceMode: 'none',\n          zoom: MediaSettingsRange,\n        });\n        reject(new DOMException('OperationError'));\n      });\n    }\n    /**\n     * Implements https://www.w3.org/TR/image-capture/#dom-imagecapture-setoptions\n     * @param {Object} photoSettings - Photo settings dictionary, https://www.w3.org/TR/image-capture/#idl-def-photosettings\n     * @return {Promise<void>} Fulfilled promise on success, rejected promise on failure\n     */\n    setOptions(_photoSettings = {}) {\n      return new Promise(function executorSO(_resolve, _reject) {\n        // TODO\n      });\n    }\n    /**\n     * TODO\n     * Implements https://www.w3.org/TR/image-capture/#dom-imagecapture-takephoto\n     * @return {Promise<Blob>} Fulfilled promise with [Blob](https://www.w3.org/TR/FileAPI/#blob)\n     * argument on success; rejected promise on failure\n     */\n    takePhoto() {\n      const self = this;\n      return new Promise(function executorTP(resolve, reject) {\n        // `If the readyState of the MediaStreamTrack provided in the constructor is not live,\n        // return a promise rejected with a new DOMException whose name is \"InvalidStateError\".`\n        if (self._videoStreamTrack.readyState !== 'live') {\n          return reject(new DOMException('InvalidStateError'));\n        }\n        self.videoElementPlaying.then(() => {\n          try {\n            self.canvasElement.width = self.videoElement.videoWidth;\n            self.canvasElement.height = self.videoElement.videoHeight;\n            self.canvas2dContext.drawImage(self.videoElement, 0, 0);\n            self.canvasElement.toBlob(resolve);\n          }\n          catch (error) {\n            reject(new DOMException('UnknownError'));\n          }\n        });\n      });\n    }\n    /**\n     * Implements https://www.w3.org/TR/image-capture/#dom-imagecapture-grabframe\n     * @return {Promise<ImageBitmap>} Fulfilled promise with\n     * [ImageBitmap](https://www.w3.org/TR/html51/webappapis.html#webappapis-images)\n     * argument on success; rejected promise on failure\n     */\n    grabFrame() {\n      const self = this;\n      return new Promise(function executorGF(resolve, reject) {\n        // `If the readyState of the MediaStreamTrack provided in the constructor is not live,\n        // return a promise rejected with a new DOMException whose name is \"InvalidStateError\".`\n        if (self._videoStreamTrack.readyState !== 'live') {\n          return reject(new DOMException('InvalidStateError'));\n        }\n        self.videoElementPlaying.then(() => {\n          try {\n            self.canvasElement.width = self.videoElement.videoWidth;\n            self.canvasElement.height = self.videoElement.videoHeight;\n            self.canvas2dContext.drawImage(self.videoElement, 0, 0);\n            // TODO polyfill https://developer.mozilla.org/en-US/docs/Web/API/ImageBitmapFactories/createImageBitmap for IE\n            resolve(window.createImageBitmap(self.canvasElement));\n          }\n          catch (error) {\n            reject(new DOMException('UnknownError'));\n          }\n        });\n      });\n    }\n  };\n}\nwindow.ImageCapture = ImageCapture;\n\nconst cameraCss = \":host{--header-height:4em;--footer-height:9em;--header-height-landscape:3em;--footer-height-landscape:6em;--shutter-size:6em;--icon-size-header:1.5em;--icon-size-footer:2.5em;--margin-size-header:1.5em;--margin-size-footer:2.0em;font-family:-apple-system, BlinkMacSystemFont,\\n    “Segoe UI”, “Roboto”, “Droid Sans”, “Helvetica Neue”, sans-serif;display:block;width:100%;height:100%}.items{-webkit-box-sizing:border-box;box-sizing:border-box;display:-ms-flexbox;display:flex;width:100%;height:100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.items .item{-ms-flex:1;flex:1;text-align:center}.items .item:first-child{text-align:left}.items .item:last-child{text-align:right}.camera-wrapper{position:relative;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;width:100%;height:100%}.camera-header{color:white;background-color:black;height:var(--header-height)}.camera-header .items{padding:var(--margin-size-header)}.camera-footer{position:relative;color:white;background-color:black;height:var(--footer-height)}.camera-footer .items{padding:var(--margin-size-footer)}@media (max-height: 375px){.camera-header{--header-height:var(--header-height-landscape)}.camera-footer{--footer-height:var(--footer-height-landscape)}.camera-footer .shutter{--shutter-size:4em}}.camera-video{position:relative;-ms-flex:1;flex:1;overflow:hidden;background-color:black}video{width:100%;height:100%;max-height:100%;min-height:100%;-o-object-fit:cover;object-fit:cover;background-color:black}.pick-image{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;position:absolute;left:var(--margin-size-footer);top:0;height:100%;width:var(--icon-size-footer);color:white}.pick-image input{visibility:hidden}.pick-image svg{cursor:pointer;fill:white;width:var(--icon-size-footer);height:var(--icon-size-footer)}.shutter{position:absolute;left:50%;top:50%;width:var(--shutter-size);height:var(--shutter-size);margin-top:calc(var(--shutter-size) / -2);margin-left:calc(var(--shutter-size) / -2);border-radius:100%;background-color:#c6cdd8;padding:12px;-webkit-box-sizing:border-box;box-sizing:border-box}.shutter:active .shutter-button{background-color:#9da9bb}.shutter-button{background-color:white;border-radius:100%;width:100%;height:100%}.rotate{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;position:absolute;right:var(--margin-size-footer);top:0;height:100%;width:var(--icon-size-footer);color:white}.rotate img{width:var(--icon-size-footer);height:var(--icon-size-footer)}.shutter-overlay{z-index:5;position:absolute;width:100%;height:100%;background-color:black}.error{width:100%;height:100%;color:white;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center}.no-device{background-color:black;-ms-flex:1;flex:1;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;color:white}.no-device label{cursor:pointer;background:#fff;border-radius:6px;padding:6px 8px;color:black}.no-device input{visibility:hidden;height:0;margin-top:16px}.accept{background-color:black;-ms-flex:1;flex:1;overflow:hidden}.accept .accept-image{width:100%;height:100%;max-height:100%;background-position:center center;background-size:cover;background-repeat:no-repeat}.close img{cursor:pointer;width:var(--icon-size-header);height:var(--icon-size-header)}.flash img{width:var(--icon-size-header);height:var(--icon-size-header)}.accept-use img{width:var(--icon-size-footer);height:var(--icon-size-footer)}.accept-cancel img{width:var(--icon-size-footer);height:var(--icon-size-footer)}.offscreen-image-render{top:0;left:0;visibility:hidden;pointer-events:none;width:100%;height:100%}\";\n\nconst CameraPWA = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    // Whether the device has multiple cameras (front/back)\n    this.hasMultipleCameras = false;\n    // Whether the device has flash support\n    this.hasFlash = false;\n    // Flash modes for camera\n    this.flashModes = [];\n    // Current flash mode\n    this.flashMode = 'off';\n    this.handlePickFile = (_e) => {\n    };\n    this.handleShutterClick = (_e) => {\n      console.debug('shutter click');\n      this.capture();\n    };\n    this.handleRotateClick = (_e) => {\n      this.rotate();\n    };\n    this.handleClose = (_e) => {\n      this.handlePhoto && this.handlePhoto(null);\n    };\n    this.handleFlashClick = (_e) => {\n      this.cycleFlash();\n    };\n    this.handleCancelPhoto = (_e) => {\n      const track = this.stream && this.stream.getTracks()[0];\n      let c = track && track.getConstraints();\n      this.photo = null;\n      this.photoSrc = null;\n      if (c) {\n        this.initCamera({\n          video: {\n            facingMode: c.facingMode\n          }\n        });\n      }\n      else {\n        this.initCamera();\n      }\n    };\n    this.handleAcceptPhoto = (_e) => {\n      this.handlePhoto && this.handlePhoto(this.photo);\n    };\n    this.handleFileInputChange = async (e) => {\n      const input = e.target;\n      const file = input.files[0];\n      try {\n        const orientation = await this.getOrientation(file);\n        console.debug('Got orientation', orientation);\n        this.photoOrientation = orientation;\n      }\n      catch (e) {\n      }\n      this.handlePhoto && this.handlePhoto(file);\n    };\n    this.handleVideoMetadata = (e) => {\n      console.debug('Video metadata', e);\n    };\n    this.facingMode = 'user';\n    this.handlePhoto = undefined;\n    this.hidePicker = false;\n    this.handleNoDeviceError = undefined;\n    this.noDevicesText = 'No camera found';\n    this.noDevicesButtonText = 'Choose image';\n    this.photo = undefined;\n    this.photoSrc = undefined;\n    this.showShutterOverlay = false;\n    this.flashIndex = 0;\n    this.hasCamera = null;\n    this.rotation = 0;\n    this.deviceError = null;\n  }\n  async componentDidLoad() {\n    this.defaultConstraints = {\n      video: {\n        facingMode: this.facingMode\n      }\n    };\n    // Figure out how many cameras we have\n    await this.queryDevices();\n    // Initialize the camera\n    await this.initCamera();\n  }\n  disconnectedCallback() {\n    this.stopStream();\n    this.photoSrc && URL.revokeObjectURL(this.photoSrc);\n  }\n  hasImageCapture() {\n    return 'ImageCapture' in window;\n  }\n  /**\n   * Query the list of connected devices and figure out how many video inputs we have.\n   */\n  async queryDevices() {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const videoDevices = devices.filter(d => d.kind == 'videoinput');\n      this.hasCamera = !!videoDevices.length;\n      this.hasMultipleCameras = videoDevices.length > 1;\n    }\n    catch (e) {\n      this.deviceError = e;\n    }\n  }\n  async initCamera(constraints) {\n    if (!constraints) {\n      constraints = this.defaultConstraints;\n    }\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia(Object.assign({ video: true, audio: false }, constraints));\n      this.initStream(stream);\n    }\n    catch (e) {\n      this.deviceError = e;\n      this.handleNoDeviceError && this.handleNoDeviceError(e);\n    }\n  }\n  async initStream(stream) {\n    this.stream = stream;\n    this.videoElement.srcObject = stream;\n    if (this.hasImageCapture()) {\n      this.imageCapture = new window.ImageCapture(stream.getVideoTracks()[0]);\n      await this.initPhotoCapabilities(this.imageCapture);\n    }\n    else {\n      this.deviceError = 'No image capture';\n      this.handleNoDeviceError && this.handleNoDeviceError();\n    }\n    // Always re-render\n    forceUpdate(this.el);\n  }\n  async initPhotoCapabilities(imageCapture) {\n    const c = await imageCapture.getPhotoCapabilities();\n    if (c.fillLightMode && c.fillLightMode.length > 1) {\n      this.flashModes = c.fillLightMode.map(m => m);\n      // Try to recall the current flash mode\n      if (this.flashMode) {\n        this.flashMode = this.flashModes[this.flashModes.indexOf(this.flashMode)] || 'off';\n        this.flashIndex = this.flashModes.indexOf(this.flashMode) || 0;\n      }\n      else {\n        this.flashIndex = 0;\n      }\n    }\n  }\n  stopStream() {\n    if (this.videoElement) {\n      this.videoElement.srcObject = null;\n    }\n    this.stream && this.stream.getTracks().forEach(track => track.stop());\n  }\n  async capture() {\n    if (this.hasImageCapture()) {\n      try {\n        const photo = await this.imageCapture.takePhoto({\n          fillLightMode: this.flashModes.length > 1 ? this.flashMode : undefined\n        });\n        await this.flashScreen();\n        this.promptAccept(photo);\n      }\n      catch (e) {\n        console.error('Unable to take photo!', e);\n      }\n    }\n    this.stopStream();\n  }\n  async promptAccept(photo) {\n    this.photo = photo;\n    const orientation = await this.getOrientation(photo);\n    console.debug('Got orientation', orientation);\n    this.photoOrientation = orientation;\n    if (orientation) {\n      switch (orientation) {\n        case 1:\n        case 2:\n          this.rotation = 0;\n          break;\n        case 3:\n        case 4:\n          this.rotation = 180;\n          break;\n        case 5:\n        case 6:\n          this.rotation = 90;\n          break;\n        case 7:\n        case 8:\n          this.rotation = 270;\n          break;\n      }\n    }\n    this.photoSrc = URL.createObjectURL(photo);\n  }\n  getOrientation(file) {\n    return new Promise(resolve => {\n      const reader = new FileReader();\n      reader.onload = (event) => {\n        const view = new DataView(event.target.result);\n        if (view.getUint16(0, false) !== 0xFFD8) {\n          return resolve(-2);\n        }\n        const length = view.byteLength;\n        let offset = 2;\n        while (offset < length) {\n          const marker = view.getUint16(offset, false);\n          offset += 2;\n          if (marker === 0xFFE1) {\n            if (view.getUint32(offset += 2, false) !== 0x45786966) {\n              return resolve(-1);\n            }\n            const little = view.getUint16(offset += 6, false) === 0x4949;\n            offset += view.getUint32(offset + 4, little);\n            const tags = view.getUint16(offset, little);\n            offset += 2;\n            for (let i = 0; i < tags; i++) {\n              if (view.getUint16(offset + (i * 12), little) === 0x0112) {\n                return resolve(view.getUint16(offset + (i * 12) + 8, little));\n              }\n            }\n          }\n          else if ((marker & 0xFF00) !== 0xFF00) {\n            break;\n          }\n          else {\n            offset += view.getUint16(offset, false);\n          }\n        }\n        return resolve(-1);\n      };\n      reader.readAsArrayBuffer(file.slice(0, 64 * 1024));\n    });\n  }\n  rotate() {\n    this.stopStream();\n    const track = this.stream && this.stream.getTracks()[0];\n    if (!track) {\n      return;\n    }\n    let c = track.getConstraints();\n    let facingMode = c.facingMode;\n    if (!facingMode) {\n      let c = track.getCapabilities();\n      if (c.facingMode) {\n        facingMode = c.facingMode[0];\n      }\n    }\n    if (facingMode === 'environment') {\n      this.initCamera({\n        video: {\n          facingMode: 'user'\n        }\n      });\n    }\n    else {\n      this.initCamera({\n        video: {\n          facingMode: 'environment'\n        }\n      });\n    }\n  }\n  setFlashMode(mode) {\n    console.debug('New flash mode: ', mode);\n    this.flashMode = mode;\n  }\n  cycleFlash() {\n    if (this.flashModes.length > 0) {\n      this.flashIndex = (this.flashIndex + 1) % this.flashModes.length;\n      this.setFlashMode(this.flashModes[this.flashIndex]);\n    }\n  }\n  async flashScreen() {\n    return new Promise((resolve, _reject) => {\n      this.showShutterOverlay = true;\n      setTimeout(() => {\n        this.showShutterOverlay = false;\n        resolve();\n      }, 100);\n    });\n  }\n  iconExit() {\n    return `data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Cg id='Icon_5_'%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M402.2,134L378,109.8c-1.6-1.6-4.1-1.6-5.7,0L258.8,223.4c-1.6,1.6-4.1,1.6-5.7,0L139.6,109.8 c-1.6-1.6-4.1-1.6-5.7,0L109.8,134c-1.6,1.6-1.6,4.1,0,5.7l113.5,113.5c1.6,1.6,1.6,4.1,0,5.7L109.8,372.4c-1.6,1.6-1.6,4.1,0,5.7 l24.1,24.1c1.6,1.6,4.1,1.6,5.7,0l113.5-113.5c1.6-1.6,4.1-1.6,5.7,0l113.5,113.5c1.6,1.6,4.1,1.6,5.7,0l24.1-24.1 c1.6-1.6,1.6-4.1,0-5.7L288.6,258.8c-1.6-1.6-1.6-4.1,0-5.7l113.5-113.5C403.7,138.1,403.7,135.5,402.2,134z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E`;\n  }\n  iconPhotos() {\n    return (h(\"svg\", { xmlns: 'http://www.w3.org/2000/svg', width: '512', height: '512', viewBox: '0 0 512 512' }, h(\"path\", { d: 'M450.29,112H142c-34,0-62,27.51-62,61.33V418.67C80,452.49,108,480,142,480H450c34,0,62-26.18,62-60V173.33C512,139.51,484.32,112,450.29,112Zm-77.15,61.34a46,46,0,1,1-46.28,46A46.19,46.19,0,0,1,373.14,173.33Zm-231.55,276c-17,0-29.86-13.75-29.86-30.66V353.85l90.46-80.79a46.54,46.54,0,0,1,63.44,1.83L328.27,337l-113,112.33ZM480,418.67a30.67,30.67,0,0,1-30.71,30.66H259L376.08,333a46.24,46.24,0,0,1,59.44-.16L480,370.59Z' }), h(\"path\", { d: 'M384,32H64A64,64,0,0,0,0,96V352a64.11,64.11,0,0,0,48,62V152a72,72,0,0,1,72-72H446A64.11,64.11,0,0,0,384,32Z' })));\n  }\n  iconConfirm() {\n    return `data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Ccircle fill='%232CD865' cx='256' cy='256' r='256'/%3E%3Cg id='Icon_1_'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M208,301.4l-55.4-55.5c-1.5-1.5-4-1.6-5.6-0.1l-23.4,22.3c-1.6,1.6-1.7,4.1-0.1,5.7l81.6,81.4 c3.1,3.1,8.2,3.1,11.3,0l171.8-171.7c1.6-1.6,1.6-4.2-0.1-5.7l-23.4-22.3c-1.6-1.5-4.1-1.5-5.6,0.1L213.7,301.4 C212.1,303,209.6,303,208,301.4z'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E`;\n  }\n  iconReverseCamera() {\n    return `data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M352,0H160C72,0,0,72,0,160v192c0,88,72,160,160,160h192c88,0,160-72,160-160V160C512,72,440,0,352,0z M356.7,365.8l-3.7,3.3c-27,23.2-61.4,35.9-96.8,35.9c-72.4,0-135.8-54.7-147-125.6c-0.3-1.9-2-3.3-3.9-3.3H64 c-3.3,0-5.2-3.8-3.2-6.4l61.1-81.4c1.6-2.1,4.7-2.1,6.4-0.1l63.3,81.4c2,2.6,0.2,6.5-3.2,6.5h-40.6c-2.5,0-4.5,2.4-3.9,4.8 c11.5,51.5,59.2,90.6,112.4,90.6c26.4,0,51.8-9.7,73.7-27.9l3.1-2.5c1.6-1.3,3.9-1.1,5.3,0.3l18.5,18.6 C358.5,361.6,358.4,364.3,356.7,365.8z M451.4,245.6l-61,83.5c-1.6,2.2-4.8,2.2-6.4,0.1l-63.3-83.3c-2-2.6-0.1-6.4,3.2-6.4h40.8 c2.5,0,4.4-2.3,3.9-4.8c-5.1-24.2-17.8-46.5-36.5-63.7c-21.2-19.4-48.2-30.1-76-30.1c-26.5,0-52.6,9.7-73.7,27.3l-3.1,2.5 c-1.6,1.3-3.9,1.2-5.4-0.3l-18.5-18.5c-1.6-1.6-1.5-4.3,0.2-5.9l3.5-3.1c27-23.2,61.4-35.9,96.8-35.9c38,0,73.9,13.7,101.2,38.7 c23.2,21.1,40.3,55.2,45.7,90.1c0.3,1.9,1.9,3.4,3.9,3.4h41.3C451.4,239.2,453.3,243,451.4,245.6z'/%3E%3C/g%3E%3C/svg%3E`;\n  }\n  iconRetake() {\n    return `data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Ccircle fill='%23727A87' cx='256' cy='256' r='256'/%3E%3Cg id='Icon_5_'%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M394.2,142L370,117.8c-1.6-1.6-4.1-1.6-5.7,0L258.8,223.4c-1.6,1.6-4.1,1.6-5.7,0L147.6,117.8 c-1.6-1.6-4.1-1.6-5.7,0L117.8,142c-1.6,1.6-1.6,4.1,0,5.7l105.5,105.5c1.6,1.6,1.6,4.1,0,5.7L117.8,364.4c-1.6,1.6-1.6,4.1,0,5.7 l24.1,24.1c1.6,1.6,4.1,1.6,5.7,0l105.5-105.5c1.6-1.6,4.1-1.6,5.7,0l105.5,105.5c1.6,1.6,4.1,1.6,5.7,0l24.1-24.1 c1.6-1.6,1.6-4.1,0-5.7L288.6,258.8c-1.6-1.6-1.6-4.1,0-5.7l105.5-105.5C395.7,146.1,395.7,143.5,394.2,142z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E`;\n  }\n  iconFlashOff() {\n    return `data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' style='enable-background:new 0 0 512 512;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:%23FFFFFF;%7D%0A%3C/style%3E%3Cg%3E%3Cpath class='st0' d='M498,483.7L42.3,28L14,56.4l149.8,149.8L91,293.8c-2.5,3-0.1,7.2,3.9,7.2h143.9c1.6,0,2.7,1.3,2.4,2.7 L197.6,507c-1,4.4,5.8,6.9,8.9,3.2l118.6-142.8L469.6,512L498,483.7z'/%3E%3Cpath class='st0' d='M449,218.2c2.5-3,0.1-7.2-3.9-7.2H301.2c-1.6,0-2.7-1.3-2.4-2.7L342.4,5c1-4.4-5.8-6.9-8.9-3.2L214.9,144.6 l161.3,161.3L449,218.2z'/%3E%3C/g%3E%3C/svg%3E`;\n  }\n  iconFlashOn() {\n    return `data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' style='enable-background:new 0 0 512 512;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:%23FFFFFF;%7D%0A%3C/style%3E%3Cpath class='st0' d='M287.2,211c-1.6,0-2.7-1.3-2.4-2.7L328.4,5c1-4.4-5.8-6.9-8.9-3.2L77,293.8c-2.5,3-0.1,7.2,3.9,7.2h143.9 c1.6,0,2.7,1.3,2.4,2.7L183.6,507c-1,4.4,5.8,6.9,8.9,3.2l242.5-292c2.5-3,0.1-7.2-3.9-7.2L287.2,211L287.2,211z'/%3E%3C/svg%3E`;\n  }\n  iconFlashAuto() {\n    return `data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' style='enable-background:new 0 0 512 512;' xml:space='preserve'%3E%3Cstyle type='text/css'%3E .st0%7Bfill:%23FFFFFF;%7D%0A%3C/style%3E%3Cpath class='st0' d='M287.2,211c-1.6,0-2.7-1.3-2.4-2.7L328.4,5c1-4.4-5.8-6.9-8.9-3.2L77,293.8c-2.5,3-0.1,7.2,3.9,7.2h143.9 c1.6,0,2.7,1.3,2.4,2.7L183.6,507c-1,4.4,5.8,6.9,8.9,3.2l242.5-292c2.5-3,0.1-7.2-3.9-7.2L287.2,211L287.2,211z'/%3E%3Cg%3E%3Cpath class='st0' d='M321.3,186l74-186H438l74,186h-43.5l-11.9-32.5h-80.9l-12,32.5H321.3z M415.8,47.9l-27.2,70.7h54.9l-27.2-70.7 H415.8z'/%3E%3C/g%3E%3C/svg%3E`;\n  }\n  render() {\n    // const acceptStyles = { transform: `rotate(${-this.rotation}deg)` };\n    const acceptStyles = {};\n    return (h(\"div\", { class: \"camera-wrapper\" }, h(\"div\", { class: \"camera-header\" }, h(\"section\", { class: \"items\" }, h(\"div\", { class: \"item close\", onClick: e => this.handleClose(e) }, h(\"img\", { src: this.iconExit() })), h(\"div\", { class: \"item flash\", onClick: e => this.handleFlashClick(e) }, this.flashModes.length > 0 && (h(\"div\", null, this.flashMode == 'off' ? h(\"img\", { src: this.iconFlashOff() }) : '', this.flashMode == 'auto' ? h(\"img\", { src: this.iconFlashAuto() }) : '', this.flashMode == 'flash' ? h(\"img\", { src: this.iconFlashOn() }) : ''))))), (this.hasCamera === false || !!this.deviceError) && (h(\"div\", { class: \"no-device\" }, h(\"h2\", null, this.noDevicesText), h(\"label\", { htmlFor: \"_pwa-elements-camera-input\" }, this.noDevicesButtonText), h(\"input\", { type: \"file\", id: \"_pwa-elements-camera-input\", onChange: this.handleFileInputChange, accept: \"image/*\", class: \"select-file-button\" }))), this.photoSrc ? (h(\"div\", { class: \"accept\" }, h(\"div\", { class: \"accept-image\", style: Object.assign({ backgroundImage: `url(${this.photoSrc})` }, acceptStyles) }))) : (h(\"div\", { class: \"camera-video\" }, this.showShutterOverlay && (h(\"div\", { class: \"shutter-overlay\" })), this.hasImageCapture() ? (h(\"video\", { ref: (el) => this.videoElement = el, onLoadedMetaData: this.handleVideoMetadata, autoplay: true, playsinline: true })) : (h(\"canvas\", { ref: (el) => this.canvasElement = el, width: \"100%\", height: \"100%\" })), h(\"canvas\", { class: \"offscreen-image-render\", ref: e => this.offscreenCanvas = e, width: \"100%\", height: \"100%\" }))), this.hasCamera && (h(\"div\", { class: \"camera-footer\" }, !this.photo ? ([\n      !this.hidePicker && (h(\"div\", { class: \"pick-image\", onClick: this.handlePickFile }, h(\"label\", { htmlFor: \"_pwa-elements-file-pick\" }, this.iconPhotos()), h(\"input\", { type: \"file\", id: \"_pwa-elements-file-pick\", onChange: this.handleFileInputChange, accept: \"image/*\", class: \"pick-image-button\" }))),\n      h(\"div\", { class: \"shutter\", onClick: this.handleShutterClick }, h(\"div\", { class: \"shutter-button\" })),\n      h(\"div\", { class: \"rotate\", onClick: this.handleRotateClick }, h(\"img\", { src: this.iconReverseCamera() })),\n    ]) : (h(\"section\", { class: \"items\" }, h(\"div\", { class: \"item accept-cancel\", onClick: e => this.handleCancelPhoto(e) }, h(\"img\", { src: this.iconRetake() })), h(\"div\", { class: \"item accept-use\", onClick: e => this.handleAcceptPhoto(e) }, h(\"img\", { src: this.iconConfirm() }))))))));\n  }\n  static get assetsDirs() { return [\"icons\"]; }\n  get el() { return getElement(this); }\n};\nCameraPWA.style = cameraCss;\n\nexport { CameraPWA as pwa_camera };\n"], "names": ["r", "registerInstance", "f", "forceUpdate", "h", "g", "getElement", "ImageCapture", "window", "constructor", "videoStreamTrack", "kind", "DOMException", "_videoStreamTrack", "readyState", "_previewStream", "MediaStream", "videoElement", "document", "createElement", "videoElementPlaying", "Promise", "resolve", "addEventListener", "HTMLMediaElement", "srcObject", "src", "URL", "createObjectURL", "muted", "setAttribute", "play", "canvasElement", "canvas2dContext", "getContext", "getPhotoCapabilities", "executorGPC", "reject", "MediaSettingsRange", "current", "min", "max", "exposureCompensation", "exposureMode", "fillLightMode", "focusMode", "imageHeight", "imageWidth", "iso", "redEyeReduction", "whiteBalanceMode", "zoom", "setOptions", "_photoSettings", "executorSO", "_resolve", "_reject", "<PERSON><PERSON><PERSON><PERSON>", "self", "executorTP", "then", "width", "videoWidth", "height", "videoHeight", "drawImage", "toBlob", "error", "grab<PERSON><PERSON><PERSON>", "executorGF", "createImageBitmap", "cameraCss", "CameraPWA", "hostRef", "_this", "hasMultipleCameras", "hasFlash", "flashModes", "flashMode", "handlePickFile", "_e", "handleShutterClick", "console", "debug", "capture", "handleRotateClick", "rotate", "handleClose", "handlePhoto", "handleFlashClick", "cycleFlash", "handleCancelPhoto", "track", "stream", "getTracks", "c", "getConstraints", "photo", "photoSrc", "initCamera", "video", "facingMode", "handleAcceptPhoto", "handleFileInputChange", "_ref", "_asyncToGenerator", "e", "input", "target", "file", "files", "orientation", "getOrientation", "photoOrientation", "_x", "apply", "arguments", "handleVideoMetadata", "undefined", "hidePicker", "handleNoDeviceError", "noDevicesText", "noDevicesButtonText", "showShutterOverlay", "flashIndex", "hasCamera", "rotation", "deviceError", "componentDidLoad", "_this2", "defaultConstraints", "queryDevices", "disconnectedCallback", "stopStream", "revokeObjectURL", "hasImageCapture", "_this3", "devices", "navigator", "mediaDevices", "enumerateDevices", "videoDevices", "filter", "d", "length", "constraints", "_this4", "getUserMedia", "Object", "assign", "audio", "initStream", "_this5", "imageCapture", "getVideoTracks", "initPhotoCapabilities", "el", "_this6", "map", "m", "indexOf", "for<PERSON>ach", "stop", "_this7", "flashScreen", "promptAccept", "_this8", "reader", "FileReader", "onload", "event", "view", "DataView", "result", "getUint16", "byteLength", "offset", "marker", "getUint32", "little", "tags", "i", "readAsA<PERSON>y<PERSON><PERSON>er", "slice", "getCapabilities", "setFlashMode", "mode", "_this9", "setTimeout", "iconExit", "iconPhotos", "xmlns", "viewBox", "iconConfirm", "iconReverseCamera", "iconRetake", "iconFlashOff", "iconFlashOn", "iconFlashAuto", "render", "acceptStyles", "class", "onClick", "htmlFor", "type", "id", "onChange", "accept", "style", "backgroundImage", "ref", "onLoadedMetaData", "autoplay", "playsinline", "offscreenCanvas", "assetsDirs", "pwa_camera"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}