{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-input_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC+H;AACnD;AAC0D;AACnC;AACnB;AACR;AACX;AAChC;AACA;AAE7B,MAAM2B,WAAW,GAAG,80XAA80X;AACl2X,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,u/nBAAu/nB;AAC1goB,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjBhC,qDAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAG/B,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACgC,SAAS,GAAGhC,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACiC,OAAO,GAAGjC,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACkC,QAAQ,GAAGlC,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACmC,OAAO,GAAI,aAAYC,QAAQ,EAAG,EAAC;IACxC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,KAAK,GAAGD,EAAE,CAACE,MAAM;MACvB,IAAID,KAAK,EAAE;QACP,IAAI,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,IAAI,EAAE;MAClC;MACA,IAAI,CAACC,eAAe,CAACJ,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACK,QAAQ,GAAIL,EAAE,IAAK;MACpB,IAAI,CAACM,eAAe,CAACN,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACO,MAAM,GAAIP,EAAE,IAAK;MAClB,IAAI,CAACQ,QAAQ,GAAG,KAAK;MACrB,IAAI,IAAI,CAACC,YAAY,KAAK,IAAI,CAACN,KAAK,EAAE;QAClC;AAChB;AACA;AACA;QACgB,IAAI,CAACG,eAAe,CAACN,EAAE,CAAC;MAC5B;MACA,IAAI,CAACF,mBAAmB,GAAG,KAAK;MAChC,IAAI,CAACN,OAAO,CAACkB,IAAI,CAACV,EAAE,CAAC;IACzB,CAAC;IACD,IAAI,CAACW,OAAO,GAAIX,EAAE,IAAK;MACnB,IAAI,CAACQ,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACN,KAAK;MAC9B,IAAI,CAACV,QAAQ,CAACiB,IAAI,CAACV,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,CAACY,SAAS,GAAIZ,EAAE,IAAK;MACrB,IAAI,CAACa,gBAAgB,CAACb,EAAE,CAAC;IAC7B,CAAC;IACD,IAAI,CAACc,kBAAkB,GAAG,MAAM;MAC5B,IAAI,CAACjB,WAAW,GAAG,IAAI;IAC3B,CAAC;IACD,IAAI,CAACkB,gBAAgB,GAAG,MAAM;MAC1B,IAAI,CAAClB,WAAW,GAAG,KAAK;IAC5B,CAAC;IACD,IAAI,CAACmB,cAAc,GAAIhB,EAAE,IAAK;MAC1B,IAAI,IAAI,CAACiB,UAAU,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAInB,EAAE,EAAE;QAC3DA,EAAE,CAACoB,cAAc,CAAC,CAAC;QACnBpB,EAAE,CAACqB,eAAe,CAAC,CAAC;QACpB;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB;MACA,IAAI,CAACnB,KAAK,GAAG,EAAE;MACf,IAAI,CAACC,eAAe,CAACJ,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACQ,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACe,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACX,UAAU,GAAG,KAAK;IACvB,IAAI,CAACY,cAAc,GAAGL,SAAS;IAC/B,IAAI,CAACM,WAAW,GAAGN,SAAS;IAC5B,IAAI,CAACO,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,gBAAgB,GAAGR,SAAS;IACjC,IAAI,CAACS,QAAQ,GAAGT,SAAS;IACzB,IAAI,CAACL,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACe,YAAY,GAAGV,SAAS;IAC7B,IAAI,CAACW,SAAS,GAAGX,SAAS;IAC1B,IAAI,CAACY,IAAI,GAAGZ,SAAS;IACrB,IAAI,CAACa,SAAS,GAAGb,SAAS;IAC1B,IAAI,CAACc,UAAU,GAAGd,SAAS;IAC3B,IAAI,CAACe,KAAK,GAAGf,SAAS;IACtB,IAAI,CAACgB,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,GAAG,GAAGjB,SAAS;IACpB,IAAI,CAACkB,SAAS,GAAGlB,SAAS;IAC1B,IAAI,CAACmB,GAAG,GAAGnB,SAAS;IACpB,IAAI,CAACoB,SAAS,GAAGpB,SAAS;IAC1B,IAAI,CAACqB,QAAQ,GAAGrB,SAAS;IACzB,IAAI,CAACsB,IAAI,GAAG,IAAI,CAACpD,OAAO;IACxB,IAAI,CAACqD,OAAO,GAAGvB,SAAS;IACxB,IAAI,CAACwB,WAAW,GAAGxB,SAAS;IAC5B,IAAI,CAACN,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC+B,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,KAAK,GAAG1B,SAAS;IACtB,IAAI,CAAC2B,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,IAAI,GAAG5B,SAAS;IACrB,IAAI,CAAC6B,IAAI,GAAG,MAAM;IAClB,IAAI,CAAClD,KAAK,GAAG,EAAE;EACnB;EACAmD,eAAeA,CAAA,EAAG;IACd,MAAM;MAAEhE,QAAQ;MAAE2C,QAAQ;MAAEsB;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACjE,QAAQ,GAAG2C,QAAQ,KAAKT,SAAS,GAAG+B,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGjE,QAAQ,GAAGpB,uDAAa,CAACoB,QAAQ,EAAE2C,QAAQ,CAAC;EACvK;EACA;AACJ;AACA;AACA;AACA;EACIuB,YAAYA,CAAA,EAAG;IACX,MAAMC,cAAc,GAAG,IAAI,CAACC,EAAE,CAACC,aAAa,CAAC,2BAA2B,CAAC;IACzE,IAAIF,cAAc,EAAE;MAChBA,cAAc,CAACJ,IAAI,GAAG,IAAI,CAACA,IAAI;IACnC;EACJ;EACA;AACJ;AACA;EACIO,YAAYA,CAAA,EAAG;IACX,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAM1D,KAAK,GAAG,IAAI,CAAC2D,QAAQ,CAAC,CAAC;IAC7B,IAAID,WAAW,IAAIA,WAAW,CAAC1D,KAAK,KAAKA,KAAK,IAAI,CAAC,IAAI,CAACN,WAAW,EAAE;MACjE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACYgE,WAAW,CAAC1D,KAAK,GAAGA,KAAK;IAC7B;EACJ;EACA4D,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACnE,mBAAmB,GAAGoE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE9F,uDAAqB,CAAC,IAAI,CAACuF,EAAE,CAAC,CAAC,EAAEtF,uDAAiB,CAAC,IAAI,CAACsF,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC;EACpK;EACAQ,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAER;IAAG,CAAC,GAAG,IAAI;IACnB,IAAI,CAACS,sBAAsB,GAAG7F,2DAA4B,CAACoF,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM5F,qDAAW,CAAC,IAAI,CAAC,CAAC;IAClH,IAAI,CAACsG,eAAe,GAAGpG,gEAAqB,CAAC0F,EAAE,EAAE,MAAM,IAAI,CAACW,aAAa,EAAE,MAAM,IAAI,CAACC,SAAS,CAAC;IAChG,IAAI,CAAChB,eAAe,CAAC,CAAC;IACtB;MACIiB,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,iBAAiB,EAAE;QACtDC,MAAM,EAAE,IAAI,CAAChB;MACjB,CAAC,CAAC,CAAC;IACP;EACJ;EACAiB,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACpB,gBAAgB,GAAG,IAAI,CAACjE,QAAQ;IACrC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkE,YAAY,CAAC,CAAC;IACnB,IAAI,CAACF,eAAe,CAAC,CAAC;EAC1B;EACAsB,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACT,eAAe,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACAC,oBAAoBA,CAAA,EAAG;IACnB;MACIR,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;QACxDC,MAAM,EAAE,IAAI,CAAChB;MACjB,CAAC,CAAC,CAAC;IACP;IACA,IAAI,IAAI,CAACS,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAACa,OAAO,CAAC,CAAC;MACrC,IAAI,CAACb,sBAAsB,GAAG3C,SAAS;IAC3C;IACA,IAAI,IAAI,CAAC4C,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACY,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACZ,eAAe,GAAG5C,SAAS;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUF,QAAQA,CAAA,EAAG;IAAA,IAAA2D,KAAA;IAAA,OAAAC,6OAAA;MACb,IAAID,KAAI,CAACpB,WAAW,EAAE;QAClBoB,KAAI,CAACpB,WAAW,CAACsB,KAAK,CAAC,CAAC;MAC5B;IAAC;EACL;EACA;AACJ;AACA;EACUC,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAH,6OAAA;MACpB;AACR;AACA;AACA;MACQ,IAAI,CAACG,MAAI,CAACxB,WAAW,EAAE;QACnB,MAAM,IAAIyB,OAAO,CAAEC,OAAO,IAAKlH,uDAAgB,CAACgH,MAAI,CAAC3B,EAAE,EAAE6B,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACF,MAAI,CAACxB,WAAW,CAAC;IAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIvD,eAAeA,CAACkF,KAAK,EAAE;IACnB,MAAM;MAAErF;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMsF,QAAQ,GAAGtF,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAACuF,QAAQ,CAAC,CAAC;IACzD;IACA,IAAI,CAACjF,YAAY,GAAGgF,QAAQ;IAC5B,IAAI,CAAClG,SAAS,CAACmB,IAAI,CAAC;MAAEP,KAAK,EAAEsF,QAAQ;MAAED;IAAM,CAAC,CAAC;EACnD;EACA;AACJ;AACA;EACIpF,eAAeA,CAACoF,KAAK,EAAE;IACnB,MAAM;MAAErF;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMsF,QAAQ,GAAGtF,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAACuF,QAAQ,CAAC,CAAC;IACzD,IAAI,CAACpG,QAAQ,CAACoB,IAAI,CAAC;MAAEP,KAAK,EAAEsF,QAAQ;MAAED;IAAM,CAAC,CAAC;EAClD;EACAG,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEtC,IAAI;MAAEvB;IAAY,CAAC,GAAG,IAAI;IAClC,OAAOA,WAAW,KAAKN,SAAS,GAAG6B,IAAI,KAAK,UAAU,GAAGvB,WAAW;EACxE;EACAgC,QAAQA,CAAA,EAAG;IACP,OAAO,OAAO,IAAI,CAAC3D,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACA,KAAK,CAACuF,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAACvF,KAAK,IAAI,EAAE,EAAEuF,QAAQ,CAAC,CAAC;EACjG;EACA7E,gBAAgBA,CAACb,EAAE,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC2F,iBAAiB,CAAC,CAAC,EAAE;MAC3B;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMC,YAAY,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC;IACxE,MAAMC,iBAAiB,GAAGD,YAAY,CAACE,QAAQ,CAAC9F,EAAE,CAAC+F,GAAG,CAAC;IACvD;AACR;AACA;AACA;IACQ,IAAI,CAAC,IAAI,CAACjG,mBAAmB,IAAI,IAAI,CAACkG,QAAQ,CAAC,CAAC,IAAI,CAACH,iBAAiB,EAAE;MACpE,IAAI,CAAC1F,KAAK,GAAG,EAAE;MACf,IAAI,CAACC,eAAe,CAACJ,EAAE,CAAC;IAC5B;IACA;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC6F,iBAAiB,EAAE;MACpB,IAAI,CAAC/F,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAkG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClC,QAAQ,CAAC,CAAC,CAACmC,MAAM,GAAG,CAAC;EACrC;EACA;AACJ;AACA;EACIC,cAAcA,CAAA,EAAG;IACb,MAAM;MAAE5D,UAAU;MAAEH;IAAU,CAAC,GAAG,IAAI;IACtC,OAAO,CAAC3E,qDAAC,CAAC,KAAK,EAAE;MAAE2I,KAAK,EAAE;IAAc,CAAC,EAAE7D,UAAU,CAAC,EAAE9E,qDAAC,CAAC,KAAK,EAAE;MAAE2I,KAAK,EAAE;IAAa,CAAC,EAAEhE,SAAS,CAAC,CAAC;EACzG;EACAiE,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAErE,OAAO;MAAEW,SAAS;MAAEV,gBAAgB;MAAE7B;IAAM,CAAC,GAAG,IAAI;IAC5D,IAAI4B,OAAO,KAAK,IAAI,IAAIW,SAAS,KAAKlB,SAAS,EAAE;MAC7C;IACJ;IACA,OAAOhE,qDAAC,CAAC,KAAK,EAAE;MAAE2I,KAAK,EAAE;IAAU,CAAC,EAAE3H,2DAAc,CAAC2B,KAAK,EAAEuC,SAAS,EAAEV,gBAAgB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;EACIqE,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAEtE,OAAO;MAAEO,UAAU;MAAEH,SAAS;MAAEO;IAAU,CAAC,GAAG,IAAI;IAC1D;AACR;AACA;AACA;IACQ,MAAM4D,WAAW,GAAG,CAAC,CAAChE,UAAU,IAAI,CAAC,CAACH,SAAS;IAC/C,MAAMoE,UAAU,GAAGxE,OAAO,KAAK,IAAI,IAAIW,SAAS,KAAKlB,SAAS;IAC9D,IAAI,CAAC8E,WAAW,IAAI,CAACC,UAAU,EAAE;MAC7B;IACJ;IACA,OAAQ/I,qDAAC,CAAC,KAAK,EAAE;MAAE2I,KAAK,EAAE;IAAe,CAAC,EAAE,IAAI,CAACD,cAAc,CAAC,CAAC,EAAE,IAAI,CAACE,aAAa,CAAC,CAAC,CAAC;EAC5F;EACAI,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEjE;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQ/E,qDAAC,CAAC,KAAK,EAAE;MAAE2I,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACM;MACvC;IAAE,CAAC,EAAElE,KAAK,KAAKf,SAAS,GAAGhE,qDAAC,CAAC,MAAM,EAAE;MAAEsF,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAGtF,qDAAC,CAAC,KAAK,EAAE;MAAE2I,KAAK,EAAE;IAAa,CAAC,EAAE5D,KAAK,CAAC,CAAC;EAC3G;EACA;AACJ;AACA;AACA;EACI,IAAI+B,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACZ,EAAE,CAACC,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI8C,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAClE,KAAK,KAAKf,SAAS,IAAI,IAAI,CAAC8C,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACIoC,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,IAAI,GAAG7H,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM8H,cAAc,GAAGD,IAAI,KAAK,IAAI,IAAI,IAAI,CAACvE,IAAI,KAAK,SAAS;IAC/D,IAAIwE,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACHpJ,qDAAC,CAAC,KAAK,EAAE;QAAE2I,KAAK,EAAE;MAA0B,CAAC,EAAE3I,qDAAC,CAAC,KAAK,EAAE;QAAE2I,KAAK,EAAE;MAAsB,CAAC,CAAC,EAAE3I,qDAAC,CAAC,KAAK,EAAE;QAAE2I,KAAK,EAAE;UACrG,qBAAqB,EAAE,IAAI;UAC3B,4BAA4B,EAAE,CAAC,IAAI,CAACM;QACxC;MAAE,CAAC,EAAEjJ,qDAAC,CAAC,KAAK,EAAE;QAAE2I,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEU,GAAG,EAAGnD,EAAE,IAAM,IAAI,CAACW,aAAa,GAAGX;MAAI,CAAC,EAAE,IAAI,CAACnB,KAAK,CAAC,CAAC,EAAE/E,qDAAC,CAAC,KAAK,EAAE;QAAE2I,KAAK,EAAE;MAAoB,CAAC,CAAC,CAAC,EACnK,IAAI,CAACK,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACAM,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE3F,QAAQ;MAAEiB,IAAI;MAAElB,QAAQ;MAAEgC,KAAK;MAAExD,OAAO;MAAE8C,cAAc;MAAEkB,EAAE;MAAElD,QAAQ;MAAEqB;IAAe,CAAC,GAAG,IAAI;IACvG,MAAM8E,IAAI,GAAG7H,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMqB,KAAK,GAAG,IAAI,CAAC2D,QAAQ,CAAC,CAAC;IAC7B,MAAMiD,MAAM,GAAGtI,qDAAW,CAAC,UAAU,EAAE,IAAI,CAACiF,EAAE,CAAC;IAC/C,MAAMsD,qBAAqB,GAAGL,IAAI,KAAK,IAAI,IAAIvE,IAAI,KAAK,SAAS,IAAI,CAAC2E,MAAM;IAC5E,MAAME,gBAAgB,GAAGN,IAAI,KAAK,KAAK,GAAG/H,iDAAW,GAAGC,iDAAU;IAClE,MAAMqI,aAAa,GAAGrF,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGoF,gBAAgB;IAC9G,MAAMjB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAMmB,gBAAgB,GAAGzD,EAAE,CAACC,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMyD,gBAAgB,GAAG5E,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAKwD,QAAQ,IAAIxF,QAAQ,IAAI2G,gBAAgB,CAAE;IACtI,OAAQ3J,qDAAC,CAACE,iDAAI,EAAE;MAAEqI,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAEzH,qDAAkB,CAAC,IAAI,CAAC6C,KAAK,EAAE;QACjG,CAACoF,IAAI,GAAG,IAAI;QACZ,WAAW,EAAEX,QAAQ;QACrB,WAAW,EAAExF,QAAQ;QACrB,gBAAgB,EAAE4G,gBAAgB;QAClC,CAAE,cAAahF,IAAK,EAAC,GAAGA,IAAI,KAAKZ,SAAS;QAC1C,CAAE,eAAc0B,KAAM,EAAC,GAAGA,KAAK,KAAK1B,SAAS;QAC7C,CAAE,yBAAwBgB,cAAe,EAAC,GAAG,IAAI;QACjD,SAAS,EAAEuE,MAAM;QACjB,eAAe,EAAEtI,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACiF,EAAE,CAAC;QAC3D,gBAAgB,EAAEvC;MACtB,CAAC;IAAE,CAAC,EAAE3D,qDAAC,CAAC,OAAO,EAAE;MAAEuI,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE,eAAe;MAAEkB,OAAO,EAAE3H;IAAQ,CAAC,EAAE,IAAI,CAACgH,oBAAoB,CAAC,CAAC,EAAElJ,qDAAC,CAAC,KAAK,EAAE;MAAEuI,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAAiB,CAAC,EAAE3I,qDAAC,CAAC,MAAM,EAAE;MAAEuI,GAAG,EAAE,0CAA0C;MAAEjD,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEtF,qDAAC,CAAC,OAAO,EAAEwG,MAAM,CAACC,MAAM,CAAC;MAAE8B,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE,cAAc;MAAEU,GAAG,EAAG5G,KAAK,IAAM,IAAI,CAAC4D,WAAW,GAAG5D,KAAM;MAAEqH,EAAE,EAAE5H,OAAO;MAAEyB,QAAQ,EAAEA,QAAQ;MAAEoG,cAAc,EAAE,IAAI,CAAC9F,cAAc;MAAE+F,YAAY,EAAE,IAAI,CAAC9F,YAAY;MAAE+F,WAAW,EAAE,IAAI,CAAC9F,WAAW;MAAE+F,SAAS,EAAE,IAAI,CAAC9F,SAAS;MAAE+F,YAAY,EAAE,IAAI,CAACzF,YAAY;MAAE0F,SAAS,EAAE,IAAI,CAACvF,SAAS;MAAEM,GAAG,EAAE,IAAI,CAACA,GAAG;MAAEF,GAAG,EAAE,IAAI,CAACA,GAAG;MAAEoF,SAAS,EAAE,IAAI,CAACjF,SAAS;MAAEkF,SAAS,EAAE,IAAI,CAACpF,SAAS;MAAEG,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEC,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,EAAE;MAAE+E,QAAQ,EAAE7G,QAAQ;MAAE+B,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEE,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAElD,KAAK,EAAEA,KAAK;MAAEJ,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEM,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEI,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEqH,SAAS,EAAE,IAAI,CAACpH,SAAS;MAAEqH,kBAAkB,EAAE,IAAI,CAACnH,kBAAkB;MAAEoH,gBAAgB,EAAE,IAAI,CAACnH;IAAiB,CAAC,EAAE,IAAI,CAACnB,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAACqB,UAAU,IAAI,CAACC,QAAQ,IAAI,CAACC,QAAQ,IAAK3D,qDAAC,CAAC,QAAQ,EAAE;MAAEuI,GAAG,EAAE,0CAA0C;MAAE,YAAY,EAAE,OAAO;MAAE1C,IAAI,EAAE,QAAQ;MAAE8C,KAAK,EAAE,kBAAkB;MAAEgC,aAAa,EAAGnI,EAAE,IAAK;QACh4C;AAChB;AACA;AACA;AACA;QACgBA,EAAE,CAACoB,cAAc,CAAC,CAAC;MACvB,CAAC;MAAEgH,SAAS,EAAGpI,EAAE,IAAK;QAClB;AAChB;AACA;AACA;AACA;AACA;QACgBA,EAAE,CAACqB,eAAe,CAAC,CAAC;MACxB,CAAC;MAAEgH,OAAO,EAAE,IAAI,CAACrH;IAAe,CAAC,EAAExD,qDAAC,CAAC,UAAU,EAAE;MAAEuI,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEuC,IAAI,EAAEpB;IAAc,CAAC,CAAC,CAAE,EAAE1J,qDAAC,CAAC,MAAM,EAAE;MAAEuI,GAAG,EAAE,0CAA0C;MAAEjD,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAAEkE,qBAAqB,IAAIxJ,qDAAC,CAAC,KAAK,EAAE;MAAEuI,GAAG,EAAE,0CAA0C;MAAEI,KAAK,EAAE;IAAkB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACE,mBAAmB,CAAC,CAAC,CAAC;EACzX;EACA,IAAI3C,EAAEA,CAAA,EAAG;IAAE,OAAO9F,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW2K,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,MAAM,EAAE,CAAC,cAAc,CAAC;MACxB,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAI5I,QAAQ,GAAG,CAAC;AAChBR,KAAK,CAACqJ,KAAK,GAAG;EACVC,GAAG,EAAEzJ,iBAAiB;EACtB0J,EAAE,EAAExJ;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-input.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement, j as forceUpdate } from './index-c71c5417.js';\nimport { c as createNotchController } from './notch-controller-55b09e11.js';\nimport { e as debounceEvent, i as inheritAriaAttributes, h as inheritAttributes, c as componentOnReady } from './helpers-da915de8.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-09c71bc7.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as closeCircle, d as closeSharp } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-b9c0d1da.js';\nimport './index-a5d50daf.js';\nimport './index-9b0d46f4.js';\n\nconst inputIosCss = \".sc-ion-input-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-ios-h,ion-item [slot=start].sc-ion-input-ios-h,ion-item[slot=end].sc-ion-input-ios-h,ion-item [slot=end].sc-ion-input-ios-h{width:auto}.ion-color.sc-ion-input-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-ios-h,.input-label-placement-stacked.sc-ion-input-ios-h{min-height:56px}.native-input.sc-ion-input-ios{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-ios:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-ios::-ms-clear{display:none}.cloned-input.sc-ion-input-ios{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-ios{inset-inline-start:0}.cloned-input.sc-ion-input-ios:disabled{opacity:1}.input-clear-icon.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{color:inherit}.input-clear-icon.sc-ion-input-ios:focus{opacity:0.5}.has-value.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{visibility:visible}.input-wrapper.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-input-ios-h,.ion-touched.ion-invalid.sc-ion-input-ios-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:block;color:var(--ion-color-step-550, var(--ion-text-color-step-450, #737373))}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:block}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:none}.input-bottom.sc-ion-input-ios .counter.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, var(--ion-text-color-step-450, #737373));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-ios-h input.sc-ion-input-ios{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-ios,.sc-ion-input-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-ios,.input-outline-notch-hidden.sc-ion-input-ios{display:none}.input-wrapper.sc-ion-input-ios input.sc-ion-input-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text.sc-ion-input-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-ios-h input.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios,.has-value.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:1}.label-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-ios-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-ios-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-ios-h[disabled].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[disabled] .sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly] .sc-ion-input-ios-s>ion-input-password-toggle{display:none}.sc-ion-input-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px;font-size:inherit}.input-clear-icon.sc-ion-input-ios ion-icon.sc-ion-input-ios{width:18px;height:18px}.input-disabled.sc-ion-input-ios-h{opacity:0.3}.sc-ion-input-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonInputIosStyle0 = inputIosCss;\n\nconst inputMdCss = \".sc-ion-input-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-md-h,ion-item [slot=start].sc-ion-input-md-h,ion-item[slot=end].sc-ion-input-md-h,ion-item [slot=end].sc-ion-input-md-h{width:auto}.ion-color.sc-ion-input-md-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-md-h,.input-label-placement-stacked.sc-ion-input-md-h{min-height:56px}.native-input.sc-ion-input-md{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-md:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-md::-ms-clear{display:none}.cloned-input.sc-ion-input-md{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-md{inset-inline-start:0}.cloned-input.sc-ion-input-md:disabled{opacity:1}.input-clear-icon.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{color:inherit}.input-clear-icon.sc-ion-input-md:focus{opacity:0.5}.has-value.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{visibility:visible}.input-wrapper.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:block;color:var(--ion-color-step-550, var(--ion-text-color-step-450, #737373))}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:block}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:none}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, var(--ion-text-color-step-450, #737373));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-md-h input.sc-ion-input-md{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-md,.sc-ion-input-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-md,.input-outline-notch-hidden.sc-ion-input-md{display:none}.input-wrapper.sc-ion-input-md input.sc-ion-input-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text.sc-ion-input-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md,.has-value.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:1}.label-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-md-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-md-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-md-h[disabled].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[disabled] .sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly] .sc-ion-input-md-s>ion-input-password-toggle{display:none}.input-fill-solid.sc-ion-input-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.input-fill-solid.ion-valid.sc-ion-input-md-h,.input-fill-solid.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-fill-solid.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}@media (any-hover: hover){.input-fill-solid.sc-ion-input-md-h:hover{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-solid.has-focus.sc-ion-input-md-h{--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}.label-floating.input-fill-solid.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{max-width:calc(100% / 0.75)}.input-fill-outline.sc-ion-input-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-outline.input-shape-round.sc-ion-input-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.input-fill-outline.ion-valid.sc-ion-input-md-h,.input-fill-outline.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.input-fill-outline.sc-ion-input-md-h:hover{--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-outline.has-focus.sc-ion-input-md-h{--border-width:var(--highlight-height);--border-color:var(--highlight-color)}.input-fill-outline.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}.input-fill-outline.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:none}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{position:relative}.label-floating.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}.input-fill-outline.sc-ion-input-md-h .input-outline-container.sc-ion-input-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{pointer-events:none}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.input-fill-outline.sc-ion-input-md-h .notch-spacer.sc-ion-input-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px;-ms-flex-positive:1;flex-grow:1}.label-floating.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{border-top:none}.sc-ion-input-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px;font-size:inherit}.input-clear-icon.sc-ion-input-md ion-icon.sc-ion-input-md{width:22px;height:22px}.input-disabled.sc-ion-input-md-h{opacity:0.38}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{letter-spacing:0.0333333333em}.input-label-placement-floating.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.has-focus.input-label-placement-floating.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.has-focus.input-label-placement-stacked.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.input-highlight.sc-ion-input-md{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.input-highlight.sc-ion-input-md{inset-inline-start:0}.has-focus.sc-ion-input-md-h .input-highlight.sc-ion-input-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{bottom:0}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{inset-inline-start:0}.input-shape-round.sc-ion-input-md-h{--border-radius:16px}.sc-ion-input-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonInputMdStyle0 = inputMdCss;\n\nconst Input = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.inputId = `ion-input-${inputIds++}`;\n        this.inheritedAttributes = {};\n        this.isComposing = false;\n        /**\n         * `true` if the input was cleared as a result of the user typing\n         * with `clearOnEdit` enabled.\n         *\n         * Resets when the input loses focus.\n         */\n        this.didInputClearOnEdit = false;\n        this.onInput = (ev) => {\n            const input = ev.target;\n            if (input) {\n                this.value = input.value || '';\n            }\n            this.emitInputChange(ev);\n        };\n        this.onChange = (ev) => {\n            this.emitValueChange(ev);\n        };\n        this.onBlur = (ev) => {\n            this.hasFocus = false;\n            if (this.focusedValue !== this.value) {\n                /**\n                 * Emits the `ionChange` event when the input value\n                 * is different than the value when the input was focused.\n                 */\n                this.emitValueChange(ev);\n            }\n            this.didInputClearOnEdit = false;\n            this.ionBlur.emit(ev);\n        };\n        this.onFocus = (ev) => {\n            this.hasFocus = true;\n            this.focusedValue = this.value;\n            this.ionFocus.emit(ev);\n        };\n        this.onKeydown = (ev) => {\n            this.checkClearOnEdit(ev);\n        };\n        this.onCompositionStart = () => {\n            this.isComposing = true;\n        };\n        this.onCompositionEnd = () => {\n            this.isComposing = false;\n        };\n        this.clearTextInput = (ev) => {\n            if (this.clearInput && !this.readonly && !this.disabled && ev) {\n                ev.preventDefault();\n                ev.stopPropagation();\n                // Attempt to focus input again after pressing clear button\n                this.setFocus();\n            }\n            this.value = '';\n            this.emitInputChange(ev);\n        };\n        this.hasFocus = false;\n        this.color = undefined;\n        this.autocapitalize = 'off';\n        this.autocomplete = 'off';\n        this.autocorrect = 'off';\n        this.autofocus = false;\n        this.clearInput = false;\n        this.clearInputIcon = undefined;\n        this.clearOnEdit = undefined;\n        this.counter = false;\n        this.counterFormatter = undefined;\n        this.debounce = undefined;\n        this.disabled = false;\n        this.enterkeyhint = undefined;\n        this.errorText = undefined;\n        this.fill = undefined;\n        this.inputmode = undefined;\n        this.helperText = undefined;\n        this.label = undefined;\n        this.labelPlacement = 'start';\n        this.max = undefined;\n        this.maxlength = undefined;\n        this.min = undefined;\n        this.minlength = undefined;\n        this.multiple = undefined;\n        this.name = this.inputId;\n        this.pattern = undefined;\n        this.placeholder = undefined;\n        this.readonly = false;\n        this.required = false;\n        this.shape = undefined;\n        this.spellcheck = false;\n        this.step = undefined;\n        this.type = 'text';\n        this.value = '';\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    /**\n     * Whenever the type on the input changes we need\n     * to update the internal type prop on the password\n     * toggle so that that correct icon is shown.\n     */\n    onTypeChange() {\n        const passwordToggle = this.el.querySelector('ion-input-password-toggle');\n        if (passwordToggle) {\n            passwordToggle.type = this.type;\n        }\n    }\n    /**\n     * Update the native input element when the value changes\n     */\n    valueChanged() {\n        const nativeInput = this.nativeInput;\n        const value = this.getValue();\n        if (nativeInput && nativeInput.value !== value && !this.isComposing) {\n            /**\n             * Assigning the native input's value on attribute\n             * value change, allows `ionInput` implementations\n             * to override the control's value.\n             *\n             * Used for patterns such as input trimming (removing whitespace),\n             * or input masking.\n             */\n            nativeInput.value = value;\n        }\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['tabindex', 'title', 'data-form-type']));\n    }\n    connectedCallback() {\n        const { el } = this;\n        this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.debounceChanged();\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n                detail: this.el,\n            }));\n        }\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        /**\n         * Set the type on the password toggle in the event that this input's\n         * type was set async and does not match the default type for the password toggle.\n         * This can happen when the type is bound using a JS framework binding syntax\n         * such as [type] in Angular.\n         */\n        this.onTypeChange();\n        this.debounceChanged();\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    disconnectedCallback() {\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n                detail: this.el,\n            }));\n        }\n        if (this.slotMutationController) {\n            this.slotMutationController.destroy();\n            this.slotMutationController = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    /**\n     * Sets focus on the native `input` in `ion-input`. Use this method instead of the global\n     * `input.focus()`.\n     *\n     * Developers who wish to focus an input when a page enters\n     * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n     *\n     * Developers who wish to focus an input when an overlay is presented\n     * should call `setFocus` after `didPresent` has resolved.\n     *\n     * See [managing focus](/docs/developing/managing-focus) for more information.\n     */\n    async setFocus() {\n        if (this.nativeInput) {\n            this.nativeInput.focus();\n        }\n    }\n    /**\n     * Returns the native `<input>` element used under the hood.\n     */\n    async getInputElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.nativeInput) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.nativeInput);\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        // Emitting a value change should update the internal state for tracking the focused value\n        this.focusedValue = newValue;\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     */\n    emitInputChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        this.ionInput.emit({ value: newValue, event });\n    }\n    shouldClearOnEdit() {\n        const { type, clearOnEdit } = this;\n        return clearOnEdit === undefined ? type === 'password' : clearOnEdit;\n    }\n    getValue() {\n        return typeof this.value === 'number' ? this.value.toString() : (this.value || '').toString();\n    }\n    checkClearOnEdit(ev) {\n        if (!this.shouldClearOnEdit()) {\n            return;\n        }\n        /**\n         * The following keys do not modify the\n         * contents of the input. As a result, pressing\n         * them should not edit the input.\n         *\n         * We can't check to see if the value of the input\n         * was changed because we call checkClearOnEdit\n         * in a keydown listener, and the key has not yet\n         * been added to the input.\n         */\n        const IGNORED_KEYS = ['Enter', 'Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n        const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n        /**\n         * Clear the input if the control has not been previously cleared during focus.\n         * Do not clear if the user hitting enter to submit a form.\n         */\n        if (!this.didInputClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n            this.value = '';\n            this.emitInputChange(ev);\n        }\n        /**\n         * Pressing an IGNORED_KEYS first and\n         * then an allowed key will cause the input to not\n         * be cleared.\n         */\n        if (!pressedIgnoredKey) {\n            this.didInputClearOnEdit = true;\n        }\n    }\n    hasValue() {\n        return this.getValue().length > 0;\n    }\n    /**\n     * Renders the helper text or error text values\n     */\n    renderHintText() {\n        const { helperText, errorText } = this;\n        return [h(\"div\", { class: \"helper-text\" }, helperText), h(\"div\", { class: \"error-text\" }, errorText)];\n    }\n    renderCounter() {\n        const { counter, maxlength, counterFormatter, value } = this;\n        if (counter !== true || maxlength === undefined) {\n            return;\n        }\n        return h(\"div\", { class: \"counter\" }, getCounterText(value, maxlength, counterFormatter));\n    }\n    /**\n     * Responsible for rendering helper text,\n     * error text, and counter. This element should only\n     * be rendered if hint text is set or counter is enabled.\n     */\n    renderBottomContent() {\n        const { counter, helperText, errorText, maxlength } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        const hasCounter = counter === true && maxlength !== undefined;\n        if (!hasHintText && !hasCounter) {\n            return;\n        }\n        return (h(\"div\", { class: \"input-bottom\" }, this.renderHintText(), this.renderCounter()));\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            } }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container\n     * when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the input and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"input-outline-container\" }, h(\"div\", { class: \"input-outline-start\" }), h(\"div\", { class: {\n                        'input-outline-notch': true,\n                        'input-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"input-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    render() {\n        const { disabled, fill, readonly, shape, inputId, labelPlacement, el, hasFocus, clearInputIcon } = this;\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const defaultClearIcon = mode === 'ios' ? closeCircle : closeSharp;\n        const clearIconData = clearInputIcon !== null && clearInputIcon !== void 0 ? clearInputIcon : defaultClearIcon;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        /**\n         * If the label is stacked, it should always sit above the input.\n         * For floating labels, the label should move above the input if\n         * the input has a value, is focused, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the input is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots));\n        return (h(Host, { key: '907ce98a82b5cfae5a08504cd79e00a2330b7444', class: createColorClasses(this.color, {\n                [mode]: true,\n                'has-value': hasValue,\n                'has-focus': hasFocus,\n                'label-floating': labelShouldFloat,\n                [`input-fill-${fill}`]: fill !== undefined,\n                [`input-shape-${shape}`]: shape !== undefined,\n                [`input-label-placement-${labelPlacement}`]: true,\n                'in-item': inItem,\n                'in-item-color': hostContext('ion-item.ion-color', this.el),\n                'input-disabled': disabled,\n            }) }, h(\"label\", { key: '59d5bb45d2a5b828bba0ed8687a632a551e2f4d8', class: \"input-wrapper\", htmlFor: inputId }, this.renderLabelContainer(), h(\"div\", { key: 'f93f129d08246d0e9a601c100d718534d6403853', class: \"native-wrapper\" }, h(\"slot\", { key: '54eeb1a6bace662b7eb0d7e27180ea3d7e3a3729', name: \"start\" }), h(\"input\", Object.assign({ key: 'b3e0be55bc1a4a539ae3b0fdcf7fc078723cca16', class: \"native-input\", ref: (input) => (this.nativeInput = input), id: inputId, disabled: disabled, autoCapitalize: this.autocapitalize, autoComplete: this.autocomplete, autoCorrect: this.autocorrect, autoFocus: this.autofocus, enterKeyHint: this.enterkeyhint, inputMode: this.inputmode, min: this.min, max: this.max, minLength: this.minlength, maxLength: this.maxlength, multiple: this.multiple, name: this.name, pattern: this.pattern, placeholder: this.placeholder || '', readOnly: readonly, required: this.required, spellcheck: this.spellcheck, step: this.step, type: this.type, value: value, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, onKeyDown: this.onKeydown, onCompositionstart: this.onCompositionStart, onCompositionend: this.onCompositionEnd }, this.inheritedAttributes)), this.clearInput && !readonly && !disabled && (h(\"button\", { key: '5f6373504a6d0d074bfbf875c794d45ea2748175', \"aria-label\": \"reset\", type: \"button\", class: \"input-clear-icon\", onPointerDown: (ev) => {\n                /**\n                 * This prevents mobile browsers from\n                 * blurring the input when the clear\n                 * button is activated.\n                 */\n                ev.preventDefault();\n            }, onFocusin: (ev) => {\n                /**\n                 * Prevent the focusin event from bubbling otherwise it will cause the focusin\n                 * event listener in scroll assist to fire. When this fires, focus will be moved\n                 * back to the input even if the clear button was never tapped. This poses issues\n                 * for screen readers as it means users would be unable to swipe past the clear button.\n                 */\n                ev.stopPropagation();\n            }, onClick: this.clearTextInput }, h(\"ion-icon\", { key: '230d77973aa83458ceb32bf52e3abe9bc322cfe6', \"aria-hidden\": \"true\", icon: clearIconData }))), h(\"slot\", { key: '9d69ac6e8a3c4b2b303dba2478f82695d5755ed2', name: \"end\" })), shouldRenderHighlight && h(\"div\", { key: 'ac61f16237ce731e0745ab72d0fc3f066252464a', class: \"input-highlight\" })), this.renderBottomContent()));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"debounce\": [\"debounceChanged\"],\n        \"type\": [\"onTypeChange\"],\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet inputIds = 0;\nInput.style = {\n    ios: IonInputIosStyle0,\n    md: IonInputMdStyle0\n};\n\nexport { Input as ion_input };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "j", "forceUpdate", "c", "createNotchController", "e", "debounceEvent", "inheritAriaAttributes", "inheritAttributes", "componentOnReady", "createSlotMutationController", "g", "getCounterText", "hostContext", "createColorClasses", "b", "closeCircle", "closeSharp", "getIonMode", "inputIosCss", "IonInputIosStyle0", "inputMdCss", "IonInputMdStyle0", "Input", "constructor", "hostRef", "ionInput", "ionChange", "ionBlur", "ionFocus", "inputId", "inputIds", "inheritedAttributes", "isComposing", "didInputClearOnEdit", "onInput", "ev", "input", "target", "value", "emitInputChange", "onChange", "emitValueChange", "onBlur", "hasFocus", "focusedValue", "emit", "onFocus", "onKeydown", "checkClearOnEdit", "onCompositionStart", "onCompositionEnd", "clearTextInput", "clearInput", "readonly", "disabled", "preventDefault", "stopPropagation", "setFocus", "color", "undefined", "autocapitalize", "autocomplete", "autocorrect", "autofocus", "clearInputIcon", "clearOnEdit", "counter", "counterFormatter", "debounce", "enterkeyhint", "errorText", "fill", "inputmode", "helperText", "label", "labelPlacement", "max", "maxlength", "min", "minlength", "multiple", "name", "pattern", "placeholder", "required", "shape", "spellcheck", "step", "type", "debounce<PERSON><PERSON>ed", "originalIonInput", "onTypeChange", "passwordToggle", "el", "querySelector", "valueChanged", "nativeInput", "getValue", "componentWillLoad", "Object", "assign", "connectedCallback", "slotMutationController", "notchController", "notchSpacerEl", "labelSlot", "document", "dispatchEvent", "CustomEvent", "detail", "componentDidLoad", "componentDidRender", "_a", "calculateNotchWidth", "disconnectedCallback", "destroy", "_this", "_asyncToGenerator", "focus", "getInputElement", "_this2", "Promise", "resolve", "event", "newValue", "toString", "shouldClearOnEdit", "IGNORED_KEYS", "pressedIgnoredKey", "includes", "key", "hasValue", "length", "renderHintText", "class", "renderCounter", "renderBottomContent", "hasHintText", "<PERSON><PERSON><PERSON><PERSON>", "renderLabel", "<PERSON><PERSON><PERSON><PERSON>", "renderLabelContainer", "mode", "hasOutlineFill", "ref", "render", "inItem", "should<PERSON>ender<PERSON>ighlight", "defaultClearIcon", "clearIconData", "hasStartEndSlots", "labelShouldFloat", "htmlFor", "id", "autoCapitalize", "autoComplete", "autoCorrect", "autoFocus", "enterKeyHint", "inputMode", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "readOnly", "onKeyDown", "onCompositionstart", "onCompositionend", "onPointerDown", "onFocusin", "onClick", "icon", "watchers", "style", "ios", "md", "ion_input"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}