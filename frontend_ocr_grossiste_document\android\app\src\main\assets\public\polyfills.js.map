{"version": 3, "file": "polyfills.js", "mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAgBA;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;AAwBsB;AAEtB;;;AAGiB,CAAE;AAGnB;;;;;;;;;;;;;;ACpDA;;;;AAIA;AACCA,MAAc,CAACC,6BAA6B,GAAG,IAAI;;;;;;;;;;ACLvC;;AACb;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAGC,UAAU;AACzB;AACA;AACA,SAASC,UAAUA,CAACC,IAAI,EAAE;EACtB,MAAMC,YAAY,GAAGJ,MAAM,CAAC,sBAAsB,CAAC,IAAI,iBAAiB;EACxE,OAAOI,YAAY,GAAGD,IAAI;AAC9B;AACA,SAASE,QAAQA,CAAA,EAAG;EAAA,IAAAC,SAAA;EAChB,MAAMC,WAAW,GAAGP,MAAM,CAAC,aAAa,CAAC;EACzC,SAASQ,IAAIA,CAACL,IAAI,EAAE;IAChBI,WAAW,IAAIA,WAAW,CAAC,MAAM,CAAC,IAAIA,WAAW,CAAC,MAAM,CAAC,CAACJ,IAAI,CAAC;EACnE;EACA,SAASM,kBAAkBA,CAACN,IAAI,EAAEO,KAAK,EAAE;IACrCH,WAAW,IAAIA,WAAW,CAAC,SAAS,CAAC,IAAIA,WAAW,CAAC,SAAS,CAAC,CAACJ,IAAI,EAAEO,KAAK,CAAC;EAChF;EACAF,IAAI,CAAC,MAAM,CAAC;EACZ,MAAMG,QAAQ,CAAC;IAGX,OAAOC,iBAAiBA,CAAA,EAAG;MACvB,IAAIZ,MAAM,CAAC,SAAS,CAAC,KAAKa,OAAO,CAAC,kBAAkB,CAAC,EAAE;QACnD,MAAM,IAAIC,KAAK,CAAC,uEAAuE,GACnF,yBAAyB,GACzB,+DAA+D,GAC/D,kFAAkF,GAClF,sDAAsD,CAAC;MAC/D;IACJ;IACA,WAAWC,IAAIA,CAAA,EAAG;MACd,IAAIC,IAAI,GAAGL,QAAQ,CAACM,OAAO;MAC3B,OAAOD,IAAI,CAACE,MAAM,EAAE;QAChBF,IAAI,GAAGA,IAAI,CAACE,MAAM;MACtB;MACA,OAAOF,IAAI;IACf;IACA,WAAWC,OAAOA,CAAA,EAAG;MACjB,OAAOE,iBAAiB,CAACH,IAAI;IACjC;IACA,WAAWI,WAAWA,CAAA,EAAG;MACrB,OAAOC,YAAY;IACvB;IACA;IACA,OAAOC,YAAYA,CAACnB,IAAI,EAAEoB,EAAE,EAAEC,eAAe,GAAG,KAAK,EAAE;MACnD,IAAIX,OAAO,CAACY,cAAc,CAACtB,IAAI,CAAC,EAAE;QAC9B;QACA;QACA;QACA,MAAMuB,cAAc,GAAG1B,MAAM,CAACE,UAAU,CAAC,yBAAyB,CAAC,CAAC,KAAK,IAAI;QAC7E,IAAI,CAACsB,eAAe,IAAIE,cAAc,EAAE;UACpC,MAAMZ,KAAK,CAAC,wBAAwB,GAAGX,IAAI,CAAC;QAChD;MACJ,CAAC,MACI,IAAI,CAACH,MAAM,CAAC,iBAAiB,GAAGG,IAAI,CAAC,EAAE;QACxC,MAAMwB,QAAQ,GAAG,OAAO,GAAGxB,IAAI;QAC/BK,IAAI,CAACmB,QAAQ,CAAC;QACdd,OAAO,CAACV,IAAI,CAAC,GAAGoB,EAAE,CAACvB,MAAM,EAAEW,QAAQ,EAAEiB,IAAI,CAAC;QAC1CnB,kBAAkB,CAACkB,QAAQ,EAAEA,QAAQ,CAAC;MAC1C;IACJ;IACA,IAAIT,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACW,OAAO;IACvB;IACA,IAAI1B,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAAC2B,KAAK;IACrB;IACAC,WAAWA,CAACb,MAAM,EAAEc,QAAQ,EAAE;MAC1B,IAAI,CAACH,OAAO,GAAGX,MAAM;MACrB,IAAI,CAACY,KAAK,GAAGE,QAAQ,GAAGA,QAAQ,CAAC7B,IAAI,IAAI,SAAS,GAAG,QAAQ;MAC7D,IAAI,CAAC8B,WAAW,GAAID,QAAQ,IAAIA,QAAQ,CAACE,UAAU,IAAK,CAAC,CAAC;MAC1D,IAAI,CAACC,aAAa,GAAG,IAAIC,aAAa,CAAC,IAAI,EAAE,IAAI,CAACP,OAAO,IAAI,IAAI,CAACA,OAAO,CAACM,aAAa,EAAEH,QAAQ,CAAC;IACtG;IACAK,GAAGA,CAACC,GAAG,EAAE;MACL,MAAMtB,IAAI,GAAG,IAAI,CAACuB,WAAW,CAACD,GAAG,CAAC;MAClC,IAAItB,IAAI,EACJ,OAAOA,IAAI,CAACiB,WAAW,CAACK,GAAG,CAAC;IACpC;IACAC,WAAWA,CAACD,GAAG,EAAE;MACb,IAAIrB,OAAO,GAAG,IAAI;MAClB,OAAOA,OAAO,EAAE;QACZ,IAAIA,OAAO,CAACgB,WAAW,CAACR,cAAc,CAACa,GAAG,CAAC,EAAE;UACzC,OAAOrB,OAAO;QAClB;QACAA,OAAO,GAAGA,OAAO,CAACY,OAAO;MAC7B;MACA,OAAO,IAAI;IACf;IACAW,IAAIA,CAACR,QAAQ,EAAE;MACX,IAAI,CAACA,QAAQ,EACT,MAAM,IAAIlB,KAAK,CAAC,oBAAoB,CAAC;MACzC,OAAO,IAAI,CAACqB,aAAa,CAACK,IAAI,CAAC,IAAI,EAAER,QAAQ,CAAC;IAClD;IACAS,IAAIA,CAACC,QAAQ,EAAEC,MAAM,EAAE;MACnB,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;QAChC,MAAM,IAAI5B,KAAK,CAAC,0BAA0B,GAAG4B,QAAQ,CAAC;MAC1D;MACA,MAAME,SAAS,GAAG,IAAI,CAACT,aAAa,CAACU,SAAS,CAAC,IAAI,EAAEH,QAAQ,EAAEC,MAAM,CAAC;MACtE,MAAM3B,IAAI,GAAG,IAAI;MACjB,OAAO,YAAY;QACf,OAAOA,IAAI,CAAC8B,UAAU,CAACF,SAAS,EAAE,IAAI,EAAEG,SAAS,EAAEJ,MAAM,CAAC;MAC9D,CAAC;IACL;IACAK,GAAGA,CAACN,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACxCxB,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,OAAO,IAAI,CAACmB,aAAa,CAACgB,MAAM,CAAC,IAAI,EAAET,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC;MAClF,CAAC,SACO;QACJxB,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;MAChD;IACJ;IACA4B,UAAUA,CAACJ,QAAQ,EAAEO,SAAS,GAAG,IAAI,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACtDxB,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,IAAI;UACA,OAAO,IAAI,CAACmB,aAAa,CAACgB,MAAM,CAAC,IAAI,EAAET,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC;QAClF,CAAC,CACD,OAAOS,KAAK,EAAE;UACV,IAAI,IAAI,CAACjB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAED,KAAK,CAAC,EAAE;YAC7C,MAAMA,KAAK;UACf;QACJ;MACJ,CAAC,SACO;QACJjC,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;MAChD;IACJ;IACAoC,OAAOA,CAACC,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;MAChC,IAAIK,IAAI,CAACvC,IAAI,IAAI,IAAI,EAAE;QACnB,MAAM,IAAIF,KAAK,CAAC,6DAA6D,GACzE,CAACyC,IAAI,CAACvC,IAAI,IAAIwC,OAAO,EAAErD,IAAI,GAC3B,eAAe,GACf,IAAI,CAACA,IAAI,GACT,GAAG,CAAC;MACZ;MACA;MACA;MACA;MACA,IAAIoD,IAAI,CAACE,KAAK,KAAKC,YAAY,KAAKH,IAAI,CAACI,IAAI,KAAKC,SAAS,IAAIL,IAAI,CAACI,IAAI,KAAKE,SAAS,CAAC,EAAE;QACrF;MACJ;MACA,MAAMC,YAAY,GAAGP,IAAI,CAACE,KAAK,IAAIM,OAAO;MAC1CD,YAAY,IAAIP,IAAI,CAACS,aAAa,CAACD,OAAO,EAAEE,SAAS,CAAC;MACtDV,IAAI,CAACW,QAAQ,EAAE;MACf,MAAMC,YAAY,GAAG9C,YAAY;MACjCA,YAAY,GAAGkC,IAAI;MACnBpC,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,IAAIuC,IAAI,CAACI,IAAI,IAAIE,SAAS,IAAIN,IAAI,CAACa,IAAI,IAAI,CAACb,IAAI,CAACa,IAAI,CAACC,UAAU,EAAE;UAC9Dd,IAAI,CAACe,QAAQ,GAAGC,SAAS;QAC7B;QACA,IAAI;UACA,OAAO,IAAI,CAACpC,aAAa,CAACqC,UAAU,CAAC,IAAI,EAAEjB,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC;QAC1E,CAAC,CACD,OAAOE,KAAK,EAAE;UACV,IAAI,IAAI,CAACjB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAED,KAAK,CAAC,EAAE;YAC7C,MAAMA,KAAK;UACf;QACJ;MACJ,CAAC,SACO;QACJ;QACA;QACA,IAAIG,IAAI,CAACE,KAAK,KAAKC,YAAY,IAAIH,IAAI,CAACE,KAAK,KAAKgB,OAAO,EAAE;UACvD,IAAIlB,IAAI,CAACI,IAAI,IAAIC,SAAS,IAAKL,IAAI,CAACa,IAAI,IAAIb,IAAI,CAACa,IAAI,CAACC,UAAW,EAAE;YAC/DP,YAAY,IAAIP,IAAI,CAACS,aAAa,CAACC,SAAS,EAAEF,OAAO,CAAC;UAC1D,CAAC,MACI;YACDR,IAAI,CAACW,QAAQ,GAAG,CAAC;YACjB,IAAI,CAACQ,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC,CAAC;YAC/BO,YAAY,IACRP,IAAI,CAACS,aAAa,CAACN,YAAY,EAAEK,OAAO,EAAEL,YAAY,CAAC;UAC/D;QACJ;QACAvC,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;QAC5CG,YAAY,GAAG8C,YAAY;MAC/B;IACJ;IACAQ,YAAYA,CAACpB,IAAI,EAAE;MACf,IAAIA,IAAI,CAACvC,IAAI,IAAIuC,IAAI,CAACvC,IAAI,KAAK,IAAI,EAAE;QACjC;QACA;QACA,IAAI4D,OAAO,GAAG,IAAI;QAClB,OAAOA,OAAO,EAAE;UACZ,IAAIA,OAAO,KAAKrB,IAAI,CAACvC,IAAI,EAAE;YACvB,MAAMF,KAAK,CAAE,8BAA6B,IAAI,CAACX,IAAK,8CAA6CoD,IAAI,CAACvC,IAAI,CAACb,IAAK,EAAC,CAAC;UACtH;UACAyE,OAAO,GAAGA,OAAO,CAAC1D,MAAM;QAC5B;MACJ;MACAqC,IAAI,CAACS,aAAa,CAACa,UAAU,EAAEnB,YAAY,CAAC;MAC5C,MAAMoB,aAAa,GAAG,EAAE;MACxBvB,IAAI,CAACwB,cAAc,GAAGD,aAAa;MACnCvB,IAAI,CAACyB,KAAK,GAAG,IAAI;MACjB,IAAI;QACAzB,IAAI,GAAG,IAAI,CAACpB,aAAa,CAACwC,YAAY,CAAC,IAAI,EAAEpB,IAAI,CAAC;MACtD,CAAC,CACD,OAAO0B,GAAG,EAAE;QACR;QACA;QACA1B,IAAI,CAACS,aAAa,CAACS,OAAO,EAAEI,UAAU,EAAEnB,YAAY,CAAC;QACrD;QACA,IAAI,CAACvB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAE4B,GAAG,CAAC;QACzC,MAAMA,GAAG;MACb;MACA,IAAI1B,IAAI,CAACwB,cAAc,KAAKD,aAAa,EAAE;QACvC;QACA,IAAI,CAACJ,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC;MAClC;MACA,IAAIA,IAAI,CAACE,KAAK,IAAIoB,UAAU,EAAE;QAC1BtB,IAAI,CAACS,aAAa,CAACC,SAAS,EAAEY,UAAU,CAAC;MAC7C;MACA,OAAOtB,IAAI;IACf;IACA2B,iBAAiBA,CAACvC,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAE;MACtD,OAAO,IAAI,CAACR,YAAY,CAAC,IAAIS,QAAQ,CAACC,SAAS,EAAE1C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEZ,SAAS,CAAC,CAAC;IACxG;IACAe,iBAAiBA,CAAC3C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;MACpE,OAAO,IAAI,CAACZ,YAAY,CAAC,IAAIS,QAAQ,CAACvB,SAAS,EAAElB,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC,CAAC;IAC3G;IACAC,iBAAiBA,CAAC7C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;MACpE,OAAO,IAAI,CAACZ,YAAY,CAAC,IAAIS,QAAQ,CAACxB,SAAS,EAAEjB,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC,CAAC;IAC3G;IACAE,UAAUA,CAAClC,IAAI,EAAE;MACb,IAAIA,IAAI,CAACvC,IAAI,IAAI,IAAI,EACjB,MAAM,IAAIF,KAAK,CAAC,mEAAmE,GAC/E,CAACyC,IAAI,CAACvC,IAAI,IAAIwC,OAAO,EAAErD,IAAI,GAC3B,eAAe,GACf,IAAI,CAACA,IAAI,GACT,GAAG,CAAC;MACZ,IAAIoD,IAAI,CAACE,KAAK,KAAKQ,SAAS,IAAIV,IAAI,CAACE,KAAK,KAAKM,OAAO,EAAE;QACpD;MACJ;MACAR,IAAI,CAACS,aAAa,CAAC0B,SAAS,EAAEzB,SAAS,EAAEF,OAAO,CAAC;MACjD,IAAI;QACA,IAAI,CAAC5B,aAAa,CAACsD,UAAU,CAAC,IAAI,EAAElC,IAAI,CAAC;MAC7C,CAAC,CACD,OAAO0B,GAAG,EAAE;QACR;QACA1B,IAAI,CAACS,aAAa,CAACS,OAAO,EAAEiB,SAAS,CAAC;QACtC,IAAI,CAACvD,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAE4B,GAAG,CAAC;QACzC,MAAMA,GAAG;MACb;MACA,IAAI,CAACP,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC,CAAC;MAC/BA,IAAI,CAACS,aAAa,CAACN,YAAY,EAAEgC,SAAS,CAAC;MAC3CnC,IAAI,CAACW,QAAQ,GAAG,CAAC;MACjB,OAAOX,IAAI;IACf;IACAmB,gBAAgBA,CAACnB,IAAI,EAAEoC,KAAK,EAAE;MAC1B,MAAMb,aAAa,GAAGvB,IAAI,CAACwB,cAAc;MACzC,IAAIY,KAAK,IAAI,CAAC,CAAC,EAAE;QACbpC,IAAI,CAACwB,cAAc,GAAG,IAAI;MAC9B;MACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,aAAa,CAACe,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3Cd,aAAa,CAACc,CAAC,CAAC,CAAClB,gBAAgB,CAACnB,IAAI,CAACI,IAAI,EAAEgC,KAAK,CAAC;MACvD;IACJ;EACJ;EAACrF,SAAA,GAhPKK,QAAQ;EACV;EACSL,SAAA,CAAKJ,UAAU,GAAGA,UAAU;EA+OzC,MAAM4F,WAAW,GAAG;IAChB3F,IAAI,EAAE,EAAE;IACR4F,SAAS,EAAEA,CAACC,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAEC,YAAY,KAAKH,QAAQ,CAACI,OAAO,CAACF,MAAM,EAAEC,YAAY,CAAC;IACxFE,cAAc,EAAEA,CAACL,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAE3C,IAAI,KAAKyC,QAAQ,CAACrB,YAAY,CAACuB,MAAM,EAAE3C,IAAI,CAAC;IAClF+C,YAAY,EAAEA,CAACN,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAE3C,IAAI,EAAEN,SAAS,EAAEC,SAAS,KAAK8C,QAAQ,CAACxB,UAAU,CAAC0B,MAAM,EAAE3C,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC;IAC1HqD,YAAY,EAAEA,CAACP,QAAQ,EAAEC,CAAC,EAAEC,MAAM,EAAE3C,IAAI,KAAKyC,QAAQ,CAACP,UAAU,CAACS,MAAM,EAAE3C,IAAI;EACjF,CAAC;EACD,MAAMnB,aAAa,CAAC;IAChB,IAAIpB,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACgE,KAAK;IACrB;IACAjD,WAAWA,CAACf,IAAI,EAAEwF,cAAc,EAAExE,QAAQ,EAAE;MACxC,IAAI,CAACyE,WAAW,GAAG;QACf,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,CAAC;QACd,WAAW,EAAE;MACjB,CAAC;MACD,IAAI,CAACzB,KAAK,GAAGhE,IAAI;MACjB,IAAI,CAAC0F,eAAe,GAAGF,cAAc;MACrC,IAAI,CAACG,OAAO,GAAG3E,QAAQ,KAAKA,QAAQ,IAAIA,QAAQ,CAAC4E,MAAM,GAAG5E,QAAQ,GAAGwE,cAAc,CAACG,OAAO,CAAC;MAC5F,IAAI,CAACE,SAAS,GAAG7E,QAAQ,KAAKA,QAAQ,CAAC4E,MAAM,GAAGJ,cAAc,GAAGA,cAAc,CAACK,SAAS,CAAC;MAC1F,IAAI,CAACC,aAAa,GACd9E,QAAQ,KAAKA,QAAQ,CAAC4E,MAAM,GAAG,IAAI,CAAC5B,KAAK,GAAGwB,cAAc,CAACM,aAAa,CAAC;MAC7E,IAAI,CAACC,YAAY,GACb/E,QAAQ,KAAKA,QAAQ,CAACgF,WAAW,GAAGhF,QAAQ,GAAGwE,cAAc,CAACO,YAAY,CAAC;MAC/E,IAAI,CAACE,cAAc,GACfjF,QAAQ,KAAKA,QAAQ,CAACgF,WAAW,GAAGR,cAAc,GAAGA,cAAc,CAACS,cAAc,CAAC;MACvF,IAAI,CAACC,kBAAkB,GACnBlF,QAAQ,KAAKA,QAAQ,CAACgF,WAAW,GAAG,IAAI,CAAChC,KAAK,GAAGwB,cAAc,CAACU,kBAAkB,CAAC;MACvF,IAAI,CAACC,SAAS,GAAGnF,QAAQ,KAAKA,QAAQ,CAACoF,QAAQ,GAAGpF,QAAQ,GAAGwE,cAAc,CAACW,SAAS,CAAC;MACtF,IAAI,CAACE,WAAW,GACZrF,QAAQ,KAAKA,QAAQ,CAACoF,QAAQ,GAAGZ,cAAc,GAAGA,cAAc,CAACa,WAAW,CAAC;MACjF,IAAI,CAACC,eAAe,GAChBtF,QAAQ,KAAKA,QAAQ,CAACoF,QAAQ,GAAG,IAAI,CAACpC,KAAK,GAAGwB,cAAc,CAACc,eAAe,CAAC;MACjF,IAAI,CAACC,cAAc,GACfvF,QAAQ,KAAKA,QAAQ,CAACwF,aAAa,GAAGxF,QAAQ,GAAGwE,cAAc,CAACe,cAAc,CAAC;MACnF,IAAI,CAACE,gBAAgB,GACjBzF,QAAQ,KAAKA,QAAQ,CAACwF,aAAa,GAAGhB,cAAc,GAAGA,cAAc,CAACiB,gBAAgB,CAAC;MAC3F,IAAI,CAACC,oBAAoB,GACrB1F,QAAQ,KAAKA,QAAQ,CAACwF,aAAa,GAAG,IAAI,CAACxC,KAAK,GAAGwB,cAAc,CAACkB,oBAAoB,CAAC;MAC3F,IAAI,CAACC,eAAe,GAChB3F,QAAQ,KAAKA,QAAQ,CAACqE,cAAc,GAAGrE,QAAQ,GAAGwE,cAAc,CAACmB,eAAe,CAAC;MACrF,IAAI,CAACC,iBAAiB,GAClB5F,QAAQ,KAAKA,QAAQ,CAACqE,cAAc,GAAGG,cAAc,GAAGA,cAAc,CAACoB,iBAAiB,CAAC;MAC7F,IAAI,CAACC,qBAAqB,GACtB7F,QAAQ,KAAKA,QAAQ,CAACqE,cAAc,GAAG,IAAI,CAACrB,KAAK,GAAGwB,cAAc,CAACqB,qBAAqB,CAAC;MAC7F,IAAI,CAACC,aAAa,GACd9F,QAAQ,KAAKA,QAAQ,CAACsE,YAAY,GAAGtE,QAAQ,GAAGwE,cAAc,CAACsB,aAAa,CAAC;MACjF,IAAI,CAACC,eAAe,GAChB/F,QAAQ,KAAKA,QAAQ,CAACsE,YAAY,GAAGE,cAAc,GAAGA,cAAc,CAACuB,eAAe,CAAC;MACzF,IAAI,CAACC,mBAAmB,GACpBhG,QAAQ,KAAKA,QAAQ,CAACsE,YAAY,GAAG,IAAI,CAACtB,KAAK,GAAGwB,cAAc,CAACwB,mBAAmB,CAAC;MACzF,IAAI,CAACC,aAAa,GACdjG,QAAQ,KAAKA,QAAQ,CAACuE,YAAY,GAAGvE,QAAQ,GAAGwE,cAAc,CAACyB,aAAa,CAAC;MACjF,IAAI,CAACC,eAAe,GAChBlG,QAAQ,KAAKA,QAAQ,CAACuE,YAAY,GAAGC,cAAc,GAAGA,cAAc,CAAC0B,eAAe,CAAC;MACzF,IAAI,CAACC,mBAAmB,GACpBnG,QAAQ,KAAKA,QAAQ,CAACuE,YAAY,GAAG,IAAI,CAACvB,KAAK,GAAGwB,cAAc,CAAC2B,mBAAmB,CAAC;MACzF,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,MAAMC,eAAe,GAAGxG,QAAQ,IAAIA,QAAQ,CAAC+D,SAAS;MACtD,MAAM0C,aAAa,GAAGjC,cAAc,IAAIA,cAAc,CAAC4B,UAAU;MACjE,IAAII,eAAe,IAAIC,aAAa,EAAE;QAClC;QACA;QACA,IAAI,CAACL,UAAU,GAAGI,eAAe,GAAGxG,QAAQ,GAAG8D,WAAW;QAC1D,IAAI,CAACuC,YAAY,GAAG7B,cAAc;QAClC,IAAI,CAAC8B,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACvD,KAAK;QAClC,IAAI,CAAChD,QAAQ,CAACqE,cAAc,EAAE;UAC1B,IAAI,CAACsB,eAAe,GAAG7B,WAAW;UAClC,IAAI,CAAC8B,iBAAiB,GAAGpB,cAAc;UACvC,IAAI,CAACqB,qBAAqB,GAAG,IAAI,CAAC7C,KAAK;QAC3C;QACA,IAAI,CAAChD,QAAQ,CAACsE,YAAY,EAAE;UACxB,IAAI,CAACwB,aAAa,GAAGhC,WAAW;UAChC,IAAI,CAACiC,eAAe,GAAGvB,cAAc;UACrC,IAAI,CAACwB,mBAAmB,GAAG,IAAI,CAAChD,KAAK;QACzC;QACA,IAAI,CAAChD,QAAQ,CAACuE,YAAY,EAAE;UACxB,IAAI,CAAC0B,aAAa,GAAGnC,WAAW;UAChC,IAAI,CAACoC,eAAe,GAAG1B,cAAc;UACrC,IAAI,CAAC2B,mBAAmB,GAAG,IAAI,CAACnD,KAAK;QACzC;MACJ;IACJ;IACAxC,IAAIA,CAACkG,UAAU,EAAE1G,QAAQ,EAAE;MACvB,OAAO,IAAI,CAAC2E,OAAO,GACb,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACC,SAAS,EAAE,IAAI,CAAC7F,IAAI,EAAE0H,UAAU,EAAE1G,QAAQ,CAAC,GACpE,IAAIrB,QAAQ,CAAC+H,UAAU,EAAE1G,QAAQ,CAAC;IAC5C;IACAa,SAASA,CAAC6F,UAAU,EAAEhG,QAAQ,EAAEC,MAAM,EAAE;MACpC,OAAO,IAAI,CAACoE,YAAY,GAClB,IAAI,CAACA,YAAY,CAACC,WAAW,CAAC,IAAI,CAACC,cAAc,EAAE,IAAI,CAACC,kBAAkB,EAAEwB,UAAU,EAAEhG,QAAQ,EAAEC,MAAM,CAAC,GACzGD,QAAQ;IAClB;IACAS,MAAMA,CAACuF,UAAU,EAAEhG,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACvD,OAAO,IAAI,CAACwE,SAAS,GACf,IAAI,CAACA,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,eAAe,EAAEoB,UAAU,EAAEhG,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC,GACnHD,QAAQ,CAACiG,KAAK,CAAC1F,SAAS,EAAEC,SAAS,CAAC;IAC9C;IACAG,WAAWA,CAACqF,UAAU,EAAEtF,KAAK,EAAE;MAC3B,OAAO,IAAI,CAACmE,cAAc,GACpB,IAAI,CAACA,cAAc,CAACC,aAAa,CAAC,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,oBAAoB,EAAEgB,UAAU,EAAEtF,KAAK,CAAC,GACtG,IAAI;IACd;IACAuB,YAAYA,CAAC+D,UAAU,EAAEnF,IAAI,EAAE;MAC3B,IAAIqF,UAAU,GAAGrF,IAAI;MACrB,IAAI,IAAI,CAACoE,eAAe,EAAE;QACtB,IAAI,IAAI,CAACS,UAAU,EAAE;UACjBQ,UAAU,CAAC7D,cAAc,CAAC8D,IAAI,CAAC,IAAI,CAACP,iBAAiB,CAAC;QAC1D;QACAM,UAAU,GAAG,IAAI,CAACjB,eAAe,CAACtB,cAAc,CAAC,IAAI,CAACuB,iBAAiB,EAAE,IAAI,CAACC,qBAAqB,EAAEa,UAAU,EAAEnF,IAAI,CAAC;QACtH,IAAI,CAACqF,UAAU,EACXA,UAAU,GAAGrF,IAAI;MACzB,CAAC,MACI;QACD,IAAIA,IAAI,CAACuF,UAAU,EAAE;UACjBvF,IAAI,CAACuF,UAAU,CAACvF,IAAI,CAAC;QACzB,CAAC,MACI,IAAIA,IAAI,CAACI,IAAI,IAAI0B,SAAS,EAAE;UAC7BH,iBAAiB,CAAC3B,IAAI,CAAC;QAC3B,CAAC,MACI;UACD,MAAM,IAAIzC,KAAK,CAAC,6BAA6B,CAAC;QAClD;MACJ;MACA,OAAO8H,UAAU;IACrB;IACApE,UAAUA,CAACkE,UAAU,EAAEnF,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;MAC/C,OAAO,IAAI,CAAC4E,aAAa,GACnB,IAAI,CAACA,aAAa,CAACxB,YAAY,CAAC,IAAI,CAACyB,eAAe,EAAE,IAAI,CAACC,mBAAmB,EAAEU,UAAU,EAAEnF,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC,GACvHK,IAAI,CAACb,QAAQ,CAACiG,KAAK,CAAC1F,SAAS,EAAEC,SAAS,CAAC;IACnD;IACAuC,UAAUA,CAACiD,UAAU,EAAEnF,IAAI,EAAE;MACzB,IAAIwF,KAAK;MACT,IAAI,IAAI,CAACd,aAAa,EAAE;QACpBc,KAAK,GAAG,IAAI,CAACd,aAAa,CAAC1B,YAAY,CAAC,IAAI,CAAC2B,eAAe,EAAE,IAAI,CAACC,mBAAmB,EAAEO,UAAU,EAAEnF,IAAI,CAAC;MAC7G,CAAC,MACI;QACD,IAAI,CAACA,IAAI,CAACe,QAAQ,EAAE;UAChB,MAAMxD,KAAK,CAAC,wBAAwB,CAAC;QACzC;QACAiI,KAAK,GAAGxF,IAAI,CAACe,QAAQ,CAACf,IAAI,CAAC;MAC/B;MACA,OAAOwF,KAAK;IAChB;IACA3C,OAAOA,CAACsC,UAAU,EAAEM,OAAO,EAAE;MACzB;MACA;MACA,IAAI;QACA,IAAI,CAACZ,UAAU,IACX,IAAI,CAACA,UAAU,CAACrC,SAAS,CAAC,IAAI,CAACsC,YAAY,EAAE,IAAI,CAACE,gBAAgB,EAAEG,UAAU,EAAEM,OAAO,CAAC;MAChG,CAAC,CACD,OAAO/D,GAAG,EAAE;QACR,IAAI,CAAC5B,WAAW,CAACqF,UAAU,EAAEzD,GAAG,CAAC;MACrC;IACJ;IACA;IACAP,gBAAgBA,CAACf,IAAI,EAAEgC,KAAK,EAAE;MAC1B,MAAMsD,MAAM,GAAG,IAAI,CAACxC,WAAW;MAC/B,MAAMyC,IAAI,GAAGD,MAAM,CAACtF,IAAI,CAAC;MACzB,MAAMwF,IAAI,GAAIF,MAAM,CAACtF,IAAI,CAAC,GAAGuF,IAAI,GAAGvD,KAAM;MAC1C,IAAIwD,IAAI,GAAG,CAAC,EAAE;QACV,MAAM,IAAIrI,KAAK,CAAC,0CAA0C,CAAC;MAC/D;MACA,IAAIoI,IAAI,IAAI,CAAC,IAAIC,IAAI,IAAI,CAAC,EAAE;QACxB,MAAMH,OAAO,GAAG;UACZ3D,SAAS,EAAE4D,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCpF,SAAS,EAAEoF,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCrF,SAAS,EAAEqF,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCG,MAAM,EAAEzF;QACZ,CAAC;QACD,IAAI,CAACyC,OAAO,CAAC,IAAI,CAACpB,KAAK,EAAEgE,OAAO,CAAC;MACrC;IACJ;EACJ;EACA,MAAM5D,QAAQ,CAAC;IACXrD,WAAWA,CAAC4B,IAAI,EAAEhB,MAAM,EAAED,QAAQ,EAAE2G,OAAO,EAAEP,UAAU,EAAExE,QAAQ,EAAE;MAC/D;MACA,IAAI,CAACU,KAAK,GAAG,IAAI;MACjB,IAAI,CAACd,QAAQ,GAAG,CAAC;MACjB;MACA,IAAI,CAACa,cAAc,GAAG,IAAI;MAC1B;MACA,IAAI,CAACuE,MAAM,GAAG,cAAc;MAC5B,IAAI,CAAC3F,IAAI,GAAGA,IAAI;MAChB,IAAI,CAAChB,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACyB,IAAI,GAAGiF,OAAO;MACnB,IAAI,CAACP,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACxE,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAAC5B,QAAQ,EAAE;QACX,MAAM,IAAI5B,KAAK,CAAC,yBAAyB,CAAC;MAC9C;MACA,IAAI,CAAC4B,QAAQ,GAAGA,QAAQ;MACxB,MAAM6G,IAAI,GAAG,IAAI;MACjB;MACA,IAAI5F,IAAI,KAAKC,SAAS,IAAIyF,OAAO,IAAIA,OAAO,CAACG,IAAI,EAAE;QAC/C,IAAI,CAACrG,MAAM,GAAGiC,QAAQ,CAACZ,UAAU;MACrC,CAAC,MACI;QACD,IAAI,CAACrB,MAAM,GAAG,YAAY;UACtB,OAAOiC,QAAQ,CAACZ,UAAU,CAACiF,IAAI,CAACzJ,MAAM,EAAEuJ,IAAI,EAAE,IAAI,EAAExG,SAAS,CAAC;QAClE,CAAC;MACL;IACJ;IACA,OAAOyB,UAAUA,CAACjB,IAAI,EAAE2C,MAAM,EAAEwD,IAAI,EAAE;MAClC,IAAI,CAACnG,IAAI,EAAE;QACPA,IAAI,GAAG,IAAI;MACf;MACAoG,yBAAyB,EAAE;MAC3B,IAAI;QACApG,IAAI,CAACW,QAAQ,EAAE;QACf,OAAOX,IAAI,CAACvC,IAAI,CAACsC,OAAO,CAACC,IAAI,EAAE2C,MAAM,EAAEwD,IAAI,CAAC;MAChD,CAAC,SACO;QACJ,IAAIC,yBAAyB,IAAI,CAAC,EAAE;UAChCC,mBAAmB,CAAC,CAAC;QACzB;QACAD,yBAAyB,EAAE;MAC/B;IACJ;IACA,IAAI3I,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACgE,KAAK;IACrB;IACA,IAAIvB,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAAC6F,MAAM;IACtB;IACAO,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAAC7F,aAAa,CAACN,YAAY,EAAEmB,UAAU,CAAC;IAChD;IACA;IACAb,aAAaA,CAAC8F,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAE;MAC3C,IAAI,IAAI,CAACV,MAAM,KAAKS,UAAU,IAAI,IAAI,CAACT,MAAM,KAAKU,UAAU,EAAE;QAC1D,IAAI,CAACV,MAAM,GAAGQ,OAAO;QACrB,IAAIA,OAAO,IAAIpG,YAAY,EAAE;UACzB,IAAI,CAACqB,cAAc,GAAG,IAAI;QAC9B;MACJ,CAAC,MACI;QACD,MAAM,IAAIjE,KAAK,CAAE,GAAE,IAAI,CAAC6C,IAAK,KAAI,IAAI,CAAChB,MAAO,6BAA4BmH,OAAQ,uBAAsBC,UAAW,IAAGC,UAAU,GAAG,OAAO,GAAGA,UAAU,GAAG,GAAG,GAAG,EAAG,UAAS,IAAI,CAACV,MAAO,IAAG,CAAC;MAC/L;IACJ;IACAW,QAAQA,CAAA,EAAG;MACP,IAAI,IAAI,CAAC7F,IAAI,IAAI,OAAO,IAAI,CAACA,IAAI,CAAC8F,QAAQ,KAAK,WAAW,EAAE;QACxD,OAAO,IAAI,CAAC9F,IAAI,CAAC8F,QAAQ,CAACD,QAAQ,CAAC,CAAC;MACxC,CAAC,MACI;QACD,OAAOE,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;MAC/C;IACJ;IACA;IACA;IACAY,MAAMA,CAAA,EAAG;MACL,OAAO;QACH1G,IAAI,EAAE,IAAI,CAACA,IAAI;QACfF,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBd,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB3B,IAAI,EAAE,IAAI,CAACA,IAAI,CAACb,IAAI;QACpB+D,QAAQ,EAAE,IAAI,CAACA;MACnB,CAAC;IACL;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAAMoG,gBAAgB,GAAGpK,UAAU,CAAC,YAAY,CAAC;EACjD,MAAMqK,aAAa,GAAGrK,UAAU,CAAC,SAAS,CAAC;EAC3C,MAAMsK,UAAU,GAAGtK,UAAU,CAAC,MAAM,CAAC;EACrC,IAAIuK,eAAe,GAAG,EAAE;EACxB,IAAIC,yBAAyB,GAAG,KAAK;EACrC,IAAIC,2BAA2B;EAC/B,SAASC,uBAAuBA,CAACC,IAAI,EAAE;IACnC,IAAI,CAACF,2BAA2B,EAAE;MAC9B,IAAI3K,MAAM,CAACuK,aAAa,CAAC,EAAE;QACvBI,2BAA2B,GAAG3K,MAAM,CAACuK,aAAa,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC;MAClE;IACJ;IACA,IAAIH,2BAA2B,EAAE;MAC7B,IAAII,UAAU,GAAGJ,2BAA2B,CAACH,UAAU,CAAC;MACxD,IAAI,CAACO,UAAU,EAAE;QACb;QACA;QACAA,UAAU,GAAGJ,2BAA2B,CAAC,MAAM,CAAC;MACpD;MACAI,UAAU,CAACtB,IAAI,CAACkB,2BAA2B,EAAEE,IAAI,CAAC;IACtD,CAAC,MACI;MACD7K,MAAM,CAACsK,gBAAgB,CAAC,CAACO,IAAI,EAAE,CAAC,CAAC;IACrC;EACJ;EACA,SAAS3F,iBAAiBA,CAAC3B,IAAI,EAAE;IAC7B;IACA;IACA,IAAIoG,yBAAyB,KAAK,CAAC,IAAIc,eAAe,CAAC5E,MAAM,KAAK,CAAC,EAAE;MACjE;MACA+E,uBAAuB,CAAChB,mBAAmB,CAAC;IAChD;IACArG,IAAI,IAAIkH,eAAe,CAAC5B,IAAI,CAACtF,IAAI,CAAC;EACtC;EACA,SAASqG,mBAAmBA,CAAA,EAAG;IAC3B,IAAI,CAACc,yBAAyB,EAAE;MAC5BA,yBAAyB,GAAG,IAAI;MAChC,OAAOD,eAAe,CAAC5E,MAAM,EAAE;QAC3B,MAAMmF,KAAK,GAAGP,eAAe;QAC7BA,eAAe,GAAG,EAAE;QACpB,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoF,KAAK,CAACnF,MAAM,EAAED,CAAC,EAAE,EAAE;UACnC,MAAMrC,IAAI,GAAGyH,KAAK,CAACpF,CAAC,CAAC;UACrB,IAAI;YACArC,IAAI,CAACvC,IAAI,CAACsC,OAAO,CAACC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACvC,CAAC,CACD,OAAOH,KAAK,EAAE;YACVxB,IAAI,CAACqJ,gBAAgB,CAAC7H,KAAK,CAAC;UAChC;QACJ;MACJ;MACAxB,IAAI,CAACsJ,kBAAkB,CAAC,CAAC;MACzBR,yBAAyB,GAAG,KAAK;IACrC;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAAMlH,OAAO,GAAG;IAAErD,IAAI,EAAE;EAAU,CAAC;EACnC,MAAMuD,YAAY,GAAG,cAAc;IAAEmB,UAAU,GAAG,YAAY;IAAEZ,SAAS,GAAG,WAAW;IAAEF,OAAO,GAAG,SAAS;IAAE2B,SAAS,GAAG,WAAW;IAAEjB,OAAO,GAAG,SAAS;EAC1J,MAAMY,SAAS,GAAG,WAAW;IAAExB,SAAS,GAAG,WAAW;IAAED,SAAS,GAAG,WAAW;EAC/E,MAAM/C,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMe,IAAI,GAAG;IACTuJ,MAAM,EAAEjL,UAAU;IAClBkL,gBAAgB,EAAEA,CAAA,KAAMjK,iBAAiB;IACzC8J,gBAAgB,EAAEI,IAAI;IACtBH,kBAAkB,EAAEG,IAAI;IACxBnG,iBAAiB,EAAEA,iBAAiB;IACpCoG,iBAAiB,EAAEA,CAAA,KAAM,CAAC3K,QAAQ,CAACT,UAAU,CAAC,iCAAiC,CAAC,CAAC;IACjFqL,gBAAgB,EAAEA,CAAA,KAAM,EAAE;IAC1BC,iBAAiB,EAAEH,IAAI;IACvBI,WAAW,EAAEA,CAAA,KAAMJ,IAAI;IACvBK,aAAa,EAAEA,CAAA,KAAM,EAAE;IACvBC,SAAS,EAAEA,CAAA,KAAMN,IAAI;IACrBO,cAAc,EAAEA,CAAA,KAAMP,IAAI;IAC1BQ,mBAAmB,EAAEA,CAAA,KAAMR,IAAI;IAC/BS,UAAU,EAAEA,CAAA,KAAM,KAAK;IACvBC,gBAAgB,EAAEA,CAAA,KAAMxH,SAAS;IACjCyH,oBAAoB,EAAEA,CAAA,KAAMX,IAAI;IAChCY,8BAA8B,EAAEA,CAAA,KAAM1H,SAAS;IAC/C2H,YAAY,EAAEA,CAAA,KAAM3H,SAAS;IAC7B4H,UAAU,EAAEA,CAAA,KAAM,EAAE;IACpBC,UAAU,EAAEA,CAAA,KAAMf,IAAI;IACtBgB,mBAAmB,EAAEA,CAAA,KAAMhB,IAAI;IAC/BiB,gBAAgB,EAAEA,CAAA,KAAM,EAAE;IAC1BC,qBAAqB,EAAEA,CAAA,KAAMlB,IAAI;IACjCmB,iBAAiB,EAAEA,CAAA,KAAMnB,IAAI;IAC7BoB,cAAc,EAAEA,CAAA,KAAMpB,IAAI;IAC1BT,uBAAuB,EAAEA;EAC7B,CAAC;EACD,IAAIzJ,iBAAiB,GAAG;IAAED,MAAM,EAAE,IAAI;IAAEF,IAAI,EAAE,IAAIL,QAAQ,CAAC,IAAI,EAAE,IAAI;EAAE,CAAC;EACxE,IAAIU,YAAY,GAAG,IAAI;EACvB,IAAIsI,yBAAyB,GAAG,CAAC;EACjC,SAAS0B,IAAIA,CAAA,EAAG,CAAE;EAClB5K,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC;EAClC,OAAOE,QAAQ;AACnB;AAEA,SAAS+L,QAAQA,CAAA,EAAG;EAAA,IAAAC,KAAA,EAAAC,aAAA;EAChB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM5M,MAAM,GAAGC,UAAU;EACzB,MAAMyB,cAAc,GAAG1B,MAAM,CAACE,UAAU,CAAC,yBAAyB,CAAC,CAAC,KAAK,IAAI;EAC7E,IAAIF,MAAM,CAAC,MAAM,CAAC,KAAK0B,cAAc,IAAI,OAAO1B,MAAM,CAAC,MAAM,CAAC,CAACE,UAAU,KAAK,UAAU,CAAC,EAAE;IACvF,MAAM,IAAIY,KAAK,CAAC,sBAAsB,CAAC;EAC3C;EACA;EACA,CAAA8L,aAAA,GAAA5M,MAAM,CAAA2M,KAAA,GAAC,MAAM,CAAC,cAAAC,aAAA,cAAAA,aAAA,GAAd5M,MAAM,CAAA2M,KAAA,CAAQ,GAAKtM,QAAQ,CAAC,CAAC;EAC7B,OAAOL,MAAM,CAAC,MAAM,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiM,8BAA8B,GAAG9B,MAAM,CAAC0C,wBAAwB;AACtE;AACA,MAAMb,oBAAoB,GAAG7B,MAAM,CAAC2C,cAAc;AAClD;AACA,MAAMC,oBAAoB,GAAG5C,MAAM,CAAC6C,cAAc;AAClD;AACA,MAAMd,YAAY,GAAG/B,MAAM,CAAC8C,MAAM;AAClC;AACA,MAAMd,UAAU,GAAGe,KAAK,CAAC9C,SAAS,CAAC+C,KAAK;AACxC;AACA,MAAMC,sBAAsB,GAAG,kBAAkB;AACjD;AACA,MAAMC,yBAAyB,GAAG,qBAAqB;AACvD;AACA,MAAMC,8BAA8B,GAAGpN,UAAU,CAACkN,sBAAsB,CAAC;AACzE;AACA,MAAMG,iCAAiC,GAAGrN,UAAU,CAACmN,yBAAyB,CAAC;AAC/E;AACA,MAAMG,QAAQ,GAAG,MAAM;AACvB;AACA,MAAMC,SAAS,GAAG,OAAO;AACzB;AACA,MAAMC,kBAAkB,GAAGxN,UAAU,CAAC,EAAE,CAAC;AACzC,SAASmM,mBAAmBA,CAAC3J,QAAQ,EAAEC,MAAM,EAAE;EAC3C,OAAOgL,IAAI,CAAC1M,OAAO,CAACwB,IAAI,CAACC,QAAQ,EAAEC,MAAM,CAAC;AAC9C;AACA,SAASiL,gCAAgCA,CAACjL,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;EAC5F,OAAOoI,IAAI,CAAC1M,OAAO,CAACqE,iBAAiB,CAAC3C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC;AAC/F;AACA,MAAMsI,UAAU,GAAG3N,UAAU;AAC7B,MAAM4N,cAAc,GAAG,OAAOhO,MAAM,KAAK,WAAW;AACpD,MAAMiO,cAAc,GAAGD,cAAc,GAAGhO,MAAM,GAAGyE,SAAS;AAC1D,MAAMyJ,OAAO,GAAIF,cAAc,IAAIC,cAAc,IAAK9N,UAAU;AAChE,MAAMgO,gBAAgB,GAAG,iBAAiB;AAC1C,SAASvC,aAAaA,CAAChC,IAAI,EAAE/G,MAAM,EAAE;EACjC,KAAK,IAAIiD,CAAC,GAAG8D,IAAI,CAAC7D,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,IAAI,OAAO8D,IAAI,CAAC9D,CAAC,CAAC,KAAK,UAAU,EAAE;MAC/B8D,IAAI,CAAC9D,CAAC,CAAC,GAAGyG,mBAAmB,CAAC3C,IAAI,CAAC9D,CAAC,CAAC,EAAEjD,MAAM,GAAG,GAAG,GAAGiD,CAAC,CAAC;IAC5D;EACJ;EACA,OAAO8D,IAAI;AACf;AACA,SAASwE,cAAcA,CAAC9D,SAAS,EAAE+D,OAAO,EAAE;EACxC,MAAMxL,MAAM,GAAGyH,SAAS,CAACrI,WAAW,CAAC,MAAM,CAAC;EAC5C,KAAK,IAAI6D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuI,OAAO,CAACtI,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMzF,IAAI,GAAGgO,OAAO,CAACvI,CAAC,CAAC;IACvB,MAAMI,QAAQ,GAAGoE,SAAS,CAACjK,IAAI,CAAC;IAChC,IAAI6F,QAAQ,EAAE;MACV,MAAMoI,aAAa,GAAGnC,8BAA8B,CAAC7B,SAAS,EAAEjK,IAAI,CAAC;MACrE,IAAI,CAACkO,kBAAkB,CAACD,aAAa,CAAC,EAAE;QACpC;MACJ;MACAhE,SAAS,CAACjK,IAAI,CAAC,GAAG,CAAE6F,QAAQ,IAAK;QAC7B,MAAMsI,OAAO,GAAG,SAAAA,CAAA,EAAY;UACxB,OAAOtI,QAAQ,CAAC2C,KAAK,CAAC,IAAI,EAAE+C,aAAa,CAAC3I,SAAS,EAAEJ,MAAM,GAAG,GAAG,GAAGxC,IAAI,CAAC,CAAC;QAC9E,CAAC;QACDoM,qBAAqB,CAAC+B,OAAO,EAAEtI,QAAQ,CAAC;QACxC,OAAOsI,OAAO;MAClB,CAAC,EAAEtI,QAAQ,CAAC;IAChB;EACJ;AACJ;AACA,SAASqI,kBAAkBA,CAACE,YAAY,EAAE;EACtC,IAAI,CAACA,YAAY,EAAE;IACf,OAAO,IAAI;EACf;EACA,IAAIA,YAAY,CAACC,QAAQ,KAAK,KAAK,EAAE;IACjC,OAAO,KAAK;EAChB;EACA,OAAO,EAAE,OAAOD,YAAY,CAAClM,GAAG,KAAK,UAAU,IAAI,OAAOkM,YAAY,CAACE,GAAG,KAAK,WAAW,CAAC;AAC/F;AACA,MAAMC,WAAW,GAAG,OAAOC,iBAAiB,KAAK,WAAW,IAAIpF,IAAI,YAAYoF,iBAAiB;AACjG;AACA;AACA,MAAMC,MAAM,GAAG,EAAE,IAAI,IAAIZ,OAAO,CAAC,IAC7B,OAAOA,OAAO,CAACa,OAAO,KAAK,WAAW,IACtCb,OAAO,CAACa,OAAO,CAAC5E,QAAQ,CAAC,CAAC,KAAK,kBAAkB;AACrD,MAAM6E,SAAS,GAAG,CAACF,MAAM,IAAI,CAACF,WAAW,IAAI,CAAC,EAAEZ,cAAc,IAAIC,cAAc,CAAC,aAAa,CAAC,CAAC;AAChG;AACA;AACA;AACA,MAAMgB,KAAK,GAAG,OAAOf,OAAO,CAACa,OAAO,KAAK,WAAW,IAChDb,OAAO,CAACa,OAAO,CAAC5E,QAAQ,CAAC,CAAC,KAAK,kBAAkB,IACjD,CAACyE,WAAW,IACZ,CAAC,EAAEZ,cAAc,IAAIC,cAAc,CAAC,aAAa,CAAC,CAAC;AACvD,MAAMiB,sBAAsB,GAAG,CAAC,CAAC;AACjC,MAAMC,MAAM,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAC5B;EACA;EACAA,KAAK,GAAGA,KAAK,IAAIlB,OAAO,CAACkB,KAAK;EAC9B,IAAI,CAACA,KAAK,EAAE;IACR;EACJ;EACA,IAAIC,eAAe,GAAGH,sBAAsB,CAACE,KAAK,CAACvL,IAAI,CAAC;EACxD,IAAI,CAACwL,eAAe,EAAE;IAClBA,eAAe,GAAGH,sBAAsB,CAACE,KAAK,CAACvL,IAAI,CAAC,GAAGkK,UAAU,CAAC,aAAa,GAAGqB,KAAK,CAACvL,IAAI,CAAC;EACjG;EACA,MAAMuC,MAAM,GAAG,IAAI,IAAIgJ,KAAK,CAAChJ,MAAM,IAAI8H,OAAO;EAC9C,MAAMoB,QAAQ,GAAGlJ,MAAM,CAACiJ,eAAe,CAAC;EACxC,IAAIE,MAAM;EACV,IAAIP,SAAS,IAAI5I,MAAM,KAAK6H,cAAc,IAAImB,KAAK,CAACvL,IAAI,KAAK,OAAO,EAAE;IAClE;IACA;IACA;IACA,MAAM2L,UAAU,GAAGJ,KAAK;IACxBG,MAAM,GACFD,QAAQ,IACJA,QAAQ,CAAC3F,IAAI,CAAC,IAAI,EAAE6F,UAAU,CAACC,OAAO,EAAED,UAAU,CAACE,QAAQ,EAAEF,UAAU,CAACG,MAAM,EAAEH,UAAU,CAACI,KAAK,EAAEJ,UAAU,CAAClM,KAAK,CAAC;IAC3H,IAAIiM,MAAM,KAAK,IAAI,EAAE;MACjBH,KAAK,CAACS,cAAc,CAAC,CAAC;IAC1B;EACJ,CAAC,MACI;IACDN,MAAM,GAAGD,QAAQ,IAAIA,QAAQ,CAACzG,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;IACpD,IAAIsM,MAAM,IAAI9K,SAAS,IAAI,CAAC8K,MAAM,EAAE;MAChCH,KAAK,CAACS,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA,OAAON,MAAM;AACjB,CAAC;AACD,SAASO,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAE1F,SAAS,EAAE;EACzC,IAAI2F,IAAI,GAAG9D,8BAA8B,CAAC4D,GAAG,EAAEC,IAAI,CAAC;EACpD,IAAI,CAACC,IAAI,IAAI3F,SAAS,EAAE;IACpB;IACA,MAAMgE,aAAa,GAAGnC,8BAA8B,CAAC7B,SAAS,EAAE0F,IAAI,CAAC;IACrE,IAAI1B,aAAa,EAAE;MACf2B,IAAI,GAAG;QAAEC,UAAU,EAAE,IAAI;QAAEC,YAAY,EAAE;MAAK,CAAC;IACnD;EACJ;EACA;EACA;EACA,IAAI,CAACF,IAAI,IAAI,CAACA,IAAI,CAACE,YAAY,EAAE;IAC7B;EACJ;EACA,MAAMC,mBAAmB,GAAGrC,UAAU,CAAC,IAAI,GAAGiC,IAAI,GAAG,SAAS,CAAC;EAC/D,IAAID,GAAG,CAACpO,cAAc,CAACyO,mBAAmB,CAAC,IAAIL,GAAG,CAACK,mBAAmB,CAAC,EAAE;IACrE;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,OAAOH,IAAI,CAACvB,QAAQ;EACpB,OAAOuB,IAAI,CAAChH,KAAK;EACjB,MAAMoH,eAAe,GAAGJ,IAAI,CAAC1N,GAAG;EAChC,MAAM+N,eAAe,GAAGL,IAAI,CAACtB,GAAG;EAChC;EACA,MAAM4B,SAAS,GAAGP,IAAI,CAAC3C,KAAK,CAAC,CAAC,CAAC;EAC/B,IAAIgC,eAAe,GAAGH,sBAAsB,CAACqB,SAAS,CAAC;EACvD,IAAI,CAAClB,eAAe,EAAE;IAClBA,eAAe,GAAGH,sBAAsB,CAACqB,SAAS,CAAC,GAAGxC,UAAU,CAAC,aAAa,GAAGwC,SAAS,CAAC;EAC/F;EACAN,IAAI,CAACtB,GAAG,GAAG,UAAU6B,QAAQ,EAAE;IAC3B;IACA;IACA,IAAIpK,MAAM,GAAG,IAAI;IACjB,IAAI,CAACA,MAAM,IAAI2J,GAAG,KAAK7B,OAAO,EAAE;MAC5B9H,MAAM,GAAG8H,OAAO;IACpB;IACA,IAAI,CAAC9H,MAAM,EAAE;MACT;IACJ;IACA,MAAMqK,aAAa,GAAGrK,MAAM,CAACiJ,eAAe,CAAC;IAC7C,IAAI,OAAOoB,aAAa,KAAK,UAAU,EAAE;MACrCrK,MAAM,CAACsK,mBAAmB,CAACH,SAAS,EAAEpB,MAAM,CAAC;IACjD;IACA;IACA;IACAmB,eAAe,IAAIA,eAAe,CAAC3G,IAAI,CAACvD,MAAM,EAAE,IAAI,CAAC;IACrDA,MAAM,CAACiJ,eAAe,CAAC,GAAGmB,QAAQ;IAClC,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAChCpK,MAAM,CAACuK,gBAAgB,CAACJ,SAAS,EAAEpB,MAAM,EAAE,KAAK,CAAC;IACrD;EACJ,CAAC;EACD;EACA;EACAc,IAAI,CAAC1N,GAAG,GAAG,YAAY;IACnB;IACA;IACA,IAAI6D,MAAM,GAAG,IAAI;IACjB,IAAI,CAACA,MAAM,IAAI2J,GAAG,KAAK7B,OAAO,EAAE;MAC5B9H,MAAM,GAAG8H,OAAO;IACpB;IACA,IAAI,CAAC9H,MAAM,EAAE;MACT,OAAO,IAAI;IACf;IACA,MAAMkJ,QAAQ,GAAGlJ,MAAM,CAACiJ,eAAe,CAAC;IACxC,IAAIC,QAAQ,EAAE;MACV,OAAOA,QAAQ;IACnB,CAAC,MACI,IAAIe,eAAe,EAAE;MACtB;MACA;MACA;MACA;MACA;MACA;MACA,IAAIpH,KAAK,GAAGoH,eAAe,CAAC1G,IAAI,CAAC,IAAI,CAAC;MACtC,IAAIV,KAAK,EAAE;QACPgH,IAAI,CAACtB,GAAG,CAAChF,IAAI,CAAC,IAAI,EAAEV,KAAK,CAAC;QAC1B,IAAI,OAAO7C,MAAM,CAAC+H,gBAAgB,CAAC,KAAK,UAAU,EAAE;UAChD/H,MAAM,CAACwK,eAAe,CAACZ,IAAI,CAAC;QAChC;QACA,OAAO/G,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDiD,oBAAoB,CAAC6D,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;EACrCF,GAAG,CAACK,mBAAmB,CAAC,GAAG,IAAI;AACnC;AACA,SAAS1E,iBAAiBA,CAACqE,GAAG,EAAE3N,UAAU,EAAEkI,SAAS,EAAE;EACnD,IAAIlI,UAAU,EAAE;IACZ,KAAK,IAAI0D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1D,UAAU,CAAC2D,MAAM,EAAED,CAAC,EAAE,EAAE;MACxCgK,aAAa,CAACC,GAAG,EAAE,IAAI,GAAG3N,UAAU,CAAC0D,CAAC,CAAC,EAAEwE,SAAS,CAAC;IACvD;EACJ,CAAC,MACI;IACD,MAAMuG,YAAY,GAAG,EAAE;IACvB,KAAK,MAAMb,IAAI,IAAID,GAAG,EAAE;MACpB,IAAIC,IAAI,CAAC3C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE;QAC1BwD,YAAY,CAAC9H,IAAI,CAACiH,IAAI,CAAC;MAC3B;IACJ;IACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,CAAC9K,MAAM,EAAE+K,CAAC,EAAE,EAAE;MAC1ChB,aAAa,CAACC,GAAG,EAAEc,YAAY,CAACC,CAAC,CAAC,EAAExG,SAAS,CAAC;IAClD;EACJ;AACJ;AACA,MAAMyG,mBAAmB,GAAGhD,UAAU,CAAC,kBAAkB,CAAC;AAC1D;AACA,SAASzB,UAAUA,CAAC0E,SAAS,EAAE;EAC3B,MAAMC,aAAa,GAAG/C,OAAO,CAAC8C,SAAS,CAAC;EACxC,IAAI,CAACC,aAAa,EACd;EACJ;EACA/C,OAAO,CAACH,UAAU,CAACiD,SAAS,CAAC,CAAC,GAAGC,aAAa;EAC9C/C,OAAO,CAAC8C,SAAS,CAAC,GAAG,YAAY;IAC7B,MAAME,CAAC,GAAGtF,aAAa,CAAC3I,SAAS,EAAE+N,SAAS,CAAC;IAC7C,QAAQE,CAAC,CAACnL,MAAM;MACZ,KAAK,CAAC;QACF,IAAI,CAACgL,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAAC,CAAC;QAC/C;MACJ,KAAK,CAAC;QACF,IAAI,CAACF,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE;MACJ;QACI,MAAM,IAAIlQ,KAAK,CAAC,oBAAoB,CAAC;IAC7C;EACJ,CAAC;EACD;EACAyL,qBAAqB,CAACyB,OAAO,CAAC8C,SAAS,CAAC,EAAEC,aAAa,CAAC;EACxD,MAAME,QAAQ,GAAG,IAAIF,aAAa,CAAC,YAAY,CAAE,CAAC,CAAC;EACnD,IAAIjB,IAAI;EACR,KAAKA,IAAI,IAAImB,QAAQ,EAAE;IACnB;IACA,IAAIH,SAAS,KAAK,gBAAgB,IAAIhB,IAAI,KAAK,cAAc,EACzD;IACJ,CAAC,UAAUA,IAAI,EAAE;MACb,IAAI,OAAOmB,QAAQ,CAACnB,IAAI,CAAC,KAAK,UAAU,EAAE;QACtC9B,OAAO,CAAC8C,SAAS,CAAC,CAAC1G,SAAS,CAAC0F,IAAI,CAAC,GAAG,YAAY;UAC7C,OAAO,IAAI,CAACe,mBAAmB,CAAC,CAACf,IAAI,CAAC,CAACnH,KAAK,CAAC,IAAI,CAACkI,mBAAmB,CAAC,EAAE9N,SAAS,CAAC;QACtF,CAAC;MACL,CAAC,MACI;QACDiJ,oBAAoB,CAACgC,OAAO,CAAC8C,SAAS,CAAC,CAAC1G,SAAS,EAAE0F,IAAI,EAAE;UACrDrB,GAAG,EAAE,SAAAA,CAAUlN,EAAE,EAAE;YACf,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;cAC1B,IAAI,CAACsP,mBAAmB,CAAC,CAACf,IAAI,CAAC,GAAGzD,mBAAmB,CAAC9K,EAAE,EAAEuP,SAAS,GAAG,GAAG,GAAGhB,IAAI,CAAC;cACjF;cACA;cACA;cACAvD,qBAAqB,CAAC,IAAI,CAACsE,mBAAmB,CAAC,CAACf,IAAI,CAAC,EAAEvO,EAAE,CAAC;YAC9D,CAAC,MACI;cACD,IAAI,CAACsP,mBAAmB,CAAC,CAACf,IAAI,CAAC,GAAGvO,EAAE;YACxC;UACJ,CAAC;UACDc,GAAG,EAAE,SAAAA,CAAA,EAAY;YACb,OAAO,IAAI,CAACwO,mBAAmB,CAAC,CAACf,IAAI,CAAC;UAC1C;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,EAAEA,IAAI,CAAC;EACZ;EACA,KAAKA,IAAI,IAAIiB,aAAa,EAAE;IACxB,IAAIjB,IAAI,KAAK,WAAW,IAAIiB,aAAa,CAACtP,cAAc,CAACqO,IAAI,CAAC,EAAE;MAC5D9B,OAAO,CAAC8C,SAAS,CAAC,CAAChB,IAAI,CAAC,GAAGiB,aAAa,CAACjB,IAAI,CAAC;IAClD;EACJ;AACJ;AACA,SAASrE,WAAWA,CAACvF,MAAM,EAAE/F,IAAI,EAAE+Q,OAAO,EAAE;EACxC,IAAIC,KAAK,GAAGjL,MAAM;EAClB,OAAOiL,KAAK,IAAI,CAACA,KAAK,CAAC1P,cAAc,CAACtB,IAAI,CAAC,EAAE;IACzCgR,KAAK,GAAGpE,oBAAoB,CAACoE,KAAK,CAAC;EACvC;EACA,IAAI,CAACA,KAAK,IAAIjL,MAAM,CAAC/F,IAAI,CAAC,EAAE;IACxB;IACAgR,KAAK,GAAGjL,MAAM;EAClB;EACA,MAAMkL,YAAY,GAAGvD,UAAU,CAAC1N,IAAI,CAAC;EACrC,IAAI6F,QAAQ,GAAG,IAAI;EACnB,IAAImL,KAAK,KAAK,EAAEnL,QAAQ,GAAGmL,KAAK,CAACC,YAAY,CAAC,CAAC,IAAI,CAACD,KAAK,CAAC1P,cAAc,CAAC2P,YAAY,CAAC,CAAC,EAAE;IACrFpL,QAAQ,GAAGmL,KAAK,CAACC,YAAY,CAAC,GAAGD,KAAK,CAAChR,IAAI,CAAC;IAC5C;IACA;IACA,MAAM4P,IAAI,GAAGoB,KAAK,IAAIlF,8BAA8B,CAACkF,KAAK,EAAEhR,IAAI,CAAC;IACjE,IAAIkO,kBAAkB,CAAC0B,IAAI,CAAC,EAAE;MAC1B,MAAMsB,aAAa,GAAGH,OAAO,CAAClL,QAAQ,EAAEoL,YAAY,EAAEjR,IAAI,CAAC;MAC3DgR,KAAK,CAAChR,IAAI,CAAC,GAAG,YAAY;QACtB,OAAOkR,aAAa,CAAC,IAAI,EAAEtO,SAAS,CAAC;MACzC,CAAC;MACDwJ,qBAAqB,CAAC4E,KAAK,CAAChR,IAAI,CAAC,EAAE6F,QAAQ,CAAC;IAChD;EACJ;EACA,OAAOA,QAAQ;AACnB;AACA;AACA,SAAS4F,cAAcA,CAACiE,GAAG,EAAEyB,QAAQ,EAAEC,WAAW,EAAE;EAChD,IAAIC,SAAS,GAAG,IAAI;EACpB,SAAS7M,YAAYA,CAACpB,IAAI,EAAE;IACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;IACtBA,IAAI,CAACsF,IAAI,CAACtF,IAAI,CAACqN,KAAK,CAAC,GAAG,YAAY;MAChClO,IAAI,CAACJ,MAAM,CAACwF,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;IACtC,CAAC;IACDyO,SAAS,CAAC7I,KAAK,CAACvE,IAAI,CAAC8B,MAAM,EAAE9B,IAAI,CAACsF,IAAI,CAAC;IACvC,OAAOnG,IAAI;EACf;EACAiO,SAAS,GAAG/F,WAAW,CAACoE,GAAG,EAAEyB,QAAQ,EAAGtL,QAAQ,IAAK,UAAUuD,IAAI,EAAEG,IAAI,EAAE;IACvE,MAAMgI,IAAI,GAAGH,WAAW,CAAChI,IAAI,EAAEG,IAAI,CAAC;IACpC,IAAIgI,IAAI,CAACD,KAAK,IAAI,CAAC,IAAI,OAAO/H,IAAI,CAACgI,IAAI,CAACD,KAAK,CAAC,KAAK,UAAU,EAAE;MAC3D,OAAO7D,gCAAgC,CAAC8D,IAAI,CAACvR,IAAI,EAAEuJ,IAAI,CAACgI,IAAI,CAACD,KAAK,CAAC,EAAEC,IAAI,EAAE/M,YAAY,CAAC;IAC5F,CAAC,MACI;MACD;MACA,OAAOqB,QAAQ,CAAC2C,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;IACrC;EACJ,CAAC,CAAC;AACN;AACA,SAAS6C,qBAAqBA,CAAC+B,OAAO,EAAEqD,QAAQ,EAAE;EAC9CrD,OAAO,CAACT,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAG8D,QAAQ;AACtD;AACA,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,QAAQ,GAAG,KAAK;AACpB,SAASC,IAAIA,CAAA,EAAG;EACZ,IAAI;IACA,MAAMC,EAAE,GAAGhE,cAAc,CAACiE,SAAS,CAACC,SAAS;IAC7C,IAAIF,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7D,OAAO,IAAI;IACf;EACJ,CAAC,CACD,OAAO9O,KAAK,EAAE,CAAE;EAChB,OAAO,KAAK;AAChB;AACA,SAAS0I,UAAUA,CAAA,EAAG;EAClB,IAAI8F,kBAAkB,EAAE;IACpB,OAAOC,QAAQ;EACnB;EACAD,kBAAkB,GAAG,IAAI;EACzB,IAAI;IACA,MAAMG,EAAE,GAAGhE,cAAc,CAACiE,SAAS,CAACC,SAAS;IAC7C,IAAIF,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3FL,QAAQ,GAAG,IAAI;IACnB;EACJ,CAAC,CACD,OAAOzO,KAAK,EAAE,CAAE;EAChB,OAAOyO,QAAQ;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIM,gBAAgB,GAAG,KAAK;AAC5B,IAAI,OAAOrS,MAAM,KAAK,WAAW,EAAE;EAC/B,IAAI;IACA,MAAMuJ,OAAO,GAAGc,MAAM,CAAC2C,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;MACjDzK,GAAG,EAAE,SAAAA,CAAA,EAAY;QACb8P,gBAAgB,GAAG,IAAI;MAC3B;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACArS,MAAM,CAAC2Q,gBAAgB,CAAC,MAAM,EAAEpH,OAAO,EAAEA,OAAO,CAAC;IACjDvJ,MAAM,CAAC0Q,mBAAmB,CAAC,MAAM,EAAEnH,OAAO,EAAEA,OAAO,CAAC;EACxD,CAAC,CACD,OAAOpE,GAAG,EAAE;IACRkN,gBAAgB,GAAG,KAAK;EAC5B;AACJ;AACA;AACA,MAAMC,8BAA8B,GAAG;EACnC5I,IAAI,EAAE;AACV,CAAC;AACD,MAAM6I,oBAAoB,GAAG,CAAC,CAAC;AAC/B,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,MAAMC,sBAAsB,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAG9E,kBAAkB,GAAG,qBAAqB,CAAC;AAC3F,MAAM+E,4BAA4B,GAAG5E,UAAU,CAAC,oBAAoB,CAAC;AACrE,SAAS6E,iBAAiBA,CAACrC,SAAS,EAAEsC,iBAAiB,EAAE;EACrD,MAAMC,cAAc,GAAG,CAACD,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAS,CAAC,GAAGA,SAAS,IAAI5C,SAAS;EACjG,MAAMoF,aAAa,GAAG,CAACF,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAS,CAAC,GAAGA,SAAS,IAAI7C,QAAQ;EAC/F,MAAMrC,MAAM,GAAGuC,kBAAkB,GAAGkF,cAAc;EAClD,MAAME,aAAa,GAAGpF,kBAAkB,GAAGmF,aAAa;EACxDR,oBAAoB,CAAChC,SAAS,CAAC,GAAG,CAAC,CAAC;EACpCgC,oBAAoB,CAAChC,SAAS,CAAC,CAAC5C,SAAS,CAAC,GAAGtC,MAAM;EACnDkH,oBAAoB,CAAChC,SAAS,CAAC,CAAC7C,QAAQ,CAAC,GAAGsF,aAAa;AAC7D;AACA,SAASvH,gBAAgBA,CAACyC,OAAO,EAAE+E,GAAG,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACxD,MAAMC,kBAAkB,GAAID,YAAY,IAAIA,YAAY,CAACE,GAAG,IAAK/F,sBAAsB;EACvF,MAAMgG,qBAAqB,GAAIH,YAAY,IAAIA,YAAY,CAACI,EAAE,IAAKhG,yBAAyB;EAC5F,MAAMiG,wBAAwB,GAAIL,YAAY,IAAIA,YAAY,CAACM,SAAS,IAAK,gBAAgB;EAC7F,MAAMC,mCAAmC,GAAIP,YAAY,IAAIA,YAAY,CAACQ,KAAK,IAAK,oBAAoB;EACxG,MAAMC,0BAA0B,GAAG7F,UAAU,CAACqF,kBAAkB,CAAC;EACjE,MAAMS,yBAAyB,GAAG,GAAG,GAAGT,kBAAkB,GAAG,GAAG;EAChE,MAAMU,sBAAsB,GAAG,iBAAiB;EAChD,MAAMC,6BAA6B,GAAG,GAAG,GAAGD,sBAAsB,GAAG,GAAG;EACxE,MAAMpP,UAAU,GAAG,SAAAA,CAAUjB,IAAI,EAAE2C,MAAM,EAAEgJ,KAAK,EAAE;IAC9C;IACA;IACA,IAAI3L,IAAI,CAACuQ,SAAS,EAAE;MAChB;IACJ;IACA,MAAM9N,QAAQ,GAAGzC,IAAI,CAACb,QAAQ;IAC9B,IAAI,OAAOsD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAAC+N,WAAW,EAAE;MACtD;MACAxQ,IAAI,CAACb,QAAQ,GAAIwM,KAAK,IAAKlJ,QAAQ,CAAC+N,WAAW,CAAC7E,KAAK,CAAC;MACtD3L,IAAI,CAACyQ,gBAAgB,GAAGhO,QAAQ;IACpC;IACA;IACA;IACA;IACA;IACA,IAAI5C,KAAK;IACT,IAAI;MACAG,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE2C,MAAM,EAAE,CAACgJ,KAAK,CAAC,CAAC;IACtC,CAAC,CACD,OAAOjK,GAAG,EAAE;MACR7B,KAAK,GAAG6B,GAAG;IACf;IACA,MAAMoE,OAAO,GAAG9F,IAAI,CAAC8F,OAAO;IAC5B,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAAC4K,IAAI,EAAE;MACxD;MACA;MACA;MACA,MAAMjO,QAAQ,GAAGzC,IAAI,CAACyQ,gBAAgB,GAAGzQ,IAAI,CAACyQ,gBAAgB,GAAGzQ,IAAI,CAACb,QAAQ;MAC9EwD,MAAM,CAACkN,qBAAqB,CAAC,CAAC3J,IAAI,CAACvD,MAAM,EAAEgJ,KAAK,CAACvL,IAAI,EAAEqC,QAAQ,EAAEqD,OAAO,CAAC;IAC7E;IACA,OAAOjG,KAAK;EAChB,CAAC;EACD,SAAS8Q,cAAcA,CAACC,OAAO,EAAEjF,KAAK,EAAEkF,SAAS,EAAE;IAC/C;IACA;IACAlF,KAAK,GAAGA,KAAK,IAAIlB,OAAO,CAACkB,KAAK;IAC9B,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA;IACA;IACA,MAAMhJ,MAAM,GAAGiO,OAAO,IAAIjF,KAAK,CAAChJ,MAAM,IAAI8H,OAAO;IACjD,MAAMqG,KAAK,GAAGnO,MAAM,CAACmM,oBAAoB,CAACnD,KAAK,CAACvL,IAAI,CAAC,CAACyQ,SAAS,GAAG5G,QAAQ,GAAGC,SAAS,CAAC,CAAC;IACxF,IAAI4G,KAAK,EAAE;MACP,MAAMC,MAAM,GAAG,EAAE;MACjB;MACA;MACA,IAAID,KAAK,CAACxO,MAAM,KAAK,CAAC,EAAE;QACpB,MAAMZ,GAAG,GAAGT,UAAU,CAAC6P,KAAK,CAAC,CAAC,CAAC,EAAEnO,MAAM,EAAEgJ,KAAK,CAAC;QAC/CjK,GAAG,IAAIqP,MAAM,CAACzL,IAAI,CAAC5D,GAAG,CAAC;MAC3B,CAAC,MACI;QACD;QACA;QACA;QACA,MAAMsP,SAAS,GAAGF,KAAK,CAAClH,KAAK,CAAC,CAAC;QAC/B,KAAK,IAAIvH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2O,SAAS,CAAC1O,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC,IAAIsJ,KAAK,IAAIA,KAAK,CAACuD,4BAA4B,CAAC,KAAK,IAAI,EAAE;YACvD;UACJ;UACA,MAAMxN,GAAG,GAAGT,UAAU,CAAC+P,SAAS,CAAC3O,CAAC,CAAC,EAAEM,MAAM,EAAEgJ,KAAK,CAAC;UACnDjK,GAAG,IAAIqP,MAAM,CAACzL,IAAI,CAAC5D,GAAG,CAAC;QAC3B;MACJ;MACA;MACA;MACA,IAAIqP,MAAM,CAACzO,MAAM,KAAK,CAAC,EAAE;QACrB,MAAMyO,MAAM,CAAC,CAAC,CAAC;MACnB,CAAC,MACI;QACD,KAAK,IAAI1O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0O,MAAM,CAACzO,MAAM,EAAED,CAAC,EAAE,EAAE;UACpC,MAAMX,GAAG,GAAGqP,MAAM,CAAC1O,CAAC,CAAC;UACrBmN,GAAG,CAACnI,uBAAuB,CAAC,MAAM;YAC9B,MAAM3F,GAAG;UACb,CAAC,CAAC;QACN;MACJ;IACJ;EACJ;EACA;EACA,MAAMuP,uBAAuB,GAAG,SAAAA,CAAUtF,KAAK,EAAE;IAC7C,OAAOgF,cAAc,CAAC,IAAI,EAAEhF,KAAK,EAAE,KAAK,CAAC;EAC7C,CAAC;EACD;EACA,MAAMuF,8BAA8B,GAAG,SAAAA,CAAUvF,KAAK,EAAE;IACpD,OAAOgF,cAAc,CAAC,IAAI,EAAEhF,KAAK,EAAE,IAAI,CAAC;EAC5C,CAAC;EACD,SAASwF,uBAAuBA,CAAC7E,GAAG,EAAEoD,YAAY,EAAE;IAChD,IAAI,CAACpD,GAAG,EAAE;MACN,OAAO,KAAK;IAChB;IACA,IAAI8E,iBAAiB,GAAG,IAAI;IAC5B,IAAI1B,YAAY,IAAIA,YAAY,CAACzJ,IAAI,KAAKjF,SAAS,EAAE;MACjDoQ,iBAAiB,GAAG1B,YAAY,CAACzJ,IAAI;IACzC;IACA,MAAMoL,eAAe,GAAG3B,YAAY,IAAIA,YAAY,CAAC4B,EAAE;IACvD,IAAInT,cAAc,GAAG,IAAI;IACzB,IAAIuR,YAAY,IAAIA,YAAY,CAAC6B,MAAM,KAAKvQ,SAAS,EAAE;MACnD7C,cAAc,GAAGuR,YAAY,CAAC6B,MAAM;IACxC;IACA,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAI9B,YAAY,IAAIA,YAAY,CAAC+B,EAAE,KAAKzQ,SAAS,EAAE;MAC/CwQ,YAAY,GAAG9B,YAAY,CAAC+B,EAAE;IAClC;IACA,IAAI7D,KAAK,GAAGtB,GAAG;IACf,OAAOsB,KAAK,IAAI,CAACA,KAAK,CAAC1P,cAAc,CAACyR,kBAAkB,CAAC,EAAE;MACvD/B,KAAK,GAAGpE,oBAAoB,CAACoE,KAAK,CAAC;IACvC;IACA,IAAI,CAACA,KAAK,IAAItB,GAAG,CAACqD,kBAAkB,CAAC,EAAE;MACnC;MACA/B,KAAK,GAAGtB,GAAG;IACf;IACA,IAAI,CAACsB,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;IACA,IAAIA,KAAK,CAACuC,0BAA0B,CAAC,EAAE;MACnC,OAAO,KAAK;IAChB;IACA,MAAMf,iBAAiB,GAAGM,YAAY,IAAIA,YAAY,CAACN,iBAAiB;IACxE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMsC,QAAQ,GAAG,CAAC,CAAC;IACnB,MAAMC,sBAAsB,GAAI/D,KAAK,CAACuC,0BAA0B,CAAC,GAAGvC,KAAK,CAAC+B,kBAAkB,CAAE;IAC9F,MAAMiC,yBAAyB,GAAIhE,KAAK,CAACtD,UAAU,CAACuF,qBAAqB,CAAC,CAAC,GACvEjC,KAAK,CAACiC,qBAAqB,CAAE;IACjC,MAAMgC,eAAe,GAAIjE,KAAK,CAACtD,UAAU,CAACyF,wBAAwB,CAAC,CAAC,GAChEnC,KAAK,CAACmC,wBAAwB,CAAE;IACpC,MAAM+B,wBAAwB,GAAIlE,KAAK,CAACtD,UAAU,CAAC2F,mCAAmC,CAAC,CAAC,GACpFrC,KAAK,CAACqC,mCAAmC,CAAE;IAC/C,IAAI8B,0BAA0B;IAC9B,IAAIrC,YAAY,IAAIA,YAAY,CAACsC,OAAO,EAAE;MACtCD,0BAA0B,GAAGnE,KAAK,CAACtD,UAAU,CAACoF,YAAY,CAACsC,OAAO,CAAC,CAAC,GAChEpE,KAAK,CAAC8B,YAAY,CAACsC,OAAO,CAAC;IACnC;IACA;AACR;AACA;AACA;IACQ,SAASC,yBAAyBA,CAACnM,OAAO,EAAEoM,OAAO,EAAE;MACjD,IAAI,CAACtD,gBAAgB,IAAI,OAAO9I,OAAO,KAAK,QAAQ,IAAIA,OAAO,EAAE;QAC7D;QACA;QACA;QACA,OAAO,CAAC,CAACA,OAAO,CAACqM,OAAO;MAC5B;MACA,IAAI,CAACvD,gBAAgB,IAAI,CAACsD,OAAO,EAAE;QAC/B,OAAOpM,OAAO;MAClB;MACA,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;QAC9B,OAAO;UAAEqM,OAAO,EAAErM,OAAO;UAAEoM,OAAO,EAAE;QAAK,CAAC;MAC9C;MACA,IAAI,CAACpM,OAAO,EAAE;QACV,OAAO;UAAEoM,OAAO,EAAE;QAAK,CAAC;MAC5B;MACA,IAAI,OAAOpM,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACoM,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO;UAAE,GAAGpM,OAAO;UAAEoM,OAAO,EAAE;QAAK,CAAC;MACxC;MACA,OAAOpM,OAAO;IAClB;IACA,MAAMsM,oBAAoB,GAAG,SAAAA,CAAUpS,IAAI,EAAE;MACzC;MACA;MACA,IAAI0R,QAAQ,CAACW,UAAU,EAAE;QACrB;MACJ;MACA,OAAOV,sBAAsB,CAACzL,IAAI,CAACwL,QAAQ,CAAC/O,MAAM,EAAE+O,QAAQ,CAAC5E,SAAS,EAAE4E,QAAQ,CAACS,OAAO,GAAGjB,8BAA8B,GAAGD,uBAAuB,EAAES,QAAQ,CAAC5L,OAAO,CAAC;IAC1K,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMwM,kBAAkB,GAAG,SAAAA,CAAUtS,IAAI,EAAE;MACvC;MACA;MACA;MACA,IAAI,CAACA,IAAI,CAACuQ,SAAS,EAAE;QACjB,MAAMgC,gBAAgB,GAAGzD,oBAAoB,CAAC9O,IAAI,CAAC8M,SAAS,CAAC;QAC7D,IAAI0F,eAAe;QACnB,IAAID,gBAAgB,EAAE;UAClBC,eAAe,GAAGD,gBAAgB,CAACvS,IAAI,CAACmS,OAAO,GAAGlI,QAAQ,GAAGC,SAAS,CAAC;QAC3E;QACA,MAAMuI,aAAa,GAAGD,eAAe,IAAIxS,IAAI,CAAC2C,MAAM,CAAC6P,eAAe,CAAC;QACrE,IAAIC,aAAa,EAAE;UACf,KAAK,IAAIpQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoQ,aAAa,CAACnQ,MAAM,EAAED,CAAC,EAAE,EAAE;YAC3C,MAAMqQ,YAAY,GAAGD,aAAa,CAACpQ,CAAC,CAAC;YACrC,IAAIqQ,YAAY,KAAK1S,IAAI,EAAE;cACvByS,aAAa,CAACE,MAAM,CAACtQ,CAAC,EAAE,CAAC,CAAC;cAC1B;cACArC,IAAI,CAACuQ,SAAS,GAAG,IAAI;cACrB,IAAIvQ,IAAI,CAAC4S,mBAAmB,EAAE;gBAC1B5S,IAAI,CAAC4S,mBAAmB,CAAC,CAAC;gBAC1B5S,IAAI,CAAC4S,mBAAmB,GAAG,IAAI;cACnC;cACA,IAAIH,aAAa,CAACnQ,MAAM,KAAK,CAAC,EAAE;gBAC5B;gBACA;gBACAtC,IAAI,CAAC6S,UAAU,GAAG,IAAI;gBACtB7S,IAAI,CAAC2C,MAAM,CAAC6P,eAAe,CAAC,GAAG,IAAI;cACvC;cACA;YACJ;UACJ;QACJ;MACJ;MACA;MACA;MACA;MACA,IAAI,CAACxS,IAAI,CAAC6S,UAAU,EAAE;QAClB;MACJ;MACA,OAAOjB,yBAAyB,CAAC1L,IAAI,CAAClG,IAAI,CAAC2C,MAAM,EAAE3C,IAAI,CAAC8M,SAAS,EAAE9M,IAAI,CAACmS,OAAO,GAAGjB,8BAA8B,GAAGD,uBAAuB,EAAEjR,IAAI,CAAC8F,OAAO,CAAC;IAC7J,CAAC;IACD,MAAMgN,uBAAuB,GAAG,SAAAA,CAAU9S,IAAI,EAAE;MAC5C,OAAO2R,sBAAsB,CAACzL,IAAI,CAACwL,QAAQ,CAAC/O,MAAM,EAAE+O,QAAQ,CAAC5E,SAAS,EAAE9M,IAAI,CAACJ,MAAM,EAAE8R,QAAQ,CAAC5L,OAAO,CAAC;IAC1G,CAAC;IACD,MAAMiN,qBAAqB,GAAG,SAAAA,CAAU/S,IAAI,EAAE;MAC1C,OAAO+R,0BAA0B,CAAC7L,IAAI,CAACwL,QAAQ,CAAC/O,MAAM,EAAE+O,QAAQ,CAAC5E,SAAS,EAAE9M,IAAI,CAACJ,MAAM,EAAE8R,QAAQ,CAAC5L,OAAO,CAAC;IAC9G,CAAC;IACD,MAAMkN,qBAAqB,GAAG,SAAAA,CAAUhT,IAAI,EAAE;MAC1C,OAAO4R,yBAAyB,CAAC1L,IAAI,CAAClG,IAAI,CAAC2C,MAAM,EAAE3C,IAAI,CAAC8M,SAAS,EAAE9M,IAAI,CAACJ,MAAM,EAAEI,IAAI,CAAC8F,OAAO,CAAC;IACjG,CAAC;IACD,MAAMlE,cAAc,GAAGwP,iBAAiB,GAAGgB,oBAAoB,GAAGU,uBAAuB;IACzF,MAAM9Q,YAAY,GAAGoP,iBAAiB,GAAGkB,kBAAkB,GAAGU,qBAAqB;IACnF,MAAMC,6BAA6B,GAAG,SAAAA,CAAUjT,IAAI,EAAEyC,QAAQ,EAAE;MAC5D,MAAMyQ,cAAc,GAAG,OAAOzQ,QAAQ;MACtC,OAASyQ,cAAc,KAAK,UAAU,IAAIlT,IAAI,CAACb,QAAQ,KAAKsD,QAAQ,IAC/DyQ,cAAc,KAAK,QAAQ,IAAIlT,IAAI,CAACyQ,gBAAgB,KAAKhO,QAAS;IAC3E,CAAC;IACD,MAAM0Q,OAAO,GAAGzD,YAAY,IAAIA,YAAY,CAAC0D,IAAI,GAAG1D,YAAY,CAAC0D,IAAI,GAAGH,6BAA6B;IACrG,MAAMI,eAAe,GAAGjJ,IAAI,CAACE,UAAU,CAAC,kBAAkB,CAAC,CAAC;IAC5D,MAAMgJ,aAAa,GAAG7I,OAAO,CAACH,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC3D,SAASiJ,wBAAwBA,CAACzN,OAAO,EAAE;MACvC,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE;QACjD;QACA;QACA;QACA,MAAM0N,UAAU,GAAG;UAAE,GAAG1N;QAAQ,CAAC;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIA,OAAO,CAAC2N,MAAM,EAAE;UAChBD,UAAU,CAACC,MAAM,GAAG3N,OAAO,CAAC2N,MAAM;QACtC;QACA,OAAOD,UAAU;MACrB;MACA,OAAO1N,OAAO;IAClB;IACA,MAAM4N,eAAe,GAAG,SAAAA,CAAUC,cAAc,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,cAAc,EAAEtC,YAAY,GAAG,KAAK,EAAEQ,OAAO,GAAG,KAAK,EAAE;MAClI,OAAO,YAAY;QACf,MAAMrP,MAAM,GAAG,IAAI,IAAI8H,OAAO;QAC9B,IAAIqC,SAAS,GAAGtN,SAAS,CAAC,CAAC,CAAC;QAC5B,IAAIkQ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;UAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAiB,CAACjH,SAAS,CAAC;QACzD;QACA,IAAIrK,QAAQ,GAAGjD,SAAS,CAAC,CAAC,CAAC;QAC3B,IAAI,CAACiD,QAAQ,EAAE;UACX,OAAOkR,cAAc,CAACvO,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;QAChD;QACA,IAAI6L,MAAM,IAAIyB,SAAS,KAAK,mBAAmB,EAAE;UAC7C;UACA,OAAO6G,cAAc,CAACvO,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;QAChD;QACA;QACA;QACA;QACA,IAAIwU,aAAa,GAAG,KAAK;QACzB,IAAI,OAAOvR,QAAQ,KAAK,UAAU,EAAE;UAChC,IAAI,CAACA,QAAQ,CAAC+N,WAAW,EAAE;YACvB,OAAOmD,cAAc,CAACvO,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;UAChD;UACAwU,aAAa,GAAG,IAAI;QACxB;QACA,IAAI3C,eAAe,IAAI,CAACA,eAAe,CAACsC,cAAc,EAAElR,QAAQ,EAAEE,MAAM,EAAEnD,SAAS,CAAC,EAAE;UAClF;QACJ;QACA,MAAM0S,OAAO,GAAGtD,gBAAgB,IAAI,CAAC,CAAC0E,aAAa,IAAIA,aAAa,CAAC3E,OAAO,CAAC7B,SAAS,CAAC,KAAK,CAAC,CAAC;QAC9F,MAAMhH,OAAO,GAAGyN,wBAAwB,CAACtB,yBAAyB,CAACzS,SAAS,CAAC,CAAC,CAAC,EAAE0S,OAAO,CAAC,CAAC;QAC1F,MAAMuB,MAAM,GAAG3N,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2N,MAAM;QAC9B,IAAIA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEQ,OAAO,EAAE;UACjB;UACA;QACJ;QACA,IAAIZ,eAAe,EAAE;UACjB;UACA,KAAK,IAAIhR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgR,eAAe,CAAC/Q,MAAM,EAAED,CAAC,EAAE,EAAE;YAC7C,IAAIyK,SAAS,KAAKuG,eAAe,CAAChR,CAAC,CAAC,EAAE;cAClC,IAAI6P,OAAO,EAAE;gBACT,OAAOyB,cAAc,CAACzN,IAAI,CAACvD,MAAM,EAAEmK,SAAS,EAAErK,QAAQ,EAAEqD,OAAO,CAAC;cACpE,CAAC,MACI;gBACD,OAAO6N,cAAc,CAACvO,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;cAChD;YACJ;UACJ;QACJ;QACA,MAAM2S,OAAO,GAAG,CAACrM,OAAO,GAAG,KAAK,GAAG,OAAOA,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO,CAACqM,OAAO;QACxF,MAAMzB,IAAI,GAAG5K,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,GAAGA,OAAO,CAAC4K,IAAI,GAAG,KAAK;QAC1E,MAAMjT,IAAI,GAAG2M,IAAI,CAAC1M,OAAO;QACzB,IAAI6U,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAS,CAAC;QACtD,IAAI,CAACyF,gBAAgB,EAAE;UACnBpD,iBAAiB,CAACrC,SAAS,EAAEsC,iBAAiB,CAAC;UAC/CmD,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAS,CAAC;QACtD;QACA,MAAM0F,eAAe,GAAGD,gBAAgB,CAACJ,OAAO,GAAGlI,QAAQ,GAAGC,SAAS,CAAC;QACxE,IAAIuI,aAAa,GAAG9P,MAAM,CAAC6P,eAAe,CAAC;QAC3C,IAAIH,UAAU,GAAG,KAAK;QACtB,IAAII,aAAa,EAAE;UACf;UACAJ,UAAU,GAAG,IAAI;UACjB,IAAIlU,cAAc,EAAE;YAChB,KAAK,IAAIkE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoQ,aAAa,CAACnQ,MAAM,EAAED,CAAC,EAAE,EAAE;cAC3C,IAAI8Q,OAAO,CAACV,aAAa,CAACpQ,CAAC,CAAC,EAAEI,QAAQ,CAAC,EAAE;gBACrC;gBACA;cACJ;YACJ;UACJ;QACJ,CAAC,MACI;UACDgQ,aAAa,GAAG9P,MAAM,CAAC6P,eAAe,CAAC,GAAG,EAAE;QAChD;QACA,IAAIpT,MAAM;QACV,MAAM8U,eAAe,GAAGvR,MAAM,CAACnE,WAAW,CAAC,MAAM,CAAC;QAClD,MAAM2V,YAAY,GAAGpF,aAAa,CAACmF,eAAe,CAAC;QACnD,IAAIC,YAAY,EAAE;UACd/U,MAAM,GAAG+U,YAAY,CAACrH,SAAS,CAAC;QACpC;QACA,IAAI,CAAC1N,MAAM,EAAE;UACTA,MAAM,GACF8U,eAAe,GACXN,SAAS,IACRxE,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAS,CAAC,GAAGA,SAAS,CAAC;QAC1E;QACA;QACA;QACA;QACA;QACA;QACA4E,QAAQ,CAAC5L,OAAO,GAAGA,OAAO;QAC1B,IAAI4K,IAAI,EAAE;UACN;UACA;UACA;UACAgB,QAAQ,CAAC5L,OAAO,CAAC4K,IAAI,GAAG,KAAK;QACjC;QACAgB,QAAQ,CAAC/O,MAAM,GAAGA,MAAM;QACxB+O,QAAQ,CAACS,OAAO,GAAGA,OAAO;QAC1BT,QAAQ,CAAC5E,SAAS,GAAGA,SAAS;QAC9B4E,QAAQ,CAACW,UAAU,GAAGA,UAAU;QAChC,MAAMxR,IAAI,GAAGuQ,iBAAiB,GAAGvC,8BAA8B,GAAG7N,SAAS;QAC3E;QACA,IAAIH,IAAI,EAAE;UACNA,IAAI,CAAC6Q,QAAQ,GAAGA,QAAQ;QAC5B;QACA,IAAI+B,MAAM,EAAE;UACR;UACA;UACA;UACA/B,QAAQ,CAAC5L,OAAO,CAAC2N,MAAM,GAAGzS,SAAS;QACvC;QACA;QACA;QACA;QACA;QACA,MAAMhB,IAAI,GAAGvC,IAAI,CAACwE,iBAAiB,CAAC7C,MAAM,EAAEqD,QAAQ,EAAE5B,IAAI,EAAEgT,gBAAgB,EAAEC,cAAc,CAAC;QAC7F,IAAIL,MAAM,EAAE;UACR;UACA/B,QAAQ,CAAC5L,OAAO,CAAC2N,MAAM,GAAGA,MAAM;UAChC;UACA;UACA;UACA,MAAMW,OAAO,GAAGA,CAAA,KAAMpU,IAAI,CAACvC,IAAI,CAACyE,UAAU,CAAClC,IAAI,CAAC;UAChD2T,cAAc,CAACzN,IAAI,CAACuN,MAAM,EAAE,OAAO,EAAEW,OAAO,EAAE;YAAE1D,IAAI,EAAE;UAAK,CAAC,CAAC;UAC7D;UACA;UACA;UACA;UACA1Q,IAAI,CAAC4S,mBAAmB,GAAG,MAAMa,MAAM,CAACxG,mBAAmB,CAAC,OAAO,EAAEmH,OAAO,CAAC;QACjF;QACA;QACA;QACA1C,QAAQ,CAAC/O,MAAM,GAAG,IAAI;QACtB;QACA,IAAI9B,IAAI,EAAE;UACNA,IAAI,CAAC6Q,QAAQ,GAAG,IAAI;QACxB;QACA;QACA;QACA,IAAIhB,IAAI,EAAE;UACNgB,QAAQ,CAAC5L,OAAO,CAAC4K,IAAI,GAAG,IAAI;QAChC;QACA,IAAI,EAAE,CAAC9B,gBAAgB,IAAI,OAAO5O,IAAI,CAAC8F,OAAO,KAAK,SAAS,CAAC,EAAE;UAC3D;UACA;UACA9F,IAAI,CAAC8F,OAAO,GAAGA,OAAO;QAC1B;QACA9F,IAAI,CAAC2C,MAAM,GAAGA,MAAM;QACpB3C,IAAI,CAACmS,OAAO,GAAGA,OAAO;QACtBnS,IAAI,CAAC8M,SAAS,GAAGA,SAAS;QAC1B,IAAIkH,aAAa,EAAE;UACf;UACAhU,IAAI,CAACyQ,gBAAgB,GAAGhO,QAAQ;QACpC;QACA,IAAI,CAACuP,OAAO,EAAE;UACVS,aAAa,CAACnN,IAAI,CAACtF,IAAI,CAAC;QAC5B,CAAC,MACI;UACDyS,aAAa,CAAC4B,OAAO,CAACrU,IAAI,CAAC;QAC/B;QACA,IAAIwR,YAAY,EAAE;UACd,OAAO7O,MAAM;QACjB;MACJ,CAAC;IACL,CAAC;IACDiL,KAAK,CAAC+B,kBAAkB,CAAC,GAAG+D,eAAe,CAAC/B,sBAAsB,EAAEvB,yBAAyB,EAAExO,cAAc,EAAEI,YAAY,EAAEwP,YAAY,CAAC;IAC1I,IAAIO,0BAA0B,EAAE;MAC5BnE,KAAK,CAACyC,sBAAsB,CAAC,GAAGqD,eAAe,CAAC3B,0BAA0B,EAAEzB,6BAA6B,EAAEyC,qBAAqB,EAAE/Q,YAAY,EAAEwP,YAAY,EAAE,IAAI,CAAC;IACvK;IACA5D,KAAK,CAACiC,qBAAqB,CAAC,GAAG,YAAY;MACvC,MAAMlN,MAAM,GAAG,IAAI,IAAI8H,OAAO;MAC9B,IAAIqC,SAAS,GAAGtN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAIkQ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;QAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAiB,CAACjH,SAAS,CAAC;MACzD;MACA,MAAMhH,OAAO,GAAGtG,SAAS,CAAC,CAAC,CAAC;MAC5B,MAAM2S,OAAO,GAAG,CAACrM,OAAO,GAAG,KAAK,GAAG,OAAOA,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO,CAACqM,OAAO;MACxF,MAAM1P,QAAQ,GAAGjD,SAAS,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACiD,QAAQ,EAAE;QACX,OAAOmP,yBAAyB,CAACxM,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;MAC3D;MACA,IAAI6R,eAAe,IACf,CAACA,eAAe,CAACO,yBAAyB,EAAEnP,QAAQ,EAAEE,MAAM,EAAEnD,SAAS,CAAC,EAAE;QAC1E;MACJ;MACA,MAAM+S,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAS,CAAC;MACxD,IAAI0F,eAAe;MACnB,IAAID,gBAAgB,EAAE;QAClBC,eAAe,GAAGD,gBAAgB,CAACJ,OAAO,GAAGlI,QAAQ,GAAGC,SAAS,CAAC;MACtE;MACA,MAAMuI,aAAa,GAAGD,eAAe,IAAI7P,MAAM,CAAC6P,eAAe,CAAC;MAChE;MACA;MACA;MACA;MACA,IAAIC,aAAa,EAAE;QACf,KAAK,IAAIpQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoQ,aAAa,CAACnQ,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3C,MAAMqQ,YAAY,GAAGD,aAAa,CAACpQ,CAAC,CAAC;UACrC,IAAI8Q,OAAO,CAACT,YAAY,EAAEjQ,QAAQ,CAAC,EAAE;YACjCgQ,aAAa,CAACE,MAAM,CAACtQ,CAAC,EAAE,CAAC,CAAC;YAC1B;YACAqQ,YAAY,CAACnC,SAAS,GAAG,IAAI;YAC7B,IAAIkC,aAAa,CAACnQ,MAAM,KAAK,CAAC,EAAE;cAC5B;cACA;cACAoQ,YAAY,CAACG,UAAU,GAAG,IAAI;cAC9BlQ,MAAM,CAAC6P,eAAe,CAAC,GAAG,IAAI;cAC9B;cACA;cACA;cACA;cACA;cACA,IAAI,CAACL,OAAO,IAAI,OAAOrF,SAAS,KAAK,QAAQ,EAAE;gBAC3C,MAAMwH,gBAAgB,GAAGnK,kBAAkB,GAAG,aAAa,GAAG2C,SAAS;gBACvEnK,MAAM,CAAC2R,gBAAgB,CAAC,GAAG,IAAI;cACnC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA5B,YAAY,CAACjV,IAAI,CAACyE,UAAU,CAACwQ,YAAY,CAAC;YAC1C,IAAIlB,YAAY,EAAE;cACd,OAAO7O,MAAM;YACjB;YACA;UACJ;QACJ;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAOiP,yBAAyB,CAACxM,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;IAC3D,CAAC;IACDoO,KAAK,CAACmC,wBAAwB,CAAC,GAAG,YAAY;MAC1C,MAAMpN,MAAM,GAAG,IAAI,IAAI8H,OAAO;MAC9B,IAAIqC,SAAS,GAAGtN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAIkQ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;QAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAiB,CAACjH,SAAS,CAAC;MACzD;MACA,MAAMkD,SAAS,GAAG,EAAE;MACpB,MAAMc,KAAK,GAAGyD,cAAc,CAAC5R,MAAM,EAAEyM,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAS,CAAC,GAAGA,SAAS,CAAC;MAClG,KAAK,IAAIzK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyO,KAAK,CAACxO,MAAM,EAAED,CAAC,EAAE,EAAE;QACnC,MAAMrC,IAAI,GAAG8Q,KAAK,CAACzO,CAAC,CAAC;QACrB,IAAII,QAAQ,GAAGzC,IAAI,CAACyQ,gBAAgB,GAAGzQ,IAAI,CAACyQ,gBAAgB,GAAGzQ,IAAI,CAACb,QAAQ;QAC5E6Q,SAAS,CAAC1K,IAAI,CAAC7C,QAAQ,CAAC;MAC5B;MACA,OAAOuN,SAAS;IACpB,CAAC;IACDpC,KAAK,CAACqC,mCAAmC,CAAC,GAAG,YAAY;MACrD,MAAMtN,MAAM,GAAG,IAAI,IAAI8H,OAAO;MAC9B,IAAIqC,SAAS,GAAGtN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAI,CAACsN,SAAS,EAAE;QACZ,MAAM0H,IAAI,GAAG5N,MAAM,CAAC4N,IAAI,CAAC7R,MAAM,CAAC;QAChC,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmS,IAAI,CAAClS,MAAM,EAAED,CAAC,EAAE,EAAE;UAClC,MAAMkK,IAAI,GAAGiI,IAAI,CAACnS,CAAC,CAAC;UACpB,MAAMoS,KAAK,GAAGzF,sBAAsB,CAAC0F,IAAI,CAACnI,IAAI,CAAC;UAC/C,IAAIoI,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;UAC/B;UACA;UACA;UACA;UACA,IAAIE,OAAO,IAAIA,OAAO,KAAK,gBAAgB,EAAE;YACzC,IAAI,CAAC1E,mCAAmC,CAAC,CAAC/J,IAAI,CAAC,IAAI,EAAEyO,OAAO,CAAC;UACjE;QACJ;QACA;QACA,IAAI,CAAC1E,mCAAmC,CAAC,CAAC/J,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC;MAC1E,CAAC,MACI;QACD,IAAIwJ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;UAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAiB,CAACjH,SAAS,CAAC;QACzD;QACA,MAAMyF,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAS,CAAC;QACxD,IAAIyF,gBAAgB,EAAE;UAClB,MAAMC,eAAe,GAAGD,gBAAgB,CAACrI,SAAS,CAAC;UACnD,MAAM0K,sBAAsB,GAAGrC,gBAAgB,CAACtI,QAAQ,CAAC;UACzD,MAAM6G,KAAK,GAAGnO,MAAM,CAAC6P,eAAe,CAAC;UACrC,MAAMqC,YAAY,GAAGlS,MAAM,CAACiS,sBAAsB,CAAC;UACnD,IAAI9D,KAAK,EAAE;YACP,MAAMgE,WAAW,GAAGhE,KAAK,CAAClH,KAAK,CAAC,CAAC;YACjC,KAAK,IAAIvH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyS,WAAW,CAACxS,MAAM,EAAED,CAAC,EAAE,EAAE;cACzC,MAAMrC,IAAI,GAAG8U,WAAW,CAACzS,CAAC,CAAC;cAC3B,IAAII,QAAQ,GAAGzC,IAAI,CAACyQ,gBAAgB,GAAGzQ,IAAI,CAACyQ,gBAAgB,GAAGzQ,IAAI,CAACb,QAAQ;cAC5E,IAAI,CAAC0Q,qBAAqB,CAAC,CAAC3J,IAAI,CAAC,IAAI,EAAE4G,SAAS,EAAErK,QAAQ,EAAEzC,IAAI,CAAC8F,OAAO,CAAC;YAC7E;UACJ;UACA,IAAI+O,YAAY,EAAE;YACd,MAAMC,WAAW,GAAGD,YAAY,CAACjL,KAAK,CAAC,CAAC;YACxC,KAAK,IAAIvH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyS,WAAW,CAACxS,MAAM,EAAED,CAAC,EAAE,EAAE;cACzC,MAAMrC,IAAI,GAAG8U,WAAW,CAACzS,CAAC,CAAC;cAC3B,IAAII,QAAQ,GAAGzC,IAAI,CAACyQ,gBAAgB,GAAGzQ,IAAI,CAACyQ,gBAAgB,GAAGzQ,IAAI,CAACb,QAAQ;cAC5E,IAAI,CAAC0Q,qBAAqB,CAAC,CAAC3J,IAAI,CAAC,IAAI,EAAE4G,SAAS,EAAErK,QAAQ,EAAEzC,IAAI,CAAC8F,OAAO,CAAC;YAC7E;UACJ;QACJ;MACJ;MACA,IAAI0L,YAAY,EAAE;QACd,OAAO,IAAI;MACf;IACJ,CAAC;IACD;IACAxI,qBAAqB,CAAC4E,KAAK,CAAC+B,kBAAkB,CAAC,EAAEgC,sBAAsB,CAAC;IACxE3I,qBAAqB,CAAC4E,KAAK,CAACiC,qBAAqB,CAAC,EAAE+B,yBAAyB,CAAC;IAC9E,IAAIE,wBAAwB,EAAE;MAC1B9I,qBAAqB,CAAC4E,KAAK,CAACqC,mCAAmC,CAAC,EAAE6B,wBAAwB,CAAC;IAC/F;IACA,IAAID,eAAe,EAAE;MACjB7I,qBAAqB,CAAC4E,KAAK,CAACmC,wBAAwB,CAAC,EAAE8B,eAAe,CAAC;IAC3E;IACA,OAAO,IAAI;EACf;EACA,IAAIkD,OAAO,GAAG,EAAE;EAChB,KAAK,IAAI1S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoN,IAAI,CAACnN,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC0S,OAAO,CAAC1S,CAAC,CAAC,GAAG8O,uBAAuB,CAAC1B,IAAI,CAACpN,CAAC,CAAC,EAAEqN,YAAY,CAAC;EAC/D;EACA,OAAOqF,OAAO;AAClB;AACA,SAASR,cAAcA,CAAC5R,MAAM,EAAEmK,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,EAAE;IACZ,MAAMkI,UAAU,GAAG,EAAE;IACrB,KAAK,IAAIzI,IAAI,IAAI5J,MAAM,EAAE;MACrB,MAAM8R,KAAK,GAAGzF,sBAAsB,CAAC0F,IAAI,CAACnI,IAAI,CAAC;MAC/C,IAAIoI,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;MAC/B,IAAIE,OAAO,KAAK,CAAC7H,SAAS,IAAI6H,OAAO,KAAK7H,SAAS,CAAC,EAAE;QAClD,MAAMgE,KAAK,GAAGnO,MAAM,CAAC4J,IAAI,CAAC;QAC1B,IAAIuE,KAAK,EAAE;UACP,KAAK,IAAIzO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyO,KAAK,CAACxO,MAAM,EAAED,CAAC,EAAE,EAAE;YACnC2S,UAAU,CAAC1P,IAAI,CAACwL,KAAK,CAACzO,CAAC,CAAC,CAAC;UAC7B;QACJ;MACJ;IACJ;IACA,OAAO2S,UAAU;EACrB;EACA,IAAIxC,eAAe,GAAG1D,oBAAoB,CAAChC,SAAS,CAAC;EACrD,IAAI,CAAC0F,eAAe,EAAE;IAClBrD,iBAAiB,CAACrC,SAAS,CAAC;IAC5B0F,eAAe,GAAG1D,oBAAoB,CAAChC,SAAS,CAAC;EACrD;EACA,MAAMmI,iBAAiB,GAAGtS,MAAM,CAAC6P,eAAe,CAACtI,SAAS,CAAC,CAAC;EAC5D,MAAMgL,gBAAgB,GAAGvS,MAAM,CAAC6P,eAAe,CAACvI,QAAQ,CAAC,CAAC;EAC1D,IAAI,CAACgL,iBAAiB,EAAE;IACpB,OAAOC,gBAAgB,GAAGA,gBAAgB,CAACtL,KAAK,CAAC,CAAC,GAAG,EAAE;EAC3D,CAAC,MACI;IACD,OAAOsL,gBAAgB,GACjBD,iBAAiB,CAACE,MAAM,CAACD,gBAAgB,CAAC,GAC1CD,iBAAiB,CAACrL,KAAK,CAAC,CAAC;EACnC;AACJ;AACA,SAAStB,mBAAmBA,CAAC7L,MAAM,EAAE+S,GAAG,EAAE;EACtC,MAAM4F,KAAK,GAAG3Y,MAAM,CAAC,OAAO,CAAC;EAC7B,IAAI2Y,KAAK,IAAIA,KAAK,CAACvO,SAAS,EAAE;IAC1B2I,GAAG,CAACtH,WAAW,CAACkN,KAAK,CAACvO,SAAS,EAAE,0BAA0B,EAAGpE,QAAQ,IAAK,UAAUuD,IAAI,EAAEG,IAAI,EAAE;MAC7FH,IAAI,CAACkJ,4BAA4B,CAAC,GAAG,IAAI;MACzC;MACA;MACA;MACAzM,QAAQ,IAAIA,QAAQ,CAAC2C,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;IAC1C,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASkP,mBAAmBA,CAAC5Y,MAAM,EAAE+S,GAAG,EAAE;EACtCA,GAAG,CAACtH,WAAW,CAACzL,MAAM,EAAE,gBAAgB,EAAGgG,QAAQ,IAAK;IACpD,OAAO,UAAUuD,IAAI,EAAEG,IAAI,EAAE;MACzBiE,IAAI,CAAC1M,OAAO,CAACiE,iBAAiB,CAAC,gBAAgB,EAAEwE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;EACL,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA,MAAMmP,UAAU,GAAGhL,UAAU,CAAC,UAAU,CAAC;AACzC,SAASiL,UAAUA,CAAChZ,MAAM,EAAEiZ,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACzD,IAAIzH,SAAS,GAAG,IAAI;EACpB,IAAI0H,WAAW,GAAG,IAAI;EACtBH,OAAO,IAAIE,UAAU;EACrBD,UAAU,IAAIC,UAAU;EACxB,MAAME,eAAe,GAAG,CAAC,CAAC;EAC1B,SAASxU,YAAYA,CAACpB,IAAI,EAAE;IACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;IACtBA,IAAI,CAACsF,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY;MACvB,OAAOnG,IAAI,CAACJ,MAAM,CAACwF,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;IAC7C,CAAC;IACDqB,IAAI,CAAC8F,QAAQ,GAAGsH,SAAS,CAAC7I,KAAK,CAAC7I,MAAM,EAAEsE,IAAI,CAACsF,IAAI,CAAC;IAClD,OAAOnG,IAAI;EACf;EACA,SAAS6V,SAASA,CAAC7V,IAAI,EAAE;IACrB,OAAO2V,WAAW,CAACzP,IAAI,CAAC3J,MAAM,EAAEyD,IAAI,CAACa,IAAI,CAAC8F,QAAQ,CAAC;EACvD;EACAsH,SAAS,GAAG/F,WAAW,CAAC3L,MAAM,EAAEiZ,OAAO,EAAG/S,QAAQ,IAAK,UAAUuD,IAAI,EAAEG,IAAI,EAAE;IACzE,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;MAC/B,MAAML,OAAO,GAAG;QACZhF,UAAU,EAAE4U,UAAU,KAAK,UAAU;QACrCI,KAAK,EAAEJ,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,UAAU,GAAGvP,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGnF,SAAS;QACvFmF,IAAI,EAAEA;MACV,CAAC;MACD,MAAMhH,QAAQ,GAAGgH,IAAI,CAAC,CAAC,CAAC;MACxBA,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS4P,KAAKA,CAAA,EAAG;QACvB,IAAI;UACA,OAAO5W,QAAQ,CAACiG,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;QAC1C,CAAC,SACO;UACJ;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAACsG,OAAO,CAAChF,UAAU,EAAE;YACrB,IAAI,OAAOgF,OAAO,CAACa,QAAQ,KAAK,QAAQ,EAAE;cACtC;cACA;cACA,OAAOiP,eAAe,CAAC9P,OAAO,CAACa,QAAQ,CAAC;YAC5C,CAAC,MACI,IAAIb,OAAO,CAACa,QAAQ,EAAE;cACvB;cACA;cACAb,OAAO,CAACa,QAAQ,CAAC2O,UAAU,CAAC,GAAG,IAAI;YACvC;UACJ;QACJ;MACJ,CAAC;MACD,MAAMtV,IAAI,GAAGqK,gCAAgC,CAACmL,OAAO,EAAErP,IAAI,CAAC,CAAC,CAAC,EAAEL,OAAO,EAAE1E,YAAY,EAAEyU,SAAS,CAAC;MACjG,IAAI,CAAC7V,IAAI,EAAE;QACP,OAAOA,IAAI;MACf;MACA;MACA,MAAMgW,MAAM,GAAGhW,IAAI,CAACa,IAAI,CAAC8F,QAAQ;MACjC,IAAI,OAAOqP,MAAM,KAAK,QAAQ,EAAE;QAC5B;QACA;QACAJ,eAAe,CAACI,MAAM,CAAC,GAAGhW,IAAI;MAClC,CAAC,MACI,IAAIgW,MAAM,EAAE;QACb;QACA;QACAA,MAAM,CAACV,UAAU,CAAC,GAAGtV,IAAI;MAC7B;MACA;MACA;MACA,IAAIgW,MAAM,IACNA,MAAM,CAACC,GAAG,IACVD,MAAM,CAACE,KAAK,IACZ,OAAOF,MAAM,CAACC,GAAG,KAAK,UAAU,IAChC,OAAOD,MAAM,CAACE,KAAK,KAAK,UAAU,EAAE;QACpClW,IAAI,CAACiW,GAAG,GAAGD,MAAM,CAACC,GAAG,CAACE,IAAI,CAACH,MAAM,CAAC;QAClChW,IAAI,CAACkW,KAAK,GAAGF,MAAM,CAACE,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;MAC1C;MACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,EAAE;QACtC,OAAOA,MAAM;MACjB;MACA,OAAOhW,IAAI;IACf,CAAC,MACI;MACD;MACA,OAAOyC,QAAQ,CAAC2C,KAAK,CAAC7I,MAAM,EAAE4J,IAAI,CAAC;IACvC;EACJ,CAAC,CAAC;EACFwP,WAAW,GAAGzN,WAAW,CAAC3L,MAAM,EAAEkZ,UAAU,EAAGhT,QAAQ,IAAK,UAAUuD,IAAI,EAAEG,IAAI,EAAE;IAC9E,MAAMiQ,EAAE,GAAGjQ,IAAI,CAAC,CAAC,CAAC;IAClB,IAAInG,IAAI;IACR,IAAI,OAAOoW,EAAE,KAAK,QAAQ,EAAE;MACxB;MACApW,IAAI,GAAG4V,eAAe,CAACQ,EAAE,CAAC;IAC9B,CAAC,MACI;MACD;MACApW,IAAI,GAAGoW,EAAE,IAAIA,EAAE,CAACd,UAAU,CAAC;MAC3B;MACA,IAAI,CAACtV,IAAI,EAAE;QACPA,IAAI,GAAGoW,EAAE;MACb;IACJ;IACA,IAAIpW,IAAI,IAAI,OAAOA,IAAI,CAACI,IAAI,KAAK,QAAQ,EAAE;MACvC,IAAIJ,IAAI,CAACE,KAAK,KAAK,cAAc,KAC3BF,IAAI,CAACe,QAAQ,IAAIf,IAAI,CAACa,IAAI,CAACC,UAAU,IAAKd,IAAI,CAACW,QAAQ,KAAK,CAAC,CAAC,EAAE;QAClE,IAAI,OAAOyV,EAAE,KAAK,QAAQ,EAAE;UACxB,OAAOR,eAAe,CAACQ,EAAE,CAAC;QAC9B,CAAC,MACI,IAAIA,EAAE,EAAE;UACTA,EAAE,CAACd,UAAU,CAAC,GAAG,IAAI;QACzB;QACA;QACAtV,IAAI,CAACvC,IAAI,CAACyE,UAAU,CAAClC,IAAI,CAAC;MAC9B;IACJ,CAAC,MACI;MACD;MACAyC,QAAQ,CAAC2C,KAAK,CAAC7I,MAAM,EAAE4J,IAAI,CAAC;IAChC;EACJ,CAAC,CAAC;AACN;AAEA,SAASkQ,mBAAmBA,CAAC5L,OAAO,EAAE+E,GAAG,EAAE;EACvC,MAAM;IAAEjE,SAAS;IAAEC;EAAM,CAAC,GAAGgE,GAAG,CAAChH,gBAAgB,CAAC,CAAC;EACnD,IAAK,CAAC+C,SAAS,IAAI,CAACC,KAAK,IAAK,CAACf,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,gBAAgB,IAAIA,OAAO,CAAC,EAAE;IACxF;EACJ;EACA;EACA,MAAM6L,SAAS,GAAG,CACd,mBAAmB,EACnB,sBAAsB,EACtB,iBAAiB,EACjB,0BAA0B,EAC1B,wBAAwB,EACxB,sBAAsB,EACtB,mBAAmB,EACnB,0BAA0B,CAC7B;EACD9G,GAAG,CAACtG,cAAc,CAACsG,GAAG,EAAE/E,OAAO,CAAC8L,cAAc,EAAE,gBAAgB,EAAE,QAAQ,EAAED,SAAS,CAAC;AAC1F;AAEA,SAASE,gBAAgBA,CAAC/L,OAAO,EAAE+E,GAAG,EAAE;EACpC,IAAIpF,IAAI,CAACoF,GAAG,CAAC5H,MAAM,CAAC,kBAAkB,CAAC,CAAC,EAAE;IACtC;IACA;EACJ;EACA,MAAM;IAAE6O,UAAU;IAAE3H,oBAAoB;IAAE7E,QAAQ;IAAEC,SAAS;IAAEC;EAAmB,CAAC,GAAGqF,GAAG,CAAChH,gBAAgB,CAAC,CAAC;EAC5G;EACA,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoU,UAAU,CAACnU,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,MAAMyK,SAAS,GAAG2J,UAAU,CAACpU,CAAC,CAAC;IAC/B,MAAMgN,cAAc,GAAGvC,SAAS,GAAG5C,SAAS;IAC5C,MAAMoF,aAAa,GAAGxC,SAAS,GAAG7C,QAAQ;IAC1C,MAAMrC,MAAM,GAAGuC,kBAAkB,GAAGkF,cAAc;IAClD,MAAME,aAAa,GAAGpF,kBAAkB,GAAGmF,aAAa;IACxDR,oBAAoB,CAAChC,SAAS,CAAC,GAAG,CAAC,CAAC;IACpCgC,oBAAoB,CAAChC,SAAS,CAAC,CAAC5C,SAAS,CAAC,GAAGtC,MAAM;IACnDkH,oBAAoB,CAAChC,SAAS,CAAC,CAAC7C,QAAQ,CAAC,GAAGsF,aAAa;EAC7D;EACA,MAAMmH,YAAY,GAAGjM,OAAO,CAAC,aAAa,CAAC;EAC3C,IAAI,CAACiM,YAAY,IAAI,CAACA,YAAY,CAAC7P,SAAS,EAAE;IAC1C;EACJ;EACA2I,GAAG,CAACxH,gBAAgB,CAACyC,OAAO,EAAE+E,GAAG,EAAE,CAACkH,YAAY,IAAIA,YAAY,CAAC7P,SAAS,CAAC,CAAC;EAC5E,OAAO,IAAI;AACf;AACA,SAAS8P,UAAUA,CAACla,MAAM,EAAE+S,GAAG,EAAE;EAC7BA,GAAG,CAAClH,mBAAmB,CAAC7L,MAAM,EAAE+S,GAAG,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA,SAASzG,gBAAgBA,CAACpG,MAAM,EAAEyK,YAAY,EAAEwJ,gBAAgB,EAAE;EAC9D,IAAI,CAACA,gBAAgB,IAAIA,gBAAgB,CAACtU,MAAM,KAAK,CAAC,EAAE;IACpD,OAAO8K,YAAY;EACvB;EACA,MAAMyJ,GAAG,GAAGD,gBAAgB,CAACE,MAAM,CAAEC,EAAE,IAAKA,EAAE,CAACpU,MAAM,KAAKA,MAAM,CAAC;EACjE,IAAI,CAACkU,GAAG,IAAIA,GAAG,CAACvU,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAO8K,YAAY;EACvB;EACA,MAAM4J,sBAAsB,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACD,gBAAgB;EACtD,OAAOxJ,YAAY,CAAC0J,MAAM,CAAEG,EAAE,IAAKD,sBAAsB,CAACrI,OAAO,CAACsI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACjF;AACA,SAASC,uBAAuBA,CAACvU,MAAM,EAAEyK,YAAY,EAAEwJ,gBAAgB,EAAE/P,SAAS,EAAE;EAChF;EACA;EACA,IAAI,CAAClE,MAAM,EAAE;IACT;EACJ;EACA,MAAMwU,kBAAkB,GAAGpO,gBAAgB,CAACpG,MAAM,EAAEyK,YAAY,EAAEwJ,gBAAgB,CAAC;EACnF3O,iBAAiB,CAACtF,MAAM,EAAEwU,kBAAkB,EAAEtQ,SAAS,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA,SAASuQ,eAAeA,CAACzU,MAAM,EAAE;EAC7B,OAAOiE,MAAM,CAACyQ,mBAAmB,CAAC1U,MAAM,CAAC,CACpCmU,MAAM,CAAEla,IAAI,IAAKA,IAAI,CAAC0a,UAAU,CAAC,IAAI,CAAC,IAAI1a,IAAI,CAAC0F,MAAM,GAAG,CAAC,CAAC,CAC1DiV,GAAG,CAAE3a,IAAI,IAAKA,IAAI,CAAC4a,SAAS,CAAC,CAAC,CAAC,CAAC;AACzC;AACA,SAASC,uBAAuBA,CAACjI,GAAG,EAAE/E,OAAO,EAAE;EAC3C,IAAIY,MAAM,IAAI,CAACG,KAAK,EAAE;IAClB;EACJ;EACA,IAAIpB,IAAI,CAACoF,GAAG,CAAC5H,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE;IACjC;IACA;EACJ;EACA,MAAMgP,gBAAgB,GAAGnM,OAAO,CAAC,6BAA6B,CAAC;EAC/D;EACA,IAAIiN,YAAY,GAAG,EAAE;EACrB,IAAInM,SAAS,EAAE;IACX,MAAMf,cAAc,GAAGjO,MAAM;IAC7Bmb,YAAY,GAAGA,YAAY,CAACvC,MAAM,CAAC,CAC/B,UAAU,EACV,YAAY,EACZ,SAAS,EACT,aAAa,EACb,iBAAiB,EACjB,kBAAkB,EAClB,qBAAqB,EACrB,kBAAkB,EAClB,mBAAmB,EACnB,oBAAoB,EACpB,QAAQ,CACX,CAAC;IACF,MAAMwC,qBAAqB,GAAGpJ,IAAI,CAAC,CAAC,GAC9B,CAAC;MAAE5L,MAAM,EAAE6H,cAAc;MAAEoM,gBAAgB,EAAE,CAAC,OAAO;IAAE,CAAC,CAAC,GACzD,EAAE;IACR;IACA;IACAM,uBAAuB,CAAC1M,cAAc,EAAE4M,eAAe,CAAC5M,cAAc,CAAC,EAAEoM,gBAAgB,GAAGA,gBAAgB,CAACzB,MAAM,CAACwC,qBAAqB,CAAC,GAAGf,gBAAgB,EAAEpN,oBAAoB,CAACgB,cAAc,CAAC,CAAC;EACxM;EACAkN,YAAY,GAAGA,YAAY,CAACvC,MAAM,CAAC,CAC/B,gBAAgB,EAChB,2BAA2B,EAC3B,UAAU,EACV,YAAY,EACZ,kBAAkB,EAClB,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,WAAW,CACd,CAAC;EACF,KAAK,IAAI9S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqV,YAAY,CAACpV,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,MAAMM,MAAM,GAAG8H,OAAO,CAACiN,YAAY,CAACrV,CAAC,CAAC,CAAC;IACvCM,MAAM,IACFA,MAAM,CAACkE,SAAS,IAChBqQ,uBAAuB,CAACvU,MAAM,CAACkE,SAAS,EAAEuQ,eAAe,CAACzU,MAAM,CAACkE,SAAS,CAAC,EAAE+P,gBAAgB,CAAC;EACtG;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASgB,YAAYA,CAACxN,IAAI,EAAE;EACxBA,IAAI,CAACrM,YAAY,CAAC,QAAQ,EAAGtB,MAAM,IAAK;IACpC,MAAMob,WAAW,GAAGpb,MAAM,CAAC2N,IAAI,CAACzN,UAAU,CAAC,aAAa,CAAC,CAAC;IAC1D,IAAIkb,WAAW,EAAE;MACbA,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,CAAC;EACFzN,IAAI,CAACrM,YAAY,CAAC,QAAQ,EAAGtB,MAAM,IAAK;IACpC,MAAMyO,GAAG,GAAG,KAAK;IACjB,MAAM4M,KAAK,GAAG,OAAO;IACrBvC,UAAU,CAAC9Y,MAAM,EAAEyO,GAAG,EAAE4M,KAAK,EAAE,SAAS,CAAC;IACzCvC,UAAU,CAAC9Y,MAAM,EAAEyO,GAAG,EAAE4M,KAAK,EAAE,UAAU,CAAC;IAC1CvC,UAAU,CAAC9Y,MAAM,EAAEyO,GAAG,EAAE4M,KAAK,EAAE,WAAW,CAAC;EAC/C,CAAC,CAAC;EACF1N,IAAI,CAACrM,YAAY,CAAC,uBAAuB,EAAGtB,MAAM,IAAK;IACnD8Y,UAAU,CAAC9Y,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,CAAC;IACzD8Y,UAAU,CAAC9Y,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,gBAAgB,CAAC;IAC/D8Y,UAAU,CAAC9Y,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,gBAAgB,CAAC;EACzE,CAAC,CAAC;EACF2N,IAAI,CAACrM,YAAY,CAAC,UAAU,EAAE,CAACtB,MAAM,EAAE2N,IAAI,KAAK;IAC5C,MAAM2N,eAAe,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;IACtD,KAAK,IAAI1V,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0V,eAAe,CAACzV,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMzF,IAAI,GAAGmb,eAAe,CAAC1V,CAAC,CAAC;MAC/B6F,WAAW,CAACzL,MAAM,EAAEG,IAAI,EAAE,CAAC6F,QAAQ,EAAEmF,MAAM,EAAEhL,IAAI,KAAK;QAClD,OAAO,UAAUob,CAAC,EAAE7R,IAAI,EAAE;UACtB,OAAOiE,IAAI,CAAC1M,OAAO,CAAC+B,GAAG,CAACgD,QAAQ,EAAEhG,MAAM,EAAE0J,IAAI,EAAEvJ,IAAI,CAAC;QACzD,CAAC;MACL,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACFwN,IAAI,CAACrM,YAAY,CAAC,aAAa,EAAE,CAACtB,MAAM,EAAE2N,IAAI,EAAEoF,GAAG,KAAK;IACpDmH,UAAU,CAACla,MAAM,EAAE+S,GAAG,CAAC;IACvBgH,gBAAgB,CAAC/Z,MAAM,EAAE+S,GAAG,CAAC;IAC7B;IACA,MAAMyI,yBAAyB,GAAGxb,MAAM,CAAC,2BAA2B,CAAC;IACrE,IAAIwb,yBAAyB,IAAIA,yBAAyB,CAACpR,SAAS,EAAE;MAClE2I,GAAG,CAACxH,gBAAgB,CAACvL,MAAM,EAAE+S,GAAG,EAAE,CAACyI,yBAAyB,CAACpR,SAAS,CAAC,CAAC;IAC5E;EACJ,CAAC,CAAC;EACFuD,IAAI,CAACrM,YAAY,CAAC,kBAAkB,EAAE,CAACtB,MAAM,EAAE2N,IAAI,EAAEoF,GAAG,KAAK;IACzD3G,UAAU,CAAC,kBAAkB,CAAC;IAC9BA,UAAU,CAAC,wBAAwB,CAAC;EACxC,CAAC,CAAC;EACFuB,IAAI,CAACrM,YAAY,CAAC,sBAAsB,EAAE,CAACtB,MAAM,EAAE2N,IAAI,EAAEoF,GAAG,KAAK;IAC7D3G,UAAU,CAAC,sBAAsB,CAAC;EACtC,CAAC,CAAC;EACFuB,IAAI,CAACrM,YAAY,CAAC,YAAY,EAAE,CAACtB,MAAM,EAAE2N,IAAI,EAAEoF,GAAG,KAAK;IACnD3G,UAAU,CAAC,YAAY,CAAC;EAC5B,CAAC,CAAC;EACFuB,IAAI,CAACrM,YAAY,CAAC,aAAa,EAAE,CAACtB,MAAM,EAAE2N,IAAI,EAAEoF,GAAG,KAAK;IACpDiI,uBAAuB,CAACjI,GAAG,EAAE/S,MAAM,CAAC;EACxC,CAAC,CAAC;EACF2N,IAAI,CAACrM,YAAY,CAAC,gBAAgB,EAAE,CAACtB,MAAM,EAAE2N,IAAI,EAAEoF,GAAG,KAAK;IACvD6G,mBAAmB,CAAC5Z,MAAM,EAAE+S,GAAG,CAAC;EACpC,CAAC,CAAC;EACFpF,IAAI,CAACrM,YAAY,CAAC,KAAK,EAAE,CAACtB,MAAM,EAAE2N,IAAI,KAAK;IACvC;IACA8N,QAAQ,CAACzb,MAAM,CAAC;IAChB,MAAM0b,QAAQ,GAAG7N,UAAU,CAAC,SAAS,CAAC;IACtC,MAAM8N,QAAQ,GAAG9N,UAAU,CAAC,SAAS,CAAC;IACtC,MAAM+N,YAAY,GAAG/N,UAAU,CAAC,aAAa,CAAC;IAC9C,MAAMgO,aAAa,GAAGhO,UAAU,CAAC,cAAc,CAAC;IAChD,MAAMiO,OAAO,GAAGjO,UAAU,CAAC,QAAQ,CAAC;IACpC,MAAMkO,0BAA0B,GAAGlO,UAAU,CAAC,yBAAyB,CAAC;IACxE,SAAS4N,QAAQA,CAAC3b,MAAM,EAAE;MACtB,MAAMkc,cAAc,GAAGlc,MAAM,CAAC,gBAAgB,CAAC;MAC/C,IAAI,CAACkc,cAAc,EAAE;QACjB;QACA;MACJ;MACA,MAAMC,uBAAuB,GAAGD,cAAc,CAAC5R,SAAS;MACxD,SAAS8R,eAAeA,CAAChW,MAAM,EAAE;QAC7B,OAAOA,MAAM,CAACwV,QAAQ,CAAC;MAC3B;MACA,IAAIS,cAAc,GAAGF,uBAAuB,CAAC3O,8BAA8B,CAAC;MAC5E,IAAI8O,iBAAiB,GAAGH,uBAAuB,CAAC1O,iCAAiC,CAAC;MAClF,IAAI,CAAC4O,cAAc,EAAE;QACjB,MAAMX,yBAAyB,GAAG1b,MAAM,CAAC,2BAA2B,CAAC;QACrE,IAAI0b,yBAAyB,EAAE;UAC3B,MAAMa,kCAAkC,GAAGb,yBAAyB,CAACpR,SAAS;UAC9E+R,cAAc,GAAGE,kCAAkC,CAAC/O,8BAA8B,CAAC;UACnF8O,iBAAiB,GAAGC,kCAAkC,CAAC9O,iCAAiC,CAAC;QAC7F;MACJ;MACA,MAAM+O,kBAAkB,GAAG,kBAAkB;MAC7C,MAAMC,SAAS,GAAG,WAAW;MAC7B,SAAS5X,YAAYA,CAACpB,IAAI,EAAE;QACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;QACtB,MAAM8B,MAAM,GAAG9B,IAAI,CAAC8B,MAAM;QAC1BA,MAAM,CAAC2V,aAAa,CAAC,GAAG,KAAK;QAC7B3V,MAAM,CAAC6V,0BAA0B,CAAC,GAAG,KAAK;QAC1C;QACA,MAAM3M,QAAQ,GAAGlJ,MAAM,CAAC0V,YAAY,CAAC;QACrC,IAAI,CAACO,cAAc,EAAE;UACjBA,cAAc,GAAGjW,MAAM,CAACoH,8BAA8B,CAAC;UACvD8O,iBAAiB,GAAGlW,MAAM,CAACqH,iCAAiC,CAAC;QACjE;QACA,IAAI6B,QAAQ,EAAE;UACVgN,iBAAiB,CAAC3S,IAAI,CAACvD,MAAM,EAAEoW,kBAAkB,EAAElN,QAAQ,CAAC;QAChE;QACA,MAAMoN,WAAW,GAAItW,MAAM,CAAC0V,YAAY,CAAC,GAAG,MAAM;UAC9C,IAAI1V,MAAM,CAACuW,UAAU,KAAKvW,MAAM,CAACwW,IAAI,EAAE;YACnC;YACA;YACA,IAAI,CAACtY,IAAI,CAACoT,OAAO,IAAItR,MAAM,CAAC2V,aAAa,CAAC,IAAItY,IAAI,CAACE,KAAK,KAAK8Y,SAAS,EAAE;cACpE;cACA;cACA;cACA;cACA;cACA;cACA;cACA,MAAMI,SAAS,GAAGzW,MAAM,CAACyH,IAAI,CAACzN,UAAU,CAAC,WAAW,CAAC,CAAC;cACtD,IAAIgG,MAAM,CAAC0W,MAAM,KAAK,CAAC,IAAID,SAAS,IAAIA,SAAS,CAAC9W,MAAM,GAAG,CAAC,EAAE;gBAC1D,MAAMgX,SAAS,GAAGtZ,IAAI,CAACJ,MAAM;gBAC7BI,IAAI,CAACJ,MAAM,GAAG,YAAY;kBACtB;kBACA;kBACA,MAAMwZ,SAAS,GAAGzW,MAAM,CAACyH,IAAI,CAACzN,UAAU,CAAC,WAAW,CAAC,CAAC;kBACtD,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+W,SAAS,CAAC9W,MAAM,EAAED,CAAC,EAAE,EAAE;oBACvC,IAAI+W,SAAS,CAAC/W,CAAC,CAAC,KAAKrC,IAAI,EAAE;sBACvBoZ,SAAS,CAACzG,MAAM,CAACtQ,CAAC,EAAE,CAAC,CAAC;oBAC1B;kBACJ;kBACA,IAAI,CAACxB,IAAI,CAACoT,OAAO,IAAIjU,IAAI,CAACE,KAAK,KAAK8Y,SAAS,EAAE;oBAC3CM,SAAS,CAACpT,IAAI,CAAClG,IAAI,CAAC;kBACxB;gBACJ,CAAC;gBACDoZ,SAAS,CAAC9T,IAAI,CAACtF,IAAI,CAAC;cACxB,CAAC,MACI;gBACDA,IAAI,CAACJ,MAAM,CAAC,CAAC;cACjB;YACJ,CAAC,MACI,IAAI,CAACiB,IAAI,CAACoT,OAAO,IAAItR,MAAM,CAAC2V,aAAa,CAAC,KAAK,KAAK,EAAE;cACvD;cACA3V,MAAM,CAAC6V,0BAA0B,CAAC,GAAG,IAAI;YAC7C;UACJ;QACJ,CAAE;QACFI,cAAc,CAAC1S,IAAI,CAACvD,MAAM,EAAEoW,kBAAkB,EAAEE,WAAW,CAAC;QAC5D,MAAMM,UAAU,GAAG5W,MAAM,CAACwV,QAAQ,CAAC;QACnC,IAAI,CAACoB,UAAU,EAAE;UACb5W,MAAM,CAACwV,QAAQ,CAAC,GAAGnY,IAAI;QAC3B;QACAwZ,UAAU,CAACpU,KAAK,CAACzC,MAAM,EAAE9B,IAAI,CAACsF,IAAI,CAAC;QACnCxD,MAAM,CAAC2V,aAAa,CAAC,GAAG,IAAI;QAC5B,OAAOtY,IAAI;MACf;MACA,SAASyZ,mBAAmBA,CAAA,EAAG,CAAE;MACjC,SAAS5D,SAASA,CAAC7V,IAAI,EAAE;QACrB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;QACtB;QACA;QACAA,IAAI,CAACoT,OAAO,GAAG,IAAI;QACnB,OAAOyF,WAAW,CAACtU,KAAK,CAACvE,IAAI,CAAC8B,MAAM,EAAE9B,IAAI,CAACsF,IAAI,CAAC;MACpD;MACA,MAAMwT,UAAU,GAAGzR,WAAW,CAACwQ,uBAAuB,EAAE,MAAM,EAAE,MAAM,UAAU1S,IAAI,EAAEG,IAAI,EAAE;QACxFH,IAAI,CAACoS,QAAQ,CAAC,GAAGjS,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK;QACjCH,IAAI,CAACuS,OAAO,CAAC,GAAGpS,IAAI,CAAC,CAAC,CAAC;QACvB,OAAOwT,UAAU,CAACvU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;MACvC,CAAC,CAAC;MACF,MAAMyT,qBAAqB,GAAG,qBAAqB;MACnD,MAAMC,iBAAiB,GAAGvP,UAAU,CAAC,mBAAmB,CAAC;MACzD,MAAMwP,mBAAmB,GAAGxP,UAAU,CAAC,qBAAqB,CAAC;MAC7D,MAAMkP,UAAU,GAAGtR,WAAW,CAACwQ,uBAAuB,EAAE,MAAM,EAAE,MAAM,UAAU1S,IAAI,EAAEG,IAAI,EAAE;QACxF,IAAIiE,IAAI,CAAC1M,OAAO,CAACoc,mBAAmB,CAAC,KAAK,IAAI,EAAE;UAC5C;UACA;UACA;UACA,OAAON,UAAU,CAACpU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACvC;QACA,IAAIH,IAAI,CAACoS,QAAQ,CAAC,EAAE;UAChB;UACA,OAAOoB,UAAU,CAACpU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACvC,CAAC,MACI;UACD,MAAML,OAAO,GAAG;YACZnD,MAAM,EAAEqD,IAAI;YACZ+T,GAAG,EAAE/T,IAAI,CAACuS,OAAO,CAAC;YAClBzX,UAAU,EAAE,KAAK;YACjBqF,IAAI,EAAEA,IAAI;YACV8N,OAAO,EAAE;UACb,CAAC;UACD,MAAMjU,IAAI,GAAGqK,gCAAgC,CAACuP,qBAAqB,EAAEH,mBAAmB,EAAE3T,OAAO,EAAE1E,YAAY,EAAEyU,SAAS,CAAC;UAC3H,IAAI7P,IAAI,IACJA,IAAI,CAACwS,0BAA0B,CAAC,KAAK,IAAI,IACzC,CAAC1S,OAAO,CAACmO,OAAO,IAChBjU,IAAI,CAACE,KAAK,KAAK8Y,SAAS,EAAE;YAC1B;YACA;YACA;YACAhZ,IAAI,CAACJ,MAAM,CAAC,CAAC;UACjB;QACJ;MACJ,CAAC,CAAC;MACF,MAAM8Z,WAAW,GAAGxR,WAAW,CAACwQ,uBAAuB,EAAE,OAAO,EAAE,MAAM,UAAU1S,IAAI,EAAEG,IAAI,EAAE;QAC1F,MAAMnG,IAAI,GAAG2Y,eAAe,CAAC3S,IAAI,CAAC;QAClC,IAAIhG,IAAI,IAAI,OAAOA,IAAI,CAACI,IAAI,IAAI,QAAQ,EAAE;UACtC;UACA;UACA;UACA;UACA,IAAIJ,IAAI,CAACe,QAAQ,IAAI,IAAI,IAAKf,IAAI,CAACa,IAAI,IAAIb,IAAI,CAACa,IAAI,CAACoT,OAAQ,EAAE;YAC3D;UACJ;UACAjU,IAAI,CAACvC,IAAI,CAACyE,UAAU,CAAClC,IAAI,CAAC;QAC9B,CAAC,MACI,IAAIoK,IAAI,CAAC1M,OAAO,CAACmc,iBAAiB,CAAC,KAAK,IAAI,EAAE;UAC/C;UACA,OAAOH,WAAW,CAACtU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACxC;QACA;QACA;QACA;MACJ,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACFiE,IAAI,CAACrM,YAAY,CAAC,aAAa,EAAGtB,MAAM,IAAK;IACzC;IACA,IAAIA,MAAM,CAAC,WAAW,CAAC,IAAIA,MAAM,CAAC,WAAW,CAAC,CAACud,WAAW,EAAE;MACxDrP,cAAc,CAAClO,MAAM,CAAC,WAAW,CAAC,CAACud,WAAW,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;IAC5F;EACJ,CAAC,CAAC;EACF5P,IAAI,CAACrM,YAAY,CAAC,uBAAuB,EAAE,CAACtB,MAAM,EAAE2N,IAAI,KAAK;IACzD;IACA,SAAS6P,2BAA2BA,CAACtF,OAAO,EAAE;MAC1C,OAAO,UAAUuF,CAAC,EAAE;QAChB,MAAMC,UAAU,GAAG5F,cAAc,CAAC9X,MAAM,EAAEkY,OAAO,CAAC;QAClDwF,UAAU,CAACC,OAAO,CAAE/Z,SAAS,IAAK;UAC9B;UACA;UACA,MAAMga,qBAAqB,GAAG5d,MAAM,CAAC,uBAAuB,CAAC;UAC7D,IAAI4d,qBAAqB,EAAE;YACvB,MAAMC,GAAG,GAAG,IAAID,qBAAqB,CAAC1F,OAAO,EAAE;cAC3C4F,OAAO,EAAEL,CAAC,CAACK,OAAO;cAClBC,MAAM,EAAEN,CAAC,CAACO;YACd,CAAC,CAAC;YACFpa,SAAS,CAACT,MAAM,CAAC0a,GAAG,CAAC;UACzB;QACJ,CAAC,CAAC;MACN,CAAC;IACL;IACA,IAAI7d,MAAM,CAAC,uBAAuB,CAAC,EAAE;MACjC2N,IAAI,CAACE,UAAU,CAAC,kCAAkC,CAAC,CAAC,GAChD2P,2BAA2B,CAAC,oBAAoB,CAAC;MACrD7P,IAAI,CAACE,UAAU,CAAC,yBAAyB,CAAC,CAAC,GACvC2P,2BAA2B,CAAC,kBAAkB,CAAC;IACvD;EACJ,CAAC,CAAC;EACF7P,IAAI,CAACrM,YAAY,CAAC,gBAAgB,EAAE,CAACtB,MAAM,EAAE2N,IAAI,EAAEoF,GAAG,KAAK;IACvD6F,mBAAmB,CAAC5Y,MAAM,EAAE+S,GAAG,CAAC;EACpC,CAAC,CAAC;AACN;AAEA,SAASkL,YAAYA,CAACtQ,IAAI,EAAE;EACxBA,IAAI,CAACrM,YAAY,CAAC,kBAAkB,EAAE,CAACtB,MAAM,EAAE2N,IAAI,EAAEoF,GAAG,KAAK;IACzD,MAAM9G,8BAA8B,GAAG9B,MAAM,CAAC0C,wBAAwB;IACtE,MAAMb,oBAAoB,GAAG7B,MAAM,CAAC2C,cAAc;IAClD,SAASoR,sBAAsBA,CAACrO,GAAG,EAAE;MACjC,IAAIA,GAAG,IAAIA,GAAG,CAAC5F,QAAQ,KAAKE,MAAM,CAACC,SAAS,CAACH,QAAQ,EAAE;QACnD,MAAM6G,SAAS,GAAGjB,GAAG,CAAC9N,WAAW,IAAI8N,GAAG,CAAC9N,WAAW,CAAC5B,IAAI;QACzD,OAAO,CAAC2Q,SAAS,GAAGA,SAAS,GAAG,EAAE,IAAI,IAAI,GAAGqN,IAAI,CAACC,SAAS,CAACvO,GAAG,CAAC;MACpE;MACA,OAAOA,GAAG,GAAGA,GAAG,CAAC5F,QAAQ,CAAC,CAAC,GAAGE,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAACoG,GAAG,CAAC;IACrE;IACA,MAAM3P,UAAU,GAAG6S,GAAG,CAAC5H,MAAM;IAC7B,MAAMkT,sBAAsB,GAAG,EAAE;IACjC,MAAMC,yCAAyC,GAAGte,MAAM,CAACE,UAAU,CAAC,6CAA6C,CAAC,CAAC,KAAK,KAAK;IAC7H,MAAMqK,aAAa,GAAGrK,UAAU,CAAC,SAAS,CAAC;IAC3C,MAAMsK,UAAU,GAAGtK,UAAU,CAAC,MAAM,CAAC;IACrC,MAAMqe,aAAa,GAAG,mBAAmB;IACzCxL,GAAG,CAAC9H,gBAAgB,GAAIwS,CAAC,IAAK;MAC1B,IAAI1K,GAAG,CAACzH,iBAAiB,CAAC,CAAC,EAAE;QACzB,MAAM0S,SAAS,GAAGP,CAAC,IAAIA,CAAC,CAACO,SAAS;QAClC,IAAIA,SAAS,EAAE;UACXQ,OAAO,CAACpb,KAAK,CAAC,8BAA8B,EAAE4a,SAAS,YAAYld,KAAK,GAAGkd,SAAS,CAACzO,OAAO,GAAGyO,SAAS,EAAE,SAAS,EAAEP,CAAC,CAACzc,IAAI,CAACb,IAAI,EAAE,SAAS,EAAEsd,CAAC,CAACla,IAAI,IAAIka,CAAC,CAACla,IAAI,CAACZ,MAAM,EAAE,UAAU,EAAEqb,SAAS,EAAEA,SAAS,YAAYld,KAAK,GAAGkd,SAAS,CAACS,KAAK,GAAGla,SAAS,CAAC;QAC1P,CAAC,MACI;UACDia,OAAO,CAACpb,KAAK,CAACqa,CAAC,CAAC;QACpB;MACJ;IACJ,CAAC;IACD1K,GAAG,CAAC7H,kBAAkB,GAAG,MAAM;MAC3B,OAAOmT,sBAAsB,CAACxY,MAAM,EAAE;QAClC,MAAM6Y,oBAAoB,GAAGL,sBAAsB,CAACM,KAAK,CAAC,CAAC;QAC3D,IAAI;UACAD,oBAAoB,CAAC1d,IAAI,CAAC8B,UAAU,CAAC,MAAM;YACvC,IAAI4b,oBAAoB,CAACE,aAAa,EAAE;cACpC,MAAMF,oBAAoB,CAACV,SAAS;YACxC;YACA,MAAMU,oBAAoB;UAC9B,CAAC,CAAC;QACN,CAAC,CACD,OAAOtb,KAAK,EAAE;UACVyb,wBAAwB,CAACzb,KAAK,CAAC;QACnC;MACJ;IACJ,CAAC;IACD,MAAM0b,0CAA0C,GAAG5e,UAAU,CAAC,kCAAkC,CAAC;IACjG,SAAS2e,wBAAwBA,CAACpB,CAAC,EAAE;MACjC1K,GAAG,CAAC9H,gBAAgB,CAACwS,CAAC,CAAC;MACvB,IAAI;QACA,MAAMsB,OAAO,GAAGpR,IAAI,CAACmR,0CAA0C,CAAC;QAChE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;UAC/BA,OAAO,CAACtV,IAAI,CAAC,IAAI,EAAEgU,CAAC,CAAC;QACzB;MACJ,CAAC,CACD,OAAOxY,GAAG,EAAE,CAAE;IAClB;IACA,SAAS+Z,UAAUA,CAACjW,KAAK,EAAE;MACvB,OAAOA,KAAK,IAAIA,KAAK,CAACkW,IAAI;IAC9B;IACA,SAASC,iBAAiBA,CAACnW,KAAK,EAAE;MAC9B,OAAOA,KAAK;IAChB;IACA,SAASoW,gBAAgBA,CAACnB,SAAS,EAAE;MACjC,OAAOoB,gBAAgB,CAACC,MAAM,CAACrB,SAAS,CAAC;IAC7C;IACA,MAAMsB,WAAW,GAAGpf,UAAU,CAAC,OAAO,CAAC;IACvC,MAAMqf,WAAW,GAAGrf,UAAU,CAAC,OAAO,CAAC;IACvC,MAAMsf,aAAa,GAAGtf,UAAU,CAAC,SAAS,CAAC;IAC3C,MAAMuf,wBAAwB,GAAGvf,UAAU,CAAC,oBAAoB,CAAC;IACjE,MAAMwf,wBAAwB,GAAGxf,UAAU,CAAC,oBAAoB,CAAC;IACjE,MAAMyC,MAAM,GAAG,cAAc;IAC7B,MAAMgd,UAAU,GAAG,IAAI;IACvB,MAAMC,QAAQ,GAAG,IAAI;IACrB,MAAMC,QAAQ,GAAG,KAAK;IACtB,MAAMC,iBAAiB,GAAG,CAAC;IAC3B,SAASC,YAAYA,CAACjC,OAAO,EAAEra,KAAK,EAAE;MAClC,OAAQuc,CAAC,IAAK;QACV,IAAI;UACAC,cAAc,CAACnC,OAAO,EAAEra,KAAK,EAAEuc,CAAC,CAAC;QACrC,CAAC,CACD,OAAO/a,GAAG,EAAE;UACRgb,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAE7Y,GAAG,CAAC;QACvC;QACA;MACJ,CAAC;IACL;IACA,MAAMgP,IAAI,GAAG,SAAAA,CAAA,EAAY;MACrB,IAAIiM,SAAS,GAAG,KAAK;MACrB,OAAO,SAASC,OAAOA,CAACC,eAAe,EAAE;QACrC,OAAO,YAAY;UACf,IAAIF,SAAS,EAAE;YACX;UACJ;UACAA,SAAS,GAAG,IAAI;UAChBE,eAAe,CAACzX,KAAK,CAAC,IAAI,EAAE5F,SAAS,CAAC;QAC1C,CAAC;MACL,CAAC;IACL,CAAC;IACD,MAAMsd,UAAU,GAAG,8BAA8B;IACjD,MAAMC,yBAAyB,GAAGpgB,UAAU,CAAC,kBAAkB,CAAC;IAChE;IACA,SAAS+f,cAAcA,CAACnC,OAAO,EAAEra,KAAK,EAAEsF,KAAK,EAAE;MAC3C,MAAMwX,WAAW,GAAGtM,IAAI,CAAC,CAAC;MAC1B,IAAI6J,OAAO,KAAK/U,KAAK,EAAE;QACnB,MAAM,IAAIyX,SAAS,CAACH,UAAU,CAAC;MACnC;MACA,IAAIvC,OAAO,CAACwB,WAAW,CAAC,KAAKK,UAAU,EAAE;QACrC;QACA,IAAIV,IAAI,GAAG,IAAI;QACf,IAAI;UACA,IAAI,OAAOlW,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;YAC1DkW,IAAI,GAAGlW,KAAK,IAAIA,KAAK,CAACkW,IAAI;UAC9B;QACJ,CAAC,CACD,OAAOha,GAAG,EAAE;UACRsb,WAAW,CAAC,MAAM;YACdN,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAE7Y,GAAG,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC;UACJ,OAAO6Y,OAAO;QAClB;QACA;QACA,IAAIra,KAAK,KAAKoc,QAAQ,IAClB9W,KAAK,YAAYqW,gBAAgB,IACjCrW,KAAK,CAACtH,cAAc,CAAC6d,WAAW,CAAC,IACjCvW,KAAK,CAACtH,cAAc,CAAC8d,WAAW,CAAC,IACjCxW,KAAK,CAACuW,WAAW,CAAC,KAAKK,UAAU,EAAE;UACnCc,oBAAoB,CAAC1X,KAAK,CAAC;UAC3BkX,cAAc,CAACnC,OAAO,EAAE/U,KAAK,CAACuW,WAAW,CAAC,EAAEvW,KAAK,CAACwW,WAAW,CAAC,CAAC;QACnE,CAAC,MACI,IAAI9b,KAAK,KAAKoc,QAAQ,IAAI,OAAOZ,IAAI,KAAK,UAAU,EAAE;UACvD,IAAI;YACAA,IAAI,CAACxV,IAAI,CAACV,KAAK,EAAEwX,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAEra,KAAK,CAAC,CAAC,EAAE8c,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;UAC1G,CAAC,CACD,OAAO7Y,GAAG,EAAE;YACRsb,WAAW,CAAC,MAAM;cACdN,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAE7Y,GAAG,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC;UACR;QACJ,CAAC,MACI;UACD6Y,OAAO,CAACwB,WAAW,CAAC,GAAG7b,KAAK;UAC5B,MAAMuH,KAAK,GAAG8S,OAAO,CAACyB,WAAW,CAAC;UAClCzB,OAAO,CAACyB,WAAW,CAAC,GAAGxW,KAAK;UAC5B,IAAI+U,OAAO,CAAC0B,aAAa,CAAC,KAAKA,aAAa,EAAE;YAC1C;YACA,IAAI/b,KAAK,KAAKmc,QAAQ,EAAE;cACpB;cACA;cACA9B,OAAO,CAACwB,WAAW,CAAC,GAAGxB,OAAO,CAAC4B,wBAAwB,CAAC;cACxD5B,OAAO,CAACyB,WAAW,CAAC,GAAGzB,OAAO,CAAC2B,wBAAwB,CAAC;YAC5D;UACJ;UACA;UACA;UACA,IAAIhc,KAAK,KAAKoc,QAAQ,IAAI9W,KAAK,YAAYjI,KAAK,EAAE;YAC9C;YACA,MAAM4f,KAAK,GAAG/S,IAAI,CAACvM,WAAW,IAC1BuM,IAAI,CAACvM,WAAW,CAACgD,IAAI,IACrBuJ,IAAI,CAACvM,WAAW,CAACgD,IAAI,CAACma,aAAa,CAAC;YACxC,IAAImC,KAAK,EAAE;cACP;cACA1U,oBAAoB,CAACjD,KAAK,EAAEuX,yBAAyB,EAAE;gBACnDrQ,YAAY,EAAE,IAAI;gBAClBD,UAAU,EAAE,KAAK;gBACjBxB,QAAQ,EAAE,IAAI;gBACdzF,KAAK,EAAE2X;cACX,CAAC,CAAC;YACN;UACJ;UACA,KAAK,IAAI9a,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoF,KAAK,CAACnF,MAAM,GAAG;YAC/B8a,uBAAuB,CAAC7C,OAAO,EAAE9S,KAAK,CAACpF,CAAC,EAAE,CAAC,EAAEoF,KAAK,CAACpF,CAAC,EAAE,CAAC,EAAEoF,KAAK,CAACpF,CAAC,EAAE,CAAC,EAAEoF,KAAK,CAACpF,CAAC,EAAE,CAAC,CAAC;UACpF;UACA,IAAIoF,KAAK,CAACnF,MAAM,IAAI,CAAC,IAAIpC,KAAK,IAAIoc,QAAQ,EAAE;YACxC/B,OAAO,CAACwB,WAAW,CAAC,GAAGQ,iBAAiB;YACxC,IAAIpB,oBAAoB,GAAG3V,KAAK;YAChC,IAAI;cACA;cACA;cACA;cACA,MAAM,IAAIjI,KAAK,CAAC,yBAAyB,GACrCod,sBAAsB,CAACnV,KAAK,CAAC,IAC5BA,KAAK,IAAIA,KAAK,CAAC0V,KAAK,GAAG,IAAI,GAAG1V,KAAK,CAAC0V,KAAK,GAAG,EAAE,CAAC,CAAC;YACzD,CAAC,CACD,OAAOxZ,GAAG,EAAE;cACRyZ,oBAAoB,GAAGzZ,GAAG;YAC9B;YACA,IAAIqZ,yCAAyC,EAAE;cAC3C;cACA;cACAI,oBAAoB,CAACE,aAAa,GAAG,IAAI;YAC7C;YACAF,oBAAoB,CAACV,SAAS,GAAGjV,KAAK;YACtC2V,oBAAoB,CAACZ,OAAO,GAAGA,OAAO;YACtCY,oBAAoB,CAAC1d,IAAI,GAAG2M,IAAI,CAAC1M,OAAO;YACxCyd,oBAAoB,CAACnb,IAAI,GAAGoK,IAAI,CAACvM,WAAW;YAC5Cid,sBAAsB,CAACxV,IAAI,CAAC6V,oBAAoB,CAAC;YACjD3L,GAAG,CAAC7N,iBAAiB,CAAC,CAAC,CAAC,CAAC;UAC7B;QACJ;MACJ;MACA;MACA,OAAO4Y,OAAO;IAClB;IACA,MAAM8C,yBAAyB,GAAG1gB,UAAU,CAAC,yBAAyB,CAAC;IACvE,SAASugB,oBAAoBA,CAAC3C,OAAO,EAAE;MACnC,IAAIA,OAAO,CAACwB,WAAW,CAAC,KAAKQ,iBAAiB,EAAE;QAC5C;QACA;QACA;QACA;QACA;QACA,IAAI;UACA,MAAMf,OAAO,GAAGpR,IAAI,CAACiT,yBAAyB,CAAC;UAC/C,IAAI7B,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;YAC1CA,OAAO,CAACtV,IAAI,CAAC,IAAI,EAAE;cAAEuU,SAAS,EAAEF,OAAO,CAACyB,WAAW,CAAC;cAAEzB,OAAO,EAAEA;YAAQ,CAAC,CAAC;UAC7E;QACJ,CAAC,CACD,OAAO7Y,GAAG,EAAE,CAAE;QACd6Y,OAAO,CAACwB,WAAW,CAAC,GAAGO,QAAQ;QAC/B,KAAK,IAAIja,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyY,sBAAsB,CAACxY,MAAM,EAAED,CAAC,EAAE,EAAE;UACpD,IAAIkY,OAAO,KAAKO,sBAAsB,CAACzY,CAAC,CAAC,CAACkY,OAAO,EAAE;YAC/CO,sBAAsB,CAACnI,MAAM,CAACtQ,CAAC,EAAE,CAAC,CAAC;UACvC;QACJ;MACJ;IACJ;IACA,SAAS+a,uBAAuBA,CAAC7C,OAAO,EAAE9c,IAAI,EAAE6f,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAE;MACnFN,oBAAoB,CAAC3C,OAAO,CAAC;MAC7B,MAAMkD,YAAY,GAAGlD,OAAO,CAACwB,WAAW,CAAC;MACzC,MAAMtZ,QAAQ,GAAGgb,YAAY,GACvB,OAAOF,WAAW,KAAK,UAAU,GAC7BA,WAAW,GACX5B,iBAAiB,GACrB,OAAO6B,UAAU,KAAK,UAAU,GAC5BA,UAAU,GACV5B,gBAAgB;MAC1Bne,IAAI,CAACkE,iBAAiB,CAACvC,MAAM,EAAE,MAAM;QACjC,IAAI;UACA,MAAMse,kBAAkB,GAAGnD,OAAO,CAACyB,WAAW,CAAC;UAC/C,MAAM2B,gBAAgB,GAAG,CAAC,CAACL,YAAY,IAAIrB,aAAa,KAAKqB,YAAY,CAACrB,aAAa,CAAC;UACxF,IAAI0B,gBAAgB,EAAE;YAClB;YACAL,YAAY,CAACpB,wBAAwB,CAAC,GAAGwB,kBAAkB;YAC3DJ,YAAY,CAACnB,wBAAwB,CAAC,GAAGsB,YAAY;UACzD;UACA;UACA,MAAMjY,KAAK,GAAG/H,IAAI,CAACgC,GAAG,CAACgD,QAAQ,EAAEzB,SAAS,EAAE2c,gBAAgB,IAAIlb,QAAQ,KAAKmZ,gBAAgB,IAAInZ,QAAQ,KAAKkZ,iBAAiB,GACzH,EAAE,GACF,CAAC+B,kBAAkB,CAAC,CAAC;UAC3BhB,cAAc,CAACY,YAAY,EAAE,IAAI,EAAE9X,KAAK,CAAC;QAC7C,CAAC,CACD,OAAO3F,KAAK,EAAE;UACV;UACA6c,cAAc,CAACY,YAAY,EAAE,KAAK,EAAEzd,KAAK,CAAC;QAC9C;MACJ,CAAC,EAAEyd,YAAY,CAAC;IACpB;IACA,MAAMM,4BAA4B,GAAG,+CAA+C;IACpF,MAAM9V,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;IAC5B,MAAM+V,cAAc,GAAGphB,MAAM,CAACohB,cAAc;IAC5C,MAAMhC,gBAAgB,CAAC;MACnB,OAAOnV,QAAQA,CAAA,EAAG;QACd,OAAOkX,4BAA4B;MACvC;MACA,OAAOrW,OAAOA,CAAC/B,KAAK,EAAE;QAClB,IAAIA,KAAK,YAAYqW,gBAAgB,EAAE;UACnC,OAAOrW,KAAK;QAChB;QACA,OAAOkX,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAEL,QAAQ,EAAE7W,KAAK,CAAC;MAC1D;MACA,OAAOsW,MAAMA,CAACjc,KAAK,EAAE;QACjB,OAAO6c,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAEJ,QAAQ,EAAEzc,KAAK,CAAC;MAC1D;MACA,OAAOie,aAAaA,CAAA,EAAG;QACnB,MAAMhS,MAAM,GAAG,CAAC,CAAC;QACjBA,MAAM,CAACyO,OAAO,GAAG,IAAIsB,gBAAgB,CAAC,CAACkC,GAAG,EAAEC,GAAG,KAAK;UAChDlS,MAAM,CAACvE,OAAO,GAAGwW,GAAG;UACpBjS,MAAM,CAACgQ,MAAM,GAAGkC,GAAG;QACvB,CAAC,CAAC;QACF,OAAOlS,MAAM;MACjB;MACA,OAAOmS,GAAGA,CAACC,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE;UAC1D,OAAOC,OAAO,CAACvC,MAAM,CAAC,IAAI+B,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,MAAMS,QAAQ,GAAG,EAAE;QACnB,IAAIlc,KAAK,GAAG,CAAC;QACb,IAAI;UACA,KAAK,IAAIqa,CAAC,IAAIyB,MAAM,EAAE;YAClB9b,KAAK,EAAE;YACPkc,QAAQ,CAAChZ,IAAI,CAACuW,gBAAgB,CAACtU,OAAO,CAACkV,CAAC,CAAC,CAAC;UAC9C;QACJ,CAAC,CACD,OAAO/a,GAAG,EAAE;UACR,OAAO2c,OAAO,CAACvC,MAAM,CAAC,IAAI+B,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,IAAIzb,KAAK,KAAK,CAAC,EAAE;UACb,OAAOic,OAAO,CAACvC,MAAM,CAAC,IAAI+B,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,IAAIU,QAAQ,GAAG,KAAK;QACpB,MAAMxN,MAAM,GAAG,EAAE;QACjB,OAAO,IAAI8K,gBAAgB,CAAC,CAACtU,OAAO,EAAEuU,MAAM,KAAK;UAC7C,KAAK,IAAIzZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGic,QAAQ,CAAChc,MAAM,EAAED,CAAC,EAAE,EAAE;YACtCic,QAAQ,CAACjc,CAAC,CAAC,CAACqZ,IAAI,CAAEe,CAAC,IAAK;cACpB,IAAI8B,QAAQ,EAAE;gBACV;cACJ;cACAA,QAAQ,GAAG,IAAI;cACfhX,OAAO,CAACkV,CAAC,CAAC;YACd,CAAC,EAAG/a,GAAG,IAAK;cACRqP,MAAM,CAACzL,IAAI,CAAC5D,GAAG,CAAC;cAChBU,KAAK,EAAE;cACP,IAAIA,KAAK,KAAK,CAAC,EAAE;gBACbmc,QAAQ,GAAG,IAAI;gBACfzC,MAAM,CAAC,IAAI+B,cAAc,CAAC9M,MAAM,EAAE,4BAA4B,CAAC,CAAC;cACpE;YACJ,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,OAAOyN,IAAIA,CAACN,MAAM,EAAE;QAChB,IAAI3W,OAAO;QACX,IAAIuU,MAAM;QACV,IAAIvB,OAAO,GAAG,IAAI,IAAI,CAAC,CAACwD,GAAG,EAAEC,GAAG,KAAK;UACjCzW,OAAO,GAAGwW,GAAG;UACbjC,MAAM,GAAGkC,GAAG;QAChB,CAAC,CAAC;QACF,SAASS,SAASA,CAACjZ,KAAK,EAAE;UACtB+B,OAAO,CAAC/B,KAAK,CAAC;QAClB;QACA,SAASkZ,QAAQA,CAAC7e,KAAK,EAAE;UACrBic,MAAM,CAACjc,KAAK,CAAC;QACjB;QACA,KAAK,IAAI2F,KAAK,IAAI0Y,MAAM,EAAE;UACtB,IAAI,CAACzC,UAAU,CAACjW,KAAK,CAAC,EAAE;YACpBA,KAAK,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,KAAK,CAAC;UAC/B;UACAA,KAAK,CAACkW,IAAI,CAAC+C,SAAS,EAAEC,QAAQ,CAAC;QACnC;QACA,OAAOnE,OAAO;MAClB;MACA,OAAOoE,GAAGA,CAACT,MAAM,EAAE;QACf,OAAOrC,gBAAgB,CAAC+C,eAAe,CAACV,MAAM,CAAC;MACnD;MACA,OAAOW,UAAUA,CAACX,MAAM,EAAE;QACtB,MAAMY,CAAC,GAAG,IAAI,IAAI,IAAI,CAACjY,SAAS,YAAYgV,gBAAgB,GAAG,IAAI,GAAGA,gBAAgB;QACtF,OAAOiD,CAAC,CAACF,eAAe,CAACV,MAAM,EAAE;UAC7Ba,YAAY,EAAGvZ,KAAK,KAAM;YAAE6T,MAAM,EAAE,WAAW;YAAE7T;UAAM,CAAC,CAAC;UACzDwZ,aAAa,EAAGtd,GAAG,KAAM;YAAE2X,MAAM,EAAE,UAAU;YAAEmB,MAAM,EAAE9Y;UAAI,CAAC;QAChE,CAAC,CAAC;MACN;MACA,OAAOkd,eAAeA,CAACV,MAAM,EAAE/e,QAAQ,EAAE;QACrC,IAAIoI,OAAO;QACX,IAAIuU,MAAM;QACV,IAAIvB,OAAO,GAAG,IAAI,IAAI,CAAC,CAACwD,GAAG,EAAEC,GAAG,KAAK;UACjCzW,OAAO,GAAGwW,GAAG;UACbjC,MAAM,GAAGkC,GAAG;QAChB,CAAC,CAAC;QACF;QACA,IAAIiB,eAAe,GAAG,CAAC;QACvB,IAAIC,UAAU,GAAG,CAAC;QAClB,MAAMC,cAAc,GAAG,EAAE;QACzB,KAAK,IAAI3Z,KAAK,IAAI0Y,MAAM,EAAE;UACtB,IAAI,CAACzC,UAAU,CAACjW,KAAK,CAAC,EAAE;YACpBA,KAAK,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,KAAK,CAAC;UAC/B;UACA,MAAM4Z,aAAa,GAAGF,UAAU;UAChC,IAAI;YACA1Z,KAAK,CAACkW,IAAI,CAAElW,KAAK,IAAK;cAClB2Z,cAAc,CAACC,aAAa,CAAC,GAAGjgB,QAAQ,GAAGA,QAAQ,CAAC4f,YAAY,CAACvZ,KAAK,CAAC,GAAGA,KAAK;cAC/EyZ,eAAe,EAAE;cACjB,IAAIA,eAAe,KAAK,CAAC,EAAE;gBACvB1X,OAAO,CAAC4X,cAAc,CAAC;cAC3B;YACJ,CAAC,EAAGzd,GAAG,IAAK;cACR,IAAI,CAACvC,QAAQ,EAAE;gBACX2c,MAAM,CAACpa,GAAG,CAAC;cACf,CAAC,MACI;gBACDyd,cAAc,CAACC,aAAa,CAAC,GAAGjgB,QAAQ,CAAC6f,aAAa,CAACtd,GAAG,CAAC;gBAC3Dud,eAAe,EAAE;gBACjB,IAAIA,eAAe,KAAK,CAAC,EAAE;kBACvB1X,OAAO,CAAC4X,cAAc,CAAC;gBAC3B;cACJ;YACJ,CAAC,CAAC;UACN,CAAC,CACD,OAAOE,OAAO,EAAE;YACZvD,MAAM,CAACuD,OAAO,CAAC;UACnB;UACAJ,eAAe,EAAE;UACjBC,UAAU,EAAE;QAChB;QACA;QACAD,eAAe,IAAI,CAAC;QACpB,IAAIA,eAAe,KAAK,CAAC,EAAE;UACvB1X,OAAO,CAAC4X,cAAc,CAAC;QAC3B;QACA,OAAO5E,OAAO;MAClB;MACA/b,WAAWA,CAAC8gB,QAAQ,EAAE;QAClB,MAAM/E,OAAO,GAAG,IAAI;QACpB,IAAI,EAAEA,OAAO,YAAYsB,gBAAgB,CAAC,EAAE;UACxC,MAAM,IAAIte,KAAK,CAAC,gCAAgC,CAAC;QACrD;QACAgd,OAAO,CAACwB,WAAW,CAAC,GAAGK,UAAU;QACjC7B,OAAO,CAACyB,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3B,IAAI;UACA,MAAMgB,WAAW,GAAGtM,IAAI,CAAC,CAAC;UAC1B4O,QAAQ,IACJA,QAAQ,CAACtC,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAE8B,QAAQ,CAAC,CAAC,EAAEW,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAE+B,QAAQ,CAAC,CAAC,CAAC;QAC5G,CAAC,CACD,OAAOzc,KAAK,EAAE;UACV6c,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAE1a,KAAK,CAAC;QACzC;MACJ;MACA,KAAKse,MAAM,CAACoB,WAAW,IAAI;QACvB,OAAO,SAAS;MACpB;MACA,KAAKpB,MAAM,CAACqB,OAAO,IAAI;QACnB,OAAO3D,gBAAgB;MAC3B;MACAH,IAAIA,CAAC6B,WAAW,EAAEC,UAAU,EAAE;QAAA,IAAAiC,iBAAA;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIC,CAAC,IAAAD,iBAAA,GAAG,IAAI,CAACjhB,WAAW,cAAAihB,iBAAA,uBAAhBA,iBAAA,CAAmBtB,MAAM,CAACqB,OAAO,CAAC;QAC1C,IAAI,CAACE,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;UAC/BA,CAAC,GAAG,IAAI,CAAClhB,WAAW,IAAIqd,gBAAgB;QAC5C;QACA,MAAMyB,YAAY,GAAG,IAAIoC,CAAC,CAAC5X,IAAI,CAAC;QAChC,MAAMrK,IAAI,GAAG2M,IAAI,CAAC1M,OAAO;QACzB,IAAI,IAAI,CAACqe,WAAW,CAAC,IAAIK,UAAU,EAAE;UACjC,IAAI,CAACJ,WAAW,CAAC,CAAC1W,IAAI,CAAC7H,IAAI,EAAE6f,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC;QACvE,CAAC,MACI;UACDJ,uBAAuB,CAAC,IAAI,EAAE3f,IAAI,EAAE6f,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC;QAC9E;QACA,OAAOF,YAAY;MACvB;MACAqC,KAAKA,CAACnC,UAAU,EAAE;QACd,OAAO,IAAI,CAAC9B,IAAI,CAAC,IAAI,EAAE8B,UAAU,CAAC;MACtC;MACAoC,OAAOA,CAACC,SAAS,EAAE;QAAA,IAAAC,kBAAA;QACf;QACA,IAAIJ,CAAC,IAAAI,kBAAA,GAAG,IAAI,CAACthB,WAAW,cAAAshB,kBAAA,uBAAhBA,kBAAA,CAAmB3B,MAAM,CAACqB,OAAO,CAAC;QAC1C,IAAI,CAACE,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;UAC/BA,CAAC,GAAG7D,gBAAgB;QACxB;QACA,MAAMyB,YAAY,GAAG,IAAIoC,CAAC,CAAC5X,IAAI,CAAC;QAChCwV,YAAY,CAACrB,aAAa,CAAC,GAAGA,aAAa;QAC3C,MAAMxe,IAAI,GAAG2M,IAAI,CAAC1M,OAAO;QACzB,IAAI,IAAI,CAACqe,WAAW,CAAC,IAAIK,UAAU,EAAE;UACjC,IAAI,CAACJ,WAAW,CAAC,CAAC1W,IAAI,CAAC7H,IAAI,EAAE6f,YAAY,EAAEuC,SAAS,EAAEA,SAAS,CAAC;QACpE,CAAC,MACI;UACDzC,uBAAuB,CAAC,IAAI,EAAE3f,IAAI,EAAE6f,YAAY,EAAEuC,SAAS,EAAEA,SAAS,CAAC;QAC3E;QACA,OAAOvC,YAAY;MACvB;IACJ;IACA;IACA;IACAzB,gBAAgB,CAAC,SAAS,CAAC,GAAGA,gBAAgB,CAACtU,OAAO;IACtDsU,gBAAgB,CAAC,QAAQ,CAAC,GAAGA,gBAAgB,CAACC,MAAM;IACpDD,gBAAgB,CAAC,MAAM,CAAC,GAAGA,gBAAgB,CAAC2C,IAAI;IAChD3C,gBAAgB,CAAC,KAAK,CAAC,GAAGA,gBAAgB,CAAC8C,GAAG;IAC9C,MAAMoB,aAAa,GAAItjB,MAAM,CAACuK,aAAa,CAAC,GAAGvK,MAAM,CAAC,SAAS,CAAE;IACjEA,MAAM,CAAC,SAAS,CAAC,GAAGof,gBAAgB;IACpC,MAAMmE,iBAAiB,GAAGrjB,UAAU,CAAC,aAAa,CAAC;IACnD,SAASyL,SAASA,CAAC6X,IAAI,EAAE;MACrB,MAAMrS,KAAK,GAAGqS,IAAI,CAACpZ,SAAS;MAC5B,MAAM0F,IAAI,GAAG7D,8BAA8B,CAACkF,KAAK,EAAE,MAAM,CAAC;MAC1D,IAAIrB,IAAI,KAAKA,IAAI,CAACtB,QAAQ,KAAK,KAAK,IAAI,CAACsB,IAAI,CAACG,YAAY,CAAC,EAAE;QACzD;QACA;QACA;MACJ;MACA,MAAMwT,YAAY,GAAGtS,KAAK,CAAC8N,IAAI;MAC/B;MACA9N,KAAK,CAAC3G,UAAU,CAAC,GAAGiZ,YAAY;MAChCD,IAAI,CAACpZ,SAAS,CAAC6U,IAAI,GAAG,UAAU+C,SAAS,EAAEC,QAAQ,EAAE;QACjD,MAAMyB,OAAO,GAAG,IAAItE,gBAAgB,CAAC,CAACtU,OAAO,EAAEuU,MAAM,KAAK;UACtDoE,YAAY,CAACha,IAAI,CAAC,IAAI,EAAEqB,OAAO,EAAEuU,MAAM,CAAC;QAC5C,CAAC,CAAC;QACF,OAAOqE,OAAO,CAACzE,IAAI,CAAC+C,SAAS,EAAEC,QAAQ,CAAC;MAC5C,CAAC;MACDuB,IAAI,CAACD,iBAAiB,CAAC,GAAG,IAAI;IAClC;IACAxQ,GAAG,CAACpH,SAAS,GAAGA,SAAS;IACzB,SAASgY,OAAOA,CAACpiB,EAAE,EAAE;MACjB,OAAO,UAAUgI,IAAI,EAAEG,IAAI,EAAE;QACzB,IAAIka,aAAa,GAAGriB,EAAE,CAACoH,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACxC,IAAIka,aAAa,YAAYxE,gBAAgB,EAAE;UAC3C,OAAOwE,aAAa;QACxB;QACA,IAAIC,IAAI,GAAGD,aAAa,CAAC7hB,WAAW;QACpC,IAAI,CAAC8hB,IAAI,CAACN,iBAAiB,CAAC,EAAE;UAC1B5X,SAAS,CAACkY,IAAI,CAAC;QACnB;QACA,OAAOD,aAAa;MACxB,CAAC;IACL;IACA,IAAIN,aAAa,EAAE;MACf3X,SAAS,CAAC2X,aAAa,CAAC;MACxB7X,WAAW,CAACzL,MAAM,EAAE,OAAO,EAAGgG,QAAQ,IAAK2d,OAAO,CAAC3d,QAAQ,CAAC,CAAC;IACjE;IACA;IACA4b,OAAO,CAACjU,IAAI,CAACzN,UAAU,CAAC,uBAAuB,CAAC,CAAC,GAAGme,sBAAsB;IAC1E,OAAOe,gBAAgB;EAC3B,CAAC,CAAC;AACN;AAEA,SAAS0E,aAAaA,CAACnW,IAAI,EAAE;EACzB;EACA;EACAA,IAAI,CAACrM,YAAY,CAAC,UAAU,EAAGtB,MAAM,IAAK;IACtC;IACA,MAAM+jB,wBAAwB,GAAGC,QAAQ,CAAC5Z,SAAS,CAACH,QAAQ;IAC5D,MAAMga,wBAAwB,GAAGpW,UAAU,CAAC,kBAAkB,CAAC;IAC/D,MAAMqW,cAAc,GAAGrW,UAAU,CAAC,SAAS,CAAC;IAC5C,MAAMsW,YAAY,GAAGtW,UAAU,CAAC,OAAO,CAAC;IACxC,MAAMuW,mBAAmB,GAAG,SAASna,QAAQA,CAAA,EAAG;MAC5C,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;QAC5B,MAAM+J,gBAAgB,GAAG,IAAI,CAACiQ,wBAAwB,CAAC;QACvD,IAAIjQ,gBAAgB,EAAE;UAClB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;YACxC,OAAO+P,wBAAwB,CAACta,IAAI,CAACuK,gBAAgB,CAAC;UAC1D,CAAC,MACI;YACD,OAAO7J,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAACuK,gBAAgB,CAAC;UAC3D;QACJ;QACA,IAAI,IAAI,KAAK4N,OAAO,EAAE;UAClB,MAAMyC,aAAa,GAAGrkB,MAAM,CAACkkB,cAAc,CAAC;UAC5C,IAAIG,aAAa,EAAE;YACf,OAAON,wBAAwB,CAACta,IAAI,CAAC4a,aAAa,CAAC;UACvD;QACJ;QACA,IAAI,IAAI,KAAKvjB,KAAK,EAAE;UAChB,MAAMwjB,WAAW,GAAGtkB,MAAM,CAACmkB,YAAY,CAAC;UACxC,IAAIG,WAAW,EAAE;YACb,OAAOP,wBAAwB,CAACta,IAAI,CAAC6a,WAAW,CAAC;UACrD;QACJ;MACJ;MACA,OAAOP,wBAAwB,CAACta,IAAI,CAAC,IAAI,CAAC;IAC9C,CAAC;IACD2a,mBAAmB,CAACH,wBAAwB,CAAC,GAAGF,wBAAwB;IACxEC,QAAQ,CAAC5Z,SAAS,CAACH,QAAQ,GAAGma,mBAAmB;IACjD;IACA,MAAMG,sBAAsB,GAAGpa,MAAM,CAACC,SAAS,CAACH,QAAQ;IACxD,MAAMua,wBAAwB,GAAG,kBAAkB;IACnDra,MAAM,CAACC,SAAS,CAACH,QAAQ,GAAG,YAAY;MACpC,IAAI,OAAO2X,OAAO,KAAK,UAAU,IAAI,IAAI,YAAYA,OAAO,EAAE;QAC1D,OAAO4C,wBAAwB;MACnC;MACA,OAAOD,sBAAsB,CAAC9a,IAAI,CAAC,IAAI,CAAC;IAC5C,CAAC;EACL,CAAC,CAAC;AACN;AAEA,SAASgD,cAAcA,CAACsG,GAAG,EAAE7M,MAAM,EAAEue,UAAU,EAAEC,MAAM,EAAE7K,SAAS,EAAE;EAChE,MAAM1O,MAAM,GAAGwC,IAAI,CAACzN,UAAU,CAACwkB,MAAM,CAAC;EACtC,IAAIxe,MAAM,CAACiF,MAAM,CAAC,EAAE;IAChB;EACJ;EACA,MAAMwZ,cAAc,GAAIze,MAAM,CAACiF,MAAM,CAAC,GAAGjF,MAAM,CAACwe,MAAM,CAAE;EACxDxe,MAAM,CAACwe,MAAM,CAAC,GAAG,UAAUvkB,IAAI,EAAEykB,IAAI,EAAEvb,OAAO,EAAE;IAC5C,IAAIub,IAAI,IAAIA,IAAI,CAACxa,SAAS,EAAE;MACxByP,SAAS,CAAC8D,OAAO,CAAC,UAAUjb,QAAQ,EAAE;QAClC,MAAMC,MAAM,GAAI,GAAE8hB,UAAW,IAAGC,MAAO,IAAG,GAAGhiB,QAAQ;QACrD,MAAM0H,SAAS,GAAGwa,IAAI,CAACxa,SAAS;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI;UACA,IAAIA,SAAS,CAAC3I,cAAc,CAACiB,QAAQ,CAAC,EAAE;YACpC,MAAMmiB,UAAU,GAAG9R,GAAG,CAAC9G,8BAA8B,CAAC7B,SAAS,EAAE1H,QAAQ,CAAC;YAC1E,IAAImiB,UAAU,IAAIA,UAAU,CAAC9b,KAAK,EAAE;cAChC8b,UAAU,CAAC9b,KAAK,GAAGgK,GAAG,CAAC1G,mBAAmB,CAACwY,UAAU,CAAC9b,KAAK,EAAEpG,MAAM,CAAC;cACpEoQ,GAAG,CAACvG,iBAAiB,CAACoY,IAAI,CAACxa,SAAS,EAAE1H,QAAQ,EAAEmiB,UAAU,CAAC;YAC/D,CAAC,MACI,IAAIza,SAAS,CAAC1H,QAAQ,CAAC,EAAE;cAC1B0H,SAAS,CAAC1H,QAAQ,CAAC,GAAGqQ,GAAG,CAAC1G,mBAAmB,CAACjC,SAAS,CAAC1H,QAAQ,CAAC,EAAEC,MAAM,CAAC;YAC9E;UACJ,CAAC,MACI,IAAIyH,SAAS,CAAC1H,QAAQ,CAAC,EAAE;YAC1B0H,SAAS,CAAC1H,QAAQ,CAAC,GAAGqQ,GAAG,CAAC1G,mBAAmB,CAACjC,SAAS,CAAC1H,QAAQ,CAAC,EAAEC,MAAM,CAAC;UAC9E;QACJ,CAAC,CACD,MAAM;UACF;UACA;QAAA;MAER,CAAC,CAAC;IACN;IACA,OAAOgiB,cAAc,CAAClb,IAAI,CAACvD,MAAM,EAAE/F,IAAI,EAAEykB,IAAI,EAAEvb,OAAO,CAAC;EAC3D,CAAC;EACD0J,GAAG,CAACxG,qBAAqB,CAACrG,MAAM,CAACwe,MAAM,CAAC,EAAEC,cAAc,CAAC;AAC7D;AAEA,SAASG,SAASA,CAACnX,IAAI,EAAE;EACrBA,IAAI,CAACrM,YAAY,CAAC,MAAM,EAAE,CAACtB,MAAM,EAAE2N,IAAI,EAAEoF,GAAG,KAAK;IAC7C;IACA;IACA,MAAMiH,UAAU,GAAGW,eAAe,CAAC3a,MAAM,CAAC;IAC1C+S,GAAG,CAACvH,iBAAiB,GAAGA,iBAAiB;IACzCuH,GAAG,CAACtH,WAAW,GAAGA,WAAW;IAC7BsH,GAAG,CAACrH,aAAa,GAAGA,aAAa;IACjCqH,GAAG,CAACnH,cAAc,GAAGA,cAAc;IACnC;IACA;IACA;IACA;IACA;IACA,MAAMmZ,0BAA0B,GAAGpX,IAAI,CAACzN,UAAU,CAAC,qBAAqB,CAAC;IACzE,MAAM8kB,uBAAuB,GAAGrX,IAAI,CAACzN,UAAU,CAAC,kBAAkB,CAAC;IACnE,IAAIF,MAAM,CAACglB,uBAAuB,CAAC,EAAE;MACjChlB,MAAM,CAAC+kB,0BAA0B,CAAC,GAAG/kB,MAAM,CAACglB,uBAAuB,CAAC;IACxE;IACA,IAAIhlB,MAAM,CAAC+kB,0BAA0B,CAAC,EAAE;MACpCpX,IAAI,CAACoX,0BAA0B,CAAC,GAAGpX,IAAI,CAACqX,uBAAuB,CAAC,GAC5DhlB,MAAM,CAAC+kB,0BAA0B,CAAC;IAC1C;IACAhS,GAAG,CAAClH,mBAAmB,GAAGA,mBAAmB;IAC7CkH,GAAG,CAACxH,gBAAgB,GAAGA,gBAAgB;IACvCwH,GAAG,CAACjH,UAAU,GAAGA,UAAU;IAC3BiH,GAAG,CAAC/G,oBAAoB,GAAGA,oBAAoB;IAC/C+G,GAAG,CAAC9G,8BAA8B,GAAGA,8BAA8B;IACnE8G,GAAG,CAAC7G,YAAY,GAAGA,YAAY;IAC/B6G,GAAG,CAAC5G,UAAU,GAAGA,UAAU;IAC3B4G,GAAG,CAAC3G,UAAU,GAAGA,UAAU;IAC3B2G,GAAG,CAAC1G,mBAAmB,GAAGA,mBAAmB;IAC7C0G,GAAG,CAACzG,gBAAgB,GAAGA,gBAAgB;IACvCyG,GAAG,CAACxG,qBAAqB,GAAGA,qBAAqB;IACjDwG,GAAG,CAACvG,iBAAiB,GAAGrC,MAAM,CAAC2C,cAAc;IAC7CiG,GAAG,CAACtG,cAAc,GAAGA,cAAc;IACnCsG,GAAG,CAAChH,gBAAgB,GAAG,OAAO;MAC1BuG,aAAa;MACbD,oBAAoB;MACpB2H,UAAU;MACVlL,SAAS;MACTC,KAAK;MACLH,MAAM;MACNpB,QAAQ;MACRC,SAAS;MACTC,kBAAkB;MAClBN,sBAAsB;MACtBC;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AAEA,SAAS4X,WAAWA,CAACtX,IAAI,EAAE;EACvBsQ,YAAY,CAACtQ,IAAI,CAAC;EAClBmW,aAAa,CAACnW,IAAI,CAAC;EACnBmX,SAAS,CAACnX,IAAI,CAAC;AACnB;AAEA,MAAMuX,MAAM,GAAGxY,QAAQ,CAAC,CAAC;AACzBuY,WAAW,CAACC,MAAM,CAAC;AACnB/J,YAAY,CAAC+J,MAAM,CAAC", "sources": ["./src/polyfills.ts", "./src/zone-flags.ts", "./node_modules/zone.js/fesm2015/zone.js"], "sourcesContent": ["/**\n * This file includes polyfills needed by <PERSON><PERSON> and is loaded before the app.\n * You can add your own extra polyfills to this file.\n *\n * This file is divided into 2 sections:\n *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.\n *   2. Application imports. Files imported after ZoneJS that should be loaded before your main\n *      file.\n *\n * The current setup is for so-called \"evergreen\" browsers; the last versions of browsers that\n * automatically update themselves. This includes recent versions of Safari, Chrome (including\n * Opera), Edge on the desktop, and iOS and Chrome on mobile.\n *\n * Learn more in https://angular.io/guide/browser-support\n */\n\n/***************************************************************************************************\n * BROWSER POLYFILLS\n */\n\n/**\n * By default, zone.js will patch all possible macroTask and DomEvents\n * user can disable parts of macroTask/DomEvents patch by setting following flags\n * because those flags need to be set before `zone.js` being loaded, and webpack\n * will put import in the top of bundle, so user need to create a separate file\n * in this directory (for example: zone-flags.ts), and put the following flags\n * into that file, and then add the following code before importing zone.js.\n * import './zone-flags';\n *\n * The flags allowed in zone-flags.ts are listed here.\n *\n * The following flags will work for all browsers.\n *\n * (window as any).__Zone_disable_requestAnimationFrame = true; // disable patch requestAnimationFrame\n * (window as any).__Zone_disable_on_property = true; // disable patch onProperty such as onclick\n * (window as any).__zone_symbol__UNPATCHED_EVENTS = ['scroll', 'mousemove']; // disable patch specified eventNames\n *\n *  in IE/Edge developer tools, the addEventListener will also be wrapped by zone.js\n *  with the following flag, it will bypass `zone.js` patch for IE/Edge\n *\n *  (window as any).__Zone_enable_cross_context_check = true;\n *\n */\n \nimport './zone-flags';\n\n/***************************************************************************************************\n * Zone JS is required by default for Angular itself.\n */\nimport 'zone.js';  // Included with Angular CLI.\n\n\n/***************************************************************************************************\n * APPLICATION IMPORTS\n */\n", "/**\n * Prevents Angular change detection from\n * running with certain Web Component callbacks\n */\n// eslint-disable-next-line no-underscore-dangle\n(window as any).__Zone_disable_customElements = true;\n", "'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\nconst global = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n    const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n    return symbolPrefix + name;\n}\nfunction initZone() {\n    const performance = global['performance'];\n    function mark(name) {\n        performance && performance['mark'] && performance['mark'](name);\n    }\n    function performanceMeasure(name, label) {\n        performance && performance['measure'] && performance['measure'](name, label);\n    }\n    mark('Zone');\n    class ZoneImpl {\n        // tslint:disable-next-line:require-internal-with-underscore\n        static { this.__symbol__ = __symbol__; }\n        static assertZonePatched() {\n            if (global['Promise'] !== patches['ZoneAwarePromise']) {\n                throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' +\n                    'has been overwritten.\\n' +\n                    'Most likely cause is that a Promise polyfill has been loaded ' +\n                    'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' +\n                    'If you must load one, do so before loading zone.js.)');\n            }\n        }\n        static get root() {\n            let zone = ZoneImpl.current;\n            while (zone.parent) {\n                zone = zone.parent;\n            }\n            return zone;\n        }\n        static get current() {\n            return _currentZoneFrame.zone;\n        }\n        static get currentTask() {\n            return _currentTask;\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        static __load_patch(name, fn, ignoreDuplicate = false) {\n            if (patches.hasOwnProperty(name)) {\n                // `checkDuplicate` option is defined from global variable\n                // so it works for all modules.\n                // `ignoreDuplicate` can work for the specified module\n                const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n                if (!ignoreDuplicate && checkDuplicate) {\n                    throw Error('Already loaded patch: ' + name);\n                }\n            }\n            else if (!global['__Zone_disable_' + name]) {\n                const perfName = 'Zone:' + name;\n                mark(perfName);\n                patches[name] = fn(global, ZoneImpl, _api);\n                performanceMeasure(perfName, perfName);\n            }\n        }\n        get parent() {\n            return this._parent;\n        }\n        get name() {\n            return this._name;\n        }\n        constructor(parent, zoneSpec) {\n            this._parent = parent;\n            this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n            this._properties = (zoneSpec && zoneSpec.properties) || {};\n            this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n        }\n        get(key) {\n            const zone = this.getZoneWith(key);\n            if (zone)\n                return zone._properties[key];\n        }\n        getZoneWith(key) {\n            let current = this;\n            while (current) {\n                if (current._properties.hasOwnProperty(key)) {\n                    return current;\n                }\n                current = current._parent;\n            }\n            return null;\n        }\n        fork(zoneSpec) {\n            if (!zoneSpec)\n                throw new Error('ZoneSpec required!');\n            return this._zoneDelegate.fork(this, zoneSpec);\n        }\n        wrap(callback, source) {\n            if (typeof callback !== 'function') {\n                throw new Error('Expecting function got: ' + callback);\n            }\n            const _callback = this._zoneDelegate.intercept(this, callback, source);\n            const zone = this;\n            return function () {\n                return zone.runGuarded(_callback, this, arguments, source);\n            };\n        }\n        run(callback, applyThis, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runGuarded(callback, applyThis = null, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                try {\n                    return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runTask(task, applyThis, applyArgs) {\n            if (task.zone != this) {\n                throw new Error('A task can only be run in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name +\n                    '; Execution: ' +\n                    this.name +\n                    ')');\n            }\n            // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n            // will run in notScheduled(canceled) state, we should not try to\n            // run such kind of task but just return\n            if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n                return;\n            }\n            const reEntryGuard = task.state != running;\n            reEntryGuard && task._transitionTo(running, scheduled);\n            task.runCount++;\n            const previousTask = _currentTask;\n            _currentTask = task;\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n                    task.cancelFn = undefined;\n                }\n                try {\n                    return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                // if the task's state is notScheduled or unknown, then it has already been cancelled\n                // we should not reset the state to scheduled\n                if (task.state !== notScheduled && task.state !== unknown) {\n                    if (task.type == eventTask || (task.data && task.data.isPeriodic)) {\n                        reEntryGuard && task._transitionTo(scheduled, running);\n                    }\n                    else {\n                        task.runCount = 0;\n                        this._updateTaskCount(task, -1);\n                        reEntryGuard &&\n                            task._transitionTo(notScheduled, running, notScheduled);\n                    }\n                }\n                _currentZoneFrame = _currentZoneFrame.parent;\n                _currentTask = previousTask;\n            }\n        }\n        scheduleTask(task) {\n            if (task.zone && task.zone !== this) {\n                // check if the task was rescheduled, the newZone\n                // should not be the children of the original zone\n                let newZone = this;\n                while (newZone) {\n                    if (newZone === task.zone) {\n                        throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n                    }\n                    newZone = newZone.parent;\n                }\n            }\n            task._transitionTo(scheduling, notScheduled);\n            const zoneDelegates = [];\n            task._zoneDelegates = zoneDelegates;\n            task._zone = this;\n            try {\n                task = this._zoneDelegate.scheduleTask(this, task);\n            }\n            catch (err) {\n                // should set task's state to unknown when scheduleTask throw error\n                // because the err may from reschedule, so the fromState maybe notScheduled\n                task._transitionTo(unknown, scheduling, notScheduled);\n                // TODO: @JiaLiPassion, should we check the result from handleError?\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            if (task._zoneDelegates === zoneDelegates) {\n                // we have to check because internally the delegate can reschedule the task.\n                this._updateTaskCount(task, 1);\n            }\n            if (task.state == scheduling) {\n                task._transitionTo(scheduled, scheduling);\n            }\n            return task;\n        }\n        scheduleMicroTask(source, callback, data, customSchedule) {\n            return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n        }\n        scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n        }\n        scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n        }\n        cancelTask(task) {\n            if (task.zone != this)\n                throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name +\n                    '; Execution: ' +\n                    this.name +\n                    ')');\n            if (task.state !== scheduled && task.state !== running) {\n                return;\n            }\n            task._transitionTo(canceling, scheduled, running);\n            try {\n                this._zoneDelegate.cancelTask(this, task);\n            }\n            catch (err) {\n                // if error occurs when cancelTask, transit the state to unknown\n                task._transitionTo(unknown, canceling);\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            this._updateTaskCount(task, -1);\n            task._transitionTo(notScheduled, canceling);\n            task.runCount = 0;\n            return task;\n        }\n        _updateTaskCount(task, count) {\n            const zoneDelegates = task._zoneDelegates;\n            if (count == -1) {\n                task._zoneDelegates = null;\n            }\n            for (let i = 0; i < zoneDelegates.length; i++) {\n                zoneDelegates[i]._updateTaskCount(task.type, count);\n            }\n        }\n    }\n    const DELEGATE_ZS = {\n        name: '',\n        onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n        onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n        onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n        onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task),\n    };\n    class _ZoneDelegate {\n        get zone() {\n            return this._zone;\n        }\n        constructor(zone, parentDelegate, zoneSpec) {\n            this._taskCounts = {\n                'microTask': 0,\n                'macroTask': 0,\n                'eventTask': 0,\n            };\n            this._zone = zone;\n            this._parentDelegate = parentDelegate;\n            this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n            this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n            this._forkCurrZone =\n                zoneSpec && (zoneSpec.onFork ? this._zone : parentDelegate._forkCurrZone);\n            this._interceptZS =\n                zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n            this._interceptDlgt =\n                zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n            this._interceptCurrZone =\n                zoneSpec && (zoneSpec.onIntercept ? this._zone : parentDelegate._interceptCurrZone);\n            this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n            this._invokeDlgt =\n                zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n            this._invokeCurrZone =\n                zoneSpec && (zoneSpec.onInvoke ? this._zone : parentDelegate._invokeCurrZone);\n            this._handleErrorZS =\n                zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n            this._handleErrorDlgt =\n                zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n            this._handleErrorCurrZone =\n                zoneSpec && (zoneSpec.onHandleError ? this._zone : parentDelegate._handleErrorCurrZone);\n            this._scheduleTaskZS =\n                zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n            this._scheduleTaskDlgt =\n                zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n            this._scheduleTaskCurrZone =\n                zoneSpec && (zoneSpec.onScheduleTask ? this._zone : parentDelegate._scheduleTaskCurrZone);\n            this._invokeTaskZS =\n                zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n            this._invokeTaskDlgt =\n                zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n            this._invokeTaskCurrZone =\n                zoneSpec && (zoneSpec.onInvokeTask ? this._zone : parentDelegate._invokeTaskCurrZone);\n            this._cancelTaskZS =\n                zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n            this._cancelTaskDlgt =\n                zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n            this._cancelTaskCurrZone =\n                zoneSpec && (zoneSpec.onCancelTask ? this._zone : parentDelegate._cancelTaskCurrZone);\n            this._hasTaskZS = null;\n            this._hasTaskDlgt = null;\n            this._hasTaskDlgtOwner = null;\n            this._hasTaskCurrZone = null;\n            const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n            const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n            if (zoneSpecHasTask || parentHasTask) {\n                // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n                // a case all task related interceptors must go through this ZD. We can't short circuit it.\n                this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n                this._hasTaskDlgt = parentDelegate;\n                this._hasTaskDlgtOwner = this;\n                this._hasTaskCurrZone = this._zone;\n                if (!zoneSpec.onScheduleTask) {\n                    this._scheduleTaskZS = DELEGATE_ZS;\n                    this._scheduleTaskDlgt = parentDelegate;\n                    this._scheduleTaskCurrZone = this._zone;\n                }\n                if (!zoneSpec.onInvokeTask) {\n                    this._invokeTaskZS = DELEGATE_ZS;\n                    this._invokeTaskDlgt = parentDelegate;\n                    this._invokeTaskCurrZone = this._zone;\n                }\n                if (!zoneSpec.onCancelTask) {\n                    this._cancelTaskZS = DELEGATE_ZS;\n                    this._cancelTaskDlgt = parentDelegate;\n                    this._cancelTaskCurrZone = this._zone;\n                }\n            }\n        }\n        fork(targetZone, zoneSpec) {\n            return this._forkZS\n                ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec)\n                : new ZoneImpl(targetZone, zoneSpec);\n        }\n        intercept(targetZone, callback, source) {\n            return this._interceptZS\n                ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source)\n                : callback;\n        }\n        invoke(targetZone, callback, applyThis, applyArgs, source) {\n            return this._invokeZS\n                ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source)\n                : callback.apply(applyThis, applyArgs);\n        }\n        handleError(targetZone, error) {\n            return this._handleErrorZS\n                ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error)\n                : true;\n        }\n        scheduleTask(targetZone, task) {\n            let returnTask = task;\n            if (this._scheduleTaskZS) {\n                if (this._hasTaskZS) {\n                    returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n                }\n                returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n                if (!returnTask)\n                    returnTask = task;\n            }\n            else {\n                if (task.scheduleFn) {\n                    task.scheduleFn(task);\n                }\n                else if (task.type == microTask) {\n                    scheduleMicroTask(task);\n                }\n                else {\n                    throw new Error('Task is missing scheduleFn.');\n                }\n            }\n            return returnTask;\n        }\n        invokeTask(targetZone, task, applyThis, applyArgs) {\n            return this._invokeTaskZS\n                ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs)\n                : task.callback.apply(applyThis, applyArgs);\n        }\n        cancelTask(targetZone, task) {\n            let value;\n            if (this._cancelTaskZS) {\n                value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n            }\n            else {\n                if (!task.cancelFn) {\n                    throw Error('Task is not cancelable');\n                }\n                value = task.cancelFn(task);\n            }\n            return value;\n        }\n        hasTask(targetZone, isEmpty) {\n            // hasTask should not throw error so other ZoneDelegate\n            // can still trigger hasTask callback\n            try {\n                this._hasTaskZS &&\n                    this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n            }\n            catch (err) {\n                this.handleError(targetZone, err);\n            }\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _updateTaskCount(type, count) {\n            const counts = this._taskCounts;\n            const prev = counts[type];\n            const next = (counts[type] = prev + count);\n            if (next < 0) {\n                throw new Error('More tasks executed then were scheduled.');\n            }\n            if (prev == 0 || next == 0) {\n                const isEmpty = {\n                    microTask: counts['microTask'] > 0,\n                    macroTask: counts['macroTask'] > 0,\n                    eventTask: counts['eventTask'] > 0,\n                    change: type,\n                };\n                this.hasTask(this._zone, isEmpty);\n            }\n        }\n    }\n    class ZoneTask {\n        constructor(type, source, callback, options, scheduleFn, cancelFn) {\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zone = null;\n            this.runCount = 0;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zoneDelegates = null;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._state = 'notScheduled';\n            this.type = type;\n            this.source = source;\n            this.data = options;\n            this.scheduleFn = scheduleFn;\n            this.cancelFn = cancelFn;\n            if (!callback) {\n                throw new Error('callback is not defined');\n            }\n            this.callback = callback;\n            const self = this;\n            // TODO: @JiaLiPassion options should have interface\n            if (type === eventTask && options && options.useG) {\n                this.invoke = ZoneTask.invokeTask;\n            }\n            else {\n                this.invoke = function () {\n                    return ZoneTask.invokeTask.call(global, self, this, arguments);\n                };\n            }\n        }\n        static invokeTask(task, target, args) {\n            if (!task) {\n                task = this;\n            }\n            _numberOfNestedTaskFrames++;\n            try {\n                task.runCount++;\n                return task.zone.runTask(task, target, args);\n            }\n            finally {\n                if (_numberOfNestedTaskFrames == 1) {\n                    drainMicroTaskQueue();\n                }\n                _numberOfNestedTaskFrames--;\n            }\n        }\n        get zone() {\n            return this._zone;\n        }\n        get state() {\n            return this._state;\n        }\n        cancelScheduleRequest() {\n            this._transitionTo(notScheduled, scheduling);\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _transitionTo(toState, fromState1, fromState2) {\n            if (this._state === fromState1 || this._state === fromState2) {\n                this._state = toState;\n                if (toState == notScheduled) {\n                    this._zoneDelegates = null;\n                }\n            }\n            else {\n                throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? \" or '\" + fromState2 + \"'\" : ''}, was '${this._state}'.`);\n            }\n        }\n        toString() {\n            if (this.data && typeof this.data.handleId !== 'undefined') {\n                return this.data.handleId.toString();\n            }\n            else {\n                return Object.prototype.toString.call(this);\n            }\n        }\n        // add toJSON method to prevent cyclic error when\n        // call JSON.stringify(zoneTask)\n        toJSON() {\n            return {\n                type: this.type,\n                state: this.state,\n                source: this.source,\n                zone: this.zone.name,\n                runCount: this.runCount,\n            };\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  MICROTASK QUEUE\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const symbolSetTimeout = __symbol__('setTimeout');\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    let _microTaskQueue = [];\n    let _isDrainingMicrotaskQueue = false;\n    let nativeMicroTaskQueuePromise;\n    function nativeScheduleMicroTask(func) {\n        if (!nativeMicroTaskQueuePromise) {\n            if (global[symbolPromise]) {\n                nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n            }\n        }\n        if (nativeMicroTaskQueuePromise) {\n            let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n            if (!nativeThen) {\n                // native Promise is not patchable, we need to use `then` directly\n                // issue 1078\n                nativeThen = nativeMicroTaskQueuePromise['then'];\n            }\n            nativeThen.call(nativeMicroTaskQueuePromise, func);\n        }\n        else {\n            global[symbolSetTimeout](func, 0);\n        }\n    }\n    function scheduleMicroTask(task) {\n        // if we are not running in any task, and there has not been anything scheduled\n        // we must bootstrap the initial task creation by manually scheduling the drain\n        if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n            // We are not running in Task, so we need to kickstart the microtask queue.\n            nativeScheduleMicroTask(drainMicroTaskQueue);\n        }\n        task && _microTaskQueue.push(task);\n    }\n    function drainMicroTaskQueue() {\n        if (!_isDrainingMicrotaskQueue) {\n            _isDrainingMicrotaskQueue = true;\n            while (_microTaskQueue.length) {\n                const queue = _microTaskQueue;\n                _microTaskQueue = [];\n                for (let i = 0; i < queue.length; i++) {\n                    const task = queue[i];\n                    try {\n                        task.zone.runTask(task, null, null);\n                    }\n                    catch (error) {\n                        _api.onUnhandledError(error);\n                    }\n                }\n            }\n            _api.microtaskDrainDone();\n            _isDrainingMicrotaskQueue = false;\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  BOOTSTRAP\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const NO_ZONE = { name: 'NO ZONE' };\n    const notScheduled = 'notScheduled', scheduling = 'scheduling', scheduled = 'scheduled', running = 'running', canceling = 'canceling', unknown = 'unknown';\n    const microTask = 'microTask', macroTask = 'macroTask', eventTask = 'eventTask';\n    const patches = {};\n    const _api = {\n        symbol: __symbol__,\n        currentZoneFrame: () => _currentZoneFrame,\n        onUnhandledError: noop,\n        microtaskDrainDone: noop,\n        scheduleMicroTask: scheduleMicroTask,\n        showUncaughtError: () => !ZoneImpl[__symbol__('ignoreConsoleErrorUncaughtError')],\n        patchEventTarget: () => [],\n        patchOnProperties: noop,\n        patchMethod: () => noop,\n        bindArguments: () => [],\n        patchThen: () => noop,\n        patchMacroTask: () => noop,\n        patchEventPrototype: () => noop,\n        isIEOrEdge: () => false,\n        getGlobalObjects: () => undefined,\n        ObjectDefineProperty: () => noop,\n        ObjectGetOwnPropertyDescriptor: () => undefined,\n        ObjectCreate: () => undefined,\n        ArraySlice: () => [],\n        patchClass: () => noop,\n        wrapWithCurrentZone: () => noop,\n        filterProperties: () => [],\n        attachOriginToPatched: () => noop,\n        _redefineProperty: () => noop,\n        patchCallbacks: () => noop,\n        nativeScheduleMicroTask: nativeScheduleMicroTask,\n    };\n    let _currentZoneFrame = { parent: null, zone: new ZoneImpl(null, null) };\n    let _currentTask = null;\n    let _numberOfNestedTaskFrames = 0;\n    function noop() { }\n    performanceMeasure('Zone', 'Zone');\n    return ZoneImpl;\n}\n\nfunction loadZone() {\n    // if global['Zone'] already exists (maybe zone.js was already loaded or\n    // some other lib also registered a global object named Zone), we may need\n    // to throw an error, but sometimes user may not want this error.\n    // For example,\n    // we have two web pages, page1 includes zone.js, page2 doesn't.\n    // and the 1st time user load page1 and page2, everything work fine,\n    // but when user load page2 again, error occurs because global['Zone'] already exists.\n    // so we add a flag to let user choose whether to throw this error or not.\n    // By default, if existing Zone is from zone.js, we will not throw the error.\n    const global = globalThis;\n    const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n    if (global['Zone'] && (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function')) {\n        throw new Error('Zone already loaded.');\n    }\n    // Initialize global `Zone` constant.\n    global['Zone'] ??= initZone();\n    return global['Zone'];\n}\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = __symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = __symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = __symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n    return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n    return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = __symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = (isWindowExists && internalWindow) || globalThis;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (typeof args[i] === 'function') {\n            args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n        }\n    }\n    return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n    const source = prototype.constructor['name'];\n    for (let i = 0; i < fnNames.length; i++) {\n        const name = fnNames[i];\n        const delegate = prototype[name];\n        if (delegate) {\n            const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n            if (!isPropertyWritable(prototypeDesc)) {\n                continue;\n            }\n            prototype[name] = ((delegate) => {\n                const patched = function () {\n                    return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n                };\n                attachOriginToPatched(patched, delegate);\n                return patched;\n            })(delegate);\n        }\n    }\n}\nfunction isPropertyWritable(propertyDesc) {\n    if (!propertyDesc) {\n        return true;\n    }\n    if (propertyDesc.writable === false) {\n        return false;\n    }\n    return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = !('nw' in _global) &&\n    typeof _global.process !== 'undefined' &&\n    _global.process.toString() === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' &&\n    _global.process.toString() === '[object process]' &&\n    !isWebWorker &&\n    !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst wrapFn = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n        return;\n    }\n    let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n    }\n    const target = this || event.target || _global;\n    const listener = target[eventNameSymbol];\n    let result;\n    if (isBrowser && target === internalWindow && event.type === 'error') {\n        // window.onerror have different signature\n        // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n        // and onerror callback will prevent default when callback return true\n        const errorEvent = event;\n        result =\n            listener &&\n                listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n        if (result === true) {\n            event.preventDefault();\n        }\n    }\n    else {\n        result = listener && listener.apply(this, arguments);\n        if (result != undefined && !result) {\n            event.preventDefault();\n        }\n    }\n    return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n    let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n    if (!desc && prototype) {\n        // when patch window object, use prototype to check prop exist or not\n        const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n        if (prototypeDesc) {\n            desc = { enumerable: true, configurable: true };\n        }\n    }\n    // if the descriptor not exists or is not configurable\n    // just return\n    if (!desc || !desc.configurable) {\n        return;\n    }\n    const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n    if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n        return;\n    }\n    // A property descriptor cannot have getter/setter and be writable\n    // deleting the writable and value properties avoids this error:\n    //\n    // TypeError: property descriptors must not specify a value or be writable when a\n    // getter or setter has been specified\n    delete desc.writable;\n    delete desc.value;\n    const originalDescGet = desc.get;\n    const originalDescSet = desc.set;\n    // slice(2) cuz 'onclick' -> 'click', etc\n    const eventName = prop.slice(2);\n    let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n    }\n    desc.set = function (newValue) {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return;\n        }\n        const previousValue = target[eventNameSymbol];\n        if (typeof previousValue === 'function') {\n            target.removeEventListener(eventName, wrapFn);\n        }\n        // issue #978, when onload handler was added before loading zone.js\n        // we should remove it with originalDescSet\n        originalDescSet && originalDescSet.call(target, null);\n        target[eventNameSymbol] = newValue;\n        if (typeof newValue === 'function') {\n            target.addEventListener(eventName, wrapFn, false);\n        }\n    };\n    // The getter would return undefined for unassigned properties but the default value of an\n    // unassigned property is null\n    desc.get = function () {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return null;\n        }\n        const listener = target[eventNameSymbol];\n        if (listener) {\n            return listener;\n        }\n        else if (originalDescGet) {\n            // result will be null when use inline event attribute,\n            // such as <button onclick=\"func();\">OK</button>\n            // because the onclick function is internal raw uncompiled handler\n            // the onclick will be evaluated when first time event was triggered or\n            // the property is accessed, https://github.com/angular/zone.js/issues/525\n            // so we should use original native get to retrieve the handler\n            let value = originalDescGet.call(this);\n            if (value) {\n                desc.set.call(this, value);\n                if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n                    target.removeAttribute(prop);\n                }\n                return value;\n            }\n        }\n        return null;\n    };\n    ObjectDefineProperty(obj, prop, desc);\n    obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n    if (properties) {\n        for (let i = 0; i < properties.length; i++) {\n            patchProperty(obj, 'on' + properties[i], prototype);\n        }\n    }\n    else {\n        const onProperties = [];\n        for (const prop in obj) {\n            if (prop.slice(0, 2) == 'on') {\n                onProperties.push(prop);\n            }\n        }\n        for (let j = 0; j < onProperties.length; j++) {\n            patchProperty(obj, onProperties[j], prototype);\n        }\n    }\n}\nconst originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n    const OriginalClass = _global[className];\n    if (!OriginalClass)\n        return;\n    // keep original class in global\n    _global[zoneSymbol(className)] = OriginalClass;\n    _global[className] = function () {\n        const a = bindArguments(arguments, className);\n        switch (a.length) {\n            case 0:\n                this[originalInstanceKey] = new OriginalClass();\n                break;\n            case 1:\n                this[originalInstanceKey] = new OriginalClass(a[0]);\n                break;\n            case 2:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n                break;\n            case 3:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n                break;\n            case 4:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n                break;\n            default:\n                throw new Error('Arg list too long.');\n        }\n    };\n    // attach original delegate to patched function\n    attachOriginToPatched(_global[className], OriginalClass);\n    const instance = new OriginalClass(function () { });\n    let prop;\n    for (prop in instance) {\n        // https://bugs.webkit.org/show_bug.cgi?id=44721\n        if (className === 'XMLHttpRequest' && prop === 'responseBlob')\n            continue;\n        (function (prop) {\n            if (typeof instance[prop] === 'function') {\n                _global[className].prototype[prop] = function () {\n                    return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n                };\n            }\n            else {\n                ObjectDefineProperty(_global[className].prototype, prop, {\n                    set: function (fn) {\n                        if (typeof fn === 'function') {\n                            this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                            // keep callback in wrapped function so we can\n                            // use it in Function.prototype.toString to return\n                            // the native one.\n                            attachOriginToPatched(this[originalInstanceKey][prop], fn);\n                        }\n                        else {\n                            this[originalInstanceKey][prop] = fn;\n                        }\n                    },\n                    get: function () {\n                        return this[originalInstanceKey][prop];\n                    },\n                });\n            }\n        })(prop);\n    }\n    for (prop in OriginalClass) {\n        if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n            _global[className][prop] = OriginalClass[prop];\n        }\n    }\n}\nfunction patchMethod(target, name, patchFn) {\n    let proto = target;\n    while (proto && !proto.hasOwnProperty(name)) {\n        proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && target[name]) {\n        // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n        proto = target;\n    }\n    const delegateName = zoneSymbol(name);\n    let delegate = null;\n    if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n        delegate = proto[delegateName] = proto[name];\n        // check whether proto[name] is writable\n        // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n        const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n        if (isPropertyWritable(desc)) {\n            const patchDelegate = patchFn(delegate, delegateName, name);\n            proto[name] = function () {\n                return patchDelegate(this, arguments);\n            };\n            attachOriginToPatched(proto[name], delegate);\n        }\n    }\n    return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n    let setNative = null;\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[data.cbIdx] = function () {\n            task.invoke.apply(this, arguments);\n        };\n        setNative.apply(data.target, data.args);\n        return task;\n    }\n    setNative = patchMethod(obj, funcName, (delegate) => function (self, args) {\n        const meta = metaCreator(self, args);\n        if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n            return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(self, args);\n        }\n    });\n}\nfunction attachOriginToPatched(patched, original) {\n    patched[zoneSymbol('OriginalDelegate')] = original;\n}\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\nfunction isIE() {\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n            return true;\n        }\n    }\n    catch (error) { }\n    return false;\n}\nfunction isIEOrEdge() {\n    if (isDetectedIEOrEdge) {\n        return ieOrEdge;\n    }\n    isDetectedIEOrEdge = true;\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n            ieOrEdge = true;\n        }\n    }\n    catch (error) { }\n    return ieOrEdge;\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n// Note that passive event listeners are now supported by most modern browsers,\n// including Chrome, Firefox, Safari, and Edge. There's a pending change that\n// would remove support for legacy browsers by zone.js. Removing `passiveSupported`\n// from the codebase will reduce the final code size for existing apps that still use zone.js.\nlet passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        const options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n            },\n        });\n        // Note: We pass the `options` object as the event handler too. This is not compatible with the\n        // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n        // without an actual handler.\n        window.addEventListener('test', options, options);\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n    useG: true,\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n    const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n    const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n    const ADD_EVENT_LISTENER = (patchOptions && patchOptions.add) || ADD_EVENT_LISTENER_STR;\n    const REMOVE_EVENT_LISTENER = (patchOptions && patchOptions.rm) || REMOVE_EVENT_LISTENER_STR;\n    const LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.listeners) || 'eventListeners';\n    const REMOVE_ALL_LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.rmAll) || 'removeAllListeners';\n    const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n    const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n    const PREPEND_EVENT_LISTENER = 'prependListener';\n    const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n    const invokeTask = function (task, target, event) {\n        // for better performance, check isRemoved which is set\n        // by removeEventListener\n        if (task.isRemoved) {\n            return;\n        }\n        const delegate = task.callback;\n        if (typeof delegate === 'object' && delegate.handleEvent) {\n            // create the bind version of handleEvent when invoke\n            task.callback = (event) => delegate.handleEvent(event);\n            task.originalDelegate = delegate;\n        }\n        // invoke static task.invoke\n        // need to try/catch error here, otherwise, the error in one event listener\n        // will break the executions of the other event listeners. Also error will\n        // not remove the event listener when `once` options is true.\n        let error;\n        try {\n            task.invoke(task, target, [event]);\n        }\n        catch (err) {\n            error = err;\n        }\n        const options = task.options;\n        if (options && typeof options === 'object' && options.once) {\n            // if options.once is true, after invoke once remove listener here\n            // only browser need to do this, nodejs eventEmitter will cal removeListener\n            // inside EventEmitter.once\n            const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n            target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n        }\n        return error;\n    };\n    function globalCallback(context, event, isCapture) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        // event.target is needed for Samsung TV and SourceBuffer\n        // || global is needed https://github.com/angular/zone.js/issues/190\n        const target = context || event.target || _global;\n        const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n        if (tasks) {\n            const errors = [];\n            // invoke all tasks which attached to current target with given event.type and capture = false\n            // for performance concern, if task.length === 1, just invoke\n            if (tasks.length === 1) {\n                const err = invokeTask(tasks[0], target, event);\n                err && errors.push(err);\n            }\n            else {\n                // https://github.com/angular/zone.js/issues/836\n                // copy the tasks array before invoke, to avoid\n                // the callback will remove itself or other listener\n                const copyTasks = tasks.slice();\n                for (let i = 0; i < copyTasks.length; i++) {\n                    if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                        break;\n                    }\n                    const err = invokeTask(copyTasks[i], target, event);\n                    err && errors.push(err);\n                }\n            }\n            // Since there is only one error, we don't need to schedule microTask\n            // to throw the error.\n            if (errors.length === 1) {\n                throw errors[0];\n            }\n            else {\n                for (let i = 0; i < errors.length; i++) {\n                    const err = errors[i];\n                    api.nativeScheduleMicroTask(() => {\n                        throw err;\n                    });\n                }\n            }\n        }\n    }\n    // global shared zoneAwareCallback to handle all event callback with capture = false\n    const globalZoneAwareCallback = function (event) {\n        return globalCallback(this, event, false);\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = true\n    const globalZoneAwareCaptureCallback = function (event) {\n        return globalCallback(this, event, true);\n    };\n    function patchEventTargetMethods(obj, patchOptions) {\n        if (!obj) {\n            return false;\n        }\n        let useGlobalCallback = true;\n        if (patchOptions && patchOptions.useG !== undefined) {\n            useGlobalCallback = patchOptions.useG;\n        }\n        const validateHandler = patchOptions && patchOptions.vh;\n        let checkDuplicate = true;\n        if (patchOptions && patchOptions.chkDup !== undefined) {\n            checkDuplicate = patchOptions.chkDup;\n        }\n        let returnTarget = false;\n        if (patchOptions && patchOptions.rt !== undefined) {\n            returnTarget = patchOptions.rt;\n        }\n        let proto = obj;\n        while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n            proto = ObjectGetPrototypeOf(proto);\n        }\n        if (!proto && obj[ADD_EVENT_LISTENER]) {\n            // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n            proto = obj;\n        }\n        if (!proto) {\n            return false;\n        }\n        if (proto[zoneSymbolAddEventListener]) {\n            return false;\n        }\n        const eventNameToString = patchOptions && patchOptions.eventNameToString;\n        // We use a shared global `taskData` to pass data for `scheduleEventTask`,\n        // eliminating the need to create a new object solely for passing data.\n        // WARNING: This object has a static lifetime, meaning it is not created\n        // each time `addEventListener` is called. It is instantiated only once\n        // and captured by reference inside the `addEventListener` and\n        // `removeEventListener` functions. Do not add any new properties to this\n        // object, as doing so would necessitate maintaining the information\n        // between `addEventListener` calls.\n        const taskData = {};\n        const nativeAddEventListener = (proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER]);\n        const nativeRemoveEventListener = (proto[zoneSymbol(REMOVE_EVENT_LISTENER)] =\n            proto[REMOVE_EVENT_LISTENER]);\n        const nativeListeners = (proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] =\n            proto[LISTENERS_EVENT_LISTENER]);\n        const nativeRemoveAllListeners = (proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] =\n            proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER]);\n        let nativePrependEventListener;\n        if (patchOptions && patchOptions.prepend) {\n            nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] =\n                proto[patchOptions.prepend];\n        }\n        /**\n         * This util function will build an option object with passive option\n         * to handle all possible input from the user.\n         */\n        function buildEventListenerOptions(options, passive) {\n            if (!passiveSupported && typeof options === 'object' && options) {\n                // doesn't support passive but user want to pass an object as options.\n                // this will not work on some old browser, so we just pass a boolean\n                // as useCapture parameter\n                return !!options.capture;\n            }\n            if (!passiveSupported || !passive) {\n                return options;\n            }\n            if (typeof options === 'boolean') {\n                return { capture: options, passive: true };\n            }\n            if (!options) {\n                return { passive: true };\n            }\n            if (typeof options === 'object' && options.passive !== false) {\n                return { ...options, passive: true };\n            }\n            return options;\n        }\n        const customScheduleGlobal = function (task) {\n            // if there is already a task for the eventName + capture,\n            // just return, because we use the shared globalZoneAwareCallback here.\n            if (taskData.isExisting) {\n                return;\n            }\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n        };\n        /**\n         * In the context of events and listeners, this function will be\n         * called at the end by `cancelTask`, which, in turn, calls `task.cancelFn`.\n         * Cancelling a task is primarily used to remove event listeners from\n         * the task target.\n         */\n        const customCancelGlobal = function (task) {\n            // if task is not marked as isRemoved, this call is directly\n            // from Zone.prototype.cancelTask, we should remove the task\n            // from tasksList of target first\n            if (!task.isRemoved) {\n                const symbolEventNames = zoneSymbolEventNames[task.eventName];\n                let symbolEventName;\n                if (symbolEventNames) {\n                    symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n                }\n                const existingTasks = symbolEventName && task.target[symbolEventName];\n                if (existingTasks) {\n                    for (let i = 0; i < existingTasks.length; i++) {\n                        const existingTask = existingTasks[i];\n                        if (existingTask === task) {\n                            existingTasks.splice(i, 1);\n                            // set isRemoved to data for faster invokeTask check\n                            task.isRemoved = true;\n                            if (task.removeAbortListener) {\n                                task.removeAbortListener();\n                                task.removeAbortListener = null;\n                            }\n                            if (existingTasks.length === 0) {\n                                // all tasks for the eventName + capture have gone,\n                                // remove globalZoneAwareCallback and remove the task cache from target\n                                task.allRemoved = true;\n                                task.target[symbolEventName] = null;\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            // if all tasks for the eventName + capture have gone,\n            // we will really remove the global event callback,\n            // if not, return\n            if (!task.allRemoved) {\n                return;\n            }\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n        };\n        const customScheduleNonGlobal = function (task) {\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customSchedulePrepend = function (task) {\n            return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customCancelNonGlobal = function (task) {\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n        };\n        const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n        const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n        const compareTaskCallbackVsDelegate = function (task, delegate) {\n            const typeOfDelegate = typeof delegate;\n            return ((typeOfDelegate === 'function' && task.callback === delegate) ||\n                (typeOfDelegate === 'object' && task.originalDelegate === delegate));\n        };\n        const compare = patchOptions && patchOptions.diff ? patchOptions.diff : compareTaskCallbackVsDelegate;\n        const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n        const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n        function copyEventListenerOptions(options) {\n            if (typeof options === 'object' && options !== null) {\n                // We need to destructure the target `options` object since it may\n                // be frozen or sealed (possibly provided implicitly by a third-party\n                // library), or its properties may be readonly.\n                const newOptions = { ...options };\n                // The `signal` option was recently introduced, which caused regressions in\n                // third-party scenarios where `AbortController` was directly provided to\n                // `addEventListener` as options. For instance, in cases like\n                // `document.addEventListener('keydown', callback, abortControllerInstance)`,\n                // which is valid because `AbortController` includes a `signal` getter, spreading\n                // `{...options}` wouldn't copy the `signal`. Additionally, using `Object.create`\n                // isn't feasible since `AbortController` is a built-in object type, and attempting\n                // to create a new object directly with it as the prototype might result in\n                // unexpected behavior.\n                if (options.signal) {\n                    newOptions.signal = options.signal;\n                }\n                return newOptions;\n            }\n            return options;\n        }\n        const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n            return function () {\n                const target = this || _global;\n                let eventName = arguments[0];\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                let delegate = arguments[1];\n                if (!delegate) {\n                    return nativeListener.apply(this, arguments);\n                }\n                if (isNode && eventName === 'uncaughtException') {\n                    // don't patch uncaughtException of nodejs to prevent endless loop\n                    return nativeListener.apply(this, arguments);\n                }\n                // don't create the bind delegate function for handleEvent\n                // case here to improve addEventListener performance\n                // we will create the bind delegate when invoke\n                let isHandleEvent = false;\n                if (typeof delegate !== 'function') {\n                    if (!delegate.handleEvent) {\n                        return nativeListener.apply(this, arguments);\n                    }\n                    isHandleEvent = true;\n                }\n                if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n                    return;\n                }\n                const passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n                const options = copyEventListenerOptions(buildEventListenerOptions(arguments[2], passive));\n                const signal = options?.signal;\n                if (signal?.aborted) {\n                    // the signal is an aborted one, just return without attaching the event listener.\n                    return;\n                }\n                if (unpatchedEvents) {\n                    // check unpatched list\n                    for (let i = 0; i < unpatchedEvents.length; i++) {\n                        if (eventName === unpatchedEvents[i]) {\n                            if (passive) {\n                                return nativeListener.call(target, eventName, delegate, options);\n                            }\n                            else {\n                                return nativeListener.apply(this, arguments);\n                            }\n                        }\n                    }\n                }\n                const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n                const once = options && typeof options === 'object' ? options.once : false;\n                const zone = Zone.current;\n                let symbolEventNames = zoneSymbolEventNames[eventName];\n                if (!symbolEventNames) {\n                    prepareEventNames(eventName, eventNameToString);\n                    symbolEventNames = zoneSymbolEventNames[eventName];\n                }\n                const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                let existingTasks = target[symbolEventName];\n                let isExisting = false;\n                if (existingTasks) {\n                    // already have task registered\n                    isExisting = true;\n                    if (checkDuplicate) {\n                        for (let i = 0; i < existingTasks.length; i++) {\n                            if (compare(existingTasks[i], delegate)) {\n                                // same callback, same capture, same event name, just return\n                                return;\n                            }\n                        }\n                    }\n                }\n                else {\n                    existingTasks = target[symbolEventName] = [];\n                }\n                let source;\n                const constructorName = target.constructor['name'];\n                const targetSource = globalSources[constructorName];\n                if (targetSource) {\n                    source = targetSource[eventName];\n                }\n                if (!source) {\n                    source =\n                        constructorName +\n                            addSource +\n                            (eventNameToString ? eventNameToString(eventName) : eventName);\n                }\n                // In the code below, `options` should no longer be reassigned; instead, it\n                // should only be mutated. This is because we pass that object to the native\n                // `addEventListener`.\n                // It's generally recommended to use the same object reference for options.\n                // This ensures consistency and avoids potential issues.\n                taskData.options = options;\n                if (once) {\n                    // When using `addEventListener` with the `once` option, we don't pass\n                    // the `once` option directly to the native `addEventListener` method.\n                    // Instead, we keep the `once` setting and handle it ourselves.\n                    taskData.options.once = false;\n                }\n                taskData.target = target;\n                taskData.capture = capture;\n                taskData.eventName = eventName;\n                taskData.isExisting = isExisting;\n                const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n                // keep taskData into data to allow onScheduleEventTask to access the task information\n                if (data) {\n                    data.taskData = taskData;\n                }\n                if (signal) {\n                    // When using `addEventListener` with the `signal` option, we don't pass\n                    // the `signal` option directly to the native `addEventListener` method.\n                    // Instead, we keep the `signal` setting and handle it ourselves.\n                    taskData.options.signal = undefined;\n                }\n                // The `scheduleEventTask` function will ultimately call `customScheduleGlobal`,\n                // which in turn calls the native `addEventListener`. This is why `taskData.options`\n                // is updated before scheduling the task, as `customScheduleGlobal` uses\n                // `taskData.options` to pass it to the native `addEventListener`.\n                const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n                if (signal) {\n                    // after task is scheduled, we need to store the signal back to task.options\n                    taskData.options.signal = signal;\n                    // Wrapping `task` in a weak reference would not prevent memory leaks. Weak references are\n                    // primarily used for preventing strong references cycles. `onAbort` is always reachable\n                    // as it's an event listener, so its closure retains a strong reference to the `task`.\n                    const onAbort = () => task.zone.cancelTask(task);\n                    nativeListener.call(signal, 'abort', onAbort, { once: true });\n                    // We need to remove the `abort` listener when the event listener is going to be removed,\n                    // as it creates a closure that captures `task`. This closure retains a reference to the\n                    // `task` object even after it goes out of scope, preventing `task` from being garbage\n                    // collected.\n                    task.removeAbortListener = () => signal.removeEventListener('abort', onAbort);\n                }\n                // should clear taskData.target to avoid memory leak\n                // issue, https://github.com/angular/angular/issues/20442\n                taskData.target = null;\n                // need to clear up taskData because it is a global object\n                if (data) {\n                    data.taskData = null;\n                }\n                // have to save those information to task in case\n                // application may call task.zone.cancelTask() directly\n                if (once) {\n                    taskData.options.once = true;\n                }\n                if (!(!passiveSupported && typeof task.options === 'boolean')) {\n                    // if not support passive, and we pass an option object\n                    // to addEventListener, we should save the options to task\n                    task.options = options;\n                }\n                task.target = target;\n                task.capture = capture;\n                task.eventName = eventName;\n                if (isHandleEvent) {\n                    // save original delegate for compare to check duplicate\n                    task.originalDelegate = delegate;\n                }\n                if (!prepend) {\n                    existingTasks.push(task);\n                }\n                else {\n                    existingTasks.unshift(task);\n                }\n                if (returnTarget) {\n                    return target;\n                }\n            };\n        };\n        proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n        if (nativePrependEventListener) {\n            proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n        }\n        proto[REMOVE_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const options = arguments[2];\n            const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n            const delegate = arguments[1];\n            if (!delegate) {\n                return nativeRemoveEventListener.apply(this, arguments);\n            }\n            if (validateHandler &&\n                !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n                return;\n            }\n            const symbolEventNames = zoneSymbolEventNames[eventName];\n            let symbolEventName;\n            if (symbolEventNames) {\n                symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n            }\n            const existingTasks = symbolEventName && target[symbolEventName];\n            // `existingTasks` may not exist if the `addEventListener` was called before\n            // it was patched by zone.js. Please refer to the attached issue for\n            // clarification, particularly after the `if` condition, before calling\n            // the native `removeEventListener`.\n            if (existingTasks) {\n                for (let i = 0; i < existingTasks.length; i++) {\n                    const existingTask = existingTasks[i];\n                    if (compare(existingTask, delegate)) {\n                        existingTasks.splice(i, 1);\n                        // set isRemoved to data for faster invokeTask check\n                        existingTask.isRemoved = true;\n                        if (existingTasks.length === 0) {\n                            // all tasks for the eventName + capture have gone,\n                            // remove globalZoneAwareCallback and remove the task cache from target\n                            existingTask.allRemoved = true;\n                            target[symbolEventName] = null;\n                            // in the target, we have an event listener which is added by on_property\n                            // such as target.onclick = function() {}, so we need to clear this internal\n                            // property too if all delegates with capture=false were removed\n                            // https:// github.com/angular/angular/issues/31643\n                            // https://github.com/angular/angular/issues/54581\n                            if (!capture && typeof eventName === 'string') {\n                                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                                target[onPropertySymbol] = null;\n                            }\n                        }\n                        // In all other conditions, when `addEventListener` is called after being\n                        // patched by zone.js, we would always find an event task on the `EventTarget`.\n                        // This will trigger `cancelFn` on the `existingTask`, leading to `customCancelGlobal`,\n                        // which ultimately removes an event listener and cleans up the abort listener\n                        // (if an `AbortSignal` was provided when scheduling a task).\n                        existingTask.zone.cancelTask(existingTask);\n                        if (returnTarget) {\n                            return target;\n                        }\n                        return;\n                    }\n                }\n            }\n            // https://github.com/angular/zone.js/issues/930\n            // We may encounter a situation where the `addEventListener` was\n            // called on the event target before zone.js is loaded, resulting\n            // in no task being stored on the event target due to its invocation\n            // of the native implementation. In this scenario, we simply need to\n            // invoke the native `removeEventListener`.\n            return nativeRemoveEventListener.apply(this, arguments);\n        };\n        proto[LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const listeners = [];\n            const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n            for (let i = 0; i < tasks.length; i++) {\n                const task = tasks[i];\n                let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                listeners.push(delegate);\n            }\n            return listeners;\n        };\n        proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (!eventName) {\n                const keys = Object.keys(target);\n                for (let i = 0; i < keys.length; i++) {\n                    const prop = keys[i];\n                    const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                    let evtName = match && match[1];\n                    // in nodejs EventEmitter, removeListener event is\n                    // used for monitoring the removeListener call,\n                    // so just keep removeListener eventListener until\n                    // all other eventListeners are removed\n                    if (evtName && evtName !== 'removeListener') {\n                        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n                    }\n                }\n                // remove removeListener listener finally\n                this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n            }\n            else {\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                const symbolEventNames = zoneSymbolEventNames[eventName];\n                if (symbolEventNames) {\n                    const symbolEventName = symbolEventNames[FALSE_STR];\n                    const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n                    const tasks = target[symbolEventName];\n                    const captureTasks = target[symbolCaptureEventName];\n                    if (tasks) {\n                        const removeTasks = tasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                    if (captureTasks) {\n                        const removeTasks = captureTasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                }\n            }\n            if (returnTarget) {\n                return this;\n            }\n        };\n        // for native toString patch\n        attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n        attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n        if (nativeRemoveAllListeners) {\n            attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n        }\n        if (nativeListeners) {\n            attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n        }\n        return true;\n    }\n    let results = [];\n    for (let i = 0; i < apis.length; i++) {\n        results[i] = patchEventTargetMethods(apis[i], patchOptions);\n    }\n    return results;\n}\nfunction findEventTasks(target, eventName) {\n    if (!eventName) {\n        const foundTasks = [];\n        for (let prop in target) {\n            const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n            let evtName = match && match[1];\n            if (evtName && (!eventName || evtName === eventName)) {\n                const tasks = target[prop];\n                if (tasks) {\n                    for (let i = 0; i < tasks.length; i++) {\n                        foundTasks.push(tasks[i]);\n                    }\n                }\n            }\n        }\n        return foundTasks;\n    }\n    let symbolEventName = zoneSymbolEventNames[eventName];\n    if (!symbolEventName) {\n        prepareEventNames(eventName);\n        symbolEventName = zoneSymbolEventNames[eventName];\n    }\n    const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n    const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n    if (!captureFalseTasks) {\n        return captureTrueTasks ? captureTrueTasks.slice() : [];\n    }\n    else {\n        return captureTrueTasks\n            ? captureFalseTasks.concat(captureTrueTasks)\n            : captureFalseTasks.slice();\n    }\n}\nfunction patchEventPrototype(global, api) {\n    const Event = global['Event'];\n    if (Event && Event.prototype) {\n        api.patchMethod(Event.prototype, 'stopImmediatePropagation', (delegate) => function (self, args) {\n            self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n            // we need to call the native stopImmediatePropagation\n            // in case in some hybrid application, some part of\n            // application will be controlled by zone, some are not\n            delegate && delegate.apply(self, args);\n        });\n    }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchQueueMicrotask(global, api) {\n    api.patchMethod(global, 'queueMicrotask', (delegate) => {\n        return function (self, args) {\n            Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n        };\n    });\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n    let setNative = null;\n    let clearNative = null;\n    setName += nameSuffix;\n    cancelName += nameSuffix;\n    const tasksByHandleId = {};\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[0] = function () {\n            return task.invoke.apply(this, arguments);\n        };\n        data.handleId = setNative.apply(window, data.args);\n        return task;\n    }\n    function clearTask(task) {\n        return clearNative.call(window, task.data.handleId);\n    }\n    setNative = patchMethod(window, setName, (delegate) => function (self, args) {\n        if (typeof args[0] === 'function') {\n            const options = {\n                isPeriodic: nameSuffix === 'Interval',\n                delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n                args: args,\n            };\n            const callback = args[0];\n            args[0] = function timer() {\n                try {\n                    return callback.apply(this, arguments);\n                }\n                finally {\n                    // issue-934, task will be cancelled\n                    // even it is a periodic task such as\n                    // setInterval\n                    // https://github.com/angular/angular/issues/40387\n                    // Cleanup tasksByHandleId should be handled before scheduleTask\n                    // Since some zoneSpec may intercept and doesn't trigger\n                    // scheduleFn(scheduleTask) provided here.\n                    if (!options.isPeriodic) {\n                        if (typeof options.handleId === 'number') {\n                            // in non-nodejs env, we remove timerId\n                            // from local cache\n                            delete tasksByHandleId[options.handleId];\n                        }\n                        else if (options.handleId) {\n                            // Node returns complex objects as handleIds\n                            // we remove task reference from timer object\n                            options.handleId[taskSymbol] = null;\n                        }\n                    }\n                }\n            };\n            const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n            if (!task) {\n                return task;\n            }\n            // Node.js must additionally support the ref and unref functions.\n            const handle = task.data.handleId;\n            if (typeof handle === 'number') {\n                // for non nodejs env, we save handleId: task\n                // mapping in local cache for clearTimeout\n                tasksByHandleId[handle] = task;\n            }\n            else if (handle) {\n                // for nodejs env, we save task\n                // reference in timerId Object for clearTimeout\n                handle[taskSymbol] = task;\n            }\n            // check whether handle is null, because some polyfill or browser\n            // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n            if (handle &&\n                handle.ref &&\n                handle.unref &&\n                typeof handle.ref === 'function' &&\n                typeof handle.unref === 'function') {\n                task.ref = handle.ref.bind(handle);\n                task.unref = handle.unref.bind(handle);\n            }\n            if (typeof handle === 'number' || handle) {\n                return handle;\n            }\n            return task;\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(window, args);\n        }\n    });\n    clearNative = patchMethod(window, cancelName, (delegate) => function (self, args) {\n        const id = args[0];\n        let task;\n        if (typeof id === 'number') {\n            // non nodejs env.\n            task = tasksByHandleId[id];\n        }\n        else {\n            // nodejs env.\n            task = id && id[taskSymbol];\n            // other environments.\n            if (!task) {\n                task = id;\n            }\n        }\n        if (task && typeof task.type === 'string') {\n            if (task.state !== 'notScheduled' &&\n                ((task.cancelFn && task.data.isPeriodic) || task.runCount === 0)) {\n                if (typeof id === 'number') {\n                    delete tasksByHandleId[id];\n                }\n                else if (id) {\n                    id[taskSymbol] = null;\n                }\n                // Do not cancel already canceled functions\n                task.zone.cancelTask(task);\n            }\n        }\n        else {\n            // cause an error by calling it directly.\n            delegate.apply(window, args);\n        }\n    });\n}\n\nfunction patchCustomElements(_global, api) {\n    const { isBrowser, isMix } = api.getGlobalObjects();\n    if ((!isBrowser && !isMix) || !_global['customElements'] || !('customElements' in _global)) {\n        return;\n    }\n    // https://html.spec.whatwg.org/multipage/custom-elements.html#concept-custom-element-definition-lifecycle-callbacks\n    const callbacks = [\n        'connectedCallback',\n        'disconnectedCallback',\n        'adoptedCallback',\n        'attributeChangedCallback',\n        'formAssociatedCallback',\n        'formDisabledCallback',\n        'formResetCallback',\n        'formStateRestoreCallback',\n    ];\n    api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n\nfunction eventTargetPatch(_global, api) {\n    if (Zone[api.symbol('patchEventTarget')]) {\n        // EventTarget is already patched.\n        return;\n    }\n    const { eventNames, zoneSymbolEventNames, TRUE_STR, FALSE_STR, ZONE_SYMBOL_PREFIX } = api.getGlobalObjects();\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (let i = 0; i < eventNames.length; i++) {\n        const eventName = eventNames[i];\n        const falseEventName = eventName + FALSE_STR;\n        const trueEventName = eventName + TRUE_STR;\n        const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    const EVENT_TARGET = _global['EventTarget'];\n    if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n        return;\n    }\n    api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n    return true;\n}\nfunction patchEvent(global, api) {\n    api.patchEventPrototype(global, api);\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction filterProperties(target, onProperties, ignoreProperties) {\n    if (!ignoreProperties || ignoreProperties.length === 0) {\n        return onProperties;\n    }\n    const tip = ignoreProperties.filter((ip) => ip.target === target);\n    if (!tip || tip.length === 0) {\n        return onProperties;\n    }\n    const targetIgnoreProperties = tip[0].ignoreProperties;\n    return onProperties.filter((op) => targetIgnoreProperties.indexOf(op) === -1);\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n    // check whether target is available, sometimes target will be undefined\n    // because different browser or some 3rd party plugin.\n    if (!target) {\n        return;\n    }\n    const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n    patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\nfunction getOnEventNames(target) {\n    return Object.getOwnPropertyNames(target)\n        .filter((name) => name.startsWith('on') && name.length > 2)\n        .map((name) => name.substring(2));\n}\nfunction propertyDescriptorPatch(api, _global) {\n    if (isNode && !isMix) {\n        return;\n    }\n    if (Zone[api.symbol('patchEvents')]) {\n        // events are already been patched by legacy patch.\n        return;\n    }\n    const ignoreProperties = _global['__Zone_ignore_on_properties'];\n    // for browsers that we can patch the descriptor:  Chrome & Firefox\n    let patchTargets = [];\n    if (isBrowser) {\n        const internalWindow = window;\n        patchTargets = patchTargets.concat([\n            'Document',\n            'SVGElement',\n            'Element',\n            'HTMLElement',\n            'HTMLBodyElement',\n            'HTMLMediaElement',\n            'HTMLFrameSetElement',\n            'HTMLFrameElement',\n            'HTMLIFrameElement',\n            'HTMLMarqueeElement',\n            'Worker',\n        ]);\n        const ignoreErrorProperties = isIE()\n            ? [{ target: internalWindow, ignoreProperties: ['error'] }]\n            : [];\n        // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n        // so we need to pass WindowPrototype to check onProp exist or not\n        patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n    }\n    patchTargets = patchTargets.concat([\n        'XMLHttpRequest',\n        'XMLHttpRequestEventTarget',\n        'IDBIndex',\n        'IDBRequest',\n        'IDBOpenDBRequest',\n        'IDBDatabase',\n        'IDBTransaction',\n        'IDBCursor',\n        'WebSocket',\n    ]);\n    for (let i = 0; i < patchTargets.length; i++) {\n        const target = _global[patchTargets[i]];\n        target &&\n            target.prototype &&\n            patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n    }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchBrowser(Zone) {\n    Zone.__load_patch('legacy', (global) => {\n        const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n        if (legacyPatch) {\n            legacyPatch();\n        }\n    });\n    Zone.__load_patch('timers', (global) => {\n        const set = 'set';\n        const clear = 'clear';\n        patchTimer(global, set, clear, 'Timeout');\n        patchTimer(global, set, clear, 'Interval');\n        patchTimer(global, set, clear, 'Immediate');\n    });\n    Zone.__load_patch('requestAnimationFrame', (global) => {\n        patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n        patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n        patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n    });\n    Zone.__load_patch('blocking', (global, Zone) => {\n        const blockingMethods = ['alert', 'prompt', 'confirm'];\n        for (let i = 0; i < blockingMethods.length; i++) {\n            const name = blockingMethods[i];\n            patchMethod(global, name, (delegate, symbol, name) => {\n                return function (s, args) {\n                    return Zone.current.run(delegate, global, args, name);\n                };\n            });\n        }\n    });\n    Zone.__load_patch('EventTarget', (global, Zone, api) => {\n        patchEvent(global, api);\n        eventTargetPatch(global, api);\n        // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n        const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n        if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n            api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n        }\n    });\n    Zone.__load_patch('MutationObserver', (global, Zone, api) => {\n        patchClass('MutationObserver');\n        patchClass('WebKitMutationObserver');\n    });\n    Zone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n        patchClass('IntersectionObserver');\n    });\n    Zone.__load_patch('FileReader', (global, Zone, api) => {\n        patchClass('FileReader');\n    });\n    Zone.__load_patch('on_property', (global, Zone, api) => {\n        propertyDescriptorPatch(api, global);\n    });\n    Zone.__load_patch('customElements', (global, Zone, api) => {\n        patchCustomElements(global, api);\n    });\n    Zone.__load_patch('XHR', (global, Zone) => {\n        // Treat XMLHttpRequest as a macrotask.\n        patchXHR(global);\n        const XHR_TASK = zoneSymbol('xhrTask');\n        const XHR_SYNC = zoneSymbol('xhrSync');\n        const XHR_LISTENER = zoneSymbol('xhrListener');\n        const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n        const XHR_URL = zoneSymbol('xhrURL');\n        const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n        function patchXHR(window) {\n            const XMLHttpRequest = window['XMLHttpRequest'];\n            if (!XMLHttpRequest) {\n                // XMLHttpRequest is not available in service worker\n                return;\n            }\n            const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n            function findPendingTask(target) {\n                return target[XHR_TASK];\n            }\n            let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n            let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            if (!oriAddListener) {\n                const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n                if (XMLHttpRequestEventTarget) {\n                    const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n                    oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n            }\n            const READY_STATE_CHANGE = 'readystatechange';\n            const SCHEDULED = 'scheduled';\n            function scheduleTask(task) {\n                const data = task.data;\n                const target = data.target;\n                target[XHR_SCHEDULED] = false;\n                target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n                // remove existing event listener\n                const listener = target[XHR_LISTENER];\n                if (!oriAddListener) {\n                    oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n                if (listener) {\n                    oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n                }\n                const newListener = (target[XHR_LISTENER] = () => {\n                    if (target.readyState === target.DONE) {\n                        // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n                        // readyState=4 multiple times, so we need to check task state here\n                        if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n                            // check whether the xhr has registered onload listener\n                            // if that is the case, the task should invoke after all\n                            // onload listeners finish.\n                            // Also if the request failed without response (status = 0), the load event handler\n                            // will not be triggered, in that case, we should also invoke the placeholder callback\n                            // to close the XMLHttpRequest::send macroTask.\n                            // https://github.com/angular/angular/issues/38795\n                            const loadTasks = target[Zone.__symbol__('loadfalse')];\n                            if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                                const oriInvoke = task.invoke;\n                                task.invoke = function () {\n                                    // need to load the tasks again, because in other\n                                    // load listener, they may remove themselves\n                                    const loadTasks = target[Zone.__symbol__('loadfalse')];\n                                    for (let i = 0; i < loadTasks.length; i++) {\n                                        if (loadTasks[i] === task) {\n                                            loadTasks.splice(i, 1);\n                                        }\n                                    }\n                                    if (!data.aborted && task.state === SCHEDULED) {\n                                        oriInvoke.call(task);\n                                    }\n                                };\n                                loadTasks.push(task);\n                            }\n                            else {\n                                task.invoke();\n                            }\n                        }\n                        else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n                            // error occurs when xhr.send()\n                            target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n                        }\n                    }\n                });\n                oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n                const storedTask = target[XHR_TASK];\n                if (!storedTask) {\n                    target[XHR_TASK] = task;\n                }\n                sendNative.apply(target, data.args);\n                target[XHR_SCHEDULED] = true;\n                return task;\n            }\n            function placeholderCallback() { }\n            function clearTask(task) {\n                const data = task.data;\n                // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n                // to prevent it from firing. So instead, we store info for the event listener.\n                data.aborted = true;\n                return abortNative.apply(data.target, data.args);\n            }\n            const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n                self[XHR_SYNC] = args[2] == false;\n                self[XHR_URL] = args[1];\n                return openNative.apply(self, args);\n            });\n            const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n            const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n            const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n            const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n                if (Zone.current[fetchTaskScheduling] === true) {\n                    // a fetch is scheduling, so we are using xhr to polyfill fetch\n                    // and because we already schedule macroTask for fetch, we should\n                    // not schedule a macroTask for xhr again\n                    return sendNative.apply(self, args);\n                }\n                if (self[XHR_SYNC]) {\n                    // if the XHR is sync there is no task to schedule, just execute the code.\n                    return sendNative.apply(self, args);\n                }\n                else {\n                    const options = {\n                        target: self,\n                        url: self[XHR_URL],\n                        isPeriodic: false,\n                        args: args,\n                        aborted: false,\n                    };\n                    const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n                    if (self &&\n                        self[XHR_ERROR_BEFORE_SCHEDULED] === true &&\n                        !options.aborted &&\n                        task.state === SCHEDULED) {\n                        // xhr request throw error when send\n                        // we should invoke task instead of leaving a scheduled\n                        // pending macroTask\n                        task.invoke();\n                    }\n                }\n            });\n            const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n                const task = findPendingTask(self);\n                if (task && typeof task.type == 'string') {\n                    // If the XHR has already completed, do nothing.\n                    // If the XHR has already been aborted, do nothing.\n                    // Fix #569, call abort multiple times before done will cause\n                    // macroTask task count be negative number\n                    if (task.cancelFn == null || (task.data && task.data.aborted)) {\n                        return;\n                    }\n                    task.zone.cancelTask(task);\n                }\n                else if (Zone.current[fetchTaskAborting] === true) {\n                    // the abort is called from fetch polyfill, we need to call native abort of XHR.\n                    return abortNative.apply(self, args);\n                }\n                // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n                // task\n                // to cancel. Do nothing.\n            });\n        }\n    });\n    Zone.__load_patch('geolocation', (global) => {\n        /// GEO_LOCATION\n        if (global['navigator'] && global['navigator'].geolocation) {\n            patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n        }\n    });\n    Zone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n        // handle unhandled promise rejection\n        function findPromiseRejectionHandler(evtName) {\n            return function (e) {\n                const eventTasks = findEventTasks(global, evtName);\n                eventTasks.forEach((eventTask) => {\n                    // windows has added unhandledrejection event listener\n                    // trigger the event listener\n                    const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n                    if (PromiseRejectionEvent) {\n                        const evt = new PromiseRejectionEvent(evtName, {\n                            promise: e.promise,\n                            reason: e.rejection,\n                        });\n                        eventTask.invoke(evt);\n                    }\n                });\n            };\n        }\n        if (global['PromiseRejectionEvent']) {\n            Zone[zoneSymbol('unhandledPromiseRejectionHandler')] =\n                findPromiseRejectionHandler('unhandledrejection');\n            Zone[zoneSymbol('rejectionHandledHandler')] =\n                findPromiseRejectionHandler('rejectionhandled');\n        }\n    });\n    Zone.__load_patch('queueMicrotask', (global, Zone, api) => {\n        patchQueueMicrotask(global, api);\n    });\n}\n\nfunction patchPromise(Zone) {\n    Zone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n        const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n        const ObjectDefineProperty = Object.defineProperty;\n        function readableObjectToString(obj) {\n            if (obj && obj.toString === Object.prototype.toString) {\n                const className = obj.constructor && obj.constructor.name;\n                return (className ? className : '') + ': ' + JSON.stringify(obj);\n            }\n            return obj ? obj.toString() : Object.prototype.toString.call(obj);\n        }\n        const __symbol__ = api.symbol;\n        const _uncaughtPromiseErrors = [];\n        const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] !== false;\n        const symbolPromise = __symbol__('Promise');\n        const symbolThen = __symbol__('then');\n        const creationTrace = '__creationTrace__';\n        api.onUnhandledError = (e) => {\n            if (api.showUncaughtError()) {\n                const rejection = e && e.rejection;\n                if (rejection) {\n                    console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n                }\n                else {\n                    console.error(e);\n                }\n            }\n        };\n        api.microtaskDrainDone = () => {\n            while (_uncaughtPromiseErrors.length) {\n                const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n                try {\n                    uncaughtPromiseError.zone.runGuarded(() => {\n                        if (uncaughtPromiseError.throwOriginal) {\n                            throw uncaughtPromiseError.rejection;\n                        }\n                        throw uncaughtPromiseError;\n                    });\n                }\n                catch (error) {\n                    handleUnhandledRejection(error);\n                }\n            }\n        };\n        const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n        function handleUnhandledRejection(e) {\n            api.onUnhandledError(e);\n            try {\n                const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n                if (typeof handler === 'function') {\n                    handler.call(this, e);\n                }\n            }\n            catch (err) { }\n        }\n        function isThenable(value) {\n            return value && value.then;\n        }\n        function forwardResolution(value) {\n            return value;\n        }\n        function forwardRejection(rejection) {\n            return ZoneAwarePromise.reject(rejection);\n        }\n        const symbolState = __symbol__('state');\n        const symbolValue = __symbol__('value');\n        const symbolFinally = __symbol__('finally');\n        const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n        const symbolParentPromiseState = __symbol__('parentPromiseState');\n        const source = 'Promise.then';\n        const UNRESOLVED = null;\n        const RESOLVED = true;\n        const REJECTED = false;\n        const REJECTED_NO_CATCH = 0;\n        function makeResolver(promise, state) {\n            return (v) => {\n                try {\n                    resolvePromise(promise, state, v);\n                }\n                catch (err) {\n                    resolvePromise(promise, false, err);\n                }\n                // Do not return value or you will break the Promise spec.\n            };\n        }\n        const once = function () {\n            let wasCalled = false;\n            return function wrapper(wrappedFunction) {\n                return function () {\n                    if (wasCalled) {\n                        return;\n                    }\n                    wasCalled = true;\n                    wrappedFunction.apply(null, arguments);\n                };\n            };\n        };\n        const TYPE_ERROR = 'Promise resolved with itself';\n        const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n        // Promise Resolution\n        function resolvePromise(promise, state, value) {\n            const onceWrapper = once();\n            if (promise === value) {\n                throw new TypeError(TYPE_ERROR);\n            }\n            if (promise[symbolState] === UNRESOLVED) {\n                // should only get value.then once based on promise spec.\n                let then = null;\n                try {\n                    if (typeof value === 'object' || typeof value === 'function') {\n                        then = value && value.then;\n                    }\n                }\n                catch (err) {\n                    onceWrapper(() => {\n                        resolvePromise(promise, false, err);\n                    })();\n                    return promise;\n                }\n                // if (value instanceof ZoneAwarePromise) {\n                if (state !== REJECTED &&\n                    value instanceof ZoneAwarePromise &&\n                    value.hasOwnProperty(symbolState) &&\n                    value.hasOwnProperty(symbolValue) &&\n                    value[symbolState] !== UNRESOLVED) {\n                    clearRejectedNoCatch(value);\n                    resolvePromise(promise, value[symbolState], value[symbolValue]);\n                }\n                else if (state !== REJECTED && typeof then === 'function') {\n                    try {\n                        then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n                    }\n                    catch (err) {\n                        onceWrapper(() => {\n                            resolvePromise(promise, false, err);\n                        })();\n                    }\n                }\n                else {\n                    promise[symbolState] = state;\n                    const queue = promise[symbolValue];\n                    promise[symbolValue] = value;\n                    if (promise[symbolFinally] === symbolFinally) {\n                        // the promise is generated by Promise.prototype.finally\n                        if (state === RESOLVED) {\n                            // the state is resolved, should ignore the value\n                            // and use parent promise value\n                            promise[symbolState] = promise[symbolParentPromiseState];\n                            promise[symbolValue] = promise[symbolParentPromiseValue];\n                        }\n                    }\n                    // record task information in value when error occurs, so we can\n                    // do some additional work such as render longStackTrace\n                    if (state === REJECTED && value instanceof Error) {\n                        // check if longStackTraceZone is here\n                        const trace = Zone.currentTask &&\n                            Zone.currentTask.data &&\n                            Zone.currentTask.data[creationTrace];\n                        if (trace) {\n                            // only keep the long stack trace into error when in longStackTraceZone\n                            ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n                                configurable: true,\n                                enumerable: false,\n                                writable: true,\n                                value: trace,\n                            });\n                        }\n                    }\n                    for (let i = 0; i < queue.length;) {\n                        scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n                    }\n                    if (queue.length == 0 && state == REJECTED) {\n                        promise[symbolState] = REJECTED_NO_CATCH;\n                        let uncaughtPromiseError = value;\n                        try {\n                            // Here we throws a new Error to print more readable error log\n                            // and if the value is not an error, zone.js builds an `Error`\n                            // Object here to attach the stack information.\n                            throw new Error('Uncaught (in promise): ' +\n                                readableObjectToString(value) +\n                                (value && value.stack ? '\\n' + value.stack : ''));\n                        }\n                        catch (err) {\n                            uncaughtPromiseError = err;\n                        }\n                        if (isDisableWrappingUncaughtPromiseRejection) {\n                            // If disable wrapping uncaught promise reject\n                            // use the value instead of wrapping it.\n                            uncaughtPromiseError.throwOriginal = true;\n                        }\n                        uncaughtPromiseError.rejection = value;\n                        uncaughtPromiseError.promise = promise;\n                        uncaughtPromiseError.zone = Zone.current;\n                        uncaughtPromiseError.task = Zone.currentTask;\n                        _uncaughtPromiseErrors.push(uncaughtPromiseError);\n                        api.scheduleMicroTask(); // to make sure that it is running\n                    }\n                }\n            }\n            // Resolving an already resolved promise is a noop.\n            return promise;\n        }\n        const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n        function clearRejectedNoCatch(promise) {\n            if (promise[symbolState] === REJECTED_NO_CATCH) {\n                // if the promise is rejected no catch status\n                // and queue.length > 0, means there is a error handler\n                // here to handle the rejected promise, we should trigger\n                // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n                // eventHandler\n                try {\n                    const handler = Zone[REJECTION_HANDLED_HANDLER];\n                    if (handler && typeof handler === 'function') {\n                        handler.call(this, { rejection: promise[symbolValue], promise: promise });\n                    }\n                }\n                catch (err) { }\n                promise[symbolState] = REJECTED;\n                for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n                    if (promise === _uncaughtPromiseErrors[i].promise) {\n                        _uncaughtPromiseErrors.splice(i, 1);\n                    }\n                }\n            }\n        }\n        function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n            clearRejectedNoCatch(promise);\n            const promiseState = promise[symbolState];\n            const delegate = promiseState\n                ? typeof onFulfilled === 'function'\n                    ? onFulfilled\n                    : forwardResolution\n                : typeof onRejected === 'function'\n                    ? onRejected\n                    : forwardRejection;\n            zone.scheduleMicroTask(source, () => {\n                try {\n                    const parentPromiseValue = promise[symbolValue];\n                    const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n                    if (isFinallyPromise) {\n                        // if the promise is generated from finally call, keep parent promise's state and value\n                        chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n                        chainPromise[symbolParentPromiseState] = promiseState;\n                    }\n                    // should not pass value to finally callback\n                    const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution\n                        ? []\n                        : [parentPromiseValue]);\n                    resolvePromise(chainPromise, true, value);\n                }\n                catch (error) {\n                    // if error occurs, should always return this error\n                    resolvePromise(chainPromise, false, error);\n                }\n            }, chainPromise);\n        }\n        const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n        const noop = function () { };\n        const AggregateError = global.AggregateError;\n        class ZoneAwarePromise {\n            static toString() {\n                return ZONE_AWARE_PROMISE_TO_STRING;\n            }\n            static resolve(value) {\n                if (value instanceof ZoneAwarePromise) {\n                    return value;\n                }\n                return resolvePromise(new this(null), RESOLVED, value);\n            }\n            static reject(error) {\n                return resolvePromise(new this(null), REJECTED, error);\n            }\n            static withResolvers() {\n                const result = {};\n                result.promise = new ZoneAwarePromise((res, rej) => {\n                    result.resolve = res;\n                    result.reject = rej;\n                });\n                return result;\n            }\n            static any(values) {\n                if (!values || typeof values[Symbol.iterator] !== 'function') {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                const promises = [];\n                let count = 0;\n                try {\n                    for (let v of values) {\n                        count++;\n                        promises.push(ZoneAwarePromise.resolve(v));\n                    }\n                }\n                catch (err) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                if (count === 0) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                let finished = false;\n                const errors = [];\n                return new ZoneAwarePromise((resolve, reject) => {\n                    for (let i = 0; i < promises.length; i++) {\n                        promises[i].then((v) => {\n                            if (finished) {\n                                return;\n                            }\n                            finished = true;\n                            resolve(v);\n                        }, (err) => {\n                            errors.push(err);\n                            count--;\n                            if (count === 0) {\n                                finished = true;\n                                reject(new AggregateError(errors, 'All promises were rejected'));\n                            }\n                        });\n                    }\n                });\n            }\n            static race(values) {\n                let resolve;\n                let reject;\n                let promise = new this((res, rej) => {\n                    resolve = res;\n                    reject = rej;\n                });\n                function onResolve(value) {\n                    resolve(value);\n                }\n                function onReject(error) {\n                    reject(error);\n                }\n                for (let value of values) {\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    value.then(onResolve, onReject);\n                }\n                return promise;\n            }\n            static all(values) {\n                return ZoneAwarePromise.allWithCallback(values);\n            }\n            static allSettled(values) {\n                const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n                return P.allWithCallback(values, {\n                    thenCallback: (value) => ({ status: 'fulfilled', value }),\n                    errorCallback: (err) => ({ status: 'rejected', reason: err }),\n                });\n            }\n            static allWithCallback(values, callback) {\n                let resolve;\n                let reject;\n                let promise = new this((res, rej) => {\n                    resolve = res;\n                    reject = rej;\n                });\n                // Start at 2 to prevent prematurely resolving if .then is called immediately.\n                let unresolvedCount = 2;\n                let valueIndex = 0;\n                const resolvedValues = [];\n                for (let value of values) {\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    const curValueIndex = valueIndex;\n                    try {\n                        value.then((value) => {\n                            resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n                            unresolvedCount--;\n                            if (unresolvedCount === 0) {\n                                resolve(resolvedValues);\n                            }\n                        }, (err) => {\n                            if (!callback) {\n                                reject(err);\n                            }\n                            else {\n                                resolvedValues[curValueIndex] = callback.errorCallback(err);\n                                unresolvedCount--;\n                                if (unresolvedCount === 0) {\n                                    resolve(resolvedValues);\n                                }\n                            }\n                        });\n                    }\n                    catch (thenErr) {\n                        reject(thenErr);\n                    }\n                    unresolvedCount++;\n                    valueIndex++;\n                }\n                // Make the unresolvedCount zero-based again.\n                unresolvedCount -= 2;\n                if (unresolvedCount === 0) {\n                    resolve(resolvedValues);\n                }\n                return promise;\n            }\n            constructor(executor) {\n                const promise = this;\n                if (!(promise instanceof ZoneAwarePromise)) {\n                    throw new Error('Must be an instanceof Promise.');\n                }\n                promise[symbolState] = UNRESOLVED;\n                promise[symbolValue] = []; // queue;\n                try {\n                    const onceWrapper = once();\n                    executor &&\n                        executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n                }\n                catch (error) {\n                    resolvePromise(promise, false, error);\n                }\n            }\n            get [Symbol.toStringTag]() {\n                return 'Promise';\n            }\n            get [Symbol.species]() {\n                return ZoneAwarePromise;\n            }\n            then(onFulfilled, onRejected) {\n                // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n                // may be an object without a prototype (created through `Object.create(null)`); thus\n                // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n                // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n                // object and copies promise properties into that object (within the `getOrCreateLoad`\n                // function). The zone.js then checks if the resolved value has the `then` method and\n                // invokes it with the `value` context. Otherwise, this will throw an error: `TypeError:\n                // Cannot read properties of undefined (reading 'Symbol(Symbol.species)')`.\n                let C = this.constructor?.[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = this.constructor || ZoneAwarePromise;\n                }\n                const chainPromise = new C(noop);\n                const zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n                }\n                return chainPromise;\n            }\n            catch(onRejected) {\n                return this.then(null, onRejected);\n            }\n            finally(onFinally) {\n                // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n                let C = this.constructor?.[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = ZoneAwarePromise;\n                }\n                const chainPromise = new C(noop);\n                chainPromise[symbolFinally] = symbolFinally;\n                const zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n                }\n                return chainPromise;\n            }\n        }\n        // Protect against aggressive optimizers dropping seemingly unused properties.\n        // E.g. Closure Compiler in advanced mode.\n        ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n        ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n        ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n        ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n        const NativePromise = (global[symbolPromise] = global['Promise']);\n        global['Promise'] = ZoneAwarePromise;\n        const symbolThenPatched = __symbol__('thenPatched');\n        function patchThen(Ctor) {\n            const proto = Ctor.prototype;\n            const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n            if (prop && (prop.writable === false || !prop.configurable)) {\n                // check Ctor.prototype.then propertyDescriptor is writable or not\n                // in meteor env, writable is false, we should ignore such case\n                return;\n            }\n            const originalThen = proto.then;\n            // Keep a reference to the original method.\n            proto[symbolThen] = originalThen;\n            Ctor.prototype.then = function (onResolve, onReject) {\n                const wrapped = new ZoneAwarePromise((resolve, reject) => {\n                    originalThen.call(this, resolve, reject);\n                });\n                return wrapped.then(onResolve, onReject);\n            };\n            Ctor[symbolThenPatched] = true;\n        }\n        api.patchThen = patchThen;\n        function zoneify(fn) {\n            return function (self, args) {\n                let resultPromise = fn.apply(self, args);\n                if (resultPromise instanceof ZoneAwarePromise) {\n                    return resultPromise;\n                }\n                let ctor = resultPromise.constructor;\n                if (!ctor[symbolThenPatched]) {\n                    patchThen(ctor);\n                }\n                return resultPromise;\n            };\n        }\n        if (NativePromise) {\n            patchThen(NativePromise);\n            patchMethod(global, 'fetch', (delegate) => zoneify(delegate));\n        }\n        // This is not part of public API, but it is useful for tests, so we expose it.\n        Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n        return ZoneAwarePromise;\n    });\n}\n\nfunction patchToString(Zone) {\n    // override Function.prototype.toString to make zone.js patched function\n    // look like native function\n    Zone.__load_patch('toString', (global) => {\n        // patch Func.prototype.toString to let them look like native\n        const originalFunctionToString = Function.prototype.toString;\n        const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n        const PROMISE_SYMBOL = zoneSymbol('Promise');\n        const ERROR_SYMBOL = zoneSymbol('Error');\n        const newFunctionToString = function toString() {\n            if (typeof this === 'function') {\n                const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n                if (originalDelegate) {\n                    if (typeof originalDelegate === 'function') {\n                        return originalFunctionToString.call(originalDelegate);\n                    }\n                    else {\n                        return Object.prototype.toString.call(originalDelegate);\n                    }\n                }\n                if (this === Promise) {\n                    const nativePromise = global[PROMISE_SYMBOL];\n                    if (nativePromise) {\n                        return originalFunctionToString.call(nativePromise);\n                    }\n                }\n                if (this === Error) {\n                    const nativeError = global[ERROR_SYMBOL];\n                    if (nativeError) {\n                        return originalFunctionToString.call(nativeError);\n                    }\n                }\n            }\n            return originalFunctionToString.call(this);\n        };\n        newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n        Function.prototype.toString = newFunctionToString;\n        // patch Object.prototype.toString to let them look like native\n        const originalObjectToString = Object.prototype.toString;\n        const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n        Object.prototype.toString = function () {\n            if (typeof Promise === 'function' && this instanceof Promise) {\n                return PROMISE_OBJECT_TO_STRING;\n            }\n            return originalObjectToString.call(this);\n        };\n    });\n}\n\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n    const symbol = Zone.__symbol__(method);\n    if (target[symbol]) {\n        return;\n    }\n    const nativeDelegate = (target[symbol] = target[method]);\n    target[method] = function (name, opts, options) {\n        if (opts && opts.prototype) {\n            callbacks.forEach(function (callback) {\n                const source = `${targetName}.${method}::` + callback;\n                const prototype = opts.prototype;\n                // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n                // `customElements.define`. We explicitly wrap the patching code into try-catch since\n                // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n                // make those properties non-writable. This means that patching callback will throw an error\n                // `cannot assign to read-only property`. See this code as an example:\n                // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n                // We don't want to stop the application rendering if we couldn't patch some\n                // callback, e.g. `attributeChangedCallback`.\n                try {\n                    if (prototype.hasOwnProperty(callback)) {\n                        const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n                        if (descriptor && descriptor.value) {\n                            descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                            api._redefineProperty(opts.prototype, callback, descriptor);\n                        }\n                        else if (prototype[callback]) {\n                            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                        }\n                    }\n                    else if (prototype[callback]) {\n                        prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                    }\n                }\n                catch {\n                    // Note: we leave the catch block empty since there's no way to handle the error related\n                    // to non-writable property.\n                }\n            });\n        }\n        return nativeDelegate.call(target, name, opts, options);\n    };\n    api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\nfunction patchUtil(Zone) {\n    Zone.__load_patch('util', (global, Zone, api) => {\n        // Collect native event names by looking at properties\n        // on the global namespace, e.g. 'onclick'.\n        const eventNames = getOnEventNames(global);\n        api.patchOnProperties = patchOnProperties;\n        api.patchMethod = patchMethod;\n        api.bindArguments = bindArguments;\n        api.patchMacroTask = patchMacroTask;\n        // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS`\n        // to define which events will not be patched by `Zone.js`. In newer version (>=0.9.0), we\n        // change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep the name consistent with\n        // angular repo. The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be\n        // supported for backwards compatibility.\n        const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n        const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n        if (global[SYMBOL_UNPATCHED_EVENTS]) {\n            global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n        }\n        if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n            Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] =\n                global[SYMBOL_BLACK_LISTED_EVENTS];\n        }\n        api.patchEventPrototype = patchEventPrototype;\n        api.patchEventTarget = patchEventTarget;\n        api.isIEOrEdge = isIEOrEdge;\n        api.ObjectDefineProperty = ObjectDefineProperty;\n        api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n        api.ObjectCreate = ObjectCreate;\n        api.ArraySlice = ArraySlice;\n        api.patchClass = patchClass;\n        api.wrapWithCurrentZone = wrapWithCurrentZone;\n        api.filterProperties = filterProperties;\n        api.attachOriginToPatched = attachOriginToPatched;\n        api._redefineProperty = Object.defineProperty;\n        api.patchCallbacks = patchCallbacks;\n        api.getGlobalObjects = () => ({\n            globalSources,\n            zoneSymbolEventNames,\n            eventNames,\n            isBrowser,\n            isMix,\n            isNode,\n            TRUE_STR,\n            FALSE_STR,\n            ZONE_SYMBOL_PREFIX,\n            ADD_EVENT_LISTENER_STR,\n            REMOVE_EVENT_LISTENER_STR,\n        });\n    });\n}\n\nfunction patchCommon(Zone) {\n    patchPromise(Zone);\n    patchToString(Zone);\n    patchUtil(Zone);\n}\n\nconst Zone$1 = loadZone();\npatchCommon(Zone$1);\npatchBrowser(Zone$1);\n"], "names": ["window", "__Zone_disable_customElements", "global", "globalThis", "__symbol__", "name", "symbolPrefix", "initZone", "_ZoneImpl", "performance", "mark", "performanceMeasure", "label", "ZoneImpl", "assertZonePatched", "patches", "Error", "root", "zone", "current", "parent", "_currentZoneFrame", "currentTask", "_currentTask", "__load_patch", "fn", "ignoreDuplicate", "hasOwnProperty", "checkDuplicate", "perfName", "_api", "_parent", "_name", "constructor", "zoneSpec", "_properties", "properties", "_zoneDelegate", "_ZoneDelegate", "get", "key", "getZoneWith", "fork", "wrap", "callback", "source", "_callback", "intercept", "runGuarded", "arguments", "run", "applyThis", "applyArgs", "invoke", "error", "handleError", "runTask", "task", "NO_ZONE", "state", "notScheduled", "type", "eventTask", "macroTask", "re<PERSON><PERSON><PERSON><PERSON><PERSON>", "running", "_transitionTo", "scheduled", "runCount", "previousTask", "data", "isPeriodic", "cancelFn", "undefined", "invokeTask", "unknown", "_updateTaskCount", "scheduleTask", "newZone", "scheduling", "zoneDelegates", "_zoneDelegates", "_zone", "err", "scheduleMicroTask", "customSchedule", "ZoneTask", "microTask", "scheduleMacroTask", "customCancel", "scheduleEventTask", "cancelTask", "canceling", "count", "i", "length", "DELEGATE_ZS", "onHasTask", "delegate", "_", "target", "hasTaskState", "hasTask", "onScheduleTask", "onInvokeTask", "onCancelTask", "parentDelegate", "_taskCounts", "_parentDelegate", "_forkZS", "onFork", "_forkDlgt", "_forkCurrZone", "_interceptZS", "onIntercept", "_interceptDlgt", "_interceptCurrZone", "_invokeZS", "onInvoke", "_invokeDlgt", "_invokeCurrZone", "_handleErrorZS", "onHandleError", "_handleErrorDlgt", "_handleErrorCurrZone", "_scheduleTaskZS", "_scheduleTaskDlgt", "_scheduleTaskCurrZone", "_invokeTaskZS", "_invokeTaskDlgt", "_invokeTaskCurrZone", "_cancelTaskZS", "_cancelTaskDlgt", "_cancelTaskCurrZone", "_hasTaskZS", "_hasTaskDlgt", "_hasTaskDlgtOwner", "_hasTaskCurrZone", "zoneSpecHasTask", "parentHasTask", "targetZone", "apply", "returnTask", "push", "scheduleFn", "value", "isEmpty", "counts", "prev", "next", "change", "options", "_state", "self", "useG", "call", "args", "_numberOfNestedTaskFrames", "drainMicroTaskQueue", "cancelScheduleRequest", "toState", "fromState1", "fromState2", "toString", "handleId", "Object", "prototype", "toJSON", "symbolSetTimeout", "symbolPromise", "symbolThen", "_microTaskQueue", "_isDrainingMicrotaskQueue", "nativeMicroTaskQueuePromise", "nativeScheduleMicroTask", "func", "resolve", "nativeThen", "queue", "onUnhandledError", "microtaskDrainDone", "symbol", "currentZoneFrame", "noop", "showUncaughtError", "patchEventTarget", "patchOnProperties", "patchMethod", "bindArguments", "patchThen", "patchMacroTask", "patchEventPrototype", "isIEOrEdge", "getGlobalObjects", "ObjectDefineProperty", "ObjectGetOwnPropertyDescriptor", "ObjectCreate", "ArraySlice", "patchClass", "wrapWithCurrentZone", "filterProperties", "attachOriginToPatched", "_redefineProperty", "patchCallbacks", "loadZone", "_Zone", "_global$_Zone", "getOwnPropertyDescriptor", "defineProperty", "ObjectGetPrototypeOf", "getPrototypeOf", "create", "Array", "slice", "ADD_EVENT_LISTENER_STR", "REMOVE_EVENT_LISTENER_STR", "ZONE_SYMBOL_ADD_EVENT_LISTENER", "ZONE_SYMBOL_REMOVE_EVENT_LISTENER", "TRUE_STR", "FALSE_STR", "ZONE_SYMBOL_PREFIX", "Zone", "scheduleMacroTaskWithCurrentZone", "zoneSymbol", "isWindowExists", "internalWindow", "_global", "REMOVE_ATTRIBUTE", "patchPrototype", "fnNames", "prototypeDesc", "isPropertyWritable", "patched", "propertyDesc", "writable", "set", "isWebWorker", "WorkerGlobalScope", "isNode", "process", "<PERSON><PERSON><PERSON><PERSON>", "isMix", "zoneSymbolEventNames$1", "wrapFn", "event", "eventNameSymbol", "listener", "result", "errorEvent", "message", "filename", "lineno", "colno", "preventDefault", "patchProperty", "obj", "prop", "desc", "enumerable", "configurable", "onPropPatchedSymbol", "originalDescGet", "originalDescSet", "eventName", "newValue", "previousValue", "removeEventListener", "addEventListener", "removeAttribute", "onProperties", "j", "originalInstanceKey", "className", "OriginalClass", "a", "instance", "patchFn", "proto", "<PERSON><PERSON><PERSON>", "patchDelegate", "funcName", "metaCreator", "setNative", "cbIdx", "meta", "original", "isDetectedIEOrEdge", "ieOrEdge", "isIE", "ua", "navigator", "userAgent", "indexOf", "passiveSupported", "OPTIMIZED_ZONE_EVENT_TASK_DATA", "zoneSymbolEventNames", "globalSources", "EVENT_NAME_SYMBOL_REGX", "RegExp", "IMMEDIATE_PROPAGATION_SYMBOL", "prepareEventNames", "eventNameToString", "falseEventName", "trueEventName", "symbolCapture", "api", "apis", "patchOptions", "ADD_EVENT_LISTENER", "add", "REMOVE_EVENT_LISTENER", "rm", "LISTENERS_EVENT_LISTENER", "listeners", "REMOVE_ALL_LISTENERS_EVENT_LISTENER", "rmAll", "zoneSymbolAddEventListener", "ADD_EVENT_LISTENER_SOURCE", "PREPEND_EVENT_LISTENER", "PREPEND_EVENT_LISTENER_SOURCE", "isRemoved", "handleEvent", "originalDelegate", "once", "globalCallback", "context", "isCapture", "tasks", "errors", "copyTasks", "globalZoneAwareCallback", "globalZoneAwareCaptureCallback", "patchEventTargetMethods", "useGlobalCallback", "validate<PERSON><PERSON><PERSON>", "vh", "chkDup", "<PERSON><PERSON><PERSON><PERSON>", "rt", "taskData", "nativeAddEventListener", "nativeRemoveEventListener", "nativeListeners", "nativeRemoveAllListeners", "nativePrependEventListener", "prepend", "buildEventListenerOptions", "passive", "capture", "customScheduleGlobal", "isExisting", "customCancelGlobal", "symbolEventNames", "symbolEventName", "existingTasks", "existingTask", "splice", "removeAbortListener", "allRemoved", "customScheduleNonGlobal", "customSchedulePrepend", "customCancelNonGlobal", "compareTaskCallbackVsDelegate", "typeOfDelegate", "compare", "diff", "unpatchedEvents", "passiveEvents", "copyEventListenerOptions", "newOptions", "signal", "makeAddListener", "nativeListener", "addSource", "customScheduleFn", "customCancelFn", "transferEventName", "isHandleEvent", "aborted", "constructorName", "targetSource", "onAbort", "unshift", "onPropertySymbol", "findEventTasks", "keys", "match", "exec", "evtName", "symbolCaptureEventName", "captureTasks", "removeTasks", "results", "foundTasks", "captureFalseTasks", "captureTrueTasks", "concat", "Event", "patchQueueMicrotask", "taskSymbol", "patchTimer", "setName", "cancelName", "nameSuffix", "clearNative", "tasksByHandleId", "clearTask", "delay", "timer", "handle", "ref", "unref", "bind", "id", "patchCustomElements", "callbacks", "customElements", "eventTargetPatch", "eventNames", "EVENT_TARGET", "patchEvent", "ignoreProperties", "tip", "filter", "ip", "targetIgnoreProperties", "op", "patchFilteredProperties", "filteredProperties", "getOnEventNames", "getOwnPropertyNames", "startsWith", "map", "substring", "propertyDescriptorPatch", "patchTargets", "ignoreErrorProperties", "patchBrowser", "legacyPatch", "clear", "blockingMethods", "s", "XMLHttpRequestEventTarget", "patchXHR", "XHR_TASK", "XHR_SYNC", "XHR_LISTENER", "XHR_SCHEDULED", "XHR_URL", "XHR_ERROR_BEFORE_SCHEDULED", "XMLHttpRequest", "XMLHttpRequestPrototype", "findPendingTask", "oriAddListener", "oriRemoveListener", "XMLHttpRequestEventTargetPrototype", "READY_STATE_CHANGE", "SCHEDULED", "newListener", "readyState", "DONE", "loadTasks", "status", "oriInvoke", "storedTask", "sendNative", "placeholder<PERSON><PERSON><PERSON>", "abortNative", "openNative", "XMLHTTPREQUEST_SOURCE", "fetchTaskAborting", "fetchTaskScheduling", "url", "geolocation", "findPromiseRejectionHandler", "e", "eventTasks", "for<PERSON>ach", "PromiseRejectionEvent", "evt", "promise", "reason", "rejection", "patchPromise", "readableObjectToString", "JSON", "stringify", "_uncaughtPromiseErrors", "isDisableWrappingUncaughtPromiseRejection", "creationTrace", "console", "stack", "uncaughtPromiseError", "shift", "throwOriginal", "handleUnhandledRejection", "UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL", "handler", "isThenable", "then", "forwardResolution", "forwardRejection", "ZoneAwarePromise", "reject", "symbolState", "symbolValue", "symbolFinally", "symbolParentPromiseValue", "symbolParentPromiseState", "UNRESOLVED", "RESOLVED", "REJECTED", "REJECTED_NO_CATCH", "makeResolver", "v", "resolvePromise", "wasCalled", "wrapper", "wrappedFunction", "TYPE_ERROR", "CURRENT_TASK_TRACE_SYMBOL", "onceWrapper", "TypeError", "clearRejectedNoCatch", "trace", "scheduleResolveOrReject", "REJECTION_HANDLED_HANDLER", "chainPromise", "onFulfilled", "onRejected", "promiseState", "parentPromiseValue", "isFinallyPromise", "ZONE_AWARE_PROMISE_TO_STRING", "AggregateError", "withResolvers", "res", "rej", "any", "values", "Symbol", "iterator", "Promise", "promises", "finished", "race", "onResolve", "onReject", "all", "allWithCallback", "allSettled", "P", "then<PERSON>allback", "<PERSON><PERSON><PERSON><PERSON>", "unresolvedCount", "valueIndex", "resolvedV<PERSON>ues", "curValueIndex", "thenErr", "executor", "toStringTag", "species", "_this$constructor", "C", "catch", "finally", "onFinally", "_this$constructor2", "NativePromise", "symbolThenPatched", "Ctor", "originalThen", "wrapped", "zoneify", "resultPromise", "ctor", "patchToString", "originalFunctionToString", "Function", "ORIGINAL_DELEGATE_SYMBOL", "PROMISE_SYMBOL", "ERROR_SYMBOL", "newFunctionToString", "nativePromise", "nativeError", "originalObjectToString", "PROMISE_OBJECT_TO_STRING", "targetName", "method", "nativeDelegate", "opts", "descriptor", "patchUtil", "SYMBOL_BLACK_LISTED_EVENTS", "SYMBOL_UNPATCHED_EVENTS", "patchCommon", "Zone$1"], "sourceRoot": "webpack:///", "x_google_ignoreList": [2]}