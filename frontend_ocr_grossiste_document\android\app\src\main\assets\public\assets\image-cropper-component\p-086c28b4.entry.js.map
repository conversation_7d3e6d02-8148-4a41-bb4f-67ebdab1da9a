{"version": 3, "names": ["imageCropperCss", "ImageCropperStyle0", "ImageCropper", "this", "handlers", "polygonMouseDown", "polygonMouseDownPoint", "x", "y", "previousDistance", "undefined", "svgMouseDownPoint", "handlerMouseDownPoint", "originalPoints", "usingTouchEvent", "usingQuad", "componentDidLoad", "containerElement", "addEventListener", "e", "onContainerTouchMove", "hideMagnifier", "selectedHandlerIndex", "watchImgPropHandler", "newValue", "console", "log", "resetStates", "viewBox", "naturalWidth", "naturalHeight", "root", "inActiveStroke", "parseInt", "style", "getPropertyValue", "activeStroke", "watchRectPropHandler", "points", "getPointsFromRect", "img", "restrainPointsInBounds", "rect", "point1", "point2", "width", "point3", "height", "point4", "watchQuadPropHandler", "onCanceled", "canceled", "emit", "onConfirmed", "confirmed", "getPointsData", "pointsData", "renderFooter", "hidefooter", "h", "class", "onClick", "src", "rendenInactiveSelections", "inactiveSelections", "Fragment", "map", "selection", "getPointsDataFromSelection", "pointerEvents", "getRatio", "fill", "onSelectionClicked", "index", "selectionClicked", "renderHandlers", "getHandlerPos", "getHandlerSize", "stroke", "onMouseDown", "onHandlerMouseDown", "onTouchStart", "onHandlerTouchStart", "key", "pos", "size", "ratio", "handlersize", "error", "Math", "ceil", "onSVGTouchStart", "coord", "getMousePosition", "svgElement", "touches", "length", "JSON", "parse", "stringify", "onSVGTouchEnd", "onSVGTouchMove", "stopPropagation", "preventDefault", "pinchAndZoom", "panSVG", "handleMoveEvent", "distance", "getDistanceBetweenTwoTouches", "scale", "min", "max", "touch1", "touch2", "offsetX", "clientX", "offsetY", "clientY", "onContainerMouseUp", "onSVGMouseDown", "onContainerWheel", "deltaY", "getPanAndZoomStyle", "rotation", "onSVGMouseMove", "newPoints", "point", "showMagnifier", "updateMagnifier", "pointIndex", "getPointIndexFromHandlerIndex", "selectedPoint", "imgWidth", "imgHeight", "margin", "onPolygonMouseDown", "onPolygonMouseUp", "onPolygonTouchStart", "onPolygonTouchEnd", "onHandlerMouseUp", "onHandlerPointerDown", "pointerType", "event", "svg", "CTM", "getScreenCTM", "targetTouches", "det", "a", "d", "b", "c", "invCTM", "f", "getAllSelections", "convertTo", "all", "getRectFromPoints", "push", "useQuad", "quad", "getQuad", "getRect", "getPoints", "minX", "minY", "maxX", "maxY", "floor", "getImageFromBlob", "source", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onloadend", "dataURL", "result", "document", "createElement", "onload", "onerror", "getImageFromDataURL", "detect", "getSVGWidth", "imgRatio", "clientHeight", "parentElement", "clientWidth", "onSVGPointerMove", "onSVGPointerDown", "onSVGPointerUp", "onPolygonPointerDown", "onPolygonPointerUp", "render", "Host", "ref", "el", "onWheel", "onMouseUp", "canvasElement", "version", "xmlns", "transform", "onMouseMove", "onTouchEnd", "onTouchMove", "onPointerMove", "onPointerDown", "onPointerUp", "href", "magnifierElement", "display", "magnifierSize", "magnifierLeft", "magnifierTop", "createSVGPoint", "ctm", "transformedPoint", "matrixTransform", "left", "top", "zoomLevel", "sx", "sy", "sw", "sh", "dx", "dy", "dw", "dh", "magnifier<PERSON>anvas", "magnifierCtx", "getContext", "drawImage", "strokeStyle", "lineWidth", "beginPath", "moveTo", "i", "lineTo", "closePath", "backgroundImage", "toDataURL"], "sources": ["src/components/image-cropper/image-cropper.css?tag=image-cropper&encapsulation=shadow", "src/components/image-cropper/image-cropper.tsx"], "sourcesContent": [":host {\r\n  /* --active-color: rgb(5, 197, 175); */\r\n    /* --inactive-color: gray; */\r\n  --active-color:orange;\r\n  --inactive-color:orange;\r\n  --active-stroke: 4;\r\n  --inactive-stroke: 4;\r\n  --main-background: transparent;\r\n  display: block;\r\n  position: relative;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n* {\r\n  user-select:none;\r\n  -webkit-user-select:none;\r\n  -moz-user-select:none;\r\n}\r\n\r\n.container {\r\n  display: flex;\r\n  justify-content: center;\r\n  background: var(--main-background);\r\n  overflow: hidden;\r\n}\r\n\r\n.absolute {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.cropper-controls {\r\n  stroke:var(--active-color);\r\n}\r\n\r\n.footer {\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 0;\r\n  height: 100px;\r\n  width: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.items {\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 2.0em;\r\n}\r\n\r\n.items .item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n.items .item:first-child {\r\n  text-align: left;\r\n}\r\n.items .item:last-child {\r\n  text-align: right;\r\n}\r\n\r\n.accept-use img {\r\n  width: 2.5em;\r\n  height: 2.5em;\r\n  pointer-events: all;\r\n  cursor:pointer;\r\n}\r\n\r\n.accept-cancel img {\r\n  width: 2.5em;\r\n  height: 2.5em;\r\n  pointer-events: all;\r\n  cursor:pointer;\r\n}\r\n\r\n.cropper-svg {\r\n  align-self: center;\r\n  touch-action: none;\r\n  cursor:grab;\r\n  color: var(--active-color);\r\n}\r\n\r\n.cropper-svg polygon {\r\n  cursor:move;\r\n}\r\n\r\n.cropper-svg rect {\r\n  cursor:grab;\r\n}\r\n\r\n.hidden-canvas {\r\n  display: none;\r\n}\r\n\r\n.cropper-svg .inactive-selection {\r\n  stroke:var(--inactive-color);\r\n  cursor:pointer;\r\n}\r\n\r\n.dashed {\r\n  stroke-dasharray:10,10;\r\n}\r\n\r\n\r\n.magnifier {\r\n  position: absolute;\r\n  width: 100px;\r\n  height: 100px;\r\n  left: 0;\r\n  top: 0;\r\n  border: 1px solid #00bceb;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  display: none;\r\n  pointer-events: none;\r\n  background-size: 100%;\r\n  background-repeat: no-repeat;\r\n}", "import { Component, Event, EventEmitter, Fragment, Host, Method, Prop, State, Watch, h } from '@stencil/core';\r\n\r\nexport interface DetectedQuadResult{\r\n  location: Quad;\r\n  confidenceAsDocumentBoundary: number;\r\n}\r\n\r\nexport interface Quad{\r\n  points:[Point,Point,Point,Point];\r\n}\r\n\r\nexport interface Point{\r\n  x:number;\r\n  y:number;\r\n}\r\n\r\nexport interface Rect{\r\n  x:number;\r\n  y:number;\r\n  width:number;\r\n  height:number;\r\n}\r\n\r\nexport interface CropOptions {\r\n  perspectiveTransform?:boolean;\r\n  colorMode?:\"binary\"|\"gray\"|\"color\";\r\n  selection?:Quad|Rect;\r\n  source?:Blob|string|HTMLImageElement|HTMLCanvasElement;\r\n}\r\n\r\n@Component({\r\n  tag: 'image-cropper',\r\n  styleUrl: 'image-cropper.css',\r\n  shadow: true,\r\n})\r\nexport class ImageCropper {\r\n  handlers:number[] = [0,1,2,3,4,5,6,7];\r\n  polygonMouseDown:boolean = false;\r\n  polygonMouseDownPoint:Point = {x:0,y:0};\r\n  previousDistance:number|undefined = undefined;\r\n  svgMouseDownPoint:Point|undefined = undefined;\r\n  handlerMouseDownPoint:Point = {x:0,y:0};\r\n  root:HTMLElement;\r\n  containerElement:HTMLElement;\r\n  svgElement:SVGElement;\r\n  canvasElement:HTMLCanvasElement;\r\n  originalPoints:[Point,Point,Point,Point] = undefined;\r\n   usingTouchEvent:boolean = false;\r\n  usingQuad = false;\r\n  magnifierElement: HTMLElement; // Add this line\r\n\r\n  @Prop() img?: HTMLImageElement;\r\n  @Prop() rect?: Rect;\r\n  @Prop() quad?: Quad;\r\n  @Prop() license?: string;\r\n  @Prop() hidefooter?: string;\r\n  @Prop() handlersize?: string;\r\n  @Prop() inactiveSelections?: (Quad|Rect)[];\r\n  @State() viewBox:string = \"0 0 1280 720\";\r\n  @State() activeStroke:number = 2;\r\n  @Prop() rotation:number = 0;\r\n  @State() inActiveStroke:number = 4;\r\n  @State() selectedHandlerIndex:number = -1;\r\n  @State() points:[Point,Point,Point,Point] = undefined;\r\n  @State() offsetX = 0;\r\n  @State() offsetY = 0;\r\n  @State() scale = 1.0;\r\n  @Event() confirmed?: EventEmitter<void>;\r\n  @Event() canceled?: EventEmitter<void>;\r\n  @Event() selectionClicked?: EventEmitter<number>;\r\n\r\n  // componentDidLoad(){\r\n  //   this.containerElement.addEventListener(\"touchmove\", (e:TouchEvent) => {\r\n  //     this.onContainerTouchMove(e);\r\n  //   })\r\n  //   this.containerElement.addEventListener(\"touchend\", () => {\r\n  //     this.previousDistance = undefined;\r\n  //     this.hideMagnifier(); // Hide magnifier on touch end\r\n  //   })\r\n  // }\r\n\r\n  componentDidLoad() {\r\n    // If user moves their finger on the container\r\n    this.containerElement.addEventListener('touchmove', (e: TouchEvent) => {\r\n      this.onContainerTouchMove(e);\r\n    });\r\n  \r\n    // When user lifts their finger anywhere in container:\r\n    this.containerElement.addEventListener('touchend', () => {\r\n      this.previousDistance = undefined;\r\n      this.hideMagnifier(); \r\n      // === End any drag that was in progress ===\r\n      this.selectedHandlerIndex = -1;\r\n      this.polygonMouseDown = false;\r\n    });\r\n  }\r\n\r\n  @Watch('img')\r\n  watchImgPropHandler(newValue: HTMLImageElement) {\r\n    if (newValue) {\r\n      console.log('watchImgPropHandler triggered with newValue:', newValue);\r\n      this.resetStates();\r\n      this.viewBox = `0 0 ${newValue.naturalWidth} ${newValue.naturalHeight}`;\r\n      console.log('viewBox set to:', this.viewBox);\r\n      if (this.root) {\r\n        const inActiveStroke = parseInt(this.root.style.getPropertyValue(\"--inactive-stroke\"));\r\n        const activeStroke = parseInt(this.root.style.getPropertyValue(\"--active-stroke\"));\r\n        console.log('inActiveStroke:', inActiveStroke, 'activeStroke:', activeStroke);\r\n        if (inActiveStroke) {\r\n          this.inActiveStroke = inActiveStroke;\r\n        }\r\n        if (activeStroke) {\r\n          this.activeStroke = activeStroke;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  @Watch('rect')\r\n  watchRectPropHandler(newValue: Rect) {\r\n    if (newValue) {\r\n      this.usingQuad = false;\r\n      let points = this.getPointsFromRect(newValue);\r\n      if (this.img) {\r\n        this.restrainPointsInBounds(points,this.img.naturalWidth,this.img.naturalHeight);\r\n      }\r\n      this.points = points;\r\n    }\r\n  }\r\n\r\n  getPointsFromRect(rect:Rect):[Point,Point,Point,Point]{\r\n    const point1:Point = {x:rect.x,y:rect.y};\r\n    const point2:Point = {x:rect.x+rect.width,y:rect.y};\r\n    const point3:Point = {x:rect.x+rect.width,y:rect.y+rect.height};\r\n    const point4:Point = {x:rect.x,y:rect.y+rect.height};\r\n    return [point1,point2,point3,point4];\r\n  }\r\n\r\n  @Watch('quad')\r\n  watchQuadPropHandler(newValue: Quad) {\r\n    if (newValue) {\r\n      this.usingQuad = true;\r\n      let points = newValue.points;\r\n      if (this.img) {\r\n        this.restrainPointsInBounds(points,this.img.naturalWidth,this.img.naturalHeight);\r\n      }\r\n      this.points = newValue.points;\r\n    }\r\n  }\r\n\r\n  onCanceled(){\r\n    if (this.canceled){\r\n      this.canceled.emit();\r\n    }\r\n  }\r\n\r\n  onConfirmed(){\r\n    if (this.confirmed){\r\n      this.confirmed.emit();\r\n    }\r\n  }\r\n\r\n  getPointsData(){\r\n    if (this.points) {\r\n      let pointsData = this.points[0].x + \",\" + this.points[0].y + \" \";\r\n      pointsData = pointsData + this.points[1].x + \",\" + this.points[1].y +\" \";\r\n      pointsData = pointsData + this.points[2].x + \",\" + this.points[2].y +\" \";\r\n      pointsData = pointsData + this.points[3].x + \",\" + this.points[3].y;\r\n      return pointsData;\r\n    }\r\n    return \"\";\r\n  }\r\n\r\n  renderFooter(){\r\n    if (this.hidefooter === \"\") {\r\n      return \"\";\r\n    }\r\n    return (\r\n      <div class=\"footer\">\r\n        <section class=\"items\">\r\n          <div class=\"item accept-cancel\" onClick={() => this.onCanceled()}>\r\n            <img src=\"data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Ccircle fill='%23727A87' cx='256' cy='256' r='256'/%3E%3Cg id='Icon_5_'%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M394.2,142L370,117.8c-1.6-1.6-4.1-1.6-5.7,0L258.8,223.4c-1.6,1.6-4.1,1.6-5.7,0L147.6,117.8 c-1.6-1.6-4.1-1.6-5.7,0L117.8,142c-1.6,1.6-1.6,4.1,0,5.7l105.5,105.5c1.6,1.6,1.6,4.1,0,5.7L117.8,364.4c-1.6,1.6-1.6,4.1,0,5.7 l24.1,24.1c1.6,1.6,4.1,1.6,5.7,0l105.5-105.5c1.6-1.6,4.1-1.6,5.7,0l105.5,105.5c1.6,1.6,4.1,1.6,5.7,0l24.1-24.1 c1.6-1.6,1.6-4.1,0-5.7L288.6,258.8c-1.6-1.6-1.6-4.1,0-5.7l105.5-105.5C395.7,146.1,395.7,143.5,394.2,142z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\" />\r\n          </div>\r\n          <div class=\"item accept-use\" onClick={() => this.onConfirmed()}>\r\n            <img src=\"data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Ccircle fill='%232CD865' cx='256' cy='256' r='256'/%3E%3Cg id='Icon_1_'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M208,301.4l-55.4-55.5c-1.5-1.5-4-1.6-5.6-0.1l-23.4,22.3c-1.6,1.6-1.7,4.1-0.1,5.7l81.6,81.4 c3.1,3.1,8.2,3.1,11.3,0l171.8-171.7c1.6-1.6,1.6-4.2-0.1-5.7l-23.4-22.3c-1.6-1.5-4.1-1.5-5.6,0.1L213.7,301.4 C212.1,303,209.6,303,208,301.4z'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\" />\r\n          </div>\r\n        </section>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  rendenInactiveSelections(){\r\n    if (!this.inactiveSelections) {\r\n      return \"\";\r\n    }\r\n    return (\r\n      <Fragment>\r\n        {this.inactiveSelections.map((selection) => (\r\n          <polygon\r\n            points={this.getPointsDataFromSelection(selection)}\r\n            class=\"inactive-selection\"\r\n            style={{ pointerEvents: 'none' }}\r\n            stroke-width={this.inActiveStroke * this.getRatio()}\r\n            fill=\"transparent\"\r\n            // onMouseUp={()=>this.onSelectionClicked(index)}\r\n            // onTouchStart={()=>this.onSelectionClicked(index)}\r\n          >\r\n         </polygon>\r\n        ))}\r\n      </Fragment>\r\n    );\r\n  }\r\n\r\n  onSelectionClicked(index:number) {\r\n    if (this.selectionClicked) {\r\n      this.selectionClicked.emit(index);\r\n    }\r\n  }\r\n\r\n  getPointsDataFromSelection(selection:Quad|Rect){\r\n    let points:Point[] = [];\r\n    if (\"width\" in selection) { //is Rect\r\n      points = this.getPointsFromRect(selection);\r\n    }else{\r\n      points = selection.points;\r\n    }\r\n    let pointsData = points[0].x + \",\" + points[0].y + \" \";\r\n    pointsData = pointsData + points[1].x + \",\" + points[1].y +\" \";\r\n    pointsData = pointsData + points[2].x + \",\" + points[2].y +\" \";\r\n    pointsData = pointsData + points[3].x + \",\" + points[3].y;\r\n    return pointsData;\r\n  }\r\n\r\n  renderHandlers(){\r\n    if (!this.points) {\r\n      return (<div></div>)\r\n    }\r\n    return (\r\n      <Fragment>\r\n      {this.handlers.map(index => (\r\n        <g>\r\n          {/* Invisible hit area with larger stroke */}\r\n          <rect\r\n            x={this.getHandlerPos(index, \"x\")}\r\n            y={this.getHandlerPos(index, \"y\")}\r\n            width={this.getHandlerSize()}\r\n            height={this.getHandlerSize()}\r\n            class=\"cropper-controls-hit\"\r\n            stroke=\"transparent\"\r\n            stroke-width={20 * this.getRatio()} // Larger hit area\r\n            fill=\"none\"\r\n            pointer-events=\"visibleStroke\"\r\n            onMouseDown={(e: MouseEvent) => this.onHandlerMouseDown(e, index)}\r\n            onTouchStart={(e: TouchEvent) => this.onHandlerTouchStart(e, index)}\r\n          />\r\n          {/* Visible handle */}\r\n          <rect\r\n            x={this.getHandlerPos(index, \"x\")}\r\n            y={this.getHandlerPos(index, \"y\")}\r\n            width={this.getHandlerSize()}\r\n            height={this.getHandlerSize()}\r\n            class=\"cropper-controls-visual\"\r\n            stroke-width={index === this.selectedHandlerIndex \r\n              ? this.activeStroke * 2 * this.getRatio() \r\n              : this.activeStroke * this.getRatio()}\r\n            stroke=\"currentColor\"\r\n            fill=\"none\"\r\n            pointer-events=\"none\"\r\n          />\r\n        </g>\r\n      ))}\r\n    </Fragment>\r\n    )\r\n  }\r\n\r\n  getHandlerPos(index:number,key:string) {\r\n    let pos = 0;\r\n    let size = this.getHandlerSize();\r\n    if (index === 0){\r\n      pos = this.points[0][key];\r\n    }else if (index === 1) {\r\n      pos = this.points[0][key] + (this.points[1][key] - this.points[0][key])/2;\r\n    }else if (index === 2) {\r\n      pos = this.points[1][key];\r\n    }else if (index === 3) {\r\n      pos = this.points[1][key] + (this.points[2][key] - this.points[1][key])/2;\r\n    }else if (index === 4) {\r\n      pos = this.points[2][key];\r\n    }else if (index === 5) {\r\n      pos = this.points[3][key] + (this.points[2][key] - this.points[3][key])/2;\r\n    }else if (index === 6) {\r\n      pos = this.points[3][key];\r\n    }else if (index === 7) {\r\n      pos = this.points[0][key] + (this.points[3][key] - this.points[0][key])/2;\r\n    }\r\n    pos = pos - size/2;\r\n    return pos;\r\n  }\r\n\r\n  getHandlerSize() {\r\n    let ratio = this.getRatio();\r\n    let size:number = 25;\r\n    if (this.handlersize) {\r\n      try {\r\n        size = parseInt(this.handlersize);\r\n      } catch (error) {\r\n        console.log(error);\r\n      }\r\n    }\r\n    return Math.ceil(size*ratio);\r\n  }\r\n\r\n  onSVGTouchStart(e:TouchEvent) {\r\n    this.usingTouchEvent = true;\r\n    this.svgMouseDownPoint = undefined;\r\n    this.previousDistance = undefined;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    if (e.touches.length > 1) {\r\n      this.selectedHandlerIndex = -1;\r\n    }else{\r\n      if (this.selectedHandlerIndex != -1) {\r\n        this.originalPoints = JSON.parse(JSON.stringify(this.points));  //We need this info so that whether we start dragging the rectangular in the center or in the corner will not affect the result.\r\n        this.handlerMouseDownPoint.x = coord.x;\r\n        this.handlerMouseDownPoint.y = coord.y;\r\n      }else{\r\n        this.svgMouseDownPoint = {x:coord.x,y:coord.y};\r\n        this.polygonMouseDown = true; // Add this line to enable dragging immediately\r\n        this.polygonMouseDownPoint = { x: coord.x, y: coord.y }; // Add this line to store the initial touch point\r\n        this.originalPoints = JSON.parse(JSON.stringify(this.points)); // Add this line to store the original points\r\n      }\r\n    }\r\n  }\r\n\r\n  onSVGTouchEnd() {\r\n    this.svgMouseDownPoint = undefined;\r\n  }\r\n\r\n  onSVGTouchMove(e:TouchEvent) {\r\n    e.stopPropagation();\r\n    e.preventDefault();\r\n    if (e.touches.length === 2) {\r\n      this.pinchAndZoom(e);\r\n    }else{\r\n      if (this.svgMouseDownPoint) {\r\n        this.panSVG(e);\r\n      } else if (this.polygonMouseDown) { // Add this condition to handle dragging\r\n        this.handleMoveEvent(e);\r\n      }else{\r\n        this.handleMoveEvent(e);\r\n      }\r\n    }\r\n  }\r\n\r\n  //handle pinch and zoom\r\n  pinchAndZoom(e:TouchEvent){\r\n    const distance = this.getDistanceBetweenTwoTouches(e.touches[0],e.touches[1]);\r\n    if (this.previousDistance) {\r\n      if ((distance - this.previousDistance)>0) { //zoom\r\n        this.scale = Math.min(10, this.scale + 0.02);\r\n      }else{\r\n        this.scale = Math.max(0.1,this.scale - 0.02);\r\n      }\r\n      this.previousDistance = distance;\r\n    }else{\r\n      this.previousDistance = distance;\r\n    }\r\n  }\r\n\r\n  getDistanceBetweenTwoTouches(touch1:Touch,touch2:Touch){\r\n    const offsetX = touch1.clientX - touch2.clientX;\r\n    const offsetY = touch1.clientY - touch2.clientY;\r\n    const distance = offsetX * offsetX + offsetY + offsetY;\r\n    return distance;\r\n  }\r\n\r\n  onContainerMouseUp(){\r\n    this.svgMouseDownPoint = undefined;\r\n    if (!this.usingTouchEvent) {\r\n      this.selectedHandlerIndex = -1;\r\n      this.polygonMouseDown = false;\r\n      this.hideMagnifier(); // Hide the magnifier\r\n    }\r\n  }\r\n\r\n  onSVGMouseDown(e:MouseEvent) {\r\n    if (!this.usingTouchEvent) {\r\n      let coord = this.getMousePosition(e,this.svgElement);\r\n      this.svgMouseDownPoint = {x:coord.x,y:coord.y};\r\n    }\r\n  }\r\n\r\n  onContainerWheel(e:WheelEvent) {\r\n    if (e.deltaY<0) {\r\n      this.scale = this.scale + 0.1;\r\n    }else{\r\n      this.scale = Math.max(0.1, this.scale - 0.1);\r\n    }\r\n    e.preventDefault();\r\n  }\r\n\r\n  onContainerTouchMove(e:TouchEvent) {\r\n    e.preventDefault();\r\n    if (e.touches.length === 2) {\r\n      this.pinchAndZoom(e);\r\n    }\r\n  }\r\n\r\n  getPanAndZoomStyle(){\r\n    if (this.img) {\r\n      // const percentX = this.offsetX / this.img.naturalWidth * 100;\r\n      // const percentY = this.offsetY / this.img.naturalHeight * 100;\r\n      return `scale(1.0)  rotate(${this.rotation}deg)`;\r\n      // return \"scale(\"+this.scale+\") translateX(\"+percentX+\"%)translateY(\"+percentY+\"%)\";\r\n    }else{\r\n      return \"scale(1.0)\";\r\n    }\r\n  }\r\n\r\n  onSVGMouseMove(e:MouseEvent){\r\n    if (this.svgMouseDownPoint) {\r\n      this.panSVG(e);\r\n    }else{\r\n      this.handleMoveEvent(e);\r\n    }\r\n  }\r\n\r\n  panSVG(e:TouchEvent|MouseEvent){\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    let offsetX = coord.x - this.svgMouseDownPoint.x;\r\n    let offsetY = coord.y - this.svgMouseDownPoint.y;\r\n    //console.log(\"coord\");\r\n    //console.log(coord);\r\n    //console.log(\"svgMouseDownPoint\");\r\n    //console.log(this.svgMouseDownPoint);\r\n\r\n    //console.log(offsetX)\r\n    //console.log(offsetY)\r\n    //e.g img width: 100, offsetX: -10, translateX: -10%\r\n    this.offsetX = this.offsetX + offsetX;\r\n    this.offsetY = this.offsetY + offsetY;\r\n  }\r\n\r\n  handleMoveEvent(e:MouseEvent|TouchEvent){\r\n    if (this.polygonMouseDown) {\r\n      let coord = this.getMousePosition(e,this.svgElement);\r\n      let offsetX = coord.x - this.polygonMouseDownPoint.x;\r\n      let offsetY = coord.y - this.polygonMouseDownPoint.y;\r\n      let newPoints = JSON.parse(JSON.stringify(this.originalPoints));\r\n      for (const point of newPoints) {\r\n        point.x = point.x + offsetX;\r\n        point.y = point.y + offsetY;\r\n        if (point.x < 0 || point.y < 0 || point.x > this.img.naturalWidth || point.y > this.img.naturalHeight){\r\n          console.log(\"reach bounds\");\r\n          return;\r\n        }\r\n      }\r\n      this.points = newPoints;\r\n      this.showMagnifier(); // Show the magnifier when the rect is moved\r\n      this.updateMagnifier(coord); // Update the magnifier position and content\r\n    }\r\n    if (this.selectedHandlerIndex >= 0) {\r\n      let pointIndex = this.getPointIndexFromHandlerIndex(this.selectedHandlerIndex);\r\n      let coord = this.getMousePosition(e,this.svgElement);\r\n      let offsetX = coord.x - this.handlerMouseDownPoint.x;\r\n      let offsetY = coord.y - this.handlerMouseDownPoint.y;\r\n      let newPoints = JSON.parse(JSON.stringify(this.originalPoints));\r\n      if (pointIndex != -1) {\r\n        let selectedPoint = newPoints[pointIndex];\r\n        selectedPoint.x = this.originalPoints[pointIndex].x + offsetX;\r\n        selectedPoint.y = this.originalPoints[pointIndex].y + offsetY;\r\n        if (this.usingQuad === false) { //rect mode\r\n          if (pointIndex === 0) {\r\n            newPoints[1].y = selectedPoint.y;\r\n            newPoints[3].x = selectedPoint.x;\r\n          }else if (pointIndex === 1) {\r\n            newPoints[0].y = selectedPoint.y;\r\n            newPoints[2].x = selectedPoint.x;\r\n          }else if (pointIndex === 2) {\r\n            newPoints[1].x = selectedPoint.x;\r\n            newPoints[3].y = selectedPoint.y;\r\n          }else if (pointIndex === 3) {\r\n            newPoints[0].x = selectedPoint.x;\r\n            newPoints[2].y = selectedPoint.y;\r\n          }\r\n        }\r\n      }else{ //mid-point handlers\r\n        if (this.selectedHandlerIndex === 1) {\r\n          newPoints[0].y = this.originalPoints[0].y + offsetY;\r\n          newPoints[1].y = this.originalPoints[1].y + offsetY;\r\n        }else if (this.selectedHandlerIndex === 3) {\r\n          newPoints[1].x = this.originalPoints[1].x + offsetX;\r\n          newPoints[2].x = this.originalPoints[2].x + offsetX;\r\n        }else if (this.selectedHandlerIndex === 5) {\r\n          newPoints[2].y = this.originalPoints[2].y + offsetY;\r\n          newPoints[3].y = this.originalPoints[3].y + offsetY;\r\n        }else if (this.selectedHandlerIndex === 7) {\r\n          newPoints[0].x = this.originalPoints[0].x + offsetX;\r\n          newPoints[3].x = this.originalPoints[3].x + offsetX;\r\n        }\r\n      }\r\n      if (this.img) {\r\n        this.restrainPointsInBounds(newPoints,this.img.naturalWidth,this.img.naturalHeight);\r\n      }\r\n      this.points = newPoints;\r\n      this.showMagnifier(); // Show the magnifier when the rect is moved\r\n      this.updateMagnifier(coord); // Update the magnifier position and content\r\n    }\r\n  }\r\n\r\n  restrainPointsInBounds(points: [Point, Point, Point, Point], imgWidth: number, imgHeight: number){\r\n    // Define the margin/padding you want:\r\n    const margin = 20; // or 10, 20, etc. – whichever \"inset\" you prefer\r\n    for (let index = 0; index < points.length; index++) {\r\n      const point = points[index];\r\n      point.x = Math.max(margin, point.x);\r\n      point.x = Math.min(point.x, imgWidth - margin);\r\n      point.y = Math.max(margin, point.y);\r\n      point.y = Math.min(point.y, imgHeight - margin);\r\n    }\r\n  }\r\n\r\n  onPolygonMouseDown(e:MouseEvent){\r\n    e.stopPropagation();\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    this.polygonMouseDown = true;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    this.polygonMouseDownPoint.x = coord.x;\r\n    this.polygonMouseDownPoint.y = coord.y;\r\n    this.showMagnifier(); // Show the magnifier when the rect starts being moved\r\n  }\r\n\r\n  onPolygonMouseUp(e:MouseEvent){\r\n    e.stopPropagation();\r\n    if (!this.usingTouchEvent) {\r\n      this.selectedHandlerIndex = -1;\r\n      this.polygonMouseDown = false;\r\n      this.hideMagnifier(); // Hide the magnifier when the rect stops being moved\r\n    }\r\n  }\r\n\r\n  onPolygonTouchStart(e:TouchEvent) {\r\n    this.usingTouchEvent = true;\r\n    e.stopPropagation();\r\n    this.selectedHandlerIndex = -1;\r\n    // this.polygonMouseDown = false;\r\n    this.polygonMouseDown = true;\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    // this.polygonMouseDown = true;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    // this.polygonMouseDownPoint.x = coord.x;\r\n    // this.polygonMouseDownPoint.y = coord.y;\r\n    this.polygonMouseDownPoint = { x: coord.x, y: coord.y }; // Store the initial touch point\r\n    this.showMagnifier(); // Show the magnifier when the rect starts being moved\r\n\r\n  }\r\n\r\n  onPolygonTouchEnd(e:TouchEvent) {\r\n    e.stopPropagation();\r\n    this.selectedHandlerIndex = -1;\r\n    this.polygonMouseDown = false;\r\n    this.hideMagnifier(); // Hide the magnifier when the rect stops being moved\r\n  }\r\n\r\n  // onHandlerMouseDown(e:MouseEvent,index:number){\r\n  //   e.stopPropagation();\r\n  //   let coord = this.getMousePosition(e,this.svgElement);\r\n  //   this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n  //   this.handlerMouseDownPoint.x = coord.x;\r\n  //   this.handlerMouseDownPoint.y = coord.y;\r\n  //   this.selectedHandlerIndex = index;\r\n  // }\r\n\r\n  onHandlerMouseDown(e: MouseEvent, index: number) {\r\n    e.stopPropagation();\r\n  \r\n    // If we’re already dragging a different handle, end that drag first:\r\n    if (this.selectedHandlerIndex !== -1 && this.selectedHandlerIndex !== index) {\r\n      // Manually call mouse-up logic to finalize the old handle drag\r\n      this.onHandlerMouseUp(e);\r\n    }\r\n  \r\n    let coord = this.getMousePosition(e, this.svgElement);\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    this.handlerMouseDownPoint.x = coord.x;\r\n    this.handlerMouseDownPoint.y = coord.y;\r\n  \r\n    // Now select the new handle\r\n    this.selectedHandlerIndex = index;\r\n  }\r\n\r\n  onHandlerMouseUp(e:MouseEvent){\r\n    e.stopPropagation();\r\n    if (!this.usingTouchEvent) {\r\n      this.selectedHandlerIndex = -1;\r\n      this.hideMagnifier(); // Hide the magnifier\r\n    }\r\n  }\r\n\r\n  onHandlerTouchStart(e:TouchEvent,index:number) {\r\n    this.usingTouchEvent = true; //Touch events are triggered before mouse events. We can use this to prevent executing mouse events.\r\n    e.stopPropagation();\r\n    this.polygonMouseDown = false;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    this.handlerMouseDownPoint.x = coord.x;\r\n    this.handlerMouseDownPoint.y = coord.y;\r\n    this.selectedHandlerIndex = index;\r\n  }\r\n\r\n  onHandlerPointerDown(e:PointerEvent,index:number) {\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.onHandlerMouseDown(e,index);\r\n      e.preventDefault();\r\n    }\r\n  }\r\n\r\n  getPointIndexFromHandlerIndex(index:number){\r\n    if (index === 0) {\r\n      return 0;\r\n    }else if (index === 2) {\r\n      return 1;\r\n    }else if (index === 4) {\r\n      return 2;\r\n    }else if (index === 6) {\r\n      return 3;\r\n    }\r\n    return -1;\r\n  }\r\n\r\n  //Convert the screen coordinates to the SVG's coordinates from https://www.petercollingridge.co.uk/tutorials/svg/interactive/dragging/\r\n  getMousePosition(event: any, svg: any) {\r\n    let CTM = svg.getScreenCTM();\r\n    if (!CTM) {\r\n      return { x: 0, y: 0 };\r\n    }\r\n\r\n    let x, y;\r\n    if (event.targetTouches) { // if it is a touch event\r\n      x = event.targetTouches[0].clientX;\r\n      y = event.targetTouches[0].clientY;\r\n    } else {\r\n      x = event.clientX;\r\n      y = event.clientY;\r\n    }\r\n\r\n    // Invert the transformation matrix\r\n    let det = CTM.a * CTM.d - CTM.b * CTM.c;\r\n    if (det === 0) {\r\n      // Handle the case where the matrix is singular\r\n      return { x: 0, y: 0 };\r\n    }\r\n\r\n    let invCTM = {\r\n      a: CTM.d / det,\r\n      b: -CTM.b / det,\r\n      c: -CTM.c / det,\r\n      d: CTM.a / det,\r\n      e: (CTM.c * CTM.f - CTM.d * CTM.e) / det,\r\n      f: (CTM.b * CTM.e - CTM.a * CTM.f) / det\r\n    };\r\n\r\n    return {\r\n      x: (x - CTM.e) * invCTM.a + (y - CTM.f) * invCTM.c,\r\n      y: (x - CTM.e) * invCTM.b + (y - CTM.f) * invCTM.d\r\n    };\r\n  }\r\n\r\n\r\n  getRatio(){\r\n    if (this.img) {\r\n      return this.img.naturalWidth/750;\r\n    }else{\r\n      return 1;\r\n    }\r\n  }\r\n\r\n  @Method()\r\n  async resetStates():Promise<void>\r\n  {\r\n    this.scale = 1.0;\r\n    this.offsetX = 0;\r\n    this.offsetY = 0;\r\n  }\r\n\r\n  @Method()\r\n  async getAllSelections(convertTo?:\"rect\"|\"quad\"):Promise<(Quad|Rect)[]>\r\n  {\r\n    let all = [];\r\n    for (let index = 0; index < this.inactiveSelections.length; index++) {\r\n      let selection = this.inactiveSelections[index];\r\n      if (convertTo) {\r\n        if (\"width\" in selection && convertTo === \"quad\") {\r\n          selection = {points:this.getPointsFromRect(selection)};\r\n        }else if (!(\"width\" in selection) && convertTo === \"rect\"){\r\n          selection = this.getRectFromPoints(selection.points);\r\n        }\r\n      }\r\n      all.push(selection);\r\n    }\r\n    let useQuad = true;\r\n    if (convertTo) {\r\n      if (convertTo === \"rect\") {\r\n        useQuad = false;\r\n      }\r\n    }else{\r\n      if (!this.usingQuad) {\r\n        useQuad = false;\r\n      }\r\n    }\r\n    if (useQuad) {\r\n      const quad = await this.getQuad();\r\n      all.push(quad);\r\n    }else{\r\n      const rect = await this.getRect();\r\n      all.push(rect);\r\n    }\r\n    return all;\r\n  }\r\n\r\n  @Method()\r\n  async getPoints():Promise<[Point,Point,Point,Point]>\r\n  {\r\n    return this.points;\r\n  }\r\n\r\n  @Method()\r\n  async getQuad():Promise<Quad>\r\n  {\r\n    return {points:this.points};\r\n  }\r\n\r\n  @Method()\r\n  async getRect():Promise<Rect>\r\n  {\r\n    return this.getRectFromPoints(this.points);\r\n  }\r\n\r\n  getRectFromPoints(points:Point[]):Rect{\r\n    let minX:number;\r\n    let minY:number;\r\n    let maxX:number;\r\n    let maxY:number;\r\n    for (const point of points) {\r\n      if (!minX) {\r\n        minX = point.x;\r\n        maxX = point.x;\r\n        minY = point.y;\r\n        maxY = point.y;\r\n      }else{\r\n        minX = Math.min(point.x,minX);\r\n        minY = Math.min(point.y,minY);\r\n        maxX = Math.max(point.x,maxX);\r\n        maxY = Math.max(point.y,maxY);\r\n      }\r\n    }\r\n    minX = Math.floor(minX);\r\n    maxX = Math.floor(maxX);\r\n    minY = Math.floor(minY);\r\n    maxY = Math.floor(maxY);\r\n    return {x:minX,y:minY,width:maxX - minX,height:maxY - minY};\r\n  }\r\n\r\n\r\n\r\n  async getImageFromBlob(source:Blob){\r\n    return new Promise<HTMLImageElement>((resolve, reject) => {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(source);\r\n      reader.onloadend = function () {\r\n        let dataURL:string = reader.result as string;\r\n        let img = document.createElement(\"img\");\r\n        img.onload = function(){\r\n          resolve(img);\r\n        };\r\n        img.onerror = function(){\r\n          reject();\r\n        }\r\n        img.src = dataURL;\r\n      }\r\n    })\r\n  }\r\n\r\n  async getImageFromDataURL(source:string){\r\n    return new Promise<HTMLImageElement>((resolve, reject) => {\r\n      let img = document.createElement(\"img\");\r\n      img.onload = function(){\r\n        resolve(img);\r\n      };\r\n      img.onerror = function(){\r\n        reject();\r\n      }\r\n      img.src = source;\r\n    })\r\n  }\r\n\r\n @Method()\r\n async detect(){}\r\n\r\n\r\n\r\n  getSVGWidth(){\r\n    if (this.img && this.svgElement) {\r\n      this.svgElement.style.height = \"100%\";\r\n      let imgRatio = this.img.naturalWidth/this.img.naturalHeight;\r\n      let width = this.svgElement.clientHeight * imgRatio;\r\n      if (width>this.svgElement.parentElement.clientWidth) {\r\n        width = this.svgElement.parentElement.clientWidth;\r\n        this.svgElement.style.height = width / imgRatio + \"px\";\r\n      }\r\n      return width;\r\n    }\r\n    return \"100%\";\r\n  }\r\n\r\n  onSVGPointerMove(e:PointerEvent){\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      e.stopPropagation();\r\n      e.preventDefault();\r\n      this.onSVGMouseMove(e);\r\n    }\r\n  }\r\n\r\n  onSVGPointerDown(e:PointerEvent){\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.onSVGMouseDown(e);\r\n    }\r\n  }\r\n\r\n  onSVGPointerUp(e:PointerEvent) {\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.svgMouseDownPoint = undefined;\r\n      this.selectedHandlerIndex = -1;\r\n    }\r\n  }\r\n\r\n  onPolygonPointerDown(e:PointerEvent){\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.onPolygonMouseDown(e);\r\n    }\r\n  }\r\n\r\n  onPolygonPointerUp(e:PointerEvent){\r\n    e.stopPropagation();\r\n    this.selectedHandlerIndex = -1;\r\n    this.polygonMouseDown = false;\r\n  }\r\n\r\n  render() {\r\n    return (\r\n      <Host ref={(el) => this.root = el}>\r\n        <div class=\"container absolute\"\r\n          ref={(el) => this.containerElement = el}\r\n          onWheel={(e:WheelEvent)=>this.onContainerWheel(e)}\r\n          onMouseUp={()=>this.onContainerMouseUp()}\r\n        >\r\n          <canvas\r\n            ref={(el) => this.canvasElement = el as HTMLCanvasElement}\r\n            class=\"hidden-canvas\"\r\n          ></canvas>\r\n          <svg\r\n            version=\"1.1\"\r\n            ref={(el) => this.svgElement = el as SVGElement}\r\n            class=\"cropper-svg\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            viewBox={this.viewBox}\r\n            width={this.getSVGWidth()}\r\n            style={{transform:this.getPanAndZoomStyle()}}\r\n            onMouseMove={(e:MouseEvent)=>this.onSVGMouseMove(e)}\r\n            onMouseDown={(e:MouseEvent)=>this.onSVGMouseDown(e)}\r\n            onTouchStart={(e:TouchEvent)=>this.onSVGTouchStart(e)}\r\n            onTouchEnd={()=>this.onSVGTouchEnd()}\r\n            onTouchMove={(e:TouchEvent)=>this.onSVGTouchMove(e)}\r\n            onPointerMove={(e:PointerEvent)=>this.onSVGPointerMove(e)}\r\n            onPointerDown={(e:PointerEvent)=>this.onSVGPointerDown(e)}\r\n            onPointerUp={(e:PointerEvent)=>this.onSVGPointerUp(e)}\r\n          >\r\n            <image href={this.img ? this.img.src : \"\"}></image>\r\n            {this.rendenInactiveSelections()}\r\n            {/* <polygon\r\n              points={this.getPointsData()}\r\n              class=\"cropper-controls dashed\"\r\n              style={{ pointerEvents: 'none' }}\r\n              stroke-width={this.activeStroke * this.getRatio()}\r\n              fill=\"transparent\"\r\n              // onMouseDown={(e:MouseEvent)=>this.onPolygonMouseDown(e)}\r\n              // onMouseUp={(e:MouseEvent)=>this.onPolygonMouseUp(e)}\r\n              // onTouchStart={(e:TouchEvent)=>this.onPolygonTouchStart(e)}\r\n              // onTouchEnd={(e:TouchEvent)=>this.onPolygonTouchEnd(e)}\r\n              // onPointerDown={(e:PointerEvent)=>this.onPolygonPointerDown(e)}\r\n              // onPointerUp={(e:PointerEvent)=>this.onPolygonPointerUp(e)}\r\n            >\r\n            </polygon> */}\r\n\r\n            <polygon\r\n                points={this.getPointsData()}\r\n                class=\"cropper-controls-visual\"\r\n                stroke-width={this.activeStroke * this.getRatio()}\r\n                stroke=\"currentColor\"\r\n                fill=\"transparent\"\r\n                pointer-events=\"none\">\r\n            </polygon> \r\n            \r\n            {this.renderHandlers()}\r\n          </svg>\r\n          {this.renderFooter()}\r\n          <div class=\"magnifier\" ref={(el) => this.magnifierElement = el as HTMLElement}></div>\r\n          <slot></slot>\r\n        </div>\r\n      </Host>\r\n    );\r\n  }\r\n\r\n  showMagnifier() {\r\n    if (this.magnifierElement) {\r\n      this.magnifierElement.style.display = 'block';\r\n    }\r\n  }\r\n\r\n  hideMagnifier() {\r\n    if (this.magnifierElement) {\r\n      this.magnifierElement.style.display = 'none';\r\n    }\r\n  }\r\n\r\n  updateMagnifier(coord: Point) {\r\n    if (!this.magnifierElement || !this.img) return;\r\n\r\n    // Get the coordinates and dimensions of the rect\r\n    const rect = this.getRectFromPoints(this.points);\r\n\r\n    // Calculate the position of the magnifier\r\n    const magnifierSize = 100; // Example size\r\n    // const magnifierLeft = (coord.x - 300) - magnifierSize / 2 ;\r\n    // const magnifierTop = (coord.y - 200) - magnifierSize / 2;\r\n    const magnifierLeft = coord.x - magnifierSize ;\r\n    const magnifierTop = coord.y - magnifierSize;\r\n\r\n    // Cast svgElement to SVGSVGElement to use createSVGPoint\r\n    const svgElement = this.svgElement as unknown as SVGSVGElement;\r\n\r\n    // Check if getScreenCTM and createSVGPoint methods are available\r\n    if (svgElement.getScreenCTM && svgElement.createSVGPoint) {\r\n      const ctm = svgElement.getScreenCTM();\r\n      const point = svgElement.createSVGPoint();\r\n      point.x = magnifierLeft;\r\n      point.y = magnifierTop;\r\n      const transformedPoint = point.matrixTransform(ctm);\r\n\r\n      // Set the magnifier's position\r\n      this.magnifierElement.style.left = `${transformedPoint.x - 40}px`;\r\n      this.magnifierElement.style.top = `${transformedPoint.y - 210}px`;\r\n    } else {\r\n      // Fallback if methods are not available\r\n      this.magnifierElement.style.left = `${magnifierLeft}px`;\r\n      this.magnifierElement.style.top = `${magnifierTop}px`;\r\n    }\r\n\r\n    // Set the magnifier's content (e.g., magnified image)\r\n    const zoomLevel = 0.5; // Example zoom level\r\n    const sx = Math.max(0, rect.x + (coord.x - rect.x) / this.scale - magnifierSize / zoomLevel / 2);\r\n    const sy = Math.max(0, rect.y + (coord.y - rect.y) / this.scale - magnifierSize / zoomLevel / 2);\r\n    const sw = magnifierSize / zoomLevel;\r\n    const sh = magnifierSize / zoomLevel;\r\n    const dx = 0;\r\n    const dy = 0;\r\n    const dw = magnifierSize;\r\n    const dh = magnifierSize;\r\n\r\n    const magnifierCanvas = document.createElement(\"canvas\");\r\n    magnifierCanvas.width = magnifierSize;\r\n    magnifierCanvas.height = magnifierSize;\r\n    const magnifierCtx = magnifierCanvas.getContext(\"2d\");\r\n\r\n    magnifierCtx.drawImage(this.img, sx, sy, sw, sh, dx, dy, dw, dh);\r\n\r\n    // Draw the polygon on the magnifier canvas\r\n    magnifierCtx.scale(zoomLevel, zoomLevel);\r\n    magnifierCtx.strokeStyle = 'orange'; // Set the style as needed\r\n    magnifierCtx.lineWidth = this.activeStroke / zoomLevel; // Adjust the line width\r\n    magnifierCtx.beginPath();\r\n    magnifierCtx.moveTo((this.points[0].x - sx), (this.points[0].y - sy));\r\n    for (let i = 1; i < this.points.length; i++) {\r\n      magnifierCtx.lineTo((this.points[i].x - sx), (this.points[i].y - sy));\r\n    }\r\n    magnifierCtx.closePath();\r\n    magnifierCtx.stroke();\r\n\r\n    this.magnifierElement.style.backgroundImage = `url(${magnifierCanvas.toDataURL()})`;\r\n  }\r\n\r\n\r\n\r\n\r\n}\r\n"], "mappings": "gEAAA,MAAMA,EAAkB,65CACxB,MAAAC,EAAeD,E,MCkCFE,EAAY,M,oJACvBC,KAAAC,SAAoB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GACnCD,KAAAE,iBAA2B,MAC3BF,KAAAG,sBAA8B,CAACC,EAAE,EAAEC,EAAE,GACrCL,KAAAM,iBAAoCC,UACpCP,KAAAQ,kBAAoCD,UACpCP,KAAAS,sBAA8B,CAACL,EAAE,EAAEC,EAAE,GAKrCL,KAAAU,eAA2CH,UAC1CP,KAAAW,gBAA0B,MAC3BX,KAAAY,UAAY,M,sLAUc,e,kBACK,E,cACL,E,oBACO,E,2BACO,E,YACIL,U,aACzB,E,aACA,E,WACF,C,CAejB,gBAAAM,GAEEb,KAAKc,iBAAiBC,iBAAiB,aAAcC,IACnDhB,KAAKiB,qBAAqBD,EAAE,IAI9BhB,KAAKc,iBAAiBC,iBAAiB,YAAY,KACjDf,KAAKM,iBAAmBC,UACxBP,KAAKkB,gBAELlB,KAAKmB,sBAAwB,EAC7BnB,KAAKE,iBAAmB,KAAK,G,CAKjC,mBAAAkB,CAAoBC,GAClB,GAAIA,EAAU,CACZC,QAAQC,IAAI,+CAAgDF,GAC5DrB,KAAKwB,cACLxB,KAAKyB,QAAU,OAAOJ,EAASK,gBAAgBL,EAASM,gBACxDL,QAAQC,IAAI,kBAAmBvB,KAAKyB,SACpC,GAAIzB,KAAK4B,KAAM,CACb,MAAMC,EAAiBC,SAAS9B,KAAK4B,KAAKG,MAAMC,iBAAiB,sBACjE,MAAMC,EAAeH,SAAS9B,KAAK4B,KAAKG,MAAMC,iBAAiB,oBAC/DV,QAAQC,IAAI,kBAAmBM,EAAgB,gBAAiBI,GAChE,GAAIJ,EAAgB,CAClB7B,KAAK6B,eAAiBA,C,CAExB,GAAII,EAAc,CAChBjC,KAAKiC,aAAeA,C,IAQ5B,oBAAAC,CAAqBb,GACnB,GAAIA,EAAU,CACZrB,KAAKY,UAAY,MACjB,IAAIuB,EAASnC,KAAKoC,kBAAkBf,GACpC,GAAIrB,KAAKqC,IAAK,CACZrC,KAAKsC,uBAAuBH,EAAOnC,KAAKqC,IAAIX,aAAa1B,KAAKqC,IAAIV,c,CAEpE3B,KAAKmC,OAASA,C,EAIlB,iBAAAC,CAAkBG,GAChB,MAAMC,EAAe,CAACpC,EAAEmC,EAAKnC,EAAEC,EAAEkC,EAAKlC,GACtC,MAAMoC,EAAe,CAACrC,EAAEmC,EAAKnC,EAAEmC,EAAKG,MAAMrC,EAAEkC,EAAKlC,GACjD,MAAMsC,EAAe,CAACvC,EAAEmC,EAAKnC,EAAEmC,EAAKG,MAAMrC,EAAEkC,EAAKlC,EAAEkC,EAAKK,QACxD,MAAMC,EAAe,CAACzC,EAAEmC,EAAKnC,EAAEC,EAAEkC,EAAKlC,EAAEkC,EAAKK,QAC7C,MAAO,CAACJ,EAAOC,EAAOE,EAAOE,E,CAI/B,oBAAAC,CAAqBzB,GACnB,GAAIA,EAAU,CACZrB,KAAKY,UAAY,KACjB,IAAIuB,EAASd,EAASc,OACtB,GAAInC,KAAKqC,IAAK,CACZrC,KAAKsC,uBAAuBH,EAAOnC,KAAKqC,IAAIX,aAAa1B,KAAKqC,IAAIV,c,CAEpE3B,KAAKmC,OAASd,EAASc,M,EAI3B,UAAAY,GACE,GAAI/C,KAAKgD,SAAS,CAChBhD,KAAKgD,SAASC,M,EAIlB,WAAAC,GACE,GAAIlD,KAAKmD,UAAU,CACjBnD,KAAKmD,UAAUF,M,EAInB,aAAAG,GACE,GAAIpD,KAAKmC,OAAQ,CACf,IAAIkB,EAAarD,KAAKmC,OAAO,GAAG/B,EAAI,IAAMJ,KAAKmC,OAAO,GAAG9B,EAAI,IAC7DgD,EAAaA,EAAarD,KAAKmC,OAAO,GAAG/B,EAAI,IAAMJ,KAAKmC,OAAO,GAAG9B,EAAG,IACrEgD,EAAaA,EAAarD,KAAKmC,OAAO,GAAG/B,EAAI,IAAMJ,KAAKmC,OAAO,GAAG9B,EAAG,IACrEgD,EAAaA,EAAarD,KAAKmC,OAAO,GAAG/B,EAAI,IAAMJ,KAAKmC,OAAO,GAAG9B,EAClE,OAAOgD,C,CAET,MAAO,E,CAGT,YAAAC,GACE,GAAItD,KAAKuD,aAAe,GAAI,CAC1B,MAAO,E,CAET,OACEC,EAAA,OAAKC,MAAM,UACTD,EAAA,WAASC,MAAM,SACbD,EAAA,OAAKC,MAAM,qBAAqBC,QAAS,IAAM1D,KAAK+C,cAClDS,EAAA,OAAKG,IAAI,wyBAEXH,EAAA,OAAKC,MAAM,kBAAkBC,QAAS,IAAM1D,KAAKkD,eAC/CM,EAAA,OAAKG,IAAI,8mB,CAOnB,wBAAAC,GACE,IAAK5D,KAAK6D,mBAAoB,CAC5B,MAAO,E,CAET,OACEL,EAACM,EAAQ,KACN9D,KAAK6D,mBAAmBE,KAAKC,GAC5BR,EAAA,WACErB,OAAQnC,KAAKiE,2BAA2BD,GACxCP,MAAM,qBACN1B,MAAO,CAAEmC,cAAe,QAAQ,eAClBlE,KAAK6B,eAAiB7B,KAAKmE,WACzCC,KAAK,kB,CAUf,kBAAAC,CAAmBC,GACjB,GAAItE,KAAKuE,iBAAkB,CACzBvE,KAAKuE,iBAAiBtB,KAAKqB,E,EAI/B,0BAAAL,CAA2BD,GACzB,IAAI7B,EAAiB,GACrB,GAAI,UAAW6B,EAAW,CACxB7B,EAASnC,KAAKoC,kBAAkB4B,E,KAC7B,CACH7B,EAAS6B,EAAU7B,M,CAErB,IAAIkB,EAAalB,EAAO,GAAG/B,EAAI,IAAM+B,EAAO,GAAG9B,EAAI,IACnDgD,EAAaA,EAAalB,EAAO,GAAG/B,EAAI,IAAM+B,EAAO,GAAG9B,EAAG,IAC3DgD,EAAaA,EAAalB,EAAO,GAAG/B,EAAI,IAAM+B,EAAO,GAAG9B,EAAG,IAC3DgD,EAAaA,EAAalB,EAAO,GAAG/B,EAAI,IAAM+B,EAAO,GAAG9B,EACxD,OAAOgD,C,CAGT,cAAAmB,GACE,IAAKxE,KAAKmC,OAAQ,CAChB,OAAQqB,EAAA,W,CAEV,OACEA,EAACM,EAAQ,KACR9D,KAAKC,SAAS8D,KAAIO,GACjBd,EAAA,SAEEA,EAAA,QACEpD,EAAGJ,KAAKyE,cAAcH,EAAO,KAC7BjE,EAAGL,KAAKyE,cAAcH,EAAO,KAC7B5B,MAAO1C,KAAK0E,iBACZ9B,OAAQ5C,KAAK0E,iBACbjB,MAAM,uBACNkB,OAAO,cAAa,eACN,GAAK3E,KAAKmE,WACxBC,KAAK,OAAM,iBACI,gBACfQ,YAAc5D,GAAkBhB,KAAK6E,mBAAmB7D,EAAGsD,GAC3DQ,aAAe9D,GAAkBhB,KAAK+E,oBAAoB/D,EAAGsD,KAG/Dd,EAAA,QACEpD,EAAGJ,KAAKyE,cAAcH,EAAO,KAC7BjE,EAAGL,KAAKyE,cAAcH,EAAO,KAC7B5B,MAAO1C,KAAK0E,iBACZ9B,OAAQ5C,KAAK0E,iBACbjB,MAAM,0BAAyB,eACjBa,IAAUtE,KAAKmB,qBACzBnB,KAAKiC,aAAe,EAAIjC,KAAKmE,WAC7BnE,KAAKiC,aAAejC,KAAKmE,WAC7BQ,OAAO,eACPP,KAAK,OAAM,iBACI,Y,CAQzB,aAAAK,CAAcH,EAAaU,GACzB,IAAIC,EAAM,EACV,IAAIC,EAAOlF,KAAK0E,iBAChB,GAAIJ,IAAU,EAAE,CACdW,EAAMjF,KAAKmC,OAAO,GAAG6C,E,MACjB,GAAIV,IAAU,EAAG,CACrBW,EAAMjF,KAAKmC,OAAO,GAAG6C,IAAQhF,KAAKmC,OAAO,GAAG6C,GAAOhF,KAAKmC,OAAO,GAAG6C,IAAM,C,MACpE,GAAIV,IAAU,EAAG,CACrBW,EAAMjF,KAAKmC,OAAO,GAAG6C,E,MACjB,GAAIV,IAAU,EAAG,CACrBW,EAAMjF,KAAKmC,OAAO,GAAG6C,IAAQhF,KAAKmC,OAAO,GAAG6C,GAAOhF,KAAKmC,OAAO,GAAG6C,IAAM,C,MACpE,GAAIV,IAAU,EAAG,CACrBW,EAAMjF,KAAKmC,OAAO,GAAG6C,E,MACjB,GAAIV,IAAU,EAAG,CACrBW,EAAMjF,KAAKmC,OAAO,GAAG6C,IAAQhF,KAAKmC,OAAO,GAAG6C,GAAOhF,KAAKmC,OAAO,GAAG6C,IAAM,C,MACpE,GAAIV,IAAU,EAAG,CACrBW,EAAMjF,KAAKmC,OAAO,GAAG6C,E,MACjB,GAAIV,IAAU,EAAG,CACrBW,EAAMjF,KAAKmC,OAAO,GAAG6C,IAAQhF,KAAKmC,OAAO,GAAG6C,GAAOhF,KAAKmC,OAAO,GAAG6C,IAAM,C,CAE1EC,EAAMA,EAAMC,EAAK,EACjB,OAAOD,C,CAGT,cAAAP,GACE,IAAIS,EAAQnF,KAAKmE,WACjB,IAAIe,EAAc,GAClB,GAAIlF,KAAKoF,YAAa,CACpB,IACEF,EAAOpD,SAAS9B,KAAKoF,Y,CACrB,MAAOC,GACP/D,QAAQC,IAAI8D,E,EAGhB,OAAOC,KAAKC,KAAKL,EAAKC,E,CAGxB,eAAAK,CAAgBxE,GACdhB,KAAKW,gBAAkB,KACvBX,KAAKQ,kBAAoBD,UACzBP,KAAKM,iBAAmBC,UACxB,IAAIkF,EAAQzF,KAAK0F,iBAAiB1E,EAAEhB,KAAK2F,YACzC,GAAI3E,EAAE4E,QAAQC,OAAS,EAAG,CACxB7F,KAAKmB,sBAAwB,C,KAC1B,CACH,GAAInB,KAAKmB,uBAAyB,EAAG,CACnCnB,KAAKU,eAAiBoF,KAAKC,MAAMD,KAAKE,UAAUhG,KAAKmC,SACrDnC,KAAKS,sBAAsBL,EAAIqF,EAAMrF,EACrCJ,KAAKS,sBAAsBJ,EAAIoF,EAAMpF,C,KAClC,CACHL,KAAKQ,kBAAoB,CAACJ,EAAEqF,EAAMrF,EAAEC,EAAEoF,EAAMpF,GAC5CL,KAAKE,iBAAmB,KACxBF,KAAKG,sBAAwB,CAAEC,EAAGqF,EAAMrF,EAAGC,EAAGoF,EAAMpF,GACpDL,KAAKU,eAAiBoF,KAAKC,MAAMD,KAAKE,UAAUhG,KAAKmC,Q,GAK3D,aAAA8D,GACEjG,KAAKQ,kBAAoBD,S,CAG3B,cAAA2F,CAAelF,GACbA,EAAEmF,kBACFnF,EAAEoF,iBACF,GAAIpF,EAAE4E,QAAQC,SAAW,EAAG,CAC1B7F,KAAKqG,aAAarF,E,KACf,CACH,GAAIhB,KAAKQ,kBAAmB,CAC1BR,KAAKsG,OAAOtF,E,MACP,GAAIhB,KAAKE,iBAAkB,CAChCF,KAAKuG,gBAAgBvF,E,KAClB,CACHhB,KAAKuG,gBAAgBvF,E,GAM3B,YAAAqF,CAAarF,GACX,MAAMwF,EAAWxG,KAAKyG,6BAA6BzF,EAAE4E,QAAQ,GAAG5E,EAAE4E,QAAQ,IAC1E,GAAI5F,KAAKM,iBAAkB,CACzB,GAAKkG,EAAWxG,KAAKM,iBAAkB,EAAG,CACxCN,KAAK0G,MAAQpB,KAAKqB,IAAI,GAAI3G,KAAK0G,MAAQ,I,KACpC,CACH1G,KAAK0G,MAAQpB,KAAKsB,IAAI,GAAI5G,KAAK0G,MAAQ,I,CAEzC1G,KAAKM,iBAAmBkG,C,KACrB,CACHxG,KAAKM,iBAAmBkG,C,EAI5B,4BAAAC,CAA6BI,EAAaC,GACxC,MAAMC,EAAUF,EAAOG,QAAUF,EAAOE,QACxC,MAAMC,EAAUJ,EAAOK,QAAUJ,EAAOI,QACxC,MAAMV,EAAWO,EAAUA,EAAUE,EAAUA,EAC/C,OAAOT,C,CAGT,kBAAAW,GACEnH,KAAKQ,kBAAoBD,UACzB,IAAKP,KAAKW,gBAAiB,CACzBX,KAAKmB,sBAAwB,EAC7BnB,KAAKE,iBAAmB,MACxBF,KAAKkB,e,EAIT,cAAAkG,CAAepG,GACb,IAAKhB,KAAKW,gBAAiB,CACzB,IAAI8E,EAAQzF,KAAK0F,iBAAiB1E,EAAEhB,KAAK2F,YACzC3F,KAAKQ,kBAAoB,CAACJ,EAAEqF,EAAMrF,EAAEC,EAAEoF,EAAMpF,E,EAIhD,gBAAAgH,CAAiBrG,GACf,GAAIA,EAAEsG,OAAO,EAAG,CACdtH,KAAK0G,MAAQ1G,KAAK0G,MAAQ,E,KACvB,CACH1G,KAAK0G,MAAQpB,KAAKsB,IAAI,GAAK5G,KAAK0G,MAAQ,G,CAE1C1F,EAAEoF,gB,CAGJ,oBAAAnF,CAAqBD,GACnBA,EAAEoF,iBACF,GAAIpF,EAAE4E,QAAQC,SAAW,EAAG,CAC1B7F,KAAKqG,aAAarF,E,EAItB,kBAAAuG,GACE,GAAIvH,KAAKqC,IAAK,CAGZ,MAAO,sBAAsBrC,KAAKwH,c,KAE/B,CACH,MAAO,Y,EAIX,cAAAC,CAAezG,GACb,GAAIhB,KAAKQ,kBAAmB,CAC1BR,KAAKsG,OAAOtF,E,KACT,CACHhB,KAAKuG,gBAAgBvF,E,EAIzB,MAAAsF,CAAOtF,GACL,IAAIyE,EAAQzF,KAAK0F,iBAAiB1E,EAAEhB,KAAK2F,YACzC,IAAIoB,EAAUtB,EAAMrF,EAAIJ,KAAKQ,kBAAkBJ,EAC/C,IAAI6G,EAAUxB,EAAMpF,EAAIL,KAAKQ,kBAAkBH,EAS/CL,KAAK+G,QAAU/G,KAAK+G,QAAUA,EAC9B/G,KAAKiH,QAAUjH,KAAKiH,QAAUA,C,CAGhC,eAAAV,CAAgBvF,GACd,GAAIhB,KAAKE,iBAAkB,CACzB,IAAIuF,EAAQzF,KAAK0F,iBAAiB1E,EAAEhB,KAAK2F,YACzC,IAAIoB,EAAUtB,EAAMrF,EAAIJ,KAAKG,sBAAsBC,EACnD,IAAI6G,EAAUxB,EAAMpF,EAAIL,KAAKG,sBAAsBE,EACnD,IAAIqH,EAAY5B,KAAKC,MAAMD,KAAKE,UAAUhG,KAAKU,iBAC/C,IAAK,MAAMiH,KAASD,EAAW,CAC7BC,EAAMvH,EAAIuH,EAAMvH,EAAI2G,EACpBY,EAAMtH,EAAIsH,EAAMtH,EAAI4G,EACpB,GAAIU,EAAMvH,EAAI,GAAKuH,EAAMtH,EAAI,GAAKsH,EAAMvH,EAAIJ,KAAKqC,IAAIX,cAAgBiG,EAAMtH,EAAIL,KAAKqC,IAAIV,cAAc,CACpGL,QAAQC,IAAI,gBACZ,M,EAGJvB,KAAKmC,OAASuF,EACd1H,KAAK4H,gBACL5H,KAAK6H,gBAAgBpC,E,CAEvB,GAAIzF,KAAKmB,sBAAwB,EAAG,CAClC,IAAI2G,EAAa9H,KAAK+H,8BAA8B/H,KAAKmB,sBACzD,IAAIsE,EAAQzF,KAAK0F,iBAAiB1E,EAAEhB,KAAK2F,YACzC,IAAIoB,EAAUtB,EAAMrF,EAAIJ,KAAKS,sBAAsBL,EACnD,IAAI6G,EAAUxB,EAAMpF,EAAIL,KAAKS,sBAAsBJ,EACnD,IAAIqH,EAAY5B,KAAKC,MAAMD,KAAKE,UAAUhG,KAAKU,iBAC/C,GAAIoH,IAAe,EAAG,CACpB,IAAIE,EAAgBN,EAAUI,GAC9BE,EAAc5H,EAAIJ,KAAKU,eAAeoH,GAAY1H,EAAI2G,EACtDiB,EAAc3H,EAAIL,KAAKU,eAAeoH,GAAYzH,EAAI4G,EACtD,GAAIjH,KAAKY,YAAc,MAAO,CAC5B,GAAIkH,IAAe,EAAG,CACpBJ,EAAU,GAAGrH,EAAI2H,EAAc3H,EAC/BqH,EAAU,GAAGtH,EAAI4H,EAAc5H,C,MAC3B,GAAI0H,IAAe,EAAG,CAC1BJ,EAAU,GAAGrH,EAAI2H,EAAc3H,EAC/BqH,EAAU,GAAGtH,EAAI4H,EAAc5H,C,MAC3B,GAAI0H,IAAe,EAAG,CAC1BJ,EAAU,GAAGtH,EAAI4H,EAAc5H,EAC/BsH,EAAU,GAAGrH,EAAI2H,EAAc3H,C,MAC3B,GAAIyH,IAAe,EAAG,CAC1BJ,EAAU,GAAGtH,EAAI4H,EAAc5H,EAC/BsH,EAAU,GAAGrH,EAAI2H,EAAc3H,C,OAGhC,CACH,GAAIL,KAAKmB,uBAAyB,EAAG,CACnCuG,EAAU,GAAGrH,EAAIL,KAAKU,eAAe,GAAGL,EAAI4G,EAC5CS,EAAU,GAAGrH,EAAIL,KAAKU,eAAe,GAAGL,EAAI4G,C,MACxC,GAAIjH,KAAKmB,uBAAyB,EAAG,CACzCuG,EAAU,GAAGtH,EAAIJ,KAAKU,eAAe,GAAGN,EAAI2G,EAC5CW,EAAU,GAAGtH,EAAIJ,KAAKU,eAAe,GAAGN,EAAI2G,C,MACxC,GAAI/G,KAAKmB,uBAAyB,EAAG,CACzCuG,EAAU,GAAGrH,EAAIL,KAAKU,eAAe,GAAGL,EAAI4G,EAC5CS,EAAU,GAAGrH,EAAIL,KAAKU,eAAe,GAAGL,EAAI4G,C,MACxC,GAAIjH,KAAKmB,uBAAyB,EAAG,CACzCuG,EAAU,GAAGtH,EAAIJ,KAAKU,eAAe,GAAGN,EAAI2G,EAC5CW,EAAU,GAAGtH,EAAIJ,KAAKU,eAAe,GAAGN,EAAI2G,C,EAGhD,GAAI/G,KAAKqC,IAAK,CACZrC,KAAKsC,uBAAuBoF,EAAU1H,KAAKqC,IAAIX,aAAa1B,KAAKqC,IAAIV,c,CAEvE3B,KAAKmC,OAASuF,EACd1H,KAAK4H,gBACL5H,KAAK6H,gBAAgBpC,E,EAIzB,sBAAAnD,CAAuBH,EAAsC8F,EAAkBC,GAE7E,MAAMC,EAAS,GACf,IAAK,IAAI7D,EAAQ,EAAGA,EAAQnC,EAAO0D,OAAQvB,IAAS,CAClD,MAAMqD,EAAQxF,EAAOmC,GACrBqD,EAAMvH,EAAIkF,KAAKsB,IAAIuB,EAAQR,EAAMvH,GACjCuH,EAAMvH,EAAIkF,KAAKqB,IAAIgB,EAAMvH,EAAG6H,EAAWE,GACvCR,EAAMtH,EAAIiF,KAAKsB,IAAIuB,EAAQR,EAAMtH,GACjCsH,EAAMtH,EAAIiF,KAAKqB,IAAIgB,EAAMtH,EAAG6H,EAAYC,E,EAI5C,kBAAAC,CAAmBpH,GACjBA,EAAEmF,kBACFnG,KAAKU,eAAiBoF,KAAKC,MAAMD,KAAKE,UAAUhG,KAAKmC,SACrDnC,KAAKE,iBAAmB,KACxB,IAAIuF,EAAQzF,KAAK0F,iBAAiB1E,EAAEhB,KAAK2F,YACzC3F,KAAKG,sBAAsBC,EAAIqF,EAAMrF,EACrCJ,KAAKG,sBAAsBE,EAAIoF,EAAMpF,EACrCL,KAAK4H,e,CAGP,gBAAAS,CAAiBrH,GACfA,EAAEmF,kBACF,IAAKnG,KAAKW,gBAAiB,CACzBX,KAAKmB,sBAAwB,EAC7BnB,KAAKE,iBAAmB,MACxBF,KAAKkB,e,EAIT,mBAAAoH,CAAoBtH,GAClBhB,KAAKW,gBAAkB,KACvBK,EAAEmF,kBACFnG,KAAKmB,sBAAwB,EAE7BnB,KAAKE,iBAAmB,KACxBF,KAAKU,eAAiBoF,KAAKC,MAAMD,KAAKE,UAAUhG,KAAKmC,SAErD,IAAIsD,EAAQzF,KAAK0F,iBAAiB1E,EAAEhB,KAAK2F,YAGzC3F,KAAKG,sBAAwB,CAAEC,EAAGqF,EAAMrF,EAAGC,EAAGoF,EAAMpF,GACpDL,KAAK4H,e,CAIP,iBAAAW,CAAkBvH,GAChBA,EAAEmF,kBACFnG,KAAKmB,sBAAwB,EAC7BnB,KAAKE,iBAAmB,MACxBF,KAAKkB,e,CAYP,kBAAA2D,CAAmB7D,EAAesD,GAChCtD,EAAEmF,kBAGF,GAAInG,KAAKmB,wBAA0B,GAAKnB,KAAKmB,uBAAyBmD,EAAO,CAE3EtE,KAAKwI,iBAAiBxH,E,CAGxB,IAAIyE,EAAQzF,KAAK0F,iBAAiB1E,EAAGhB,KAAK2F,YAC1C3F,KAAKU,eAAiBoF,KAAKC,MAAMD,KAAKE,UAAUhG,KAAKmC,SACrDnC,KAAKS,sBAAsBL,EAAIqF,EAAMrF,EACrCJ,KAAKS,sBAAsBJ,EAAIoF,EAAMpF,EAGrCL,KAAKmB,qBAAuBmD,C,CAG9B,gBAAAkE,CAAiBxH,GACfA,EAAEmF,kBACF,IAAKnG,KAAKW,gBAAiB,CACzBX,KAAKmB,sBAAwB,EAC7BnB,KAAKkB,e,EAIT,mBAAA6D,CAAoB/D,EAAasD,GAC/BtE,KAAKW,gBAAkB,KACvBK,EAAEmF,kBACFnG,KAAKE,iBAAmB,MACxB,IAAIuF,EAAQzF,KAAK0F,iBAAiB1E,EAAEhB,KAAK2F,YACzC3F,KAAKU,eAAiBoF,KAAKC,MAAMD,KAAKE,UAAUhG,KAAKmC,SACrDnC,KAAKS,sBAAsBL,EAAIqF,EAAMrF,EACrCJ,KAAKS,sBAAsBJ,EAAIoF,EAAMpF,EACrCL,KAAKmB,qBAAuBmD,C,CAG9B,oBAAAmE,CAAqBzH,EAAesD,GAClC,GAAItD,EAAE0H,aAAe,UAAY1I,KAAKW,gBAAiB,CACrDX,KAAK6E,mBAAmB7D,EAAEsD,GAC1BtD,EAAEoF,gB,EAIN,6BAAA2B,CAA8BzD,GAC5B,GAAIA,IAAU,EAAG,CACf,OAAO,C,MACH,GAAIA,IAAU,EAAG,CACrB,OAAO,C,MACH,GAAIA,IAAU,EAAG,CACrB,OAAO,C,MACH,GAAIA,IAAU,EAAG,CACrB,OAAO,C,CAET,OAAQ,C,CAIV,gBAAAoB,CAAiBiD,EAAYC,GAC3B,IAAIC,EAAMD,EAAIE,eACd,IAAKD,EAAK,CACR,MAAO,CAAEzI,EAAG,EAAGC,EAAG,E,CAGpB,IAAID,EAAGC,EACP,GAAIsI,EAAMI,cAAe,CACvB3I,EAAIuI,EAAMI,cAAc,GAAG/B,QAC3B3G,EAAIsI,EAAMI,cAAc,GAAG7B,O,KACtB,CACL9G,EAAIuI,EAAM3B,QACV3G,EAAIsI,EAAMzB,O,CAIZ,IAAI8B,EAAMH,EAAII,EAAIJ,EAAIK,EAAIL,EAAIM,EAAIN,EAAIO,EACtC,GAAIJ,IAAQ,EAAG,CAEb,MAAO,CAAE5I,EAAG,EAAGC,EAAG,E,CAGpB,IAAIgJ,EAAS,CACXJ,EAAGJ,EAAIK,EAAIF,EACXG,GAAIN,EAAIM,EAAIH,EACZI,GAAIP,EAAIO,EAAIJ,EACZE,EAAGL,EAAII,EAAID,EACXhI,GAAI6H,EAAIO,EAAIP,EAAIS,EAAIT,EAAIK,EAAIL,EAAI7H,GAAKgI,EACrCM,GAAIT,EAAIM,EAAIN,EAAI7H,EAAI6H,EAAII,EAAIJ,EAAIS,GAAKN,GAGvC,MAAO,CACL5I,GAAIA,EAAIyI,EAAI7H,GAAKqI,EAAOJ,GAAK5I,EAAIwI,EAAIS,GAAKD,EAAOD,EACjD/I,GAAID,EAAIyI,EAAI7H,GAAKqI,EAAOF,GAAK9I,EAAIwI,EAAIS,GAAKD,EAAOH,E,CAKrD,QAAA/E,GACE,GAAInE,KAAKqC,IAAK,CACZ,OAAOrC,KAAKqC,IAAIX,aAAa,G,KAC1B,CACH,OAAO,C,EAKX,iBAAMF,GAEJxB,KAAK0G,MAAQ,EACb1G,KAAK+G,QAAU,EACf/G,KAAKiH,QAAU,C,CAIjB,sBAAMsC,CAAiBC,GAErB,IAAIC,EAAM,GACV,IAAK,IAAInF,EAAQ,EAAGA,EAAQtE,KAAK6D,mBAAmBgC,OAAQvB,IAAS,CACnE,IAAIN,EAAYhE,KAAK6D,mBAAmBS,GACxC,GAAIkF,EAAW,CACb,GAAI,UAAWxF,GAAawF,IAAc,OAAQ,CAChDxF,EAAY,CAAC7B,OAAOnC,KAAKoC,kBAAkB4B,G,MACvC,KAAM,UAAWA,IAAcwF,IAAc,OAAO,CACxDxF,EAAYhE,KAAK0J,kBAAkB1F,EAAU7B,O,EAGjDsH,EAAIE,KAAK3F,E,CAEX,IAAI4F,EAAU,KACd,GAAIJ,EAAW,CACb,GAAIA,IAAc,OAAQ,CACxBI,EAAU,K,MAET,CACH,IAAK5J,KAAKY,UAAW,CACnBgJ,EAAU,K,EAGd,GAAIA,EAAS,CACX,MAAMC,QAAa7J,KAAK8J,UACxBL,EAAIE,KAAKE,E,KACN,CACH,MAAMtH,QAAavC,KAAK+J,UACxBN,EAAIE,KAAKpH,E,CAEX,OAAOkH,C,CAIT,eAAMO,GAEJ,OAAOhK,KAAKmC,M,CAId,aAAM2H,GAEJ,MAAO,CAAC3H,OAAOnC,KAAKmC,O,CAItB,aAAM4H,GAEJ,OAAO/J,KAAK0J,kBAAkB1J,KAAKmC,O,CAGrC,iBAAAuH,CAAkBvH,GAChB,IAAI8H,EACJ,IAAIC,EACJ,IAAIC,EACJ,IAAIC,EACJ,IAAK,MAAMzC,KAASxF,EAAQ,CAC1B,IAAK8H,EAAM,CACTA,EAAOtC,EAAMvH,EACb+J,EAAOxC,EAAMvH,EACb8J,EAAOvC,EAAMtH,EACb+J,EAAOzC,EAAMtH,C,KACV,CACH4J,EAAO3E,KAAKqB,IAAIgB,EAAMvH,EAAE6J,GACxBC,EAAO5E,KAAKqB,IAAIgB,EAAMtH,EAAE6J,GACxBC,EAAO7E,KAAKsB,IAAIe,EAAMvH,EAAE+J,GACxBC,EAAO9E,KAAKsB,IAAIe,EAAMtH,EAAE+J,E,EAG5BH,EAAO3E,KAAK+E,MAAMJ,GAClBE,EAAO7E,KAAK+E,MAAMF,GAClBD,EAAO5E,KAAK+E,MAAMH,GAClBE,EAAO9E,KAAK+E,MAAMD,GAClB,MAAO,CAAChK,EAAE6J,EAAK5J,EAAE6J,EAAKxH,MAAMyH,EAAOF,EAAKrH,OAAOwH,EAAOF,E,CAKxD,sBAAMI,CAAiBC,GACrB,OAAO,IAAIC,SAA0B,CAACC,EAASC,KAC7C,IAAIC,EAAS,IAAIC,WACjBD,EAAOE,cAAcN,GACrBI,EAAOG,UAAY,WACjB,IAAIC,EAAiBJ,EAAOK,OAC5B,IAAI3I,EAAM4I,SAASC,cAAc,OACjC7I,EAAI8I,OAAS,WACXV,EAAQpI,E,EAEVA,EAAI+I,QAAU,WACZV,G,EAEFrI,EAAIsB,IAAMoH,C,CACX,G,CAIL,yBAAMM,CAAoBd,GACxB,OAAO,IAAIC,SAA0B,CAACC,EAASC,KAC7C,IAAIrI,EAAM4I,SAASC,cAAc,OACjC7I,EAAI8I,OAAS,WACXV,EAAQpI,E,EAEVA,EAAI+I,QAAU,WACZV,G,EAEFrI,EAAIsB,IAAM4G,CAAM,G,CAKrB,YAAMe,GAAM,CAIX,WAAAC,GACE,GAAIvL,KAAKqC,KAAOrC,KAAK2F,WAAY,CAC/B3F,KAAK2F,WAAW5D,MAAMa,OAAS,OAC/B,IAAI4I,EAAWxL,KAAKqC,IAAIX,aAAa1B,KAAKqC,IAAIV,cAC9C,IAAIe,EAAQ1C,KAAK2F,WAAW8F,aAAeD,EAC3C,GAAI9I,EAAM1C,KAAK2F,WAAW+F,cAAcC,YAAa,CACnDjJ,EAAQ1C,KAAK2F,WAAW+F,cAAcC,YACtC3L,KAAK2F,WAAW5D,MAAMa,OAASF,EAAQ8I,EAAW,I,CAEpD,OAAO9I,C,CAET,MAAO,M,CAGT,gBAAAkJ,CAAiB5K,GACf,GAAIA,EAAE0H,aAAe,UAAY1I,KAAKW,gBAAiB,CACrDK,EAAEmF,kBACFnF,EAAEoF,iBACFpG,KAAKyH,eAAezG,E,EAIxB,gBAAA6K,CAAiB7K,GACf,GAAIA,EAAE0H,aAAe,UAAY1I,KAAKW,gBAAiB,CACrDX,KAAKoH,eAAepG,E,EAIxB,cAAA8K,CAAe9K,GACb,GAAIA,EAAE0H,aAAe,UAAY1I,KAAKW,gBAAiB,CACrDX,KAAKQ,kBAAoBD,UACzBP,KAAKmB,sBAAwB,C,EAIjC,oBAAA4K,CAAqB/K,GACnB,GAAIA,EAAE0H,aAAe,UAAY1I,KAAKW,gBAAiB,CACrDX,KAAKoI,mBAAmBpH,E,EAI5B,kBAAAgL,CAAmBhL,GACjBA,EAAEmF,kBACFnG,KAAKmB,sBAAwB,EAC7BnB,KAAKE,iBAAmB,K,CAG1B,MAAA+L,GACE,OACEzI,EAAC0I,EAAI,CAAAlH,IAAA,2CAACmH,IAAMC,GAAOpM,KAAK4B,KAAOwK,GAC7B5I,EAAA,OAAAwB,IAAA,2CAAKvB,MAAM,qBACT0I,IAAMC,GAAOpM,KAAKc,iBAAmBsL,EACrCC,QAAUrL,GAAehB,KAAKqH,iBAAiBrG,GAC/CsL,UAAW,IAAItM,KAAKmH,sBAEpB3D,EAAA,UAAAwB,IAAA,2CACEmH,IAAMC,GAAOpM,KAAKuM,cAAgBH,EAClC3I,MAAM,kBAERD,EAAA,OAAAwB,IAAA,2CACEwH,QAAQ,MACRL,IAAMC,GAAOpM,KAAK2F,WAAayG,EAC/B3I,MAAM,cACNgJ,MAAM,6BACNhL,QAASzB,KAAKyB,QACdiB,MAAO1C,KAAKuL,cACZxJ,MAAO,CAAC2K,UAAU1M,KAAKuH,sBACvBoF,YAAc3L,GAAehB,KAAKyH,eAAezG,GACjD4D,YAAc5D,GAAehB,KAAKoH,eAAepG,GACjD8D,aAAe9D,GAAehB,KAAKwF,gBAAgBxE,GACnD4L,WAAY,IAAI5M,KAAKiG,gBACrB4G,YAAc7L,GAAehB,KAAKkG,eAAelF,GACjD8L,cAAgB9L,GAAiBhB,KAAK4L,iBAAiB5K,GACvD+L,cAAgB/L,GAAiBhB,KAAK6L,iBAAiB7K,GACvDgM,YAAchM,GAAiBhB,KAAK8L,eAAe9K,IAEnDwC,EAAA,SAAAwB,IAAA,2CAAOiI,KAAMjN,KAAKqC,IAAMrC,KAAKqC,IAAIsB,IAAM,KACtC3D,KAAK4D,2BAgBNJ,EAAA,WAAAwB,IAAA,2CACI7C,OAAQnC,KAAKoD,gBACbK,MAAM,0BAAyB,eACjBzD,KAAKiC,aAAejC,KAAKmE,WACvCQ,OAAO,eACPP,KAAK,cAAa,iBACH,SAGlBpE,KAAKwE,kBAEPxE,KAAKsD,eACNE,EAAA,OAAAwB,IAAA,2CAAKvB,MAAM,YAAY0I,IAAMC,GAAOpM,KAAKkN,iBAAmBd,IAC5D5I,EAAA,QAAAwB,IAAA,8C,CAMR,aAAA4C,GACE,GAAI5H,KAAKkN,iBAAkB,CACzBlN,KAAKkN,iBAAiBnL,MAAMoL,QAAU,O,EAI1C,aAAAjM,GACE,GAAIlB,KAAKkN,iBAAkB,CACzBlN,KAAKkN,iBAAiBnL,MAAMoL,QAAU,M,EAI1C,eAAAtF,CAAgBpC,GACd,IAAKzF,KAAKkN,mBAAqBlN,KAAKqC,IAAK,OAGzC,MAAME,EAAOvC,KAAK0J,kBAAkB1J,KAAKmC,QAGzC,MAAMiL,EAAgB,IAGtB,MAAMC,EAAgB5H,EAAMrF,EAAIgN,EAChC,MAAME,EAAe7H,EAAMpF,EAAI+M,EAG/B,MAAMzH,EAAa3F,KAAK2F,WAGxB,GAAIA,EAAWmD,cAAgBnD,EAAW4H,eAAgB,CACxD,MAAMC,EAAM7H,EAAWmD,eACvB,MAAMnB,EAAQhC,EAAW4H,iBACzB5F,EAAMvH,EAAIiN,EACV1F,EAAMtH,EAAIiN,EACV,MAAMG,EAAmB9F,EAAM+F,gBAAgBF,GAG/CxN,KAAKkN,iBAAiBnL,MAAM4L,KAAO,GAAGF,EAAiBrN,EAAI,OAC3DJ,KAAKkN,iBAAiBnL,MAAM6L,IAAM,GAAGH,EAAiBpN,EAAI,O,KACrD,CAELL,KAAKkN,iBAAiBnL,MAAM4L,KAAO,GAAGN,MACtCrN,KAAKkN,iBAAiBnL,MAAM6L,IAAM,GAAGN,K,CAIvC,MAAMO,EAAY,GAClB,MAAMC,EAAKxI,KAAKsB,IAAI,EAAGrE,EAAKnC,GAAKqF,EAAMrF,EAAImC,EAAKnC,GAAKJ,KAAK0G,MAAQ0G,EAAgBS,EAAY,GAC9F,MAAME,EAAKzI,KAAKsB,IAAI,EAAGrE,EAAKlC,GAAKoF,EAAMpF,EAAIkC,EAAKlC,GAAKL,KAAK0G,MAAQ0G,EAAgBS,EAAY,GAC9F,MAAMG,EAAKZ,EAAgBS,EAC3B,MAAMI,EAAKb,EAAgBS,EAC3B,MAAMK,EAAK,EACX,MAAMC,EAAK,EACX,MAAMC,EAAKhB,EACX,MAAMiB,EAAKjB,EAEX,MAAMkB,EAAkBrD,SAASC,cAAc,UAC/CoD,EAAgB5L,MAAQ0K,EACxBkB,EAAgB1L,OAASwK,EACzB,MAAMmB,EAAeD,EAAgBE,WAAW,MAEhDD,EAAaE,UAAUzO,KAAKqC,IAAKyL,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAG7DE,EAAa7H,MAAMmH,EAAWA,GAC9BU,EAAaG,YAAc,SAC3BH,EAAaI,UAAY3O,KAAKiC,aAAe4L,EAC7CU,EAAaK,YACbL,EAAaM,OAAQ7O,KAAKmC,OAAO,GAAG/B,EAAI0N,EAAM9N,KAAKmC,OAAO,GAAG9B,EAAI0N,GACjE,IAAK,IAAIe,EAAI,EAAGA,EAAI9O,KAAKmC,OAAO0D,OAAQiJ,IAAK,CAC3CP,EAAaQ,OAAQ/O,KAAKmC,OAAO2M,GAAG1O,EAAI0N,EAAM9N,KAAKmC,OAAO2M,GAAGzO,EAAI0N,E,CAEnEQ,EAAaS,YACbT,EAAa5J,SAEb3E,KAAKkN,iBAAiBnL,MAAMkN,gBAAkB,OAAOX,EAAgBY,c"}