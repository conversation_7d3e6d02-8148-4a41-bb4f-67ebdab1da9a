{"file": "image-cropper.entry.js", "mappings": ";;AAAA,MAAM,eAAe,GAAG,k4CAAk4C,CAAC;AAC35C,2BAAe,eAAe;;MCkCjB,YAAY;;;;;;QACvB,aAAQ,GAAY,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;QACtC,qBAAgB,GAAW,KAAK,CAAC;QACjC,0BAAqB,GAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QACxC,qBAAgB,GAAoB,SAAS,CAAC;QAC9C,sBAAiB,GAAmB,SAAS,CAAC;QAC9C,0BAAqB,GAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QAKxC,mBAAc,GAA6B,SAAS,CAAC;QACpD,oBAAe,GAAW,KAAK,CAAC;QACjC,cAAS,GAAG,KAAK,CAAC;;;;;;;;uBAUQ,cAAc;4BACT,CAAC;wBACN,CAAC;8BACM,CAAC;oCACK,CAAC,CAAC;sBACG,SAAS;uBAClC,CAAC;uBACD,CAAC;qBACH,GAAG;;IAKpB,gBAAgB;QACd,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,CAAY;YAC/D,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;SAC9B,CAAC,CAAA;QACF,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE;YACjD,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;YAClC,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB,CAAC,CAAA;KACH;IAGD,mBAAmB,CAAC,QAA0B;QAC5C,IAAI,QAAQ,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,8CAA8C,EAAE,QAAQ,CAAC,CAAC;YACtE,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,GAAG,OAAO,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;YACxE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7C,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBACvF,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBACnF,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,cAAc,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;gBAC9E,IAAI,cAAc,EAAE;oBAClB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;iBACtC;gBACD,IAAI,YAAY,EAAE;oBAChB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;iBAClC;aACF;SACF;KACF;IAID,oBAAoB,CAAC,QAAc;QACjC,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;aAClF;YACD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB;KACF;IAED,iBAAiB,CAAC,IAAS;QACzB,MAAM,MAAM,GAAS,EAAC,CAAC,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC;QACzC,MAAM,MAAM,GAAS,EAAC,CAAC,EAAC,IAAI,CAAC,CAAC,GAAC,IAAI,CAAC,KAAK,EAAC,CAAC,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC;QACpD,MAAM,MAAM,GAAS,EAAC,CAAC,EAAC,IAAI,CAAC,CAAC,GAAC,IAAI,CAAC,KAAK,EAAC,CAAC,EAAC,IAAI,CAAC,CAAC,GAAC,IAAI,CAAC,MAAM,EAAC,CAAC;QAChE,MAAM,MAAM,GAAS,EAAC,CAAC,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC,CAAC,GAAC,IAAI,CAAC,MAAM,EAAC,CAAC;QACrD,OAAO,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,CAAC;KACtC;IAGD,oBAAoB,CAAC,QAAc;QACjC,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;aAClF;YACD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;SAC/B;KACF;IAED,UAAU;QACR,IAAI,IAAI,CAAC,QAAQ,EAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;SACtB;KACF;IAED,WAAW;QACT,IAAI,IAAI,CAAC,SAAS,EAAC;YACjB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;SACvB;KACF;IAED,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YACjE,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,GAAG,CAAC;YACzE,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,GAAG,CAAC;YACzE,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,OAAO,UAAU,CAAC;SACnB;QACD,OAAO,EAAE,CAAC;KACX;IAED,YAAY;QACV,IAAI,IAAI,CAAC,UAAU,KAAK,EAAE,EAAE;YAC1B,OAAO,EAAE,CAAC;SACX;QACD,QACE,WAAK,KAAK,EAAC,QAAQ,IACjB,eAAS,KAAK,EAAC,OAAO,IACpB,WAAK,KAAK,EAAC,oBAAoB,EAAC,OAAO,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,IAC9D,WAAK,GAAG,EAAC,oyBAAoyB,GAAG,CAC5yB,EACN,WAAK,KAAK,EAAC,iBAAiB,EAAC,OAAO,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,IAC5D,WAAK,GAAG,EAAC,ymBAAymB,GAAG,CACjnB,CACE,CACN,EACP;KACF;IAED,wBAAwB;QACtB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,OAAO,EAAE,CAAC;SACX;QACD,QACE,EAAC,QAAQ,QACN,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAC,KAAK,MAC3C,eACE,MAAM,EAAE,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAClD,KAAK,EAAC,2BAA2B,kBACnB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,EACnD,IAAI,EAAC,aAAa,EAClB,SAAS,EAAE,MAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAC7C,YAAY,EAAE,MAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAEzC,CACV,CAAC,CACO,EACX;KACH;IAED,kBAAkB,CAAC,KAAY;QAC7B,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnC;KACF;IAED,0BAA0B,CAAC,SAAmB;QAC5C,IAAI,MAAM,GAAW,EAAE,CAAC;QACxB,IAAI,OAAO,IAAI,SAAS,EAAE;YACxB,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;SAC5C;aAAI;YACH,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;SAC3B;QACD,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QACvD,UAAU,GAAG,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,GAAG,CAAC;QAC/D,UAAU,GAAG,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,GAAG,CAAC;QAC/D,UAAU,GAAG,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,OAAO,UAAU,CAAC;KACnB;IAED,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,QAAQ,cAAW,EAAC;SACrB;QACD,QACE,EAAC,QAAQ,QACN,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,KACtB,YACE,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAC,GAAG,CAAC,EAChC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAC,GAAG,CAAC,EAChC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,EAC5B,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,EAC7B,KAAK,EAAC,kBAAkB,kBACV,KAAK,KAAK,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,EACjI,IAAI,EAAC,aAAa,EAClB,WAAW,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAC,KAAK,CAAC,EAC7D,SAAS,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EACnD,YAAY,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAC,KAAK,CAAC,EAC/D,aAAa,EAAE,CAAC,CAAc,KAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAC,KAAK,CAAC,GACnE,CACH,CAAC,CACO,EACZ;KACF;IAED,aAAa,CAAC,KAAY,EAAC,GAAU;QACnC,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACjC,IAAI,KAAK,KAAK,CAAC,EAAC;YACd,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SAC3B;aAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACrB,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAE,CAAC,CAAC;SAC3E;aAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACrB,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SAC3B;aAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACrB,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAE,CAAC,CAAC;SAC3E;aAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACrB,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SAC3B;aAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACrB,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAE,CAAC,CAAC;SAC3E;aAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACrB,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SAC3B;aAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACrB,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAE,CAAC,CAAC;SAC3E;QACD,GAAG,GAAG,GAAG,GAAG,IAAI,GAAC,CAAC,CAAC;QACnB,OAAO,GAAG,CAAC;KACZ;IAED,cAAc;QACZ,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC5B,IAAI,IAAI,GAAU,EAAE,CAAC;QACrB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI;gBACF,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACnC;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aACpB;SACF;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,GAAC,KAAK,CAAC,CAAC;KAC9B;IAED,eAAe,CAAC,CAAY;QAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;SAChC;aAAI;YACH,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAC,EAAE;gBACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC9D,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBACvC,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;aACxC;iBAAI;gBACH,IAAI,CAAC,iBAAiB,GAAG,EAAC,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC;gBAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBAC7B,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;gBACxD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;aAC/D;SACF;KACF;IAED,aAAa;QACX,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;KACpC;IAED,cAAc,CAAC,CAAY;QACzB,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;SACtB;aAAI;YACH,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAChB;iBAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAChC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;aACzB;iBAAI;gBACH,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;aACzB;SACF;KACF;;IAGD,YAAY,CAAC,CAAY;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,IAAE,CAAC,EAAE;gBACxC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;aAC9C;iBAAI;gBACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;aAC9C;YACD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;SAClC;aAAI;YACH,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;SAClC;KACF;IAED,4BAA4B,CAAC,MAAY,EAAC,MAAY;QACpD,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAChD,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAChD,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;QACvD,OAAO,QAAQ,CAAC;KACjB;IAED,kBAAkB;QAChB,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;YAC/B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;KACF;IAED,cAAc,CAAC,CAAY;QACzB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,CAAC,iBAAiB,GAAG,EAAC,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC;SAChD;KACF;IAED,gBAAgB,CAAC,CAAY;QAC3B,IAAI,CAAC,CAAC,MAAM,GAAC,CAAC,EAAE;YACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;SAC/B;aAAI;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;SAC9C;QACD,CAAC,CAAC,cAAc,EAAE,CAAC;KACpB;IAED,oBAAoB,CAAC,CAAY;QAC/B,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;SACtB;KACF;IAED,kBAAkB;QAChB,IAAI,IAAI,CAAC,GAAG,EAAE;;;YAGZ,OAAO,sBAAsB,IAAI,CAAC,QAAQ,MAAM,CAAC;;SAElD;aAAI;YACH,OAAO,YAAY,CAAC;SACrB;KACF;IAED,cAAc,CAAC,CAAY;QACzB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAChB;aAAI;YACH,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;SACzB;KACF;IAED,MAAM,CAAC,CAAuB;QAC5B,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACjD,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;;;;;;;;QASjD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KACvC;IAED,eAAe,CAAC,CAAuB;QACrC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;YACrD,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;YACrD,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAChE,KAAK,MAAM,KAAK,IAAI,SAAS,EAAE;gBAC7B,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC;gBAC5B,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC;gBAC5B,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAC;oBACpG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;oBAC5B,OAAO;iBACR;aACF;YACD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;YACxB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SAC7B;QACD,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,EAAE;YAClC,IAAI,UAAU,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC/E,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;YACrD,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;YACrD,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAChE,IAAI,UAAU,IAAI,CAAC,CAAC,EAAE;gBACpB,IAAI,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC;gBAC1C,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;gBAC9D,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;gBAC9D,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;oBAC5B,IAAI,UAAU,KAAK,CAAC,EAAE;wBACpB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;wBACjC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;qBAClC;yBAAK,IAAI,UAAU,KAAK,CAAC,EAAE;wBAC1B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;wBACjC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;qBAClC;yBAAK,IAAI,UAAU,KAAK,CAAC,EAAE;wBAC1B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;wBACjC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;qBAClC;yBAAK,IAAI,UAAU,KAAK,CAAC,EAAE;wBAC1B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;wBACjC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;qBAClC;iBACF;aACF;iBAAI;gBACH,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE;oBACnC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;oBACpD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;iBACrD;qBAAK,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE;oBACzC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;oBACpD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;iBACrD;qBAAK,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE;oBACzC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;oBACpD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;iBACrD;qBAAK,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE;oBACzC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;oBACpD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;iBACrD;aACF;YACD,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;aACrF;YACD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;YACxB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SAC7B;KACF;IAED,sBAAsB,CAAC,MAAgC,EAAC,QAAe,EAAC,SAAgB;QACtF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5B,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC,QAAQ,CAAC,CAAC;YACrC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC,SAAS,CAAC,CAAC;SACvC;KACF;IAED,kBAAkB,CAAC,CAAY;QAC7B,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;IAED,gBAAgB,CAAC,CAAY;QAC3B,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;YAC/B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;KACF;IAED,mBAAmB,CAAC,CAAY;QAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;;QAE/B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;QAE9D,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;;;QAGrD,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QACxD,IAAI,CAAC,aAAa,EAAE,CAAC;KAEtB;IAED,iBAAiB,CAAC,CAAY;QAC5B,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;IAED,kBAAkB,CAAC,CAAY,EAAC,KAAY;QAC1C,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;KACnC;IAED,gBAAgB,CAAC,CAAY;QAC3B,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;YAC/B,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;KACF;IAED,mBAAmB,CAAC,CAAY,EAAC,KAAY;QAC3C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;KACnC;IAED,oBAAoB,CAAC,CAAc,EAAC,KAAY;QAC9C,IAAI,CAAC,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACrD,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAC,KAAK,CAAC,CAAC;YACjC,CAAC,CAAC,cAAc,EAAE,CAAC;SACpB;KACF;IAED,6BAA6B,CAAC,KAAY;QACxC,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,OAAO,CAAC,CAAC;SACV;aAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACrB,OAAO,CAAC,CAAC;SACV;aAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACrB,OAAO,CAAC,CAAC;SACV;aAAK,IAAI,KAAK,KAAK,CAAC,EAAE;YACrB,OAAO,CAAC,CAAC;SACV;QACD,OAAO,CAAC,CAAC,CAAC;KACX;;IAGD,gBAAgB,CAAC,KAAU,EAAE,GAAQ;QACnC,IAAI,GAAG,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;QAC7B,IAAI,CAAC,GAAG,EAAE;YACR,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;SACvB;QAED,IAAI,CAAC,EAAE,CAAC,CAAC;QACT,IAAI,KAAK,CAAC,aAAa,EAAE;YACvB,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YACnC,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;SACpC;aAAM;YACL,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;YAClB,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;SACnB;;QAGD,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACxC,IAAI,GAAG,KAAK,CAAC,EAAE;;YAEb,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;SACvB;QAED,IAAI,MAAM,GAAG;YACX,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;YACd,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;YACf,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;YACf,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;YACd,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG;YACxC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG;SACzC,CAAC;QAEF,OAAO;YACL,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;YAClD,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;SACnD,CAAC;KACH;IAGD,QAAQ;QACN,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,GAAC,GAAG,CAAC;SAClC;aAAI;YACH,OAAO,CAAC,CAAC;SACV;KACF;IAGD,MAAM,WAAW;QAEf,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;KAClB;IAGD,MAAM,gBAAgB,CAAC,SAAwB;QAE7C,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACnE,IAAI,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,SAAS,EAAE;gBACb,IAAI,OAAO,IAAI,SAAS,IAAI,SAAS,KAAK,MAAM,EAAE;oBAChD,SAAS,GAAG,EAAC,MAAM,EAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAC,CAAC;iBACxD;qBAAK,IAAI,EAAE,OAAO,IAAI,SAAS,CAAC,IAAI,SAAS,KAAK,MAAM,EAAC;oBACxD,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;iBACtD;aACF;YACD,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACrB;QACD,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,SAAS,EAAE;YACb,IAAI,SAAS,KAAK,MAAM,EAAE;gBACxB,OAAO,GAAG,KAAK,CAAC;aACjB;SACF;aAAI;YACH,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,OAAO,GAAG,KAAK,CAAC;aACjB;SACF;QACD,IAAI,OAAO,EAAE;YACX,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAChB;aAAI;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAChB;QACD,OAAO,GAAG,CAAC;KACZ;IAGD,MAAM,SAAS;QAEb,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IAGD,MAAM,OAAO;QAEX,OAAO,EAAC,MAAM,EAAC,IAAI,CAAC,MAAM,EAAC,CAAC;KAC7B;IAGD,MAAM,OAAO;QAEX,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC5C;IAED,iBAAiB,CAAC,MAAc;QAC9B,IAAI,IAAW,CAAC;QAChB,IAAI,IAAW,CAAC;QAChB,IAAI,IAAW,CAAC;QAChB,IAAI,IAAW,CAAC;QAChB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,CAAC,IAAI,EAAE;gBACT,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;gBACf,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;gBACf,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;gBACf,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;aAChB;iBAAI;gBACH,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC,IAAI,CAAC,CAAC;aAC/B;SACF;QACD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxB,OAAO,EAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,GAAG,IAAI,EAAC,MAAM,EAAC,IAAI,GAAG,IAAI,EAAC,CAAC;KAC7D;IAID,MAAM,gBAAgB,CAAC,MAAW;QAChC,OAAO,IAAI,OAAO,CAAmB,CAAC,OAAO,EAAE,MAAM;YACnD,IAAI,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;YAC9B,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC7B,MAAM,CAAC,SAAS,GAAG;gBACjB,IAAI,OAAO,GAAU,MAAM,CAAC,MAAgB,CAAC;gBAC7C,IAAI,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBACxC,GAAG,CAAC,MAAM,GAAG;oBACX,OAAO,CAAC,GAAG,CAAC,CAAC;iBACd,CAAC;gBACF,GAAG,CAAC,OAAO,GAAG;oBACZ,MAAM,EAAE,CAAC;iBACV,CAAA;gBACD,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC;aACnB,CAAA;SACF,CAAC,CAAA;KACH;IAED,MAAM,mBAAmB,CAAC,MAAa;QACrC,OAAO,IAAI,OAAO,CAAmB,CAAC,OAAO,EAAE,MAAM;YACnD,IAAI,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACxC,GAAG,CAAC,MAAM,GAAG;gBACX,OAAO,CAAC,GAAG,CAAC,CAAC;aACd,CAAC;YACF,GAAG,CAAC,OAAO,GAAG;gBACZ,MAAM,EAAE,CAAC;aACV,CAAA;YACD,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC;SAClB,CAAC,CAAA;KACH;IAGF,MAAM,MAAM,MAAI;IAIf,WAAW;QACT,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;YAC/B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YACtC,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;YAC5D,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,QAAQ,CAAC;YACpD,IAAI,KAAK,GAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,EAAE;gBACnD,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,CAAC;gBAClD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC;aACxD;YACD,OAAO,KAAK,CAAC;SACd;QACD,OAAO,MAAM,CAAC;KACf;IAED,gBAAgB,CAAC,CAAc;QAC7B,IAAI,CAAC,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACrD,CAAC,CAAC,eAAe,EAAE,CAAC;YACpB,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;SACxB;KACF;IAED,gBAAgB,CAAC,CAAc;QAC7B,IAAI,CAAC,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACrD,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;SACxB;KACF;IAED,cAAc,CAAC,CAAc;QAC3B,IAAI,CAAC,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACrD,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;YACnC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;SAChC;KACF;IAED,oBAAoB,CAAC,CAAc;QACjC,IAAI,CAAC,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACrD,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;SAC5B;KACF;IAED,kBAAkB,CAAC,CAAc;QAC/B,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;KAC/B;IAED,MAAM;QACJ,QACE,EAAC,IAAI,qDAAC,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE,IAC/B,4DAAK,KAAK,EAAC,oBAAoB,EAC7B,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,GAAG,EAAE,EACvC,OAAO,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EACjD,SAAS,EAAE,MAAI,IAAI,CAAC,kBAAkB,EAAE,IAExC,+DACE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,GAAG,EAAuB,EACzD,KAAK,EAAC,eAAe,GACb,EACV,4DACE,OAAO,EAAC,KAAK,EACb,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,GAAG,EAAgB,EAC/C,KAAK,EAAC,aAAa,EACnB,KAAK,EAAC,4BAA4B,EAClC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EACzB,KAAK,EAAE,EAAC,SAAS,EAAC,IAAI,CAAC,kBAAkB,EAAE,EAAC,EAC5C,WAAW,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EACnD,WAAW,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EACnD,YAAY,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EACrD,UAAU,EAAE,MAAI,IAAI,CAAC,aAAa,EAAE,EACpC,WAAW,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EACnD,aAAa,EAAE,CAAC,CAAc,KAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EACzD,aAAa,EAAE,CAAC,CAAc,KAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EACzD,WAAW,EAAE,CAAC,CAAc,KAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAErD,8DAAO,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAU,EAClD,IAAI,CAAC,wBAAwB,EAAE,EAChC,gEACE,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,EAC5B,KAAK,EAAC,yBAAyB,kBACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,EACjD,IAAI,EAAC,aAAa,EAClB,WAAW,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EACvD,SAAS,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EACnD,YAAY,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EACzD,UAAU,EAAE,CAAC,CAAY,KAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,EACrD,aAAa,EAAE,CAAC,CAAc,KAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAC7D,WAAW,EAAE,CAAC,CAAc,KAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAEjD,EACT,IAAI,CAAC,cAAc,EAAE,CAClB,EACL,IAAI,CAAC,YAAY,EAAE,EACpB,4DAAK,KAAK,EAAC,WAAW,EAAC,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,GAAG,EAAiB,GAAQ,EACrF,8DAAa,CACT,CACD,EACP;KACH;IAED,aAAa;QACX,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;SAC/C;KACF;IAED,aAAa;QACX,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;SAC9C;KACF;IAED,eAAe,CAAC,KAAY;QAC1B,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO;;QAGhD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;QAGjD,MAAM,aAAa,GAAG,GAAG,CAAC;;;QAG1B,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,aAAa,CAAE;QAC/C,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,GAAG,aAAa,CAAC;;QAG7C,MAAM,UAAU,GAAG,IAAI,CAAC,UAAsC,CAAC;;QAG/D,IAAI,UAAU,CAAC,YAAY,IAAI,UAAU,CAAC,cAAc,EAAE;YACxD,MAAM,GAAG,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAC1C,KAAK,CAAC,CAAC,GAAG,aAAa,CAAC;YACxB,KAAK,CAAC,CAAC,GAAG,YAAY,CAAC;YACvB,MAAM,gBAAgB,GAAG,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;;YAGpD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,gBAAgB,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC;YAClE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,gBAAgB,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;SACnE;aAAM;;YAEL,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,aAAa,IAAI,CAAC;YACxD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,YAAY,IAAI,CAAC;SACvD;;QAGD,MAAM,SAAS,GAAG,GAAG,CAAC;QACtB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,aAAa,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;QACjG,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,aAAa,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;QACjG,MAAM,EAAE,GAAG,aAAa,GAAG,SAAS,CAAC;QACrC,MAAM,EAAE,GAAG,aAAa,GAAG,SAAS,CAAC;QACrC,MAAM,EAAE,GAAG,CAAC,CAAC;QACb,MAAM,EAAE,GAAG,CAAC,CAAC;QACb,MAAM,EAAE,GAAG,aAAa,CAAC;QACzB,MAAM,EAAE,GAAG,aAAa,CAAC;QAEzB,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACzD,eAAe,CAAC,KAAK,GAAG,aAAa,CAAC;QACtC,eAAe,CAAC,MAAM,GAAG,aAAa,CAAC;QACvC,MAAM,YAAY,GAAG,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEtD,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;;QAGjE,YAAY,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACzC,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC;QACpC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QACvD,YAAY,CAAC,SAAS,EAAE,CAAC;QACzB,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC;QACtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC;SACvE;QACD,YAAY,CAAC,SAAS,EAAE,CAAC;QACzB,YAAY,CAAC,MAAM,EAAE,CAAC;QAEtB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,eAAe,CAAC,SAAS,EAAE,GAAG,CAAC;KACrF;;;;;;;;;;;", "names": [], "sources": ["src/components/image-cropper/image-cropper.css?tag=image-cropper&encapsulation=shadow", "src/components/image-cropper/image-cropper.tsx"], "sourcesContent": [":host {\r\n  /* --active-color: rgb(5, 197, 175); */\r\n    /* --inactive-color: gray; */\r\n  --active-color:orange;\r\n  --inactive-color:orange;\r\n  --active-stroke: 4;\r\n  --inactive-stroke: 4;\r\n  --main-background: transparent;\r\n  display: block;\r\n  position: relative;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n* {\r\n  user-select:none;\r\n  -webkit-user-select:none;\r\n  -moz-user-select:none;\r\n}\r\n\r\n.container {\r\n  display: flex;\r\n  justify-content: center;\r\n  background: var(--main-background);\r\n  overflow: hidden;\r\n}\r\n\r\n.absolute {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.cropper-controls {\r\n  stroke:var(--active-color);\r\n}\r\n\r\n.footer {\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 0;\r\n  height: 100px;\r\n  width: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.items {\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 2.0em;\r\n}\r\n\r\n.items .item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n.items .item:first-child {\r\n  text-align: left;\r\n}\r\n.items .item:last-child {\r\n  text-align: right;\r\n}\r\n\r\n.accept-use img {\r\n  width: 2.5em;\r\n  height: 2.5em;\r\n  pointer-events: all;\r\n  cursor:pointer;\r\n}\r\n\r\n.accept-cancel img {\r\n  width: 2.5em;\r\n  height: 2.5em;\r\n  pointer-events: all;\r\n  cursor:pointer;\r\n}\r\n\r\n.cropper-svg {\r\n  align-self: center;\r\n  touch-action: none;\r\n  cursor:grab;\r\n}\r\n\r\n.cropper-svg polygon {\r\n  cursor:move;\r\n}\r\n\r\n.cropper-svg rect {\r\n  cursor:grab;\r\n}\r\n\r\n.hidden-canvas {\r\n  display: none;\r\n}\r\n\r\n.cropper-svg .inactive-selection {\r\n  stroke:var(--inactive-color);\r\n  cursor:pointer;\r\n}\r\n\r\n.dashed {\r\n  stroke-dasharray:10,10;\r\n}\r\n\r\n\r\n.magnifier {\r\n  position: absolute;\r\n  width: 100px;\r\n  height: 100px;\r\n  left: 0;\r\n  top: 0;\r\n  border: 1px solid #00bceb;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  display: none;\r\n  pointer-events: none;\r\n  background-size: 100%;\r\n  background-repeat: no-repeat;\r\n}", "import { Component, Event, EventEmitter, Fragment, Host, Method, Prop, State, Watch, h } from '@stencil/core';\r\n\r\nexport interface DetectedQuadResult{\r\n  location: Quad;\r\n  confidenceAsDocumentBoundary: number;\r\n}\r\n\r\nexport interface Quad{\r\n  points:[Point,Point,Point,Point];\r\n}\r\n\r\nexport interface Point{\r\n  x:number;\r\n  y:number;\r\n}\r\n\r\nexport interface Rect{\r\n  x:number;\r\n  y:number;\r\n  width:number;\r\n  height:number;\r\n}\r\n\r\nexport interface CropOptions {\r\n  perspectiveTransform?:boolean;\r\n  colorMode?:\"binary\"|\"gray\"|\"color\";\r\n  selection?:Quad|Rect;\r\n  source?:Blob|string|HTMLImageElement|HTMLCanvasElement;\r\n}\r\n\r\n@Component({\r\n  tag: 'image-cropper',\r\n  styleUrl: 'image-cropper.css',\r\n  shadow: true,\r\n})\r\nexport class ImageCropper {\r\n  handlers:number[] = [0,1,2,3,4,5,6,7];\r\n  polygonMouseDown:boolean = false;\r\n  polygonMouseDownPoint:Point = {x:0,y:0};\r\n  previousDistance:number|undefined = undefined;\r\n  svgMouseDownPoint:Point|undefined = undefined;\r\n  handlerMouseDownPoint:Point = {x:0,y:0};\r\n  root:HTMLElement;\r\n  containerElement:HTMLElement;\r\n  svgElement:SVGElement;\r\n  canvasElement:HTMLCanvasElement;\r\n  originalPoints:[Point,Point,Point,Point] = undefined;\r\n   usingTouchEvent:boolean = false;\r\n  usingQuad = false;\r\n  magnifierElement: HTMLElement; // Add this line\r\n\r\n  @Prop() img?: HTMLImageElement;\r\n  @Prop() rect?: Rect;\r\n  @Prop() quad?: Quad;\r\n  @Prop() license?: string;\r\n  @Prop() hidefooter?: string;\r\n  @Prop() handlersize?: string;\r\n  @Prop() inactiveSelections?: (Quad|Rect)[];\r\n  @State() viewBox:string = \"0 0 1280 720\";\r\n  @State() activeStroke:number = 2;\r\n  @Prop() rotation:number = 0;\r\n  @State() inActiveStroke:number = 4;\r\n  @State() selectedHandlerIndex:number = -1;\r\n  @State() points:[Point,Point,Point,Point] = undefined;\r\n  @State() offsetX = 0;\r\n  @State() offsetY = 0;\r\n  @State() scale = 1.0;\r\n  @Event() confirmed?: EventEmitter<void>;\r\n  @Event() canceled?: EventEmitter<void>;\r\n  @Event() selectionClicked?: EventEmitter<number>;\r\n\r\n  componentDidLoad(){\r\n    this.containerElement.addEventListener(\"touchmove\", (e:TouchEvent) => {\r\n      this.onContainerTouchMove(e);\r\n    })\r\n    this.containerElement.addEventListener(\"touchend\", () => {\r\n      this.previousDistance = undefined;\r\n      this.hideMagnifier(); // Hide magnifier on touch end\r\n    })\r\n  }\r\n\r\n  @Watch('img')\r\n  watchImgPropHandler(newValue: HTMLImageElement) {\r\n    if (newValue) {\r\n      console.log('watchImgPropHandler triggered with newValue:', newValue);\r\n      this.resetStates();\r\n      this.viewBox = `0 0 ${newValue.naturalWidth} ${newValue.naturalHeight}`;\r\n      console.log('viewBox set to:', this.viewBox);\r\n      if (this.root) {\r\n        const inActiveStroke = parseInt(this.root.style.getPropertyValue(\"--inactive-stroke\"));\r\n        const activeStroke = parseInt(this.root.style.getPropertyValue(\"--active-stroke\"));\r\n        console.log('inActiveStroke:', inActiveStroke, 'activeStroke:', activeStroke);\r\n        if (inActiveStroke) {\r\n          this.inActiveStroke = inActiveStroke;\r\n        }\r\n        if (activeStroke) {\r\n          this.activeStroke = activeStroke;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  @Watch('rect')\r\n  watchRectPropHandler(newValue: Rect) {\r\n    if (newValue) {\r\n      this.usingQuad = false;\r\n      let points = this.getPointsFromRect(newValue);\r\n      if (this.img) {\r\n        this.restrainPointsInBounds(points,this.img.naturalWidth,this.img.naturalHeight);\r\n      }\r\n      this.points = points;\r\n    }\r\n  }\r\n\r\n  getPointsFromRect(rect:Rect):[Point,Point,Point,Point]{\r\n    const point1:Point = {x:rect.x,y:rect.y};\r\n    const point2:Point = {x:rect.x+rect.width,y:rect.y};\r\n    const point3:Point = {x:rect.x+rect.width,y:rect.y+rect.height};\r\n    const point4:Point = {x:rect.x,y:rect.y+rect.height};\r\n    return [point1,point2,point3,point4];\r\n  }\r\n\r\n  @Watch('quad')\r\n  watchQuadPropHandler(newValue: Quad) {\r\n    if (newValue) {\r\n      this.usingQuad = true;\r\n      let points = newValue.points;\r\n      if (this.img) {\r\n        this.restrainPointsInBounds(points,this.img.naturalWidth,this.img.naturalHeight);\r\n      }\r\n      this.points = newValue.points;\r\n    }\r\n  }\r\n\r\n  onCanceled(){\r\n    if (this.canceled){\r\n      this.canceled.emit();\r\n    }\r\n  }\r\n\r\n  onConfirmed(){\r\n    if (this.confirmed){\r\n      this.confirmed.emit();\r\n    }\r\n  }\r\n\r\n  getPointsData(){\r\n    if (this.points) {\r\n      let pointsData = this.points[0].x + \",\" + this.points[0].y + \" \";\r\n      pointsData = pointsData + this.points[1].x + \",\" + this.points[1].y +\" \";\r\n      pointsData = pointsData + this.points[2].x + \",\" + this.points[2].y +\" \";\r\n      pointsData = pointsData + this.points[3].x + \",\" + this.points[3].y;\r\n      return pointsData;\r\n    }\r\n    return \"\";\r\n  }\r\n\r\n  renderFooter(){\r\n    if (this.hidefooter === \"\") {\r\n      return \"\";\r\n    }\r\n    return (\r\n      <div class=\"footer\">\r\n        <section class=\"items\">\r\n          <div class=\"item accept-cancel\" onClick={() => this.onCanceled()}>\r\n            <img src=\"data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Ccircle fill='%23727A87' cx='256' cy='256' r='256'/%3E%3Cg id='Icon_5_'%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M394.2,142L370,117.8c-1.6-1.6-4.1-1.6-5.7,0L258.8,223.4c-1.6,1.6-4.1,1.6-5.7,0L147.6,117.8 c-1.6-1.6-4.1-1.6-5.7,0L117.8,142c-1.6,1.6-1.6,4.1,0,5.7l105.5,105.5c1.6,1.6,1.6,4.1,0,5.7L117.8,364.4c-1.6,1.6-1.6,4.1,0,5.7 l24.1,24.1c1.6,1.6,4.1,1.6,5.7,0l105.5-105.5c1.6-1.6,4.1-1.6,5.7,0l105.5,105.5c1.6,1.6,4.1,1.6,5.7,0l24.1-24.1 c1.6-1.6,1.6-4.1,0-5.7L288.6,258.8c-1.6-1.6-1.6-4.1,0-5.7l105.5-105.5C395.7,146.1,395.7,143.5,394.2,142z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\" />\r\n          </div>\r\n          <div class=\"item accept-use\" onClick={() => this.onConfirmed()}>\r\n            <img src=\"data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 512 512' enable-background='new 0 0 512 512' xml:space='preserve'%3E%3Ccircle fill='%232CD865' cx='256' cy='256' r='256'/%3E%3Cg id='Icon_1_'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23FFFFFF' d='M208,301.4l-55.4-55.5c-1.5-1.5-4-1.6-5.6-0.1l-23.4,22.3c-1.6,1.6-1.7,4.1-0.1,5.7l81.6,81.4 c3.1,3.1,8.2,3.1,11.3,0l171.8-171.7c1.6-1.6,1.6-4.2-0.1-5.7l-23.4-22.3c-1.6-1.5-4.1-1.5-5.6,0.1L213.7,301.4 C212.1,303,209.6,303,208,301.4z'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E\" />\r\n          </div>\r\n        </section>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  rendenInactiveSelections(){\r\n    if (!this.inactiveSelections) {\r\n      return \"\";\r\n    }\r\n    return (\r\n      <Fragment>\r\n        {this.inactiveSelections.map((selection,index) => (\r\n          <polygon\r\n            points={this.getPointsDataFromSelection(selection)}\r\n            class=\"inactive-selection dashed\"\r\n            stroke-width={this.inActiveStroke * this.getRatio()}\r\n            fill=\"transparent\"\r\n            onMouseUp={()=>this.onSelectionClicked(index)}\r\n            onTouchStart={()=>this.onSelectionClicked(index)}\r\n          >\r\n         </polygon>\r\n        ))}\r\n      </Fragment>\r\n    );\r\n  }\r\n\r\n  onSelectionClicked(index:number) {\r\n    if (this.selectionClicked) {\r\n      this.selectionClicked.emit(index);\r\n    }\r\n  }\r\n\r\n  getPointsDataFromSelection(selection:Quad|Rect){\r\n    let points:Point[] = [];\r\n    if (\"width\" in selection) { //is Rect\r\n      points = this.getPointsFromRect(selection);\r\n    }else{\r\n      points = selection.points;\r\n    }\r\n    let pointsData = points[0].x + \",\" + points[0].y + \" \";\r\n    pointsData = pointsData + points[1].x + \",\" + points[1].y +\" \";\r\n    pointsData = pointsData + points[2].x + \",\" + points[2].y +\" \";\r\n    pointsData = pointsData + points[3].x + \",\" + points[3].y;\r\n    return pointsData;\r\n  }\r\n\r\n  renderHandlers(){\r\n    if (!this.points) {\r\n      return (<div></div>)\r\n    }\r\n    return (\r\n      <Fragment>\r\n        {this.handlers.map(index => (\r\n          <rect\r\n            x={this.getHandlerPos(index,\"x\")}\r\n            y={this.getHandlerPos(index,\"y\")}\r\n            width={this.getHandlerSize()}\r\n            height={this.getHandlerSize()}\r\n            class=\"cropper-controls\"\r\n            stroke-width={index === this.selectedHandlerIndex ? this.activeStroke * 2 * this.getRatio() : this.activeStroke * this.getRatio()}\r\n            fill=\"transparent\"\r\n            onMouseDown={(e:MouseEvent)=>this.onHandlerMouseDown(e,index)}\r\n            onMouseUp={(e:MouseEvent)=>this.onHandlerMouseUp(e)}\r\n            onTouchStart={(e:TouchEvent)=>this.onHandlerTouchStart(e,index)}\r\n            onPointerDown={(e:PointerEvent)=>this.onHandlerPointerDown(e,index)}\r\n          />\r\n        ))}\r\n      </Fragment>\r\n    )\r\n  }\r\n\r\n  getHandlerPos(index:number,key:string) {\r\n    let pos = 0;\r\n    let size = this.getHandlerSize();\r\n    if (index === 0){\r\n      pos = this.points[0][key];\r\n    }else if (index === 1) {\r\n      pos = this.points[0][key] + (this.points[1][key] - this.points[0][key])/2;\r\n    }else if (index === 2) {\r\n      pos = this.points[1][key];\r\n    }else if (index === 3) {\r\n      pos = this.points[1][key] + (this.points[2][key] - this.points[1][key])/2;\r\n    }else if (index === 4) {\r\n      pos = this.points[2][key];\r\n    }else if (index === 5) {\r\n      pos = this.points[3][key] + (this.points[2][key] - this.points[3][key])/2;\r\n    }else if (index === 6) {\r\n      pos = this.points[3][key];\r\n    }else if (index === 7) {\r\n      pos = this.points[0][key] + (this.points[3][key] - this.points[0][key])/2;\r\n    }\r\n    pos = pos - size/2;\r\n    return pos;\r\n  }\r\n\r\n  getHandlerSize() {\r\n    let ratio = this.getRatio();\r\n    let size:number = 20;\r\n    if (this.handlersize) {\r\n      try {\r\n        size = parseInt(this.handlersize);\r\n      } catch (error) {\r\n        console.log(error);\r\n      }\r\n    }\r\n    return Math.ceil(size*ratio);\r\n  }\r\n\r\n  onSVGTouchStart(e:TouchEvent) {\r\n    this.usingTouchEvent = true;\r\n    this.svgMouseDownPoint = undefined;\r\n    this.previousDistance = undefined;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    if (e.touches.length > 1) {\r\n      this.selectedHandlerIndex = -1;\r\n    }else{\r\n      if (this.selectedHandlerIndex != -1) {\r\n        this.originalPoints = JSON.parse(JSON.stringify(this.points));  //We need this info so that whether we start dragging the rectangular in the center or in the corner will not affect the result.\r\n        this.handlerMouseDownPoint.x = coord.x;\r\n        this.handlerMouseDownPoint.y = coord.y;\r\n      }else{\r\n        this.svgMouseDownPoint = {x:coord.x,y:coord.y};\r\n        this.polygonMouseDown = true; // Add this line to enable dragging immediately\r\n        this.polygonMouseDownPoint = { x: coord.x, y: coord.y }; // Add this line to store the initial touch point\r\n        this.originalPoints = JSON.parse(JSON.stringify(this.points)); // Add this line to store the original points\r\n      }\r\n    }\r\n  }\r\n\r\n  onSVGTouchEnd() {\r\n    this.svgMouseDownPoint = undefined;\r\n  }\r\n\r\n  onSVGTouchMove(e:TouchEvent) {\r\n    e.stopPropagation();\r\n    e.preventDefault();\r\n    if (e.touches.length === 2) {\r\n      this.pinchAndZoom(e);\r\n    }else{\r\n      if (this.svgMouseDownPoint) {\r\n        this.panSVG(e);\r\n      } else if (this.polygonMouseDown) { // Add this condition to handle dragging\r\n        this.handleMoveEvent(e);\r\n      }else{\r\n        this.handleMoveEvent(e);\r\n      }\r\n    }\r\n  }\r\n\r\n  //handle pinch and zoom\r\n  pinchAndZoom(e:TouchEvent){\r\n    const distance = this.getDistanceBetweenTwoTouches(e.touches[0],e.touches[1]);\r\n    if (this.previousDistance) {\r\n      if ((distance - this.previousDistance)>0) { //zoom\r\n        this.scale = Math.min(10, this.scale + 0.02);\r\n      }else{\r\n        this.scale = Math.max(0.1,this.scale - 0.02);\r\n      }\r\n      this.previousDistance = distance;\r\n    }else{\r\n      this.previousDistance = distance;\r\n    }\r\n  }\r\n\r\n  getDistanceBetweenTwoTouches(touch1:Touch,touch2:Touch){\r\n    const offsetX = touch1.clientX - touch2.clientX;\r\n    const offsetY = touch1.clientY - touch2.clientY;\r\n    const distance = offsetX * offsetX + offsetY + offsetY;\r\n    return distance;\r\n  }\r\n\r\n  onContainerMouseUp(){\r\n    this.svgMouseDownPoint = undefined;\r\n    if (!this.usingTouchEvent) {\r\n      this.selectedHandlerIndex = -1;\r\n      this.polygonMouseDown = false;\r\n      this.hideMagnifier(); // Hide the magnifier\r\n    }\r\n  }\r\n\r\n  onSVGMouseDown(e:MouseEvent) {\r\n    if (!this.usingTouchEvent) {\r\n      let coord = this.getMousePosition(e,this.svgElement);\r\n      this.svgMouseDownPoint = {x:coord.x,y:coord.y};\r\n    }\r\n  }\r\n\r\n  onContainerWheel(e:WheelEvent) {\r\n    if (e.deltaY<0) {\r\n      this.scale = this.scale + 0.1;\r\n    }else{\r\n      this.scale = Math.max(0.1, this.scale - 0.1);\r\n    }\r\n    e.preventDefault();\r\n  }\r\n\r\n  onContainerTouchMove(e:TouchEvent) {\r\n    e.preventDefault();\r\n    if (e.touches.length === 2) {\r\n      this.pinchAndZoom(e);\r\n    }\r\n  }\r\n\r\n  getPanAndZoomStyle(){\r\n    if (this.img) {\r\n      // const percentX = this.offsetX / this.img.naturalWidth * 100;\r\n      // const percentY = this.offsetY / this.img.naturalHeight * 100;\r\n      return `scale(1.0)  rotate(${this.rotation}deg)`;\r\n      // return \"scale(\"+this.scale+\") translateX(\"+percentX+\"%)translateY(\"+percentY+\"%)\";\r\n    }else{\r\n      return \"scale(1.0)\";\r\n    }\r\n  }\r\n\r\n  onSVGMouseMove(e:MouseEvent){\r\n    if (this.svgMouseDownPoint) {\r\n      this.panSVG(e);\r\n    }else{\r\n      this.handleMoveEvent(e);\r\n    }\r\n  }\r\n\r\n  panSVG(e:TouchEvent|MouseEvent){\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    let offsetX = coord.x - this.svgMouseDownPoint.x;\r\n    let offsetY = coord.y - this.svgMouseDownPoint.y;\r\n    //console.log(\"coord\");\r\n    //console.log(coord);\r\n    //console.log(\"svgMouseDownPoint\");\r\n    //console.log(this.svgMouseDownPoint);\r\n\r\n    //console.log(offsetX)\r\n    //console.log(offsetY)\r\n    //e.g img width: 100, offsetX: -10, translateX: -10%\r\n    this.offsetX = this.offsetX + offsetX;\r\n    this.offsetY = this.offsetY + offsetY;\r\n  }\r\n\r\n  handleMoveEvent(e:MouseEvent|TouchEvent){\r\n    if (this.polygonMouseDown) {\r\n      let coord = this.getMousePosition(e,this.svgElement);\r\n      let offsetX = coord.x - this.polygonMouseDownPoint.x;\r\n      let offsetY = coord.y - this.polygonMouseDownPoint.y;\r\n      let newPoints = JSON.parse(JSON.stringify(this.originalPoints));\r\n      for (const point of newPoints) {\r\n        point.x = point.x + offsetX;\r\n        point.y = point.y + offsetY;\r\n        if (point.x < 0 || point.y < 0 || point.x > this.img.naturalWidth || point.y > this.img.naturalHeight){\r\n          console.log(\"reach bounds\");\r\n          return;\r\n        }\r\n      }\r\n      this.points = newPoints;\r\n      this.showMagnifier(); // Show the magnifier when the rect is moved\r\n      this.updateMagnifier(coord); // Update the magnifier position and content\r\n    }\r\n    if (this.selectedHandlerIndex >= 0) {\r\n      let pointIndex = this.getPointIndexFromHandlerIndex(this.selectedHandlerIndex);\r\n      let coord = this.getMousePosition(e,this.svgElement);\r\n      let offsetX = coord.x - this.handlerMouseDownPoint.x;\r\n      let offsetY = coord.y - this.handlerMouseDownPoint.y;\r\n      let newPoints = JSON.parse(JSON.stringify(this.originalPoints));\r\n      if (pointIndex != -1) {\r\n        let selectedPoint = newPoints[pointIndex];\r\n        selectedPoint.x = this.originalPoints[pointIndex].x + offsetX;\r\n        selectedPoint.y = this.originalPoints[pointIndex].y + offsetY;\r\n        if (this.usingQuad === false) { //rect mode\r\n          if (pointIndex === 0) {\r\n            newPoints[1].y = selectedPoint.y;\r\n            newPoints[3].x = selectedPoint.x;\r\n          }else if (pointIndex === 1) {\r\n            newPoints[0].y = selectedPoint.y;\r\n            newPoints[2].x = selectedPoint.x;\r\n          }else if (pointIndex === 2) {\r\n            newPoints[1].x = selectedPoint.x;\r\n            newPoints[3].y = selectedPoint.y;\r\n          }else if (pointIndex === 3) {\r\n            newPoints[0].x = selectedPoint.x;\r\n            newPoints[2].y = selectedPoint.y;\r\n          }\r\n        }\r\n      }else{ //mid-point handlers\r\n        if (this.selectedHandlerIndex === 1) {\r\n          newPoints[0].y = this.originalPoints[0].y + offsetY;\r\n          newPoints[1].y = this.originalPoints[1].y + offsetY;\r\n        }else if (this.selectedHandlerIndex === 3) {\r\n          newPoints[1].x = this.originalPoints[1].x + offsetX;\r\n          newPoints[2].x = this.originalPoints[2].x + offsetX;\r\n        }else if (this.selectedHandlerIndex === 5) {\r\n          newPoints[2].y = this.originalPoints[2].y + offsetY;\r\n          newPoints[3].y = this.originalPoints[3].y + offsetY;\r\n        }else if (this.selectedHandlerIndex === 7) {\r\n          newPoints[0].x = this.originalPoints[0].x + offsetX;\r\n          newPoints[3].x = this.originalPoints[3].x + offsetX;\r\n        }\r\n      }\r\n      if (this.img) {\r\n        this.restrainPointsInBounds(newPoints,this.img.naturalWidth,this.img.naturalHeight);\r\n      }\r\n      this.points = newPoints;\r\n      this.showMagnifier(); // Show the magnifier when the rect is moved\r\n      this.updateMagnifier(coord); // Update the magnifier position and content\r\n    }\r\n  }\r\n\r\n  restrainPointsInBounds(points:[Point,Point,Point,Point],imgWidth:number,imgHeight:number){\r\n    for (let index = 0; index < points.length; index++) {\r\n      const point = points[index];\r\n      point.x = Math.max(0,point.x);\r\n      point.x = Math.min(point.x,imgWidth);\r\n      point.y = Math.max(0,point.y);\r\n      point.y = Math.min(point.y,imgHeight);\r\n    }\r\n  }\r\n\r\n  onPolygonMouseDown(e:MouseEvent){\r\n    e.stopPropagation();\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    this.polygonMouseDown = true;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    this.polygonMouseDownPoint.x = coord.x;\r\n    this.polygonMouseDownPoint.y = coord.y;\r\n    this.showMagnifier(); // Show the magnifier when the rect starts being moved\r\n  }\r\n\r\n  onPolygonMouseUp(e:MouseEvent){\r\n    e.stopPropagation();\r\n    if (!this.usingTouchEvent) {\r\n      this.selectedHandlerIndex = -1;\r\n      this.polygonMouseDown = false;\r\n      this.hideMagnifier(); // Hide the magnifier when the rect stops being moved\r\n    }\r\n  }\r\n\r\n  onPolygonTouchStart(e:TouchEvent) {\r\n    this.usingTouchEvent = true;\r\n    e.stopPropagation();\r\n    this.selectedHandlerIndex = -1;\r\n    // this.polygonMouseDown = false;\r\n    this.polygonMouseDown = true;\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    // this.polygonMouseDown = true;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    // this.polygonMouseDownPoint.x = coord.x;\r\n    // this.polygonMouseDownPoint.y = coord.y;\r\n    this.polygonMouseDownPoint = { x: coord.x, y: coord.y }; // Store the initial touch point\r\n    this.showMagnifier(); // Show the magnifier when the rect starts being moved\r\n\r\n  }\r\n\r\n  onPolygonTouchEnd(e:TouchEvent) {\r\n    e.stopPropagation();\r\n    this.selectedHandlerIndex = -1;\r\n    this.polygonMouseDown = false;\r\n    this.hideMagnifier(); // Hide the magnifier when the rect stops being moved\r\n  }\r\n\r\n  onHandlerMouseDown(e:MouseEvent,index:number){\r\n    e.stopPropagation();\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    this.handlerMouseDownPoint.x = coord.x;\r\n    this.handlerMouseDownPoint.y = coord.y;\r\n    this.selectedHandlerIndex = index;\r\n  }\r\n\r\n  onHandlerMouseUp(e:MouseEvent){\r\n    e.stopPropagation();\r\n    if (!this.usingTouchEvent) {\r\n      this.selectedHandlerIndex = -1;\r\n      this.hideMagnifier(); // Hide the magnifier\r\n    }\r\n  }\r\n\r\n  onHandlerTouchStart(e:TouchEvent,index:number) {\r\n    this.usingTouchEvent = true; //Touch events are triggered before mouse events. We can use this to prevent executing mouse events.\r\n    e.stopPropagation();\r\n    this.polygonMouseDown = false;\r\n    let coord = this.getMousePosition(e,this.svgElement);\r\n    this.originalPoints = JSON.parse(JSON.stringify(this.points));\r\n    this.handlerMouseDownPoint.x = coord.x;\r\n    this.handlerMouseDownPoint.y = coord.y;\r\n    this.selectedHandlerIndex = index;\r\n  }\r\n\r\n  onHandlerPointerDown(e:PointerEvent,index:number) {\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.onHandlerMouseDown(e,index);\r\n      e.preventDefault();\r\n    }\r\n  }\r\n\r\n  getPointIndexFromHandlerIndex(index:number){\r\n    if (index === 0) {\r\n      return 0;\r\n    }else if (index === 2) {\r\n      return 1;\r\n    }else if (index === 4) {\r\n      return 2;\r\n    }else if (index === 6) {\r\n      return 3;\r\n    }\r\n    return -1;\r\n  }\r\n\r\n  //Convert the screen coordinates to the SVG's coordinates from https://www.petercollingridge.co.uk/tutorials/svg/interactive/dragging/\r\n  getMousePosition(event: any, svg: any) {\r\n    let CTM = svg.getScreenCTM();\r\n    if (!CTM) {\r\n      return { x: 0, y: 0 };\r\n    }\r\n\r\n    let x, y;\r\n    if (event.targetTouches) { // if it is a touch event\r\n      x = event.targetTouches[0].clientX;\r\n      y = event.targetTouches[0].clientY;\r\n    } else {\r\n      x = event.clientX;\r\n      y = event.clientY;\r\n    }\r\n\r\n    // Invert the transformation matrix\r\n    let det = CTM.a * CTM.d - CTM.b * CTM.c;\r\n    if (det === 0) {\r\n      // Handle the case where the matrix is singular\r\n      return { x: 0, y: 0 };\r\n    }\r\n\r\n    let invCTM = {\r\n      a: CTM.d / det,\r\n      b: -CTM.b / det,\r\n      c: -CTM.c / det,\r\n      d: CTM.a / det,\r\n      e: (CTM.c * CTM.f - CTM.d * CTM.e) / det,\r\n      f: (CTM.b * CTM.e - CTM.a * CTM.f) / det\r\n    };\r\n\r\n    return {\r\n      x: (x - CTM.e) * invCTM.a + (y - CTM.f) * invCTM.c,\r\n      y: (x - CTM.e) * invCTM.b + (y - CTM.f) * invCTM.d\r\n    };\r\n  }\r\n\r\n\r\n  getRatio(){\r\n    if (this.img) {\r\n      return this.img.naturalWidth/750;\r\n    }else{\r\n      return 1;\r\n    }\r\n  }\r\n\r\n  @Method()\r\n  async resetStates():Promise<void>\r\n  {\r\n    this.scale = 1.0;\r\n    this.offsetX = 0;\r\n    this.offsetY = 0;\r\n  }\r\n\r\n  @Method()\r\n  async getAllSelections(convertTo?:\"rect\"|\"quad\"):Promise<(Quad|Rect)[]>\r\n  {\r\n    let all = [];\r\n    for (let index = 0; index < this.inactiveSelections.length; index++) {\r\n      let selection = this.inactiveSelections[index];\r\n      if (convertTo) {\r\n        if (\"width\" in selection && convertTo === \"quad\") {\r\n          selection = {points:this.getPointsFromRect(selection)};\r\n        }else if (!(\"width\" in selection) && convertTo === \"rect\"){\r\n          selection = this.getRectFromPoints(selection.points);\r\n        }\r\n      }\r\n      all.push(selection);\r\n    }\r\n    let useQuad = true;\r\n    if (convertTo) {\r\n      if (convertTo === \"rect\") {\r\n        useQuad = false;\r\n      }\r\n    }else{\r\n      if (!this.usingQuad) {\r\n        useQuad = false;\r\n      }\r\n    }\r\n    if (useQuad) {\r\n      const quad = await this.getQuad();\r\n      all.push(quad);\r\n    }else{\r\n      const rect = await this.getRect();\r\n      all.push(rect);\r\n    }\r\n    return all;\r\n  }\r\n\r\n  @Method()\r\n  async getPoints():Promise<[Point,Point,Point,Point]>\r\n  {\r\n    return this.points;\r\n  }\r\n\r\n  @Method()\r\n  async getQuad():Promise<Quad>\r\n  {\r\n    return {points:this.points};\r\n  }\r\n\r\n  @Method()\r\n  async getRect():Promise<Rect>\r\n  {\r\n    return this.getRectFromPoints(this.points);\r\n  }\r\n\r\n  getRectFromPoints(points:Point[]):Rect{\r\n    let minX:number;\r\n    let minY:number;\r\n    let maxX:number;\r\n    let maxY:number;\r\n    for (const point of points) {\r\n      if (!minX) {\r\n        minX = point.x;\r\n        maxX = point.x;\r\n        minY = point.y;\r\n        maxY = point.y;\r\n      }else{\r\n        minX = Math.min(point.x,minX);\r\n        minY = Math.min(point.y,minY);\r\n        maxX = Math.max(point.x,maxX);\r\n        maxY = Math.max(point.y,maxY);\r\n      }\r\n    }\r\n    minX = Math.floor(minX);\r\n    maxX = Math.floor(maxX);\r\n    minY = Math.floor(minY);\r\n    maxY = Math.floor(maxY);\r\n    return {x:minX,y:minY,width:maxX - minX,height:maxY - minY};\r\n  }\r\n\r\n\r\n\r\n  async getImageFromBlob(source:Blob){\r\n    return new Promise<HTMLImageElement>((resolve, reject) => {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(source);\r\n      reader.onloadend = function () {\r\n        let dataURL:string = reader.result as string;\r\n        let img = document.createElement(\"img\");\r\n        img.onload = function(){\r\n          resolve(img);\r\n        };\r\n        img.onerror = function(){\r\n          reject();\r\n        }\r\n        img.src = dataURL;\r\n      }\r\n    })\r\n  }\r\n\r\n  async getImageFromDataURL(source:string){\r\n    return new Promise<HTMLImageElement>((resolve, reject) => {\r\n      let img = document.createElement(\"img\");\r\n      img.onload = function(){\r\n        resolve(img);\r\n      };\r\n      img.onerror = function(){\r\n        reject();\r\n      }\r\n      img.src = source;\r\n    })\r\n  }\r\n\r\n @Method()\r\n async detect(){}\r\n\r\n\r\n\r\n  getSVGWidth(){\r\n    if (this.img && this.svgElement) {\r\n      this.svgElement.style.height = \"100%\";\r\n      let imgRatio = this.img.naturalWidth/this.img.naturalHeight;\r\n      let width = this.svgElement.clientHeight * imgRatio;\r\n      if (width>this.svgElement.parentElement.clientWidth) {\r\n        width = this.svgElement.parentElement.clientWidth;\r\n        this.svgElement.style.height = width / imgRatio + \"px\";\r\n      }\r\n      return width;\r\n    }\r\n    return \"100%\";\r\n  }\r\n\r\n  onSVGPointerMove(e:PointerEvent){\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      e.stopPropagation();\r\n      e.preventDefault();\r\n      this.onSVGMouseMove(e);\r\n    }\r\n  }\r\n\r\n  onSVGPointerDown(e:PointerEvent){\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.onSVGMouseDown(e);\r\n    }\r\n  }\r\n\r\n  onSVGPointerUp(e:PointerEvent) {\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.svgMouseDownPoint = undefined;\r\n      this.selectedHandlerIndex = -1;\r\n    }\r\n  }\r\n\r\n  onPolygonPointerDown(e:PointerEvent){\r\n    if (e.pointerType != \"mouse\" && !this.usingTouchEvent) {\r\n      this.onPolygonMouseDown(e);\r\n    }\r\n  }\r\n\r\n  onPolygonPointerUp(e:PointerEvent){\r\n    e.stopPropagation();\r\n    this.selectedHandlerIndex = -1;\r\n    this.polygonMouseDown = false;\r\n  }\r\n\r\n  render() {\r\n    return (\r\n      <Host ref={(el) => this.root = el}>\r\n        <div class=\"container absolute\"\r\n          ref={(el) => this.containerElement = el}\r\n          onWheel={(e:WheelEvent)=>this.onContainerWheel(e)}\r\n          onMouseUp={()=>this.onContainerMouseUp()}\r\n        >\r\n          <canvas\r\n            ref={(el) => this.canvasElement = el as HTMLCanvasElement}\r\n            class=\"hidden-canvas\"\r\n          ></canvas>\r\n          <svg\r\n            version=\"1.1\"\r\n            ref={(el) => this.svgElement = el as SVGElement}\r\n            class=\"cropper-svg\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            viewBox={this.viewBox}\r\n            width={this.getSVGWidth()}\r\n            style={{transform:this.getPanAndZoomStyle()}}\r\n            onMouseMove={(e:MouseEvent)=>this.onSVGMouseMove(e)}\r\n            onMouseDown={(e:MouseEvent)=>this.onSVGMouseDown(e)}\r\n            onTouchStart={(e:TouchEvent)=>this.onSVGTouchStart(e)}\r\n            onTouchEnd={()=>this.onSVGTouchEnd()}\r\n            onTouchMove={(e:TouchEvent)=>this.onSVGTouchMove(e)}\r\n            onPointerMove={(e:PointerEvent)=>this.onSVGPointerMove(e)}\r\n            onPointerDown={(e:PointerEvent)=>this.onSVGPointerDown(e)}\r\n            onPointerUp={(e:PointerEvent)=>this.onSVGPointerUp(e)}\r\n          >\r\n            <image href={this.img ? this.img.src : \"\"}></image>\r\n            {this.rendenInactiveSelections()}\r\n            <polygon\r\n              points={this.getPointsData()}\r\n              class=\"cropper-controls dashed\"\r\n              stroke-width={this.activeStroke * this.getRatio()}\r\n              fill=\"transparent\"\r\n              onMouseDown={(e:MouseEvent)=>this.onPolygonMouseDown(e)}\r\n              onMouseUp={(e:MouseEvent)=>this.onPolygonMouseUp(e)}\r\n              onTouchStart={(e:TouchEvent)=>this.onPolygonTouchStart(e)}\r\n              onTouchEnd={(e:TouchEvent)=>this.onPolygonTouchEnd(e)}\r\n              onPointerDown={(e:PointerEvent)=>this.onPolygonPointerDown(e)}\r\n              onPointerUp={(e:PointerEvent)=>this.onPolygonPointerUp(e)}\r\n            >\r\n            </polygon>\r\n            {this.renderHandlers()}\r\n          </svg>\r\n          {this.renderFooter()}\r\n          <div class=\"magnifier\" ref={(el) => this.magnifierElement = el as HTMLElement}></div>\r\n          <slot></slot>\r\n        </div>\r\n      </Host>\r\n    );\r\n  }\r\n\r\n  showMagnifier() {\r\n    if (this.magnifierElement) {\r\n      this.magnifierElement.style.display = 'block';\r\n    }\r\n  }\r\n\r\n  hideMagnifier() {\r\n    if (this.magnifierElement) {\r\n      this.magnifierElement.style.display = 'none';\r\n    }\r\n  }\r\n\r\n  updateMagnifier(coord: Point) {\r\n    if (!this.magnifierElement || !this.img) return;\r\n\r\n    // Get the coordinates and dimensions of the rect\r\n    const rect = this.getRectFromPoints(this.points);\r\n\r\n    // Calculate the position of the magnifier\r\n    const magnifierSize = 100; // Example size\r\n    // const magnifierLeft = (coord.x - 300) - magnifierSize / 2 ;\r\n    // const magnifierTop = (coord.y - 200) - magnifierSize / 2;\r\n    const magnifierLeft = coord.x - magnifierSize ;\r\n    const magnifierTop = coord.y - magnifierSize;\r\n\r\n    // Cast svgElement to SVGSVGElement to use createSVGPoint\r\n    const svgElement = this.svgElement as unknown as SVGSVGElement;\r\n\r\n    // Check if getScreenCTM and createSVGPoint methods are available\r\n    if (svgElement.getScreenCTM && svgElement.createSVGPoint) {\r\n      const ctm = svgElement.getScreenCTM();\r\n      const point = svgElement.createSVGPoint();\r\n      point.x = magnifierLeft;\r\n      point.y = magnifierTop;\r\n      const transformedPoint = point.matrixTransform(ctm);\r\n\r\n      // Set the magnifier's position\r\n      this.magnifierElement.style.left = `${transformedPoint.x - 40}px`;\r\n      this.magnifierElement.style.top = `${transformedPoint.y - 210}px`;\r\n    } else {\r\n      // Fallback if methods are not available\r\n      this.magnifierElement.style.left = `${magnifierLeft}px`;\r\n      this.magnifierElement.style.top = `${magnifierTop}px`;\r\n    }\r\n\r\n    // Set the magnifier's content (e.g., magnified image)\r\n    const zoomLevel = 0.5; // Example zoom level\r\n    const sx = Math.max(0, rect.x + (coord.x - rect.x) / this.scale - magnifierSize / zoomLevel / 2);\r\n    const sy = Math.max(0, rect.y + (coord.y - rect.y) / this.scale - magnifierSize / zoomLevel / 2);\r\n    const sw = magnifierSize / zoomLevel;\r\n    const sh = magnifierSize / zoomLevel;\r\n    const dx = 0;\r\n    const dy = 0;\r\n    const dw = magnifierSize;\r\n    const dh = magnifierSize;\r\n\r\n    const magnifierCanvas = document.createElement(\"canvas\");\r\n    magnifierCanvas.width = magnifierSize;\r\n    magnifierCanvas.height = magnifierSize;\r\n    const magnifierCtx = magnifierCanvas.getContext(\"2d\");\r\n\r\n    magnifierCtx.drawImage(this.img, sx, sy, sw, sh, dx, dy, dw, dh);\r\n\r\n    // Draw the polygon on the magnifier canvas\r\n    magnifierCtx.scale(zoomLevel, zoomLevel);\r\n    magnifierCtx.strokeStyle = 'orange'; // Set the style as needed\r\n    magnifierCtx.lineWidth = this.activeStroke / zoomLevel; // Adjust the line width\r\n    magnifierCtx.beginPath();\r\n    magnifierCtx.moveTo((this.points[0].x - sx), (this.points[0].y - sy));\r\n    for (let i = 1; i < this.points.length; i++) {\r\n      magnifierCtx.lineTo((this.points[i].x - sx), (this.points[i].y - sy));\r\n    }\r\n    magnifierCtx.closePath();\r\n    magnifierCtx.stroke();\r\n\r\n    this.magnifierElement.style.backgroundImage = `url(${magnifierCanvas.toDataURL()})`;\r\n  }\r\n\r\n\r\n\r\n\r\n}\r\n"], "version": 3}