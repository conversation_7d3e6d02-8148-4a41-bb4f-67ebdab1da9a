{"version": 3, "file": "src_app_guide_guide_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAEd;;;AAEzC,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,kDAASA;CACrB,CACF;AAMK,MAAOI,sBAAsB;0BAAtBA,sBAAsB;;mBAAtBA,uBAAsB;AAAA;;QAAtBA;AAAsB;;YAHvBL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,sBAAsB;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFvBT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAEmB;AACT,CAAC;AAEf;;AAanC,MAAOc,eAAe;mBAAfA,eAAe;;mBAAfA,gBAAe;AAAA;;QAAfA;AAAe;;YARxBJ,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,yEAAsB,EACtBQ,+DAAY;AAAA;;sHAIHC,eAAe;IAAAC,YAAA,GAFXd,kDAAS;IAAAM,OAAA,GANtBG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,yEAAsB,EACtBQ,+DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;AChB2D;;;;;;;;;IC6B7DI,4DALF,cAIC,cAKG;IADAA,wDAAA,mBAAAG,8DAAA;MAAA,MAAAC,OAAA,GAAAJ,2DAAA,CAAAM,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAG,cAAA,CAAAP,OAAA,CAAAQ,KAAA,CAA0B;IAAA,EAAC;IAExCZ,0DALE,EAIE,EACE;;;;IAHFA,uDAAA,EAAsB;IAAtBA,wDAAA,CAAAI,OAAA,CAAAY,KAAA,CAAsB;IADtBhB,wDAAA,QAAAI,OAAA,CAAAQ,KAAA,EAAAZ,2DAAA,CAAkB;;;;;;IArBpBA,4DAJR,sBAA4D,iBACrB,kBACM,cACZ,SACrB;IAAAA,oDAAA,GAAiB;IACvBA,0DADuB,EAAK,EACtB;IAEJA,4DADF,cAAyC,cAKrC;IADAA,wDAAA,mBAAAoB,uDAAA;MAAA,MAAAC,QAAA,GAAArB,2DAAA,CAAAsB,GAAA,EAAAf,SAAA;MAAA,MAAAC,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAG,cAAA,CAAAU,QAAA,CAAAT,KAAA,CAA2B;IAAA,EAAC;IAG3CZ,0DANI,EAIE,EACE,EACE;IAGRA,4DADF,cAA2B,QACtB;IAAAA,oDAAA,IAAuB;IAC5BA,0DAD4B,EAAI,EAC1B;IACNA,4DAAA,mBAA6C;IAC3CA,wDAAA,KAAAwB,wCAAA,kBAIC;IASPxB,0DAFI,EAAU,EACF,EACG;;;;IA5BHA,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAqB,QAAA,CAAAK,KAAA,CAAiB;IAInB1B,uDAAA,GAAmB;IAAnBA,wDAAA,QAAAqB,QAAA,CAAAT,KAAA,EAAAZ,2DAAA,CAAmB;IAQpBA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAqB,QAAA,CAAAM,WAAA,CAAuB;IAEK3B,uDAAA,EAAW;IAAXA,wDAAA,YAAW;IAEvBA,uDAAA,EAAuB;IAAvBA,wDAAA,YAAAqB,QAAA,CAAAO,cAAA,CAAuB;;;ADlBhD,MAAO5C,SAAS;EAyGpB6C,YAAoBC,OAAsB,EAAUC,eAAgC;IAAhE,KAAAD,OAAO,GAAPA,OAAO;IAAyB,KAAAC,eAAe,GAAfA,eAAe;IApGnE,KAAAC,UAAU,GAAG,CACX;MACEpB,KAAK,EAAE,gCAAgC;MACvCc,KAAK,EAAE,yCAAyC;MAChDC,WAAW,EAAE,qIAAqI;MAClJC,cAAc,EAAE;KACjB,EACD;MACEhB,KAAK,EAAE,2BAA2B;MAClCc,KAAK,EAAE,8BAA8B;MACrCC,WAAW,EAAE,sLAAsL;MACnMC,cAAc,EAAE,CACd;QACEhB,KAAK,EAAE,oCAAoC;QAC3CI,KAAK,EAAE;OACR,EACD;QACEJ,KAAK,EAAE,oCAAoC;QAC3CI,KAAK,EAAE;OACR;KAEJ,EACD;MACEJ,KAAK,EAAE,2BAA2B;MAClCc,KAAK,EAAE,gCAAgC;MACvCC,WAAW,EAAE,4HAA4H;MACzIC,cAAc,EAAE,CACd;QACEhB,KAAK,EAAE,mCAAmC;QAC1CI,KAAK,EAAE;OACR,EACD;QACEJ,KAAK,EAAE,mCAAmC;QAC1CI,KAAK,EAAE;OACR;KAEJ,EACD;MACEJ,KAAK,EAAE,2BAA2B;MAClCc,KAAK,EAAE,8BAA8B;MACrCC,WAAW,EAAE,8GAA8G;MAC3HC,cAAc,EAAE,CACd;QACEhB,KAAK,EAAE,gCAAgC;QACvCI,KAAK,EAAE;OACR,EACD;QACEJ,KAAK,EAAE,gCAAgC;QACvCI,KAAK,EAAE;OACR;KAEJ,EACD;MACEJ,KAAK,EAAE,2BAA2B;MAClCc,KAAK,EAAE,0BAA0B;MACjCC,WAAW,EAAE,qIAAqI;MAClJC,cAAc,EAAE,CACd;QACEhB,KAAK,EAAE,oCAAoC;QAC3CI,KAAK,EAAE;OACR,EACD;QACEJ,KAAK,EAAE,oCAAoC;QAC3CI,KAAK,EAAE;OACR;KAEJ,EACD;MACEJ,KAAK,EAAE,mCAAmC;MAC1Cc,KAAK,EAAE,2BAA2B;MAClCC,WAAW,EAAE,qHAAqH;MAClIC,cAAc,EAAE,CACd;QACEhB,KAAK,EAAE,mCAAmC;QAC1CI,KAAK,EAAE;OACR,EACD;QACEJ,KAAK,EAAE,mCAAmC;QAC1CI,KAAK,EAAE;OACR;KAEJ,EACD;MACEJ,KAAK,EAAE,mCAAmC;MAC1Cc,KAAK,EAAE,gCAAgC;MACvCC,WAAW,EAAE,2HAA2H;MACxIC,cAAc,EAAE,CACd;QACEhB,KAAK,EAAE,+BAA+B;QACtCI,KAAK,EAAE;OACR,EACD;QACEJ,KAAK,EAAE,+BAA+B;QACtCI,KAAK,EAAE;OACR;KAEJ,CACF;EAGsF;EAEvFiB,eAAeA,CAAA;IACb,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,aAAa,EAAE;MAC5C,MAAMC,cAAc,GAAG,IAAI,CAACF,MAAM,CAACC,aAAa,CAACD,MAAM;;EAE3D;EAEMvB,cAAcA,CAAC0B,QAAgB;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACnC,MAAMC,KAAK,SAASF,KAAI,CAACP,eAAe,CAACU,MAAM,CAAC;QAC9CtD,SAAS,EAAEY,mFAAmB;QAC9B2C,QAAQ,EAAE,aAAa;QACvBC,cAAc,EAAE;UAAEN,QAAQ,EAAEA;QAAQ;OACrC,CAAC;MACF,aAAaG,KAAK,CAACI,OAAO,EAAE;IAAC;EAC/B;EAEAC,IAAIA,CAAA;IACF,IAAI,CAACf,OAAO,CAACgB,YAAY,CAAC,UAAU,CAAC;EACvC;EAEAC,IAAIA,CAAA;IACF,IAAI,IAAI,CAACb,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,aAAa,EAAE;MAC5C,MAAMC,cAAc,GAAG,IAAI,CAACF,MAAM,CAACC,aAAa,CAACD,MAAM;MACvD,MAAMc,YAAY,GAAGZ,cAAc,CAACa,WAAW;MAC/C,MAAMC,WAAW,GAAGd,cAAc,CAACe,MAAM,CAACC,MAAM,GAAG,CAAC;MAEpD,IAAIJ,YAAY,KAAKE,WAAW,EAAE;QAChC;QACA,IAAI,CAACL,IAAI,EAAE;OACZ,MAAM;QACL;QACAT,cAAc,CAACiB,SAAS,EAAE;;;EAGhC;;aA5IWrE,SAAS;;mBAATA,UAAS,EAAAgB,+DAAA,CAAAT,yDAAA,GAAAS,+DAAA,CAAAT,2DAAA;AAAA;;QAATP,UAAS;EAAAyE,SAAA;EAAAC,SAAA,WAAAC,gBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;MCRtB5D,4DAAA,aAAyB;MACzBA,uDAAA,iBAA0B;MAItBA,4DAFJ,kBAAc,aACoB,6BACc;MAC1CA,wDAAA,IAAA+D,iCAAA,2BAA4D;MAqClE/D,0DAJI,EAAmB,EAGf,EACM;MAGVA,4DAFJ,iBAAY,aACe,oBACuC;MAAjBA,wDAAA,mBAAAgE,+CAAA;QAAAhE,2DAAA,CAAAiE,GAAA;QAAA,OAAAjE,yDAAA,CAAS6D,GAAA,CAAAd,IAAA,EAAM;MAAA,EAAC;MAC3D/C,4DAAA,YAAM;MAAAA,oDAAA,eAAO;MAAAA,0DAAA,EAAO;MACpBA,uDAAA,mBAA6D;MAKnEA,0DAJI,EAAa,EAET,EACK,EACP;;;MA/CgCA,uDAAA,GAAa;MAAbA,wDAAA,YAAA6D,GAAA,CAAA7B,UAAA,CAAa", "sources": ["./src/app/guide/guide-routing.module.ts", "./src/app/guide/guide.module.ts", "./src/app/guide/guide.page.ts", "./src/app/guide/guide.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { GuidePage } from './guide.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: GuidePage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class GuidePageRoutingModule {}\r\n", "import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { GuidePageRoutingModule } from './guide-routing.module';\r\nimport { SharedModule } from '../shared/shared.module'; // Import SharedModule\r\n\r\nimport { GuidePage } from './guide.page';\r\n\r\n@NgModule({\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    GuidePageRoutingModule,\r\n    SharedModule\r\n  ],\r\n  declarations: [GuidePage]\r\n})\r\nexport class GuidePageModule {}\r\n", "import { Component, ViewChild, AfterViewInit, ElementRef, ViewEncapsulation } from '@angular/core';\r\nimport { NavController, ModalController } from '@ionic/angular';\r\nimport { ImageModalComponent } from '../image-modal/image-modal.component';\r\n\r\n@Component({\r\n  selector: 'app-guide',\r\n  templateUrl: './guide.page.html',\r\n  styleUrls: ['./guide.page.scss'],\r\n})\r\nexport class GuidePage implements AfterViewInit {\r\n\r\n  @ViewChild('swiper', { static: true }) swiper: ElementRef | undefined;\r\n  @ViewChild('popover', { static: true }) popover: any;\r\n\r\n  slidesData = [\r\n    {\r\n      image: 'assets/guide/welcome_guide.png',\r\n      title: 'Bienvenue dans le Guide de Numérisation',\r\n      description: 'Suivez ce guide pour capturer vos bons de livraison avec précision. Découvrez les bonnes pratiques et évitez les erreurs courantes.',\r\n      images_details: []\r\n    },\r\n    {\r\n      image: 'assets/guide/good_one.jpg',\r\n      title: 'Assurez-vous de l\\'éclairage',\r\n      description: 'Prenez des photos dans un environnement bien éclairé pour une meilleure qualité d\\'image et évitez les ombres en tenant l\\'appareil à un angle de 90 degrés par rapport au document.',\r\n      images_details: [\r\n        {\r\n          image: 'assets/guide/lighting_error_01.JPG',\r\n          class: 'img-details-2'\r\n        },\r\n        {\r\n          image: 'assets/guide/lighting_error_02.JPG',\r\n          class: 'img-details-2'\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      image: 'assets/guide/good_one.jpg',\r\n      title: 'Redressez les images inclinées',\r\n      description: 'Assurez-vous que le document est bien droit avant de prendre la photo. Une image inclinée peut rendre l\\'OCR moins précis.',\r\n      images_details: [\r\n        {\r\n          image: 'assets/guide/rotated_error_01.png',\r\n          class: 'img-details-2'\r\n        },\r\n        {\r\n          image: 'assets/guide/rotated_error_02.jpg',\r\n          class: 'img-details-2'\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      image: 'assets/guide/good_one.jpg',\r\n      title: 'Maintenez le document à plat',\r\n      description: 'Placez le document sur une surface plane et assurez-vous qu\\'il est bien aligné pour éviter les distorsions.',\r\n      images_details: [\r\n        {\r\n          image: 'assets/guide/skew_error_01.jpg',\r\n          class: 'img-details-2'\r\n        },\r\n        {\r\n          image: 'assets/guide/skew_error_02.JPG',\r\n          class: 'img-details-2'\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      image: 'assets/guide/good_one.jpg',\r\n      title: 'Lissez le papier froissé',\r\n      description: 'Si le papier est froissé, essayez de le lisser autant que possible avant de prendre la photo pour améliorer la qualité de l\\'image.',\r\n      images_details: [\r\n        {\r\n          image: 'assets/guide/wrinkled_error_01.JPG',\r\n          class: 'img-details-2'\r\n        },\r\n        {\r\n          image: 'assets/guide/wrinkled_error_02.JPG',\r\n          class: 'img-details-2'\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      image: 'assets/guide/good_one_quality.jpg',\r\n      title: 'Vérifiez la mise au point',\r\n      description: 'Assurez-vous que le document est bien mis au point avant de prendre la photo pour des résultats clairs et lisibles.',\r\n      images_details: [\r\n        {\r\n          image: 'assets/guide/quality_error_01.jpg',\r\n          class: 'img-details-2'\r\n        },\r\n        {\r\n          image: 'assets/guide/quality_error_02.jpg',\r\n          class: 'img-details-2'\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      image: 'assets/guide/good_one_quality.jpg',\r\n      title: 'Ne rien écrire sur le document',\r\n      description: 'Pour des résultats OCR optimaux, assurez-vous que le document ne contient aucune écriture manuelle avant la numérisation.',\r\n      images_details: [\r\n        {\r\n          image: 'assets/guide/pen_error_01.JPG',\r\n          class: 'img-details-2'\r\n        },\r\n        {\r\n          image: 'assets/guide/pen_error_02.JPG',\r\n          class: 'img-details-2'\r\n        }\r\n      ]\r\n    },\r\n  ];\r\n  \r\n\r\n  constructor(private navCtrl: NavController, private modalController: ModalController) {}\r\n\r\n  ngAfterViewInit() {\r\n    if (this.swiper && this.swiper.nativeElement) {\r\n      const swiperInstance = this.swiper.nativeElement.swiper;\r\n    }\r\n  }\r\n\r\n  async openImageModal(imageSrc: string) {\r\n    const modal = await this.modalController.create({\r\n      component: ImageModalComponent,\r\n      cssClass: 'guide-modal',\r\n      componentProps: { imageSrc: imageSrc }\r\n    });\r\n    return await modal.present();\r\n  }\r\n\r\n  skip() {\r\n    this.navCtrl.navigateRoot('/scan-bl');  \r\n  }\r\n\r\n  next() {\r\n    if (this.swiper && this.swiper.nativeElement) {\r\n      const swiperInstance = this.swiper.nativeElement.swiper;\r\n      const currentIndex = swiperInstance.activeIndex;\r\n      const totalSlides = swiperInstance.slides.length - 1;\r\n\r\n      if (currentIndex === totalSlides) {\r\n        // Navigate to 'scan-bl' page if it's the last slide\r\n        this.skip()\r\n      } else {\r\n        // Otherwise, navigate to the next slide\r\n        swiperInstance.slideNext();\r\n      }\r\n    }\r\n  }\r\n}", "\r\n<div class=\"fix-wrapper\">\r\n<ion-header> </ion-header>\r\n\r\n<ion-content >\r\n  <div class=\"onboarding-wrapper\">\r\n    <swiper-container #swiper pagination=\"true\">\r\n      <swiper-slide *ngFor=\"let slide of slidesData\" class=\"test\">\r\n        <ion-row class=\"img-details-content\">\r\n          <ion-col size=\"12\" class=\"slide-content\">\r\n            <div class=\"content-slide\">\r\n              <h2>{{ slide.title }}</h2>\r\n            </div>\r\n            <div class=\"image-wrapper\" data-icon=\"✅\">\r\n              <img\r\n                [src]=\"slide.image\"\r\n                class=\"slide-image\"\r\n                (click)=\"openImageModal(slide.image)\"\r\n              />\r\n            </div>\r\n          </ion-col>\r\n     \r\n          <div class=\"content-slide\">\r\n            <p>{{ slide.description }}</p>\r\n          </div>\r\n          <ion-col class=\"img-details-col\" [size]=\"12\">\r\n            <div\r\n              *ngFor=\"let item of slide.images_details\"\r\n              class=\"image-wrapper\"\r\n              data-icon=\"❌\"\r\n            >\r\n              <img\r\n                [src]=\"item.image\"\r\n                class=\"{{item.class}}\"\r\n                (click)=\"openImageModal(item.image)\"\r\n              />\r\n            </div>\r\n          </ion-col>\r\n        </ion-row>\r\n      </swiper-slide>\r\n    </swiper-container>\r\n\r\n\r\n  </div>\r\n</ion-content>\r\n<ion-footer>\r\n  <div class=\"buttons_nav\">\r\n    <ion-button fill=\"solid\" class=\"next-button\" (click)=\"next()\">\r\n      <span>SUIVANT</span>\r\n      <ion-icon slot=\"end\" name=\"arrow-forward-outline\"></ion-icon>\r\n    </ion-button>\r\n    <!-- <ion-button fill=\"clear\" class=\"skip-button\" (click)=\"skip()\">PASSER</ion-button> -->\r\n  </div>\r\n</ion-footer>\r\n</div>"], "names": ["RouterModule", "GuidePage", "routes", "path", "component", "GuidePageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SharedModule", "GuidePageModule", "declarations", "ImageModalComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "GuidePage_swiper_slide_6_div_12_Template_img_click_1_listener", "item_r6", "ɵɵrestoreView", "_r5", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "openImageModal", "image", "ɵɵelementEnd", "ɵɵadvance", "ɵɵclassMap", "class", "ɵɵproperty", "ɵɵsanitizeUrl", "ɵɵtext", "GuidePage_swiper_slide_6_Template_img_click_7_listener", "slide_r3", "_r2", "ɵɵtemplate", "GuidePage_swiper_slide_6_div_12_Template", "ɵɵtextInterpolate", "title", "description", "images_details", "constructor", "navCtrl", "modalController", "slidesData", "ngAfterViewInit", "swiper", "nativeElement", "swiperInstance", "imageSrc", "_this", "_asyncToGenerator", "modal", "create", "cssClass", "componentProps", "present", "skip", "navigateRoot", "next", "currentIndex", "activeIndex", "totalSlides", "slides", "length", "slideNext", "ɵɵdirectiveInject", "NavController", "ModalController", "selectors", "viewQuery", "GuidePage_Query", "rf", "ctx", "ɵɵelement", "GuidePage_swiper_slide_6_Template", "GuidePage_Template_ion_button_click_9_listener", "_r1"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}