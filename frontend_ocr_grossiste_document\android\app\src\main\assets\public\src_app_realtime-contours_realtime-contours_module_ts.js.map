{"version": 3, "file": "src_app_realtime-contours_realtime-contours_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAES;;;AAEhE,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,yEAAoBA;CAChC,CACF;AAMK,MAAOI,iCAAiC;qCAAjCA,iCAAiC;;mBAAjCA,kCAAiC;AAAA;;QAAjCA;AAAiC;;YAHlCL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,iCAAiC;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFlCT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAE0C;AAEvB;AACT,CAAC;;AAYlD,MAAOc,0BAA0B;8BAA1BA,0BAA0B;;mBAA1BA,2BAA0B;AAAA;;QAA1BA;AAA0B;;YARnCJ,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,gGAAiC,EACjCQ,+DAAY;AAAA;;sHAIHC,0BAA0B;IAAAC,YAAA,GAFtBd,yEAAoB;IAAAM,OAAA,GANjCG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,gGAAiC,EACjCQ,+DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IEVdG,4DAAA,aAAsD;IACpDA,uDAAA,kBAA2B;IAC3BA,4DAAA,QAAG;IAAAA,oDAAA,wBAAiB;IACtBA,0DADsB,EAAI,EACpB;;;;;IAENA,4DAAA,aAAoD;IAElDA,uDADA,kBAA+E,mBACpB;IAC7DA,0DAAA,EAAM;;;;IAFUA,uDAAA,EAAe;IAACA,wDAAhB,UAAAO,MAAA,CAAAC,KAAA,CAAe,WAAAD,MAAA,CAAAE,MAAA,CAAkB;IAC/BT,uDAAA,GAAe;IAACA,wDAAhB,UAAAO,MAAA,CAAAC,KAAA,CAAe,WAAAD,MAAA,CAAAE,MAAA,CAAkB;;;ADJ/C,MAAOxB,oBAAoB;EAW/ByB,YACUC,QAAkB,EAClBC,aAA4B,EAC5BC,cAAiC;IAFjC,KAAAF,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IAVxB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAN,KAAK,GAAG,GAAG;IACX,KAAAC,MAAM,GAAG,GAAG;IACZ,KAAAM,aAAa,GAAG,KAAK;EAQlB;EAEGC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACZ,IAAI;QACFD,KAAI,CAACE,EAAE,SAASF,KAAI,CAACL,aAAa,CAACQ,SAAS,EAAE;QAC9CH,KAAI,CAACF,aAAa,GAAG,IAAI;QACzBE,KAAI,CAACJ,cAAc,CAACQ,aAAa,EAAE;QACnC,MAAMJ,KAAI,CAACK,WAAW,EAAE;OACzB,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;IACnD;EACH;EAEA;EACMD,WAAWA,CAAA;IAAA,IAAAG,MAAA;IAAA,OAAAP,6OAAA;MACf,IAAI,CAACO,MAAI,CAACV,aAAa,EAAE;QACvBS,OAAO,CAACE,IAAI,CAAC,yBAAyB,CAAC;QACvC;;MAGF,IAAI;QACF;QACA,MAAMC,WAAW,GAAG;UAClBC,KAAK,EAAE;YACLC,UAAU,EAAE,aAAa;YACzBrB,KAAK,EAAE;cAAEsB,KAAK,EAAEL,MAAI,CAACjB;YAAK,CAAE;YAC5BC,MAAM,EAAE;cAAEqB,KAAK,EAAEL,MAAI,CAAChB;YAAM;WAC7B;UACDsB,KAAK,EAAE;SACR;QAED,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACC,YAAY,CAACR,WAAW,CAAC;QAErE,IAAIF,MAAI,CAACG,KAAK,IAAIH,MAAI,CAACG,KAAK,CAACQ,aAAa,EAAE;UAC1C,MAAMC,YAAY,GAAGZ,MAAI,CAACG,KAAK,CAACQ,aAAa;UAC7CC,YAAY,CAACC,SAAS,GAAGN,MAAM;UAE/B;UACAK,YAAY,CAACE,gBAAgB,GAAG,MAAK;YACnC;YACA,MAAMC,KAAK,GAAGR,MAAM,CAACS,cAAc,EAAE,CAAC,CAAC,CAAC;YACxC,MAAMC,QAAQ,GAAGF,KAAK,CAACG,WAAW,EAAE;YACpClB,MAAI,CAACjB,KAAK,GAAGkC,QAAQ,CAAClC,KAAK,IAAIiB,MAAI,CAACjB,KAAK;YACzCiB,MAAI,CAAChB,MAAM,GAAGiC,QAAQ,CAACjC,MAAM,IAAIgB,MAAI,CAAChB,MAAM;YAE5C,IAAIgB,MAAI,CAACmB,MAAM,EAAE;cACfnB,MAAI,CAACmB,MAAM,CAACR,aAAa,CAAC5B,KAAK,GAAGiB,MAAI,CAACjB,KAAK;cAC5CiB,MAAI,CAACmB,MAAM,CAACR,aAAa,CAAC3B,MAAM,GAAGgB,MAAI,CAAChB,MAAM;;YAGhD4B,YAAY,CAACQ,IAAI,EAAE;YACnBpB,MAAI,CAACX,SAAS,GAAG,IAAI;YACrBW,MAAI,CAACqB,YAAY,EAAE;UACrB,CAAC;;OAEJ,CAAC,OAAOC,GAAG,EAAE;QACZvB,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEwB,GAAG,CAAC;;IAC9C;EACH;EAGAD,YAAYA,CAAA;IACV,IAAI;MACF,IAAI,CAAC,IAAI,CAAChC,SAAS,EAAE;MAErB,MAAMc,KAAK,GAAG,IAAI,CAACA,KAAM,CAACQ,aAAa;MACvC,MAAMQ,MAAM,GAAG,IAAI,CAACA,MAAO,CAACR,aAAa;MACzC,MAAMY,OAAO,GAAGJ,MAAM,CAACK,UAAU,CAAC,IAAI,CAAC;MAEvC;MACAD,OAAO,CAACE,SAAS,CAACtB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAACpB,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC;MAEvD;MACA,IAAI0C,GAAG,GAAG,IAAI,CAAChC,EAAE,CAACiC,MAAM,CAACR,MAAM,CAAC;MAChC,IAAIS,GAAG,GAAGF,GAAG,CAACG,KAAK,EAAE;MAErB;MACA,IAAIC,OAAO,GAAG,IAAI,IAAI,CAACpC,EAAE,CAACqC,GAAG,EAAE;MAC/B,IAAI,CAACrC,EAAE,CAACsC,UAAU,CAACN,GAAG,EAAEI,OAAO,EAAE,CAAC,CAAC;MAEnC,IAAIG,OAAO,GAAU,EAAE;MAEvB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,IAAIC,KAAK,GAAG,IAAI,IAAI,CAACzC,EAAE,CAACqC,GAAG,EAAE;QAC7B,IAAIK,IAAI,GAAG,IAAI,IAAI,CAAC1C,EAAE,CAACqC,GAAG,EAAE;QAE5B;QACA,IAAIM,EAAE,GAAG,CAACH,CAAC,EAAE,CAAC,CAAC;QACf,IAAI,CAACxC,EAAE,CAAC4C,cAAc,CAACR,OAAO,EAAEK,KAAK,EAAED,CAAC,CAAC;QAEzC;QACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC1B,IAAIA,CAAC,KAAK,CAAC,EAAE;YACX;YACA,IAAI,CAAC7C,EAAE,CAAC8C,KAAK,CAACL,KAAK,EAAEC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACrC;YACA,IAAIK,MAAM,GAAG,IAAI,CAAC/C,EAAE,CAACgD,qBAAqB,CAAC,IAAI,CAAChD,EAAE,CAACiD,UAAU,EAAE,IAAI,IAAI,CAACjD,EAAE,CAACkD,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtF,IAAI,CAAClD,EAAE,CAACmD,MAAM,CAACT,IAAI,EAAEA,IAAI,EAAEK,MAAM,EAAE,IAAI,IAAI,CAAC/C,EAAE,CAACoD,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7DL,MAAM,CAACM,MAAM,EAAE;WAChB,MAAM;YACL;YACA,IAAI,CAACrD,EAAE,CAACsD,SAAS,CAACb,KAAK,EAAEC,IAAI,EAAE,CAACG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC7C,EAAE,CAACuD,aAAa,CAAC;;UAG/E;UACA,IAAIC,QAAQ,GAAG,IAAI,IAAI,CAACxD,EAAE,CAACyD,SAAS,EAAE;UACtC,IAAIC,SAAS,GAAG,IAAI,IAAI,CAAC1D,EAAE,CAACqC,GAAG,EAAE;UACjC,IAAI,CAACrC,EAAE,CAAC2D,YAAY,CAACjB,IAAI,EAAEc,QAAQ,EAAEE,SAAS,EAAE,IAAI,CAAC1D,EAAE,CAAC4D,SAAS,EAAE,IAAI,CAAC5D,EAAE,CAAC6D,mBAAmB,CAAC;UAE/F;UACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACO,IAAI,EAAE,EAAED,CAAC,EAAE,EAAE;YACxC,IAAIE,OAAO,GAAGR,QAAQ,CAACS,GAAG,CAACH,CAAC,CAAC;YAC7B,IAAII,MAAM,GAAG,IAAI,IAAI,CAAClE,EAAE,CAACqC,GAAG,EAAE;YAC9B,IAAI8B,SAAS,GAAG,IAAI,CAACnE,EAAE,CAACoE,SAAS,CAACJ,OAAO,EAAE,IAAI,CAAC;YAEhD;YACA,IAAI,CAAChE,EAAE,CAACqE,YAAY,CAACL,OAAO,EAAEE,MAAM,EAAE,IAAI,GAAGC,SAAS,EAAE,IAAI,CAAC;YAE7D;YACA;YACA;YACA,IAAIG,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACxE,EAAE,CAACyE,WAAW,CAACP,MAAM,CAAC,CAAC;YAEhD,IAAIA,MAAM,CAACQ,IAAI,KAAK,CAAC,IACjBJ,IAAI,GAAG,IAAI,IACXA,IAAI,GAAI,IAAI,CAACjF,KAAK,GAAG,IAAI,CAACC,MAAM,GAAG,GAAI;YAAI;YAC3C,IAAI,CAACU,EAAE,CAAC2E,eAAe,CAACT,MAAM,CAAC,EAAE;cAEnC;cACA,IAAIU,SAAS,GAAG,CAAC;cACjB,IAAIC,MAAM,GAAG,EAAE;cAEf;cACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;gBAC1BD,MAAM,CAACE,IAAI,CAAC;kBACVC,CAAC,EAAEd,MAAM,CAACe,OAAO,CAACH,CAAC,GAAG,CAAC,CAAC;kBACxBI,CAAC,EAAEhB,MAAM,CAACe,OAAO,CAACH,CAAC,GAAG,CAAC,GAAG,CAAC;iBAC5B,CAAC;;cAGJ;cACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;gBAC1B,IAAIK,MAAM,GAAGZ,IAAI,CAACC,GAAG,CAAC,IAAI,CAACY,KAAK,CAC9BP,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,EACbD,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,EACbD,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,CACd,CAAC;gBACFF,SAAS,GAAGL,IAAI,CAACc,GAAG,CAACT,SAAS,EAAEO,MAAM,CAAC;;cAGzC;cACA,IAAIP,SAAS,GAAG,GAAG,EAAE;gBACnB;gBACA,IAAI,CAAC5E,EAAE,CAACsF,YAAY,CAClBpD,GAAG,EACH,IAAI,IAAI,CAAClC,EAAE,CAACyD,SAAS,CAAC,CAACS,MAAM,CAAC,CAAC,EAC/B,CAAC,CAAC,EACF,IAAI,IAAI,CAAClE,EAAE,CAACuF,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,EAClC,CAAC,CACF;gBAED;gBACAV,MAAM,CAACW,OAAO,CAACC,KAAK,IAAG;kBACrB,IAAI,CAACzF,EAAE,CAAC0F,MAAM,CACZxD,GAAG,EACH,IAAI,IAAI,CAAClC,EAAE,CAACoD,KAAK,CAACqC,KAAK,CAACT,CAAC,EAAES,KAAK,CAACP,CAAC,CAAC,EACnC,EAAE,EACF,IAAI,IAAI,CAAClF,EAAE,CAACuF,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAClC,CAAC,CAAC,CACH;gBACH,CAAC,CAAC;;;YAGNrB,MAAM,CAACb,MAAM,EAAE;;UAGjBG,QAAQ,CAACH,MAAM,EAAE;UACjBK,SAAS,CAACL,MAAM,EAAE;;QAGpBZ,KAAK,CAACY,MAAM,EAAE;QACdX,IAAI,CAACW,MAAM,EAAE;;MAGf;MACA,IAAI,CAACrD,EAAE,CAAC2F,MAAM,CAAClE,MAAM,EAAES,GAAG,CAAC;MAE3B;MACAF,GAAG,CAACqB,MAAM,EAAE;MACZnB,GAAG,CAACmB,MAAM,EAAE;MACZjB,OAAO,CAACiB,MAAM,EAAE;MAEhB;MACAuC,qBAAqB,CAAC,MAAM,IAAI,CAACjE,YAAY,EAAE,CAAC;KAEjD,CAAC,OAAOC,GAAG,EAAE;MACZvB,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEwB,GAAG,CAAC;;EAElD;EAEA;EACAwD,KAAKA,CAACS,GAA6B,EAC7BC,GAA6B,EAC7BC,GAA6B;IAC/B,MAAMC,GAAG,GAAGH,GAAG,CAACb,CAAC,GAAGe,GAAG,CAACf,CAAC;IACzB,MAAMiB,GAAG,GAAGJ,GAAG,CAACX,CAAC,GAAGa,GAAG,CAACb,CAAC;IACzB,MAAMgB,GAAG,GAAGJ,GAAG,CAACd,CAAC,GAAGe,GAAG,CAACf,CAAC;IACzB,MAAMmB,GAAG,GAAGL,GAAG,CAACZ,CAAC,GAAGa,GAAG,CAACb,CAAC;IAEzB,OAAO,CAACc,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGE,GAAG,IACtB5B,IAAI,CAAC6B,IAAI,CAAC,CAACJ,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,KAAKC,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,CAAC,GAAG,KAAK,CAAC;EAC/E;EAGE;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEAE,eAAeA,CAACC,QAAe;IAC7B;IACA,MAAMjH,KAAK,GAAG,IAAI,CAACkH,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjD,MAAMhH,MAAM,GAAG,IAAI,CAACiH,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClD,MAAME,KAAK,GAAGnH,KAAK,GAAGC,MAAM,GAAGD,KAAK,GAACC,MAAM,GAAGA,MAAM,GAACD,KAAK;IAC1D,OAAOmH,KAAK,GAAG,GAAG,IAAIA,KAAK,GAAG,GAAG;EACnC;EAEA;EACAC,UAAUA,CAACvC,MAA2B;IACpC,MAAMwC,OAAO,GAAG,EAAE;IAClB,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B4C,OAAO,CAAC3B,IAAI,CAAC;QACXC,CAAC,EAAEd,MAAM,CAACe,OAAO,CAACnB,CAAC,GAAG,CAAC,CAAC;QACxBoB,CAAC,EAAEhB,MAAM,CAACe,OAAO,CAACnB,CAAC,GAAG,CAAC,GAAG,CAAC;OAC5B,CAAC;;IAEJ,OAAO4C,OAAO;EAChB;EAEAH,IAAIA,CAACI,EAA6B,EAAEC,EAA6B;IAC/D,OAAOrC,IAAI,CAAC6B,IAAI,CAAC,CAACQ,EAAE,CAAC5B,CAAC,GAAG2B,EAAE,CAAC3B,CAAC,KAAG,CAAC,GAAG,CAAC4B,EAAE,CAAC1B,CAAC,GAAGyB,EAAE,CAACzB,CAAC,KAAG,CAAC,CAAC;EACvD;EAEA2B,WAAWA,CAAA;IAAA,IAAAC,WAAA;IACT,IAAI,CAACnH,SAAS,GAAG,KAAK;IACtB,KAAAmH,WAAA,GAAI,IAAI,CAACrG,KAAK,cAAAqG,WAAA,eAAVA,WAAA,CAAY7F,aAAa,CAACE,SAAS,EAAE;MACvC,IAAI,CAACV,KAAK,CAACQ,aAAa,CAACE,SAAS,CAAC4F,SAAS,EAAE,CAACvB,OAAO,CAAEnE,KAAU,IAAKA,KAAK,CAAC2F,IAAI,EAAE,CAAC;;IAEtF,IAAI,IAAI,CAACC,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACC,WAAW,EAAE;;EAEzC;;wBAvVWpJ,oBAAoB;;mBAApBA,qBAAoB,EAAAe,+DAAA,CAAAR,oDAAA,GAAAQ,+DAAA,CAAAwI,oEAAA,GAAAxI,+DAAA,CAAAA,4DAAA;AAAA;;QAApBf,qBAAoB;EAAA0J,SAAA;EAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;;;MCR7B9I,4DAFJ,iBAAY,kBACG,gBACA;MAAAA,oDAAA,uBAAgB;MAE/BA,0DAF+B,EAAY,EAC3B,EACH;MAEbA,4DAAA,kBAAa;MAMXA,wDALA,IAAAiJ,mCAAA,iBAAsD,IAAAC,mCAAA,iBAKF;MAItDlJ,0DAAA,EAAc;;;MATNA,uDAAA,GAAoB;MAApBA,wDAAA,UAAA+I,GAAA,CAAAhI,aAAA,CAAoB;MAKpBf,uDAAA,EAAmB;MAAnBA,wDAAA,SAAA+I,GAAA,CAAAhI,aAAA,CAAmB;;;;;;;;;;;;;;;;;;;;;;ACVwB;;AAO7C,MAAO0H,aAAa;EAIxB/H,YAAA;IAHQ,KAAA0I,mBAAmB,GAAG,IAAID,iDAAe,CAAU,KAAK,CAAC;IACjE,KAAAE,aAAa,GAAG,IAAI,CAACD,mBAAmB,CAACE,YAAY,EAAE;IAGrD,IAAI,CAACC,UAAU,EAAE;EACnB;EAEQA,UAAUA,CAAA;IAChB;IACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAKA,MAAc,CAACrI,EAAE,EAAE;MACvDK,OAAO,CAACiI,GAAG,CAAC,0BAA0B,CAAC;MACvC,IAAI,CAACL,mBAAmB,CAACM,IAAI,CAAC,IAAI,CAAC;MACnC;;IAGF;IACA,MAAMC,UAAU,GAAG,IAAIC,OAAO,CAAQC,OAAO,IAAI;MAC9CL,MAAc,CAACM,aAAa,GAAG,MAAK;QACnCtI,OAAO,CAACiI,GAAG,CAAC,iBAAiB,CAAC;QAC9BI,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC;IAGF;IACAF,UAAU,CAACI,IAAI,CAAC,MAAK;MACnB,IAAI,CAACX,mBAAmB,CAACM,IAAI,CAAC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEOtI,SAASA,CAAA;IACd,OAAO,IAAIwI,OAAO,CAAEC,OAAO,IAAI;MAC7B,IAAI,IAAI,CAACT,mBAAmB,CAACY,KAAK,EAAE;QAClCH,OAAO,CAAEL,MAAc,CAACrI,EAAE,CAAC;OAC5B,MAAM;QACL,IAAI,CAACkI,aAAa,CAACY,SAAS,CAAEC,MAAM,IAAI;UACtC,IAAIA,MAAM,EAAE;YACVL,OAAO,CAAEL,MAAc,CAACrI,EAAE,CAAC;;QAE/B,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;;iBA3CWsH,aAAa;;mBAAbA,cAAa;AAAA;;SAAbA,cAAa;EAAA0B,OAAA,EAAb1B,cAAa,CAAA2B,IAAA;EAAAC,UAAA,EAFZ;AAAM", "sources": ["./src/app/realtime-contours/realtime-contours-routing.module.ts", "./src/app/realtime-contours/realtime-contours.module.ts", "./src/app/realtime-contours/realtime-contours.page.ts", "./src/app/realtime-contours/realtime-contours.page.html", "./src/app/services/open-cv.service.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { RealtimeContoursPage } from './realtime-contours.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: RealtimeContoursPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class RealtimeContoursPageRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { RealtimeContoursPageRoutingModule } from './realtime-contours-routing.module';\r\n\r\nimport { RealtimeContoursPage } from './realtime-contours.page';\r\nimport { SharedModule } from '../shared/shared.module'; // Import SharedModule\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    RealtimeContoursPageRoutingModule,\r\n    SharedModule\r\n  ],\r\n  declarations: [RealtimeContoursPage]\r\n})\r\nexport class RealtimeContoursPageModule {}\r\n", "import { Component, OnInit, ElementRef, <PERSON>Child, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';\r\nimport { Platform } from '@ionic/angular';\r\nimport { OpenCVService } from '../services/open-cv.service';\r\nimport { Subscription } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-realtime-contours',\r\n  templateUrl: './realtime-contours.page.html',\r\n  styleUrls: ['./realtime-contours.page.scss'],\r\n})\r\nexport class RealtimeContoursPage implements OnInit, OnDestroy {\r\n  @ViewChild('video') video: ElementRef | undefined;\r\n  @ViewChild('canvas') canvas: ElementRef | undefined;\r\n\r\n  streaming = false;\r\n  width = 640;\r\n  height = 480;\r\n  isOpenCVReady = false;\r\n  private cv: any;\r\n  private openCVSubscription: Subscription | undefined;\r\n\r\n  constructor(\r\n    private platform: Platform,\r\n    private openCVService: OpenCVService,\r\n    private changeDetector: ChangeDetectorRef\r\n  ) {}\r\n\r\n  async ngOnInit() {\r\n    try {\r\n      this.cv = await this.openCVService.getOpenCV();\r\n      this.isOpenCVReady = true;\r\n      this.changeDetector.detectChanges();\r\n      await this.startCamera();\r\n    } catch (error) {\r\n      console.error('Error initializing OpenCV:', error);\r\n    }\r\n  }\r\n\r\n  // Update your template to fix the accessibility warning:\r\n  async startCamera() {\r\n    if (!this.isOpenCVReady) {\r\n      console.warn('OpenCV is not ready yet');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Get actual device dimensions\r\n      const constraints = {\r\n        video: {\r\n          facingMode: 'environment',\r\n          width: { ideal: this.width },\r\n          height: { ideal: this.height }\r\n        },\r\n        audio: false\r\n      };\r\n\r\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\r\n      \r\n      if (this.video && this.video.nativeElement) {\r\n        const videoElement = this.video.nativeElement;\r\n        videoElement.srcObject = stream;\r\n        \r\n        // Wait for video metadata to load\r\n        videoElement.onloadedmetadata = () => {\r\n          // Update canvas dimensions to match video\r\n          const track = stream.getVideoTracks()[0];\r\n          const settings = track.getSettings();\r\n          this.width = settings.width || this.width;\r\n          this.height = settings.height || this.height;\r\n          \r\n          if (this.canvas) {\r\n            this.canvas.nativeElement.width = this.width;\r\n            this.canvas.nativeElement.height = this.height;\r\n          }\r\n          \r\n          videoElement.play();\r\n          this.streaming = true;\r\n          this.processVideo();\r\n        };\r\n      }\r\n    } catch (err) {\r\n      console.error('Error accessing camera:', err);\r\n    }\r\n  }\r\n\r\n  \r\n  processVideo() {\r\n    try {\r\n      if (!this.streaming) return;\r\n\r\n      const video = this.video!.nativeElement;\r\n      const canvas = this.canvas!.nativeElement;\r\n      const context = canvas.getContext('2d');\r\n      \r\n      // Draw video frame to canvas\r\n      context.drawImage(video, 0, 0, this.width, this.height);\r\n      \r\n      // Get image data from canvas\r\n      let src = this.cv.imread(canvas);\r\n      let dst = src.clone();\r\n      \r\n      // Blur the image to enhance edge detection\r\n      let blurred = new this.cv.Mat();\r\n      this.cv.medianBlur(src, blurred, 9);\r\n      \r\n      let squares: any[] = [];\r\n      \r\n      // Find squares in every color plane of the image\r\n      for (let c = 0; c < 3; c++) {\r\n        let gray0 = new this.cv.Mat();\r\n        let gray = new this.cv.Mat();\r\n        \r\n        // Extract the c-th color plane\r\n        let ch = [c, 0];\r\n        this.cv.extractChannel(blurred, gray0, c);\r\n        \r\n        // Try several threshold levels\r\n        for (let l = 0; l < 2; l++) {\r\n          if (l === 0) {\r\n            // Use Canny\r\n            this.cv.Canny(gray0, gray, 10, 20, 3);\r\n            // Dilate to remove potential holes between edge segments\r\n            let kernel = this.cv.getStructuringElement(this.cv.MORPH_RECT, new this.cv.Size(3, 3));\r\n            this.cv.dilate(gray, gray, kernel, new this.cv.Point(-1, -1));\r\n            kernel.delete();\r\n          } else {\r\n            // Use simple thresholding\r\n            this.cv.threshold(gray0, gray, (l + 1) * 255 / 2, 255, this.cv.THRESH_BINARY);\r\n          }\r\n          \r\n          // Find contours\r\n          let contours = new this.cv.MatVector();\r\n          let hierarchy = new this.cv.Mat();\r\n          this.cv.findContours(gray, contours, hierarchy, this.cv.RETR_LIST, this.cv.CHAIN_APPROX_SIMPLE);\r\n          \r\n          // Test each contour\r\n          for (let i = 0; i < contours.size(); i++) {\r\n            let contour = contours.get(i);\r\n            let approx = new this.cv.Mat();\r\n            let perimeter = this.cv.arcLength(contour, true);\r\n            \r\n            // Approximate contour with accuracy proportional to the contour perimeter\r\n            this.cv.approxPolyDP(contour, approx, 0.02 * perimeter, true);\r\n            \r\n            // Square contours should have 4 vertices after approximation\r\n            // Relatively large area (to filter out noisy contours)\r\n            // And be convex\r\n            let area = Math.abs(this.cv.contourArea(approx));\r\n            \r\n            if (approx.rows === 4 && \r\n                area > 1000 && \r\n                area < (this.width * this.height * 0.9) && // Add maximum area constraint\r\n                this.cv.isContourConvex(approx)) {\r\n              \r\n              // Check if angles are approximately 90 degrees\r\n              let maxCosine = 0;\r\n              let points = [];\r\n              \r\n              // Get points from approx\r\n              for (let j = 0; j < 4; j++) {\r\n                points.push({\r\n                  x: approx.data32S[j * 2],\r\n                  y: approx.data32S[j * 2 + 1]\r\n                });\r\n              }\r\n              \r\n              // Check angles\r\n              for (let j = 2; j < 5; j++) {\r\n                let cosine = Math.abs(this.angle(\r\n                  points[j % 4],\r\n                  points[j - 2],\r\n                  points[j - 1]\r\n                ));\r\n                maxCosine = Math.max(maxCosine, cosine);\r\n              }\r\n              \r\n              // If all angles are approximately 90 degrees (cos < 0.3)\r\n              if (maxCosine < 0.3) {\r\n                // Draw the square\r\n                this.cv.drawContours(\r\n                  dst,\r\n                  new this.cv.MatVector([approx]),\r\n                  -1,\r\n                  new this.cv.Scalar(0, 255, 0, 255),\r\n                  3\r\n                );\r\n                \r\n                // Draw corners\r\n                points.forEach(point => {\r\n                  this.cv.circle(\r\n                    dst,\r\n                    new this.cv.Point(point.x, point.y),\r\n                    10,\r\n                    new this.cv.Scalar(255, 0, 0, 255),\r\n                    -1\r\n                  );\r\n                });\r\n              }\r\n            }\r\n            approx.delete();\r\n          }\r\n          \r\n          contours.delete();\r\n          hierarchy.delete();\r\n        }\r\n        \r\n        gray0.delete();\r\n        gray.delete();\r\n      }\r\n      \r\n      // Show result\r\n      this.cv.imshow(canvas, dst);\r\n      \r\n      // Cleanup\r\n      src.delete();\r\n      dst.delete();\r\n      blurred.delete();\r\n      \r\n      // Continue processing\r\n      requestAnimationFrame(() => this.processVideo());\r\n      \r\n    } catch (err) {\r\n      console.error('Error in processVideo:', err);\r\n    }\r\n}\r\n\r\n// Helper function to calculate angle\r\nangle(pt1: { x: number, y: number }, \r\n      pt2: { x: number, y: number }, \r\n      pt0: { x: number, y: number }) {\r\n    const dx1 = pt1.x - pt0.x;\r\n    const dy1 = pt1.y - pt0.y;\r\n    const dx2 = pt2.x - pt0.x;\r\n    const dy2 = pt2.y - pt0.y;\r\n    \r\n    return (dx1 * dx2 + dy1 * dy2) / \r\n           Math.sqrt((dx1 * dx1 + dy1 * dy1) * (dx2 * dx2 + dy2 * dy2) + 1e-10);\r\n}\r\n\r\n\r\n  // processVideo() {\r\n  //   try {\r\n  //     const video = this.video!.nativeElement;\r\n  //     const canvas = this.canvas!.nativeElement;\r\n  //     const context = canvas.getContext('2d');\r\n      \r\n  //     if (this.streaming) {\r\n  //       // Draw video frame to canvas\r\n  //       context.drawImage(video, 0, 0, this.width, this.height);\r\n        \r\n  //       // Get image data from canvas\r\n  //       let imageData = context.getImageData(0, 0, this.width, this.height);\r\n  //       let src = this.cv.matFromImageData(imageData);\r\n  //       let dst = new this.cv.Mat();\r\n  //       let gray = new this.cv.Mat();\r\n  //       let edges = new this.cv.Mat();\r\n        \r\n  //       // Convert to grayscale\r\n  //       this.cv.cvtColor(src, dst, this.cv.COLOR_RGBA2GRAY);\r\n        \r\n  //       // Apply Gaussian blur\r\n  //       this.cv.GaussianBlur(dst, dst, new this.cv.Size(5, 5), 0);\r\n        \r\n  //       // Apply Canny edge detection\r\n  //       this.cv.Canny(dst, dst, 75, 200);\r\n        \r\n  //       // Find contours\r\n  //       let contours = new this.cv.MatVector();\r\n  //       let hierarchy = new this.cv.Mat();\r\n  //       this.cv.findContours(dst, contours, hierarchy, this.cv.RETR_EXTERNAL, this.cv.CHAIN_APPROX_SIMPLE);\r\n        \r\n  //       // Convert back to RGB for drawing\r\n  //       this.cv.cvtColor(dst, dst, this.cv.COLOR_GRAY2RGBA);\r\n        \r\n  //       // Copy original image\r\n  //       src.copyTo(dst);\r\n\r\n  //       // Find the largest contour that could be a document\r\n  //       let maxArea = 0;\r\n  //       let maxContourIndex = -1;\r\n  //       let documentContour = null;\r\n        \r\n  //       for (let i = 0; i < contours.size(); i++) {\r\n  //         const contour = contours.get(i);\r\n  //         const area = this.cv.contourArea(contour);\r\n  //         const perimeter = this.cv.arcLength(contour, true);\r\n  //         let approx = new this.cv.Mat();\r\n  //         this.cv.approxPolyDP(contour, approx, 0.02 * perimeter, true);\r\n          \r\n  //         if (area > maxArea && approx.rows === 4) {\r\n  //           maxArea = area;\r\n  //           documentContour = approx;\r\n  //           maxContourIndex = i;\r\n  //         }\r\n  //         approx.delete();\r\n  //       }\r\n        \r\n  //       // Draw the largest contour if found\r\n  //       if (maxContourIndex !== -1) {\r\n  //         this.cv.drawContours(dst, contours, maxContourIndex, new this.cv.Scalar(0, 255, 0, 255), 2);\r\n  //       }\r\n        \r\n  //       // Show result on canvas\r\n  //       this.cv.imshow(canvas, dst);\r\n        \r\n  //       // Clean up\r\n  //       src.delete();\r\n  //       dst.delete();\r\n  //       gray.delete();\r\n  //       edges.delete();\r\n  //       contours.delete();\r\n  //       hierarchy.delete()\r\n  //     }\r\n      \r\n  //     // Process next frame\r\n  //     requestAnimationFrame(() => this.processVideo());\r\n  //   } catch (err) {\r\n  //     console.error('Error processing video:', err);\r\n  //   }\r\n  // }\r\n  \r\n  isValidDocument(vertices: any[]) {\r\n    // Check aspect ratio (approximate A4 ratio 1:√2)\r\n    const width = this.dist(vertices[0], vertices[1]);\r\n    const height = this.dist(vertices[1], vertices[2]);\r\n    const ratio = width > height ? width/height : height/width;\r\n    return ratio > 1.3 && ratio < 1.5;\r\n  }\r\n  \r\n  // Helper functions\r\n  getCorners(approx: { data32S: any[]; }) {\r\n    const corners = [];\r\n    for (let i = 0; i < 4; i++) {\r\n      corners.push({\r\n        x: approx.data32S[i * 2],\r\n        y: approx.data32S[i * 2 + 1]\r\n      });\r\n    }\r\n    return corners;\r\n  }\r\n  \r\n  dist(p1: { x: number; y: number; }, p2: { x: number; y: number; }) {\r\n    return Math.sqrt((p2.x - p1.x)**2 + (p2.y - p1.y)**2);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.streaming = false;\r\n    if (this.video?.nativeElement.srcObject) {\r\n      this.video.nativeElement.srcObject.getTracks().forEach((track: any) => track.stop());\r\n    }\r\n    if (this.openCVSubscription) {\r\n      this.openCVSubscription.unsubscribe();\r\n    }\r\n  }\r\n}", "<ion-header>\r\n  <ion-toolbar>\r\n    <ion-title>Document Scanner</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content>\r\n  <div *ngIf=\"!isOpenCVReady\" class=\"loading-container\">\r\n    <ion-spinner></ion-spinner>\r\n    <p>Loading OpenCV...</p>\r\n  </div>\r\n  \r\n  <div *ngIf=\"isOpenCVReady\" class=\"camera-container\">\r\n    <video #video [width]=\"width\" [height]=\"height\" style=\"display: none;\"></video>\r\n    <canvas #canvas [width]=\"width\" [height]=\"height\"></canvas>\r\n  </div>\r\n</ion-content>", "// services/opencv.service.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\n\r\ndeclare var cv: any;\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class OpenCVService {\r\n  private openCVLoadedSubject = new BehaviorSubject<boolean>(false);\r\n  openCVLoaded$ = this.openCVLoadedSubject.asObservable();\r\n\r\n  constructor() {\r\n    this.initOpenCV();\r\n  }\r\n\r\n  private initOpenCV() {\r\n    // Check if OpenCV is already loaded\r\n    if (typeof window !== 'undefined' && (window as any).cv) {\r\n      console.log('OpenCV already available');\r\n      this.openCVLoadedSubject.next(true);\r\n      return;\r\n    }\r\n\r\n    // If not loaded, create a promise to wait for it\r\n    const loadOpenCV = new Promise<void>((resolve) => {\r\n      (window as any).onOpenCVReady = () => {\r\n        console.log('OpenCV is ready');\r\n        resolve();\r\n      };\r\n    });\r\n    \r\n\r\n    // Wait for OpenCV to be ready\r\n    loadOpenCV.then(() => {\r\n      this.openCVLoadedSubject.next(true);\r\n    });\r\n  }\r\n\r\n  public getOpenCV(): Promise<any> {\r\n    return new Promise((resolve) => {\r\n      if (this.openCVLoadedSubject.value) {\r\n        resolve((window as any).cv);\r\n      } else {\r\n        this.openCVLoaded$.subscribe((loaded) => {\r\n          if (loaded) {\r\n            resolve((window as any).cv);\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n}\r\n"], "names": ["RouterModule", "RealtimeContoursPage", "routes", "path", "component", "RealtimeContoursPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SharedModule", "RealtimeContoursPageModule", "declarations", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "width", "height", "constructor", "platform", "openCVService", "changeDetector", "streaming", "isOpenCVReady", "ngOnInit", "_this", "_asyncToGenerator", "cv", "getOpenCV", "detectChanges", "startCamera", "error", "console", "_this2", "warn", "constraints", "video", "facingMode", "ideal", "audio", "stream", "navigator", "mediaDevices", "getUserMedia", "nativeElement", "videoElement", "srcObject", "onloadedmetadata", "track", "getVideoTracks", "settings", "getSettings", "canvas", "play", "processVideo", "err", "context", "getContext", "drawImage", "src", "imread", "dst", "clone", "blurred", "Mat", "medianBlur", "squares", "c", "gray0", "gray", "ch", "extractChannel", "l", "<PERSON><PERSON>", "kernel", "getStructuringElement", "MORPH_RECT", "Size", "dilate", "Point", "delete", "threshold", "THRESH_BINARY", "contours", "MatVector", "hierarchy", "findContours", "RETR_LIST", "CHAIN_APPROX_SIMPLE", "i", "size", "contour", "get", "approx", "perimeter", "<PERSON><PERSON><PERSON><PERSON>", "approxPolyDP", "area", "Math", "abs", "contourArea", "rows", "isContourConvex", "maxCosine", "points", "j", "push", "x", "data32S", "y", "cosine", "angle", "max", "drawContours", "<PERSON><PERSON><PERSON>", "for<PERSON>ach", "point", "circle", "imshow", "requestAnimationFrame", "pt1", "pt2", "pt0", "dx1", "dy1", "dx2", "dy2", "sqrt", "isValidDocument", "vertices", "dist", "ratio", "getCorners", "corners", "p1", "p2", "ngOnDestroy", "_this$video", "getTracks", "stop", "openCVSubscription", "unsubscribe", "ɵɵdirectiveInject", "Platform", "i2", "OpenCVService", "ChangeDetectorRef", "selectors", "viewQuery", "RealtimeContoursPage_Query", "rf", "ctx", "ɵɵtemplate", "RealtimeContoursPage_div_5_Template", "RealtimeContoursPage_div_6_Template", "BehaviorSubject", "openCVLoadedSubject", "openCVLoaded$", "asObservable", "initOpenCV", "window", "log", "next", "loadOpenCV", "Promise", "resolve", "onOpenCVReady", "then", "value", "subscribe", "loaded", "factory", "ɵfac", "providedIn"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}