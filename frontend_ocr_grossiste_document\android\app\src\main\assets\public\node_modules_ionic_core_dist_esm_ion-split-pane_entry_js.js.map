{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-split-pane_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAChD;AAE7D,MAAMW,eAAe,GAAG,ooBAAooB;AAC5pB,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,cAAc,GAAG,6oBAA6oB;AACpqB,MAAMC,oBAAoB,GAAGD,cAAc;;AAE3C;AACA,MAAME,eAAe,GAAG,iBAAiB;AACzC,MAAMC,eAAe,GAAG,iBAAiB;AACzC,MAAMC,KAAK,GAAG;EACVC,EAAE,EAAE,kBAAkB;EACtBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,qBAAqB;EACzBC,KAAK,EAAE;AACX,CAAC;AACD,MAAMC,SAAS,GAAG,MAAM;EACpBC,WAAWA,CAACC,OAAO,EAAE;IACjBzB,qDAAgB,CAAC,IAAI,EAAEyB,OAAO,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAGxB,qDAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IACtE,IAAI,CAACyB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGf,KAAK,CAAC,IAAI,CAAC;EAC3B;EACAgB,cAAcA,CAACL,OAAO,EAAE;IACpB,IAAI,CAACD,mBAAmB,CAACO,IAAI,CAAC;MAAEN;IAAQ,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;EACUO,SAASA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACd,OAAOC,OAAO,CAACC,OAAO,CAACH,KAAI,CAACR,OAAO,CAAC;IAAC;EACzC;EACMY,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAJ,6OAAA;MACtB;MACA;MACA,IAAI,OAAOK,cAAc,KAAK,WAAW,IAAIA,cAAc,IAAI,IAAI,EAAE;QACjE,MAAMA,cAAc,CAACC,WAAW,CAAC,gBAAgB,CAAC;MACtD;MACAF,MAAI,CAACG,gBAAgB,CAAC,CAAC;MACvBH,MAAI,CAACI,WAAW,CAAC,CAAC;IAAC;EACvB;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACC,GAAG,EAAE;MACV,IAAI,CAACA,GAAG,CAAC,CAAC;MACV,IAAI,CAACA,GAAG,GAAGjB,SAAS;IACxB;EACJ;EACAe,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACE,GAAG,EAAE;MACV,IAAI,CAACA,GAAG,CAAC,CAAC;MACV,IAAI,CAACA,GAAG,GAAGjB,SAAS;IACxB;IACA;IACA,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACH,OAAO,GAAG,KAAK;MACpB;IACJ;IACA;IACA,MAAMoB,KAAK,GAAG,IAAI,CAAChB,IAAI;IACvB,IAAI,OAAOgB,KAAK,KAAK,SAAS,EAAE;MAC5B,IAAI,CAACpB,OAAO,GAAGoB,KAAK;MACpB;IACJ;IACA;IACA,MAAMC,UAAU,GAAGhC,KAAK,CAAC+B,KAAK,CAAC,IAAIA,KAAK;IACxC;IACA,IAAIC,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACzB,IAAI,CAACtB,OAAO,GAAG,KAAK;MACpB;IACJ;IACA;IACA,MAAMuB,QAAQ,GAAIC,CAAC,IAAK;MACpB,IAAI,CAACxB,OAAO,GAAGwB,CAAC,CAACC,OAAO;IAC5B,CAAC;IACD,MAAMC,SAAS,GAAGC,MAAM,CAACC,UAAU,CAACP,UAAU,CAAC;IAC/C;IACAK,SAAS,CAACG,WAAW,CAACN,QAAQ,CAAC;IAC/B,IAAI,CAACJ,GAAG,GAAG,MAAMO,SAAS,CAACI,cAAc,CAACP,QAAQ,CAAC;IACnD,IAAI,CAACvB,OAAO,GAAG0B,SAAS,CAACD,OAAO;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIT,gBAAgBA,CAAA,EAAG;IACf,MAAMf,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAM8B,QAAQ,GAAG,IAAI,CAACC,EAAE,CAACD,QAAQ;IACjC,MAAME,EAAE,GAAG,IAAI,CAACD,EAAE,CAACE,iBAAiB;IACpC,IAAIC,SAAS,GAAG,KAAK;IACrB,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsD,EAAE,EAAEtD,CAAC,EAAE,EAAE;MACzB,MAAMyD,KAAK,GAAGL,QAAQ,CAACpD,CAAC,CAAC;MACzB,MAAM0D,MAAM,GAAGpC,SAAS,KAAKC,SAAS,IAAIkC,KAAK,CAACE,EAAE,KAAKrC,SAAS;MAChE,IAAIoC,MAAM,EAAE;QACR,IAAIF,SAAS,EAAE;UACXI,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;UAC9D;QACJ,CAAC,MACI;UACDC,YAAY,CAACL,KAAK,EAAEC,MAAM,CAAC;UAC3BF,SAAS,GAAG,IAAI;QACpB;MACJ;IACJ;IACA,IAAI,CAACA,SAAS,EAAE;MACZI,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;IAClE;EACJ;EACAE,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAG7D,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQN,qDAAC,CAACE,iDAAI,EAAE;MAAEkE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAE,cAAaA,IAAK,EAAC,GAAG,IAAI;QAC5B,oBAAoB,EAAE,IAAI,CAAC3C;MAC/B;IAAE,CAAC,EAAExB,qDAAC,CAAC,MAAM,EAAE;MAAEoE,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;EACA,IAAIZ,EAAEA,CAAA,EAAG;IAAE,OAAOpD,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWkE,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,SAAS,EAAE,CAAC,gBAAgB,CAAC;MAC7B,UAAU,EAAE,CAAC,aAAa,CAAC;MAC3B,MAAM,EAAE,CAAC,aAAa;IAC1B,CAAC;EAAE;AACP,CAAC;AACD,MAAML,YAAY,GAAGA,CAACT,EAAE,EAAEK,MAAM,KAAK;EACjC,IAAIU,KAAK;EACT,IAAIC,QAAQ;EACZ,IAAIX,MAAM,EAAE;IACRU,KAAK,GAAG5D,eAAe;IACvB6D,QAAQ,GAAG5D,eAAe;EAC9B,CAAC,MACI;IACD2D,KAAK,GAAG3D,eAAe;IACvB4D,QAAQ,GAAG7D,eAAe;EAC9B;EACA,MAAM8D,SAAS,GAAGjB,EAAE,CAACiB,SAAS;EAC9BA,SAAS,CAACC,GAAG,CAACH,KAAK,CAAC;EACpBE,SAAS,CAACE,MAAM,CAACH,QAAQ,CAAC;AAC9B,CAAC;AACDpD,SAAS,CAACwD,KAAK,GAAG;EACdC,GAAG,EAAErE,qBAAqB;EAC1BQ,EAAE,EAAEN;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-split-pane.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-c71c5417.js';\nimport { b as getIonMode } from './ionic-global-b9c0d1da.js';\n\nconst splitPaneIosCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:0.55px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--side-min-width:270px;--side-max-width:28%}\";\nconst IonSplitPaneIosStyle0 = splitPaneIosCss;\n\nconst splitPaneMdCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--side-min-width:270px;--side-max-width:28%}\";\nconst IonSplitPaneMdStyle0 = splitPaneMdCss;\n\n// TODO(FW-2832): types\nconst SPLIT_PANE_MAIN = 'split-pane-main';\nconst SPLIT_PANE_SIDE = 'split-pane-side';\nconst QUERY = {\n    xs: '(min-width: 0px)',\n    sm: '(min-width: 576px)',\n    md: '(min-width: 768px)',\n    lg: '(min-width: 992px)',\n    xl: '(min-width: 1200px)',\n    never: '',\n};\nconst SplitPane = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionSplitPaneVisible = createEvent(this, \"ionSplitPaneVisible\", 7);\n        this.visible = false;\n        this.contentId = undefined;\n        this.disabled = false;\n        this.when = QUERY['lg'];\n    }\n    visibleChanged(visible) {\n        this.ionSplitPaneVisible.emit({ visible });\n    }\n    /**\n     * @internal\n     */\n    async isVisible() {\n        return Promise.resolve(this.visible);\n    }\n    async connectedCallback() {\n        // TODO: connectedCallback is fired in CE build\n        // before WC is defined. This needs to be fixed in Stencil.\n        if (typeof customElements !== 'undefined' && customElements != null) {\n            await customElements.whenDefined('ion-split-pane');\n        }\n        this.styleMainElement();\n        this.updateState();\n    }\n    disconnectedCallback() {\n        if (this.rmL) {\n            this.rmL();\n            this.rmL = undefined;\n        }\n    }\n    updateState() {\n        if (this.rmL) {\n            this.rmL();\n            this.rmL = undefined;\n        }\n        // Check if the split-pane is disabled\n        if (this.disabled) {\n            this.visible = false;\n            return;\n        }\n        // When query is a boolean\n        const query = this.when;\n        if (typeof query === 'boolean') {\n            this.visible = query;\n            return;\n        }\n        // When query is a string, let's find first if it is a shortcut\n        const mediaQuery = QUERY[query] || query;\n        // Media query is empty or null, we hide it\n        if (mediaQuery.length === 0) {\n            this.visible = false;\n            return;\n        }\n        // Listen on media query\n        const callback = (q) => {\n            this.visible = q.matches;\n        };\n        const mediaList = window.matchMedia(mediaQuery);\n        // TODO FW-5869\n        mediaList.addListener(callback);\n        this.rmL = () => mediaList.removeListener(callback);\n        this.visible = mediaList.matches;\n    }\n    /**\n     * Attempt to find the main content\n     * element inside of the split pane.\n     * If found, set it as the main node.\n     *\n     * We assume that the main node\n     * is available in the DOM on split\n     * pane load.\n     */\n    styleMainElement() {\n        const contentId = this.contentId;\n        const children = this.el.children;\n        const nu = this.el.childElementCount;\n        let foundMain = false;\n        for (let i = 0; i < nu; i++) {\n            const child = children[i];\n            const isMain = contentId !== undefined && child.id === contentId;\n            if (isMain) {\n                if (foundMain) {\n                    console.warn('split pane cannot have more than one main node');\n                    return;\n                }\n                else {\n                    setPaneClass(child, isMain);\n                    foundMain = true;\n                }\n            }\n        }\n        if (!foundMain) {\n            console.warn('split pane does not have a specified main node');\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'd54c356cd4bff7e55325160882dea8249f47f388', class: {\n                [mode]: true,\n                // Used internally for styling\n                [`split-pane-${mode}`]: true,\n                'split-pane-visible': this.visible,\n            } }, h(\"slot\", { key: 'af62690d0fd686cfbd29d888c79eda9001f13e2f' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"visible\": [\"visibleChanged\"],\n        \"disabled\": [\"updateState\"],\n        \"when\": [\"updateState\"]\n    }; }\n};\nconst setPaneClass = (el, isMain) => {\n    let toAdd;\n    let toRemove;\n    if (isMain) {\n        toAdd = SPLIT_PANE_MAIN;\n        toRemove = SPLIT_PANE_SIDE;\n    }\n    else {\n        toAdd = SPLIT_PANE_SIDE;\n        toRemove = SPLIT_PANE_MAIN;\n    }\n    const classList = el.classList;\n    classList.add(toAdd);\n    classList.remove(toRemove);\n};\nSplitPane.style = {\n    ios: IonSplitPaneIosStyle0,\n    md: IonSplitPaneMdStyle0\n};\n\nexport { SplitPane as ion_split_pane };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "b", "getIonMode", "splitPaneIosCss", "IonSplitPaneIosStyle0", "splitPaneMdCss", "IonSplitPaneMdStyle0", "SPLIT_PANE_MAIN", "SPLIT_PANE_SIDE", "QUERY", "xs", "sm", "md", "lg", "xl", "never", "SplitPane", "constructor", "hostRef", "ionSplitPaneVisible", "visible", "contentId", "undefined", "disabled", "when", "visibleChanged", "emit", "isVisible", "_this", "_asyncToGenerator", "Promise", "resolve", "connectedCallback", "_this2", "customElements", "whenDefined", "styleMainElement", "updateState", "disconnectedCallback", "rmL", "query", "mediaQuery", "length", "callback", "q", "matches", "mediaList", "window", "matchMedia", "addListener", "removeListener", "children", "el", "nu", "childElementCount", "<PERSON><PERSON><PERSON>", "child", "is<PERSON><PERSON>", "id", "console", "warn", "setPaneClass", "render", "mode", "key", "class", "watchers", "toAdd", "toRemove", "classList", "add", "remove", "style", "ios", "ion_split_pane"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}