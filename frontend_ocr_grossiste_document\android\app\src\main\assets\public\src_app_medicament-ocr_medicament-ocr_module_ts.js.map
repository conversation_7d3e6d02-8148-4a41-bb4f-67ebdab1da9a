{"version": 3, "file": "src_app_medicament-ocr_medicament-ocr_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAEG;;;AAE1D,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,mEAAiBA;CAC7B,CACF;AAMK,MAAOI,8BAA8B;kCAA9BA,8BAA8B;;mBAA9BA,+BAA8B;AAAA;;QAA9BA;AAA8B;;YAH/BL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,8BAA8B;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAF/BT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAEoC;AAEvB;AACH;;AAYjD,MAAOc,uBAAuB;2BAAvBA,uBAAuB;;mBAAvBA,wBAAuB;AAAA;;QAAvBA;AAAuB;;YARhCJ,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,0FAA8B,EAC9BQ,+DAAY;AAAA;;sHAIHC,uBAAuB;IAAAC,YAAA,GAFnBd,mEAAiB;IAAAM,OAAA,GAN9BG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,0FAA8B,EAC9BQ,+DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACD2D;AAChB;AAGjB;;;;;;;;;;;;;;;;;ICgBhCQ,4DAAA,WAAqB;IAAAA,oDAAA,kBAAM;IAAAA,0DAAA,EAAO;;;;;IAClCA,4DAAA,WAAoB;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAO;;;;;;IAXpCA,4DAFJ,cAAmD,cAC5B,SACf;IAAAA,oDAAA,wCAAuB;IAAAA,0DAAA,EAAK;IAChCA,4DAAA,QAAG;IACDA,oDAAA,2HACF;IACFA,0DADE,EAAI,EACA;IAIJA,4DADF,cAA0B,qBACgD;IAAvBA,wDAAA,mBAAAK,8DAAA;MAAAL,2DAAA,CAAAO,GAAA;MAAA,MAAAC,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IACrEX,uDAAA,mBAAwD;IAExDA,wDADA,IAAAc,wCAAA,mBAAqB,KAAAC,yCAAA,mBACD;IACtBf,0DAAA,EAAa;IAEbA,4DAAA,sBAA0E;IAAxBA,wDAAA,mBAAAgB,+DAAA;MAAAhB,2DAAA,CAAAO,GAAA;MAAA,MAAAC,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAS,WAAA,EAAa;IAAA,EAAC;IACvEjB,uDAAA,oBAAwD;IACxDA,oDAAA,iBACF;IAEJA,0DAFI,EAAa,EACT,EACF;;;;IATOA,uDAAA,GAAY;IAAZA,wDAAA,UAAAQ,MAAA,CAAAY,KAAA,CAAY;IACZpB,uDAAA,EAAW;IAAXA,wDAAA,SAAAQ,MAAA,CAAAY,KAAA,CAAW;;;;;;IAYtBpB,4DADF,cAA6D,aACjC;IAAAA,oDAAA,yBAAa;IAAAA,0DAAA,EAAK;IAC5CA,4DAAA,cAA2B;IACzBA,uDAAA,cAAuD;IACzDA,0DAAA,EAAM;IAENA,4DAAA,qBAAuE;IAAtBA,wDAAA,mBAAAqB,8DAAA;MAAArB,2DAAA,CAAAsB,GAAA;MAAA,MAAAd,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAe,SAAA,EAAW;IAAA,EAAC;IACpEvB,uDAAA,mBAAyD;IACzDA,oDAAA,+BACF;IACFA,0DADE,EAAa,EACT;;;;IAPGA,uDAAA,GAAuB;IAAvBA,wDAAA,QAAAQ,MAAA,CAAAgB,eAAA,EAAAxB,2DAAA,CAAuB;;;;;IAgBxBA,4DAAA,wBAAiD;IAAAA,oDAAA,GAA2B;IAAAA,0DAAA,EAAoB;;;;IAA/CA,uDAAA,EAA2B;IAA3BA,+DAAA,CAAA2B,aAAA,CAAAC,UAAA,CAA2B;;;;;IAKxE5B,4DADF,cAAuD,eACjC;IAAAA,oDAAA,kBAAW;IAAAA,0DAAA,EAAO;IACtCA,4DAAA,eAAoB;IAAAA,oDAAA,GAA2B;IACjDA,0DADiD,EAAO,EAClD;;;;IADgBA,uDAAA,GAA2B;IAA3BA,+DAAA,CAAA2B,aAAA,CAAAE,UAAA,CAA2B;;;;;IAG/C7B,4DADF,cAAgD,eAC1B;IAAAA,oDAAA,WAAI;IAAAA,0DAAA,EAAO;IAC/BA,4DAAA,eAAoB;IAAAA,oDAAA,GAAwB;IAC9CA,0DAD8C,EAAO,EAC/C;;;;IADgBA,uDAAA,GAAwB;IAAxBA,gEAAA,KAAA2B,aAAA,CAAAI,GAAA,SAAwB;;;;;IAG5C/B,4DADF,cAAmD,eAC7B;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAO;IAClCA,4DAAA,eAAoB;IAAAA,oDAAA,GAAuB;IAC7CA,0DAD6C,EAAO,EAC9C;;;;IADgBA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAA2B,aAAA,CAAAK,MAAA,CAAuB;;;;;IAf/ChC,4DAFJ,mBAAyE,sBACtD,qBACC;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAiB;IAC7DA,wDAAA,IAAAiC,gEAAA,gCAAiD;IACnDjC,0DAAA,EAAkB;IAEhBA,4DADF,uBAAkB,cACgB;IAS9BA,wDARA,IAAAkC,kDAAA,kBAAuD,IAAAC,kDAAA,kBAIP,IAAAC,kDAAA,kBAIG;IAMzDpC,0DAFI,EAAM,EACW,EACV;;;;IAnBSA,uDAAA,GAA4B;IAA5BA,+DAAA,CAAA2B,aAAA,CAAAU,WAAA,CAA4B;IACxBrC,uDAAA,EAA2B;IAA3BA,wDAAA,SAAA2B,aAAA,CAAAC,UAAA,CAA2B;IAInB5B,uDAAA,GAA2B;IAA3BA,wDAAA,SAAA2B,aAAA,CAAAE,UAAA,CAA2B;IAI3B7B,uDAAA,EAAoB;IAApBA,wDAAA,SAAA2B,aAAA,CAAAI,GAAA,CAAoB;IAIpB/B,uDAAA,EAAuB;IAAvBA,wDAAA,SAAA2B,aAAA,CAAAK,MAAA,CAAuB;;;;;IAjBzDhC,4DADF,cAAkE,aACtC;IAAAA,oDAAA,kBAAW;IAAAA,0DAAA,EAAK;IAC1CA,4DAAA,cAA6B;IAC3BA,wDAAA,IAAAsC,4CAAA,wBAAyE;IAuB7EtC,0DADE,EAAM,EACF;;;;IAvB+BA,uDAAA,GAAc;IAAdA,wDAAA,YAAAQ,MAAA,CAAA+B,WAAA,CAAc;;;;;IA0BnDvC,4DAAA,cAA0F;IACxFA,uDAAA,mBAAiD;IACjDA,4DAAA,SAAI;IAAAA,oDAAA,sCAAqB;IAAAA,0DAAA,EAAK;IAC9BA,4DAAA,QAAG;IAAAA,oDAAA,gGAA0E;IAC/EA,0DAD+E,EAAI,EAC7E;;;ADzDJ,MAAOpB,iBAAiB;EAoB5B4D,YACUC,OAAsB,EACtBC,UAAsB,EACtBC,iBAAoC,EACpCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,cAA8B,EAC9BC,eAAgC;IAPhC,KAAAP,OAAO,GAAPA,OAAO;IACP,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IA3BzB,KAAAC,WAAW,GAAG,IAAI;IAClB,KAAAC,QAAQ,GAAG,CAAC;IACZ,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAG,KAAK;IAGjB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAd,WAAW,GAAiB,EAAE;IAC9B,KAAAnB,KAAK,GAAY,KAAK;IAEb,KAAAkC,YAAY,GAAG,GAAG;IAC3B,KAAAC,WAAW,GAAG,KAAK;IAEnB;IACQ,KAAAC,QAAQ,GAAG,IAAIzD,0CAAO,EAAQ;IAepC,IAAI,CAACqB,KAAK,GAAGtB,qEAAW,CAAC2D,QAAQ,KAAK,KAAK;EAC7C;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACF,mBAAmB,EAAE;EAC5B;EAEAG,gBAAgBA,CAAA;IACd,IAAI,CAACD,WAAW,EAAE;EACpB;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACP,QAAQ,CAACQ,IAAI,EAAE;IACpB,IAAI,CAACR,QAAQ,CAACS,QAAQ,EAAE;IACxB,IAAI,IAAI,CAACC,KAAK,EAAE;MACd,IAAI,CAACrB,gBAAgB,CAACsB,KAAK,CAAC,IAAI,CAACD,KAAK,CAAC;;EAE3C;EAEQP,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAACb,cAAc,CAACsB,gBAAgB,EAAE,CAACC,SAAS,CAAEC,SAAkB,IAAI;MACtE,IAAI,CAACrB,WAAW,GAAGqB,SAAS;IAC9B,CAAC,CAAC;IACF,IAAI,CAACT,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAACT,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,WAAW,GAAG,KAAK;EAC1B;EAEAhC,SAASA,CAAA;IACP;IACA,IAAI,CAACZ,UAAU,EAAE;EACnB;EAGA4D,kBAAkBA,CAACC,KAAY;IAAA,IAAAC,eAAA;IAC7B,MAAMC,UAAU,IAAAD,eAAA,GAAG,IAAI,CAACE,SAAS,cAAAF,eAAA,uBAAdA,eAAA,CAAgBG,aAAa;IAChD,MAAMC,cAAc,GAAGL,KAAK,CAACM,MAAqB;IAElD,IAAI,IAAI,CAAC1B,SAAS,IAAIsB,UAAU,IAAI,CAACA,UAAU,CAACK,QAAQ,CAACF,cAAc,CAAC,EAAE;MACxE;MACA,IAAI,CAACA,cAAc,CAACG,OAAO,CAAC,SAAS,CAAC,EAAE;QACtC,IAAI,CAACC,WAAW,EAAE;;;EAGxB;EAEMA,WAAWA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACf;MACA,IAAID,KAAI,CAAC3B,WAAW,EAAE;QACpB;;MAGF2B,KAAI,CAAC3B,WAAW,GAAG,IAAI;MACvB2B,KAAI,CAAC9B,SAAS,GAAG,CAAC8B,KAAI,CAAC9B,SAAS;MAEhC;MACAgC,UAAU,CAAC,MAAK;QACdF,KAAI,CAAC3B,WAAW,GAAG,KAAK;MAC1B,CAAC,EAAE2B,KAAI,CAAC5B,YAAY,CAAC;IAAC;EACxB;EAEA+B,qBAAqBA,CAAA;IACnB,IAAI;MAAA,IAAAC,gBAAA;MACF,KAAAA,gBAAA,GAAI,IAAI,CAACX,SAAS,cAAAW,gBAAA,eAAdA,gBAAA,CAAgBV,aAAa,EAAE;QACjC,IAAI,CAACD,SAAS,CAACC,aAAa,CAACW,KAAK,EAAE;;KAEvC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEF,GAAG,CAAC;MAClD;MACA,IAAI,CAACpC,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;;EAEpC;EAEMzC,UAAUA,CAAA;IAAA,IAAAgF,MAAA;IAAA,OAAAR,6OAAA;MACd;MACA,IAAIQ,MAAI,CAACvC,SAAS,EAAE;QAClBuC,MAAI,CAACV,WAAW,EAAE;;MAGpB;MACAU,MAAI,CAACtC,eAAe,GAAG,KAAK;MAC5BsC,MAAI,CAACnE,eAAe,GAAGoE,SAAS;MAChCD,MAAI,CAACpD,WAAW,GAAG,EAAE;MAErB,IAAIoD,MAAI,CAACvE,KAAK,EAAE;QACd;QACA,MAAMuE,MAAI,CAACE,aAAa,EAAE;OAC3B,MAAM;QACL,IAAI;UACF;UACA,MAAMC,KAAK,SAASnG,qDAAM,CAACoG,QAAQ,CAAC;YAClCC,OAAO,EAAE,GAAG;YACZC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAEtG,+DAAgB,CAACuG,GAAG;YAChCC,MAAM,EAAEvG,2DAAY,CAACF;WACtB,CAAC;UAEF,IAAImG,KAAK,CAACO,OAAO,EAAE;YACjB,MAAMC,QAAQ,SAASC,KAAK,CAACT,KAAK,CAACO,OAAO,CAAC;YAC3C,MAAMG,IAAI,SAASF,QAAQ,CAACE,IAAI,EAAE;YAClC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE,yBAAyB,EAAE;cACvDG,IAAI,EAAE;aACP,CAAC;YAEFhB,MAAI,CAACnE,eAAe,GAAGsE,KAAK,CAACO,OAAO;YACpCV,MAAI,CAACtC,eAAe,GAAG,IAAI;YAC3B,MAAMsC,MAAI,CAACiB,sBAAsB,CAACH,IAAI,CAAC;;SAE1C,CAAC,OAAOf,KAAK,EAAE;UACdD,OAAO,CAACoB,GAAG,CAACnB,KAAK,CAAC;UAClB;;;IAEH;EACH;EAEMG,aAAaA,CAAA;IAAA,IAAAiB,MAAA;IAAA,OAAA3B,6OAAA;MACjB,MAAM4B,OAAO,SAASD,MAAI,CAACE,WAAW,EAAE;MAExC,IAAI;QACF,MAAMC,OAAO,SAASH,MAAI,CAAC/D,cAAc,CAACmE,UAAU,EAAE;QACtD,MAAMH,OAAO,CAACI,OAAO,EAAE;QAEvB,IAAI,CAACF,OAAO,EAAE;UACZ,MAAMH,MAAI,CAACM,cAAc,EAAE;;OAE9B,CAAC,OAAO1B,KAAK,EAAE;QACd,MAAMqB,OAAO,CAACI,OAAO,EAAE;QACvB,MAAML,MAAI,CAACM,cAAc,EAAE;;IAC5B;EACH;EAEcJ,WAAWA,CAAA;IAAA,IAAAK,MAAA;IAAA,OAAAlC,6OAAA;MACvB,MAAM4B,OAAO,SAASM,MAAI,CAAC1E,iBAAiB,CAAC2E,MAAM,CAAC;QAClDC,OAAO,EAAE,sCAAsC;QAC/CC,OAAO,EAAE,UAAU;QACnBC,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAE,gBAAgB;QAC1BC,QAAQ,EAAE,KAAK,CAAC;OACjB,CAAC;MACF,MAAMZ,OAAO,CAACa,OAAO,EAAE;MACvB,OAAOb,OAAO;IAAC;EACjB;EAEcK,cAAcA,CAAA;IAAA,IAAAS,MAAA;IAAA,OAAA1C,6OAAA;MAC1B,MAAM2C,KAAK,SAASD,MAAI,CAAC7E,eAAe,CAACsE,MAAM,CAAC;QAC9CC,OAAO,EAAE,0FAA0F;QACnGI,QAAQ,EAAE,IAAI;QACdI,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,SAAS;QAChBC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE;SACP;OAEJ,CAAC;MACF,MAAML,KAAK,CAACF,OAAO,EAAE;IAAC;EACxB;EAEM3G,WAAWA,CAAA;IAAA,IAAAmH,MAAA;IAAA,OAAAjD,6OAAA;MACf;MACA,IAAIiD,MAAI,CAAChF,SAAS,EAAE;QAClBgF,MAAI,CAACnD,WAAW,EAAE;;MAGpB;MACAmD,MAAI,CAAC/E,eAAe,GAAG,KAAK;MAC5B+E,MAAI,CAAC5G,eAAe,GAAGoE,SAAS;MAChCwC,MAAI,CAAC7F,WAAW,GAAG,EAAE;MAErB;MACA,IAAI;QACF,MAAMuD,KAAK,SAASnG,qDAAM,CAACoG,QAAQ,CAAC;UAClCC,OAAO,EAAE,GAAG;UACZC,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAEtG,+DAAgB,CAACuG,GAAG;UAChCC,MAAM,EAAEvG,2DAAY,CAACwI;SACtB,CAAC;QAEF,IAAIvC,KAAK,CAACO,OAAO,EAAE;UACjB,MAAMC,QAAQ,SAASC,KAAK,CAACT,KAAK,CAACO,OAAO,CAAC;UAC3C,MAAMG,IAAI,SAASF,QAAQ,CAACE,IAAI,EAAE;UAElC,IAAG,CAAC4B,MAAI,CAACE,2BAA2B,CAAC9B,IAAI,CAAC,EAAE;YAC1C;;UAGF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE,uBAAuBV,KAAK,CAACyC,MAAM,EAAE,EAAE;YACnE5B,IAAI,EAAEb,KAAK,CAACyC,MAAM,GAAG,SAASzC,KAAK,CAACyC,MAAM,EAAE,GAAG;WAChD,CAAC;UAEFH,MAAI,CAAC5G,eAAe,GAAGsE,KAAK,CAACO,OAAO;UACpC+B,MAAI,CAAC/E,eAAe,GAAG,IAAI;UAC3B,MAAM+E,MAAI,CAACxB,sBAAsB,CAACH,IAAI,CAAC;;OAE1C,CAAC,OAAOf,KAAK,EAAE;QACdD,OAAO,CAACoB,GAAG,CAAC,mBAAmB,EAAGnB,KAA0B,aAA1BA,KAA0B,uBAA1BA,KAA0B,CAAE6B,OAAO,CAAC;;IACvE;EACH;EAEAe,2BAA2BA,CAAC9B,IAAU;IACpC;IACA,MAAMG,IAAI,GAAGH,IAAI,CAACG,IAAI,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpC,IAAG7B,IAAI,KAAK,OAAO,EAAE;MACnB,IAAI,CAAC/D,eAAe,CAAC0E,MAAM,CAAC;QAC1BmB,QAAQ,EAAE,IAAI;QACdC,MAAM,EAAE,QAAQ;QAChBnB,OAAO,EAAE,wCAAwC;QACjDU,OAAO,EAAE,CAAC,IAAI;OACf,CAAC,CAACU,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAChB,OAAO,EAAE,CAAC;MACjC,OAAO,KAAK;;IAEd,OAAO,IAAI;EACb;EAEMhB,sBAAsBA,CAACH,IAAU;IAAA,IAAAoC,MAAA;IAAA,OAAA1D,6OAAA;MACrC0D,MAAI,CAAC3E,KAAK,GAAG2E,MAAI,CAACnG,UAAU,CAACoG,aAAa,EAAE;MAC5C,MAAMC,YAAY,GAAG,GAAGjJ,qEAAW,CAACkJ,YAAY,IAAIH,MAAI,CAAC3E,KAAK,EAAE;MAChE2E,MAAI,CAAChG,gBAAgB,CAACoG,OAAO,CAACF,YAAY,EAAEF,MAAI,CAAC3E,KAAK,CAAC;MAEvD;MACA2E,MAAI,CAAChG,gBAAgB,CAACqG,SAAS,CAACL,MAAI,CAAC3E,KAAK,CAAC,CAACG,SAAS,CAAEkD,OAAO,IAAI;QAChE,IAAIA,OAAO,CAACrE,QAAQ,KAAK0C,SAAS,EAAE;UAClCiD,MAAI,CAAC3F,QAAQ,GAAGqE,OAAO,CAACrE,QAAQ;UAChCuC,OAAO,CAACoB,GAAG,CAAC,WAAW,EAAEgC,MAAI,CAAC3F,QAAQ,CAAC;;MAE3C,CAAC,CAAC;MAEF2F,MAAI,CAAC1F,SAAS,GAAG,IAAI;MACrB0F,MAAI,CAACtG,WAAW,GAAG,EAAE;MAErB,IAAI;QACFsG,MAAI,CAACnG,UAAU,CAACyG,iBAAiB,CAAC1C,IAAI,EAAEoC,MAAI,CAAC3E,KAAK,CAAC,CAACG,SAAS,CAC1D+E,MAAW,IAAK;UACf3D,OAAO,CAACoB,GAAG,CAAC,eAAe,EAAEuC,MAAM,CAAC;UAEpC;UACA,IAAIA,MAAM,IAAIA,MAAM,CAACC,YAAY,EAAE;YACjCR,MAAI,CAACtG,WAAW,GAAG6G,MAAM,CAACC,YAAY,CAACC,GAAG,CAAEC,IAAS,IAAI;cACvD,OAAO;gBACLlH,WAAW,EAAEkH,IAAI,CAACC,gBAAgB,IAAI,oBAAoB;gBAC1D3H,UAAU,EAAE0H,IAAI,CAACE,cAAc,IAAI,EAAE;gBACrC7H,UAAU,EAAE,EAAE;gBACdG,GAAG,EAAEwH,IAAI,CAACG,uBAAuB,IAAI,CAAC;gBACtC1H,MAAM,EAAE,EAAE;gBACV2H,KAAK,EAAE,CAAC,CAAC;eACV;YACH,CAAC,CAAC;;UAGJd,MAAI,CAAC1F,SAAS,GAAG,KAAK;UAEtB;UACA,IAAI0F,MAAI,CAAC3E,KAAK,EAAE;YACd2E,MAAI,CAAChG,gBAAgB,CAACsB,KAAK,CAAC0E,MAAI,CAAC3E,KAAK,CAAC;;QAE3C,CAAC,EACAwB,KAAS,IAAI;UACZD,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1DmD,MAAI,CAAC1F,SAAS,GAAG,KAAK;UAEtB;UACA0F,MAAI,CAACe,cAAc,CAAC,6EAA6E,CAAC;UAElG;UACA,IAAIf,MAAI,CAAC3E,KAAK,EAAE;YACd2E,MAAI,CAAChG,gBAAgB,CAACsB,KAAK,CAAC0E,MAAI,CAAC3E,KAAK,CAAC;;QAE3C,CAAC,CACF;OACF,CAAC,OAAOwB,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDmD,MAAI,CAAC1F,SAAS,GAAG,KAAK;QACtB0F,MAAI,CAACe,cAAc,CAAC,yDAAyD,CAAC;QAE9E,IAAIf,MAAI,CAAC3E,KAAK,EAAE;UACd2E,MAAI,CAAChG,gBAAgB,CAACsB,KAAK,CAAC0E,MAAI,CAAC3E,KAAK,CAAC;;;IAE1C;EACH;EAEM0F,cAAcA,CAACrC,OAAe;IAAA,IAAAsC,MAAA;IAAA,OAAA1E,6OAAA;MAClC,MAAMyD,KAAK,SAASiB,MAAI,CAACjH,eAAe,CAAC0E,MAAM,CAAC;QAC9CoB,MAAM,EAAE,QAAQ;QAChBnB,OAAO,EAAEA,OAAO;QAChBU,OAAO,EAAE,CAAC,IAAI;OACf,CAAC;MACF,MAAMW,KAAK,CAAChB,OAAO,EAAE;IAAC;EACxB;EAEMkC,MAAMA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA5E,6OAAA;MACV,MAAM4E,MAAI,CAACrH,UAAU,CAACoH,MAAM,EAAE;MAC9BC,MAAI,CAACtH,OAAO,CAACuH,YAAY,CAAC,QAAQ,CAAC;IAAC;EACtC;;qBA7UWpL,iBAAiB;;mBAAjBA,kBAAiB,EAAAoB,+DAAA,CAAAb,0DAAA,GAAAa,+DAAA,CAAAmK,6DAAA,GAAAnK,+DAAA,CAAAb,8DAAA,GAAAa,+DAAA,CAAAb,4DAAA,GAAAa,+DAAA,CAAAuK,yEAAA,GAAAvK,+DAAA,CAAAyK,qEAAA,GAAAzK,+DAAA,CAAA2K,qEAAA,GAAA3K,+DAAA,CAAAb,4DAAA;AAAA;;QAAjBP,kBAAiB;EAAAkM,SAAA;EAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;MAAjBjL,wDAAA,mBAAAmL,2CAAAC,MAAA;QAAA,OAAAF,GAAA,CAAA3G,kBAAA,CAAA6G,MAAA,CAA0B;MAAA,UAAApL,+DAAA,CAAT;;;;;;;;MCpC5BA,4DADF,oBAA+C,kBAChC;MACXA,uDAAA,qBAEc;MACdA,4DAAA,gBAAW;MAAAA,oDAAA,8BAAkB;MAAAA,0DAAA,EAAY;MAEvCA,4DADF,qBAA0B,oBACU;MAChCA,uDAAA,kBAA0D;MAIlEA,0DAHM,EAAa,EACD,EACF,EACH;MAGXA,4DADF,qBAA+E,aACpD;MACvBA,uDAAA,yBAAuC;MA0EvCA,wDAlEA,KAAAsL,iCAAA,kBAAmD,KAAAC,iCAAA,iBAwBU,KAAAC,iCAAA,iBAaK,KAAAC,iCAAA,iBA6BwB;MAM9FzL,0DADE,EAAM,EACM;MAEdA,4DAAA,eAA+D;MAC7DA,uDAAA,4BAA2D;MAC7DA,0DAAA,EAAM;;;MApGMA,wDAAA,YAAAA,6DAAA,IAAA2L,GAAA,EAAAT,GAAA,CAAA/H,SAAA,EAAkC;MAcFnD,uDAAA,GAAkC;MAAlCA,wDAAA,YAAAA,6DAAA,KAAA2L,GAAA,EAAAT,GAAA,CAAA/H,SAAA,EAAkC;MAU/CnD,uDAAA,GAAsB;MAAtBA,wDAAA,UAAAkL,GAAA,CAAA7H,eAAA,CAAsB;MAwBXrD,uDAAA,EAAqB;MAArBA,wDAAA,SAAAkL,GAAA,CAAA7H,eAAA,CAAqB;MAavBrD,uDAAA,EAA4B;MAA5BA,wDAAA,SAAAkL,GAAA,CAAA3I,WAAA,CAAAqJ,MAAA,KAA4B;MA6BvC5L,uDAAA,EAA+D;MAA/DA,wDAAA,SAAAkL,GAAA,CAAA7H,eAAA,IAAA6H,GAAA,CAAA3I,WAAA,CAAAqJ,MAAA,WAAAV,GAAA,CAAA/H,SAAA,CAA+D;MAQhEnD,uDAAA,EAAkC;MAAlCA,wDAAA,YAAAA,6DAAA,KAAA2L,GAAA,EAAAT,GAAA,CAAA/H,SAAA,EAAkC;MAC1CnD,uDAAA,EAAqB;MAArBA,wDAAA,aAAAkL,GAAA,CAAAhI,QAAA,CAAqB", "sources": ["./src/app/medicament-ocr/medicament-ocr-routing.module.ts", "./src/app/medicament-ocr/medicament-ocr.module.ts", "./src/app/medicament-ocr/medicament-ocr.page.ts", "./src/app/medicament-ocr/medicament-ocr.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { MedicamentOcrPage } from './medicament-ocr.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: MedicamentOcrPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class MedicamentOcrPageRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { MedicamentOcrPageRoutingModule } from './medicament-ocr-routing.module';\r\n\r\nimport { MedicamentOcrPage } from './medicament-ocr.page';\r\nimport { SharedModule } from '../shared/shared.module';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    MedicamentOcrPageRoutingModule,\r\n    SharedModule\r\n  ],\r\n  declarations: [MedicamentOcrPage]\r\n})\r\nexport class MedicamentOcrPageModule {}\r\n", "import {\r\n  <PERSON>mpo<PERSON>,\r\n  OnIni<PERSON>,\r\n  inject,\r\n  Element<PERSON>ef,\r\n  <PERSON><PERSON><PERSON>d,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  HostListener,\r\n} from '@angular/core';\r\nimport {\r\n  Nav<PERSON>ontroller,\r\n  LoadingController,\r\n  AlertController,\r\n  ToastController,\r\n} from '@ionic/angular';\r\nimport { ApiService } from '../services/api.service';\r\nimport { Camera, CameraResultType, CameraSource } from '@capacitor/camera';\r\nimport { environment } from 'src/environments/environment';\r\nimport { NetworkService } from '../services/network.service';\r\nimport { WebSocketService } from '../services/websocket.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { ScannerService } from '../services/scanner.service';\r\n\r\ninterface Suggestion {\r\n  designation: string;\r\n  code_barre?: string;\r\n  laboratory?: string;\r\n  ppv?: number;\r\n  dosage?: string;\r\n  score?: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-medicament-ocr',\r\n  templateUrl: './medicament-ocr.page.html',\r\n  styleUrls: ['./medicament-ocr.page.scss'],\r\n})\r\nexport class MedicamentOcrPage implements OnInit, OnDestroy {\r\n  isConnected = true;\r\n  progress = 0;\r\n  isLoading = false;\r\n  isFabOpen = false;\r\n  jobId: string | undefined;\r\n  imagePreviewUrl: string | undefined;\r\n  hasScannedImage = false;\r\n  suggestions: Suggestion[] = [];\r\n  isWeb: boolean = false;\r\n  \r\n  readonly TOGGLE_DELAY = 300;\r\n  isAnimating = false;\r\n  \r\n  // Destroy subject for cleanup\r\n  private destroy$ = new Subject<void>();\r\n\r\n  @ViewChild('fabButton', { static: false })\r\n  fabButton!: ElementRef<HTMLIonFabButtonElement>;\r\n\r\n  constructor(\r\n    private navCtrl: NavController,\r\n    private apiService: ApiService,\r\n    private loadingController: LoadingController,\r\n    private alertController: AlertController,\r\n    private webSocketService: WebSocketService,\r\n    private networkService: NetworkService,\r\n    private scannerService: ScannerService,\r\n    private toastController: ToastController,\r\n  ) {\r\n    this.isWeb = environment.platform === 'web';\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.initializeComponent();\r\n  }\r\n\r\n  ionViewWillEnter() {\r\n    this.resetStates();\r\n    this.initializeComponent();\r\n  }\r\n\r\n  ionViewWillLeave() {\r\n    this.resetStates();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    if (this.jobId) {\r\n      this.webSocketService.close(this.jobId);\r\n    }\r\n  }\r\n\r\n  private initializeComponent() {\r\n    // Subscribe to the network status\r\n    this.networkService.getNetworkStatus().subscribe((connected: boolean) => {\r\n      this.isConnected = connected;\r\n    });\r\n    this.resetStates();\r\n  }\r\n\r\n  private resetStates() {\r\n    this.isFabOpen = false;\r\n    this.isLoading = false;\r\n    this.isAnimating = false;\r\n  }\r\n\r\n  resetScan() {\r\n    // When clicking \"Scanner à nouveau\", directly open the camera\r\n    this.openCamera();\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  handleClickOutside(event: Event) {\r\n    const fabElement = this.fabButton?.nativeElement;\r\n    const clickedElement = event.target as HTMLElement;\r\n    \r\n    if (this.isFabOpen && fabElement && !fabElement.contains(clickedElement)) {\r\n      // Check if click was outside the FAB and its children\r\n      if (!clickedElement.closest('ion-fab')) {\r\n        this.onFabToggle();\r\n      }\r\n    }\r\n  }\r\n\r\n  async onFabToggle() {\r\n    // Prevent toggle while animating\r\n    if (this.isAnimating) {\r\n      return;\r\n    }\r\n  \r\n    this.isAnimating = true;\r\n    this.isFabOpen = !this.isFabOpen;\r\n  \r\n    // Reset animation lock after transition completes\r\n    setTimeout(() => {\r\n      this.isAnimating = false;\r\n    }, this.TOGGLE_DELAY);\r\n  }\r\n\r\n  triggerFabButtonClick() {\r\n    try {\r\n      if (this.fabButton?.nativeElement) {\r\n        this.fabButton.nativeElement.click();\r\n      }\r\n    } catch (err) {\r\n      console.error('Error triggering fab button:', err);\r\n      // Fallback behavior\r\n      this.isFabOpen = !this.isFabOpen;\r\n    }\r\n  }\r\n\r\n  async openCamera() {\r\n    // If FAB is open, close it\r\n    if (this.isFabOpen) {\r\n      this.onFabToggle();\r\n    }\r\n\r\n    // Reset previous scan data\r\n    this.hasScannedImage = false;\r\n    this.imagePreviewUrl = undefined;\r\n    this.suggestions = [];\r\n\r\n    if (this.isWeb) {\r\n      // Launch scanner for web\r\n      await this.startScanning();\r\n    } else {\r\n      try {\r\n        // Logic to open the camera and capture an image\r\n        const image = await Camera.getPhoto({\r\n          quality: 100,\r\n          allowEditing: false,\r\n          resultType: CameraResultType.Uri,\r\n          source: CameraSource.Camera,\r\n        });\r\n\r\n        if (image.webPath) {\r\n          const response = await fetch(image.webPath);\r\n          const blob = await response.blob();\r\n          const file = new File([blob], 'captured-medicament.jpg', {\r\n            type: 'image/jpeg',\r\n          });\r\n\r\n          this.imagePreviewUrl = image.webPath;\r\n          this.hasScannedImage = true;\r\n          await this.processMedicamentImage(file);\r\n        }\r\n      } catch (error) {\r\n        console.log(error);\r\n        // Handle error appropriately\r\n      }\r\n    }\r\n  }\r\n\r\n  async startScanning() {\r\n    const loading = await this.showLoading();\r\n    \r\n    try {\r\n      const canScan = await this.scannerService.launchScan();\r\n      await loading.dismiss();\r\n      \r\n      if (!canScan) {\r\n        await this.showErrorToast();\r\n      }\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      await this.showErrorToast();\r\n    }\r\n  }\r\n\r\n  private async showLoading() {\r\n    const loading = await this.loadingController.create({\r\n      message: 'Tentative de connexion au scanner...',\r\n      spinner: 'circular',\r\n      translucent: true,\r\n      cssClass: 'custom-loading',\r\n      duration: 10000 // Will auto-dismiss after 10 seconds\r\n    });\r\n    await loading.present();\r\n    return loading;\r\n  }\r\n\r\n  private async showErrorToast() {\r\n    const toast = await this.toastController.create({\r\n      message: \"L'application PostAgent n'est pas ouverte. Merci de l'ouvrir avant de prendre une photo.\",\r\n      duration: 3000,\r\n      position: 'top',\r\n      color: 'warning',\r\n      buttons: [\r\n        {\r\n          text: 'OK',\r\n          role: 'cancel'\r\n        }\r\n      ]\r\n    });\r\n    await toast.present();\r\n  }\r\n\r\n  async openGallery() {\r\n    // If FAB is open, close it\r\n    if (this.isFabOpen) {\r\n      this.onFabToggle();\r\n    }\r\n\r\n    // Reset previous scan data\r\n    this.hasScannedImage = false;\r\n    this.imagePreviewUrl = undefined;\r\n    this.suggestions = [];\r\n    \r\n    // Logic to open the gallery and select an image\r\n    try {\r\n      const image = await Camera.getPhoto({\r\n        quality: 100,\r\n        allowEditing: false,\r\n        resultType: CameraResultType.Uri,\r\n        source: CameraSource.Photos,\r\n      });\r\n  \r\n      if (image.webPath) {\r\n        const response = await fetch(image.webPath);\r\n        const blob = await response.blob();\r\n  \r\n        if(!this.verifySelectedImageBlobType(blob)) {\r\n          return;\r\n        }\r\n  \r\n        const file = new File([blob], `selected-medicament.${image.format}`, {\r\n          type: image.format ? `image/${image.format}` : 'image/jpeg'\r\n        });\r\n  \r\n        this.imagePreviewUrl = image.webPath;\r\n        this.hasScannedImage = true;\r\n        await this.processMedicamentImage(file);\r\n      }\r\n    } catch (error) {\r\n      console.log(\"getPhoto Error : \", (error as unknown as Error)?.message);\r\n    }\r\n  }\r\n\r\n  verifySelectedImageBlobType(blob: Blob) {\r\n    //  should start with image/*\r\n    const type = blob.type.split('/')[0];\r\n    if(type !== 'image') {\r\n      this.alertController.create({\r\n        animated: true,\r\n        header: 'Erreur',\r\n        message: 'Veuillez sélectionner une image valide',\r\n        buttons: ['OK']\r\n      }).then(alert => alert.present());\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  async processMedicamentImage(file: File) {\r\n    this.jobId = this.apiService.generateJobId();\r\n    const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;\r\n    this.webSocketService.connect(websocketUrl, this.jobId);\r\n    \r\n    // Subscribe to WebSocket messages for progress updates\r\n    this.webSocketService.onMessage(this.jobId).subscribe((message) => {\r\n      if (message.progress !== undefined) {\r\n        this.progress = message.progress;\r\n        console.log('Progress:', this.progress);\r\n      }\r\n    });\r\n\r\n    this.isLoading = true;\r\n    this.suggestions = [];\r\n\r\n    try {\r\n      this.apiService.getMedicamentInfo(file, this.jobId).subscribe(\r\n        (result: any ) => {\r\n          console.log('API response:', result);\r\n          \r\n          // Process the suggestions\r\n          if (result && result.associations) {\r\n            this.suggestions = result.associations.map((item: any) => {\r\n              return {\r\n                designation: item.nom_base_winplus || 'Médicament inconnu',\r\n                code_barre: item.code_barre_win || '',\r\n                laboratory: '', // Not available in the response\r\n                ppv: item.prix_vente_base_winplus || 0,\r\n                dosage: '', // Not available in the response\r\n                score: 0 // Not available in the response\r\n              };\r\n            });\r\n          }\r\n          \r\n          this.isLoading = false;\r\n          \r\n          // Disconnect WebSocket after completion\r\n          if (this.jobId) {\r\n            this.webSocketService.close(this.jobId);\r\n          }\r\n        },\r\n        (error:any) => {\r\n          console.error('Error processing medicament image:', error);\r\n          this.isLoading = false;\r\n          \r\n          // Show error message\r\n          this.showErrorAlert('Une erreur est survenue lors de l\\'analyse de l\\'image. Veuillez réessayer.');\r\n          \r\n          // Disconnect WebSocket\r\n          if (this.jobId) {\r\n            this.webSocketService.close(this.jobId);\r\n          }\r\n        }\r\n      );\r\n    } catch (error) {\r\n      console.error('Exception during API call:', error);\r\n      this.isLoading = false;\r\n      this.showErrorAlert('Une erreur inattendue est survenue. Veuillez réessayer.');\r\n      \r\n      if (this.jobId) {\r\n        this.webSocketService.close(this.jobId);\r\n      }\r\n    }\r\n  }\r\n\r\n  async showErrorAlert(message: string) {\r\n    const alert = await this.alertController.create({\r\n      header: 'Erreur',\r\n      message: message,\r\n      buttons: ['OK'],\r\n    });\r\n    await alert.present();\r\n  }\r\n\r\n  async logout() {\r\n    await this.apiService.logout();\r\n    this.navCtrl.navigateRoot('/login');\r\n  }\r\n}\r\n", "<ion-header [ngClass]=\"{'loading': isLoading}\">\r\n  <ion-toolbar>\r\n    <ion-buttons slot=\"start\">\r\n      <!-- <ion-back-button defaultHref=\"/scan-bl\"></ion-back-button> -->\r\n    </ion-buttons>\r\n    <ion-title>Scanner Médicament</ion-title>\r\n    <ion-buttons slot=\"start\">\r\n      <ion-button routerLink=\"/profile\">\r\n        <ion-icon slot=\"icon-only\" name=\"menu-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content class=\"medicament-ocr-content\" [ngClass]=\"{'loading': isLoading}\">\r\n  <div class=\"div-content\">\r\n    <app-check-network></app-check-network>\r\n\r\n    <!-- <div class=\"instructions\">\r\n      <p class=\"instruction-text\">\r\n        Scannez l'étiquette ou la boîte du médicament pour obtenir des informations.\r\n      </p>\r\n    </div> -->\r\n\r\n    <div class=\"scan-wrapper\" *ngIf=\"!hasScannedImage\">\r\n      <div class=\"content\">\r\n        <h2>Aucun médicament scanné</h2>\r\n        <p>\r\n          Scannez l'étiquette ou la boîte d'un médicament pour obtenir des informations détaillées.\r\n        </p>\r\n      </div>\r\n\r\n      <!-- New scan buttons when no image is displayed -->\r\n      <div class=\"scan-buttons\">\r\n        <ion-button expand=\"block\" class=\"camera-button\" (click)=\"openCamera()\">\r\n          <ion-icon name=\"camera-outline\" slot=\"start\"></ion-icon>\r\n          <span *ngIf=\"!isWeb\">Caméra</span>\r\n          <span *ngIf=\"isWeb\">Scanner</span>\r\n        </ion-button>\r\n        \r\n        <ion-button expand=\"block\" class=\"gallery-button\" (click)=\"openGallery()\">\r\n          <ion-icon name=\"images-outline\" slot=\"start\"></ion-icon>\r\n          Galerie\r\n        </ion-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Display scanned image -->\r\n    <div class=\"scanned-image-container\" *ngIf=\"hasScannedImage\">\r\n      <h2 class=\"section-title\">Image scannée</h2>\r\n      <div class=\"image-preview\">\r\n        <img [src]=\"imagePreviewUrl\" alt=\"Médicament scanné\" />\r\n      </div>\r\n      \r\n      <ion-button expand=\"block\" class=\"rescan-button\" (click)=\"resetScan()\">\r\n        <ion-icon name=\"refresh-outline\" slot=\"start\"></ion-icon>\r\n        Scanner à nouveau\r\n      </ion-button>\r\n    </div>\r\n\r\n    <!-- Display suggestions -->\r\n    <div class=\"suggestions-container\" *ngIf=\"suggestions.length > 0\">\r\n      <h2 class=\"section-title\">Suggestions</h2>\r\n      <div class=\"suggestion-list\">\r\n        <ion-card *ngFor=\"let suggestion of suggestions\" class=\"suggestion-card\">\r\n          <ion-card-header>\r\n            <ion-card-title>{{ suggestion.designation }}</ion-card-title>\r\n            <ion-card-subtitle *ngIf=\"suggestion.laboratory\">{{ suggestion.laboratory }}</ion-card-subtitle>\r\n          </ion-card-header>\r\n          <ion-card-content>\r\n            <div class=\"suggestion-details\">\r\n              <div class=\"detail-item\" *ngIf=\"suggestion.code_barre\">\r\n                <span class=\"label\">Code Barre:</span>\r\n                <span class=\"value\">{{ suggestion.code_barre }}</span>\r\n              </div>\r\n              <div class=\"detail-item\" *ngIf=\"suggestion.ppv\">\r\n                <span class=\"label\">PPV:</span>\r\n                <span class=\"value\">{{ suggestion.ppv }} Dhs</span>\r\n              </div>\r\n              <div class=\"detail-item\" *ngIf=\"suggestion.dosage\">\r\n                <span class=\"label\">Dosage:</span>\r\n                <span class=\"value\">{{ suggestion.dosage }}</span>\r\n              </div>\r\n            </div>\r\n          </ion-card-content>\r\n        </ion-card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- No results found message -->\r\n    <div class=\"no-results\" *ngIf=\"hasScannedImage && suggestions.length === 0 && !isLoading\">\r\n      <ion-icon name=\"alert-circle-outline\"></ion-icon>\r\n      <h3>Aucun résultat trouvé</h3>\r\n      <p>Essayez de scanner à nouveau ou vérifiez que l'étiquette est bien visible.</p>\r\n    </div>\r\n  </div>\r\n</ion-content>\r\n\r\n<div class=\"alert-progress\" [ngClass]=\"{'loading': isLoading}\">\r\n  <app-custom-alert [progress]=\"progress\"></app-custom-alert>\r\n</div>\r\n"], "names": ["RouterModule", "MedicamentOcrPage", "routes", "path", "component", "MedicamentOcrPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SharedModule", "MedicamentOcrPageModule", "declarations", "Camera", "CameraResultType", "CameraSource", "environment", "Subject", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "MedicamentOcrPage_div_11_Template_ion_button_click_7_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "openCamera", "ɵɵelement", "ɵɵtemplate", "MedicamentOcrPage_div_11_span_9_Template", "MedicamentOcrPage_div_11_span_10_Template", "MedicamentOcrPage_div_11_Template_ion_button_click_11_listener", "openGallery", "ɵɵadvance", "ɵɵproperty", "isWeb", "MedicamentOcrPage_div_12_Template_ion_button_click_5_listener", "_r3", "resetScan", "imagePreviewUrl", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "suggestion_r4", "laboratory", "code_barre", "ɵɵtextInterpolate1", "ppv", "dosage", "MedicamentOcrPage_div_13_ion_card_4_ion_card_subtitle_4_Template", "MedicamentOcrPage_div_13_ion_card_4_div_7_Template", "MedicamentOcrPage_div_13_ion_card_4_div_8_Template", "MedicamentOcrPage_div_13_ion_card_4_div_9_Template", "designation", "MedicamentOcrPage_div_13_ion_card_4_Template", "suggestions", "constructor", "navCtrl", "apiService", "loadingController", "alertController", "webSocketService", "networkService", "scannerService", "toastController", "isConnected", "progress", "isLoading", "isFabOpen", "hasScannedImage", "TOGGLE_DELAY", "isAnimating", "destroy$", "platform", "ngOnInit", "initializeComponent", "ionViewWillEnter", "resetStates", "ionViewWillLeave", "ngOnDestroy", "next", "complete", "jobId", "close", "getNetworkStatus", "subscribe", "connected", "handleClickOutside", "event", "_this$fabButton", "fabElement", "fabButton", "nativeElement", "clickedElement", "target", "contains", "closest", "onFabToggle", "_this", "_asyncToGenerator", "setTimeout", "triggerFabButtonClick", "_this$fabButton2", "click", "err", "console", "error", "_this2", "undefined", "startScanning", "image", "getPhoto", "quality", "allowEditing", "resultType", "<PERSON><PERSON>", "source", "webPath", "response", "fetch", "blob", "file", "File", "type", "processMedicamentImage", "log", "_this3", "loading", "showLoading", "canScan", "launchScan", "dismiss", "showErrorToast", "_this4", "create", "message", "spinner", "translucent", "cssClass", "duration", "present", "_this5", "toast", "position", "color", "buttons", "text", "role", "_this6", "Photos", "verifySelectedImageBlobType", "format", "split", "animated", "header", "then", "alert", "_this7", "generateJobId", "websocketUrl", "webSocketUrl", "connect", "onMessage", "getMedicamentInfo", "result", "associations", "map", "item", "nom_base_winplus", "code_barre_win", "prix_vente_base_winplus", "score", "showError<PERSON><PERSON>t", "_this8", "logout", "_this9", "navigateRoot", "ɵɵdirectiveInject", "NavController", "i2", "ApiService", "LoadingController", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "WebSocketService", "i4", "NetworkService", "i5", "ScannerService", "ToastController", "selectors", "viewQuery", "MedicamentOcrPage_Query", "rf", "ctx", "MedicamentOcrPage_click_HostBindingHandler", "$event", "ɵɵresolveDocument", "MedicamentOcrPage_div_11_Template", "MedicamentOcrPage_div_12_Template", "MedicamentOcrPage_div_13_Template", "MedicamentOcrPage_div_14_Template", "ɵɵpureFunction1", "_c1", "length"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}