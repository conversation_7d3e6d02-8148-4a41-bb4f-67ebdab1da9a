<lint-module
    format="1"
    dir="C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\OCR_DOCUMENT_GROSSISTE\Frontend ocr grossiste document\frontend_ocr_grossiste_document\android\app"
    name=":app"
    type="APP"
    maven="android:app:"
    agpVersion="8.2.1"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
