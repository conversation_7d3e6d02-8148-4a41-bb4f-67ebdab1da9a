import{p as e,b as t}from"./p-60b24ee9.js";export{s as setNonce}from"./p-60b24ee9.js";const o=()=>{const t=import.meta.url;const o={};if(t!==""){o.resourcesUrl=new URL(".",t).href}return e(o)};o().then((e=>t([["p-0017b960",[[1,"image-cropper",{img:[16],rect:[16],quad:[16],license:[1],hidefooter:[1],handlersize:[1],inactiveSelections:[16],viewBox:[32],activeStroke:[32],inActiveStroke:[32],selectedHandlerIndex:[32],points:[32],offsetX:[32],offsetY:[32],scale:[32],resetStates:[64],getAllSelections:[64],getPoints:[64],getQuad:[64],getRect:[64],getCroppedImage:[64],detect:[64]},null,{img:["watchImgPropHandler"],rect:["watchRectPropHandler"],quad:["watchQuadPropHandler"]}]]]],e)));
//# sourceMappingURL=image-cropper-component.esm.js.map