{"version": 3, "file": "src_app_data-bl-success_data-bl-success_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAEI;;;AAE3D,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,oEAAiBA;CAC7B,CACF;AAMK,MAAOI,8BAA8B;kCAA9BA,8BAA8B;;mBAA9BA,+BAA8B;AAAA;;QAA9BA;AAA8B;;YAH/BL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,8BAA8B;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAF/BT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAEqC;AAEvB;AACJ,CAAC;;AAalD,MAAOc,uBAAuB;2BAAvBA,uBAAuB;;mBAAvBA,wBAAuB;AAAA;;QAAvBA;AAAuB;;YAThCJ,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,2FAA8B,EAC9BQ,+DAAY;AAAA;;sHAKHC,uBAAuB;IAAAC,YAAA,GAFnBd,oEAAiB;IAAAM,OAAA,GAP9BG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,2FAA8B,EAC9BQ,+DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVO;AAMC;AACmC;AAEN;AACtB;;;;;;;;AAOzB,MAAOZ,iBAAiB;EAc5BsB,YAAA;IAbA,KAAAC,UAAU,GAAyB,EAAE;IACrC,KAAAC,OAAO,GAAGT,qDAAM,CAACC,yDAAa,CAAC;IAC/B,KAAAS,aAAa,GAAGV,qDAAM,CAACI,mEAAa,CAAC;IACrC,KAAAO,iBAAiB,GAAGX,qDAAM,CAACE,6DAAiB,CAAC;IAC7C,KAAAU,eAAe,GAAGZ,qDAAM,CAACG,2DAAe,CAAC;IAEzC,KAAAU,aAAa,GAAY,EAAE;IAC3B,KAAAC,UAAU,GAAGd,qDAAM,CAACK,6DAAU,CAAC;IAE/B,KAAAU,eAAe,GAAY,EAAE;EAId;EAEfC,QAAQA,CAAA;IACN,IAAI,CAACC,KAAK,GAAGC,OAAO,CAACC,KAAK,CAACF,KAAK;IAChC,IAAI,CAACJ,aAAa,GAAGK,OAAO,CAACC,KAAK,CAACN,aAAa;IAChDO,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACJ,KAAK,CAAC;IAEjC,IAAI,CAACT,UAAU,GAAG,IAAI,CAACE,aAAa,CAACY,kBAAkB,EAAE;IACzD,IAAI,IAAI,CAACd,UAAU,CAACe,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACd,OAAO,CAACe,YAAY,CAAC,UAAU,CAAC;;IAEvCJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACb,UAAU,CAAC;IAE9CiB,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;EAChD;EAEAC,UAAUA,CAAA;IACR,IAAI,IAAI,CAACC,SAAS,EAAE;MAClBC,UAAU,CAAC,MAAK;QACd,IAAI,CAACD,SAAS,CAACE,QAAQ,EAAE;MAC3B,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEAC,YAAYA,CAACC,KAAY;IACvBA,KAAK,CAACC,cAAc,EAAE;IACtB,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAA0B;IAC9CD,KAAK,CAACE,KAAK,EAAE;EACf;EAGMC,SAASA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACbjC,uDAAS,CAAC;QACRmC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,0BAA0B;QACjCC,IAAI,EAAE,sBAAsB;QAC5BC,MAAM,EAAE,0DAA0D;QAClEC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAE,IAAI;QACrBC,WAAW,EAAE;UACXC,WAAW,EAAE,qBAAqB;UAClCC,KAAK,EAAE,cAAc;UACrBL,MAAM,EAAE,eAAe,CAAE;SAC1B;QACDM,OAAO,EAAEA,CAAA,KAAK;UAAA,IAAAC,eAAA;UACZ,MAAMC,UAAU,IAAAD,eAAA,GACd7C,4DAAc,EAAE,cAAA6C,eAAA,uBAAhBA,eAAA,CAAkBG,aAAa,CAAC,kBAAkB,CAAC;UACrD,IAAIF,UAAU,EAAE;YACdA,UAAU,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;cACxCjD,wDAAU,EAAE;cACZgC,KAAI,CAAC5B,aAAa,CAAC+C,aAAa,EAAE;cAClChC,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;cAC3CY,KAAI,CAAC7B,OAAO,CAACiD,YAAY,CAAC,UAAU,CAAC;YACvC,CAAC,CAAC;;QAEN;OACD,CAAC;IAAC;EACL;EAGMC,MAAMA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArB,6OAAA;MACV,IAAIqB,MAAI,CAAC3C,KAAK,EAAE;QACd,IAAI2C,MAAI,CAACC,aAAa,EAAE;UACtB,IAAI;YAAA,IAAAC,qBAAA,EAAAC,oBAAA;YACF,MAAMH,MAAI,CAAC9C,UAAU,CAClBkD,cAAc,CACbJ,MAAI,CAAC3C,KAAK,EACV,UAAU,EACV2C,MAAI,CAACC,aAAa,GAAAC,qBAAA,GAClBF,MAAI,CAAC7C,eAAe,cAAA+C,qBAAA,cAAAA,qBAAA,GAAI,EAAE,GAAAC,oBAAA,GAC1BH,MAAI,CAAC/C,aAAa,cAAAkD,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CACzB,CACAE,SAAS,EAAE;YACd;YACA3D,uDAAS,CAAC;cACRmC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,gCAAgC;cACvCC,IAAI,EAAE,0KAA0K;cAChLC,MAAM,EAAE,+CAA+C;cACvDC,iBAAiB,EAAE,KAAK;cACxBC,eAAe,EAAE,IAAI;cACrBC,WAAW,EAAE;gBACXC,WAAW,EAAE,qBAAqB;gBAClCC,KAAK,EAAE,cAAc;gBACrBL,MAAM,EAAE;eACT;cACDM,OAAO,EAAEA,CAAA,KAAK;gBAAA,IAAAgB,gBAAA;gBACZ,MAAMd,UAAU,IAAAc,gBAAA,GAAG5D,4DAAc,EAAE,cAAA4D,gBAAA,uBAAhBA,gBAAA,CAAkBZ,aAAa,CAAC,kBAAkB,CAAC;gBACtE,IAAIF,UAAU,EAAE;kBACdA,UAAU,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;oBACxCjD,wDAAU,EAAE;oBACZsD,MAAI,CAACnD,OAAO,CAACiD,YAAY,CAAC,UAAU,CAAC;kBACvC,CAAC,CAAC;;cAEN;aACD,CAAC;YACF;YACAE,MAAI,CAAClD,aAAa,CAAC+C,aAAa,EAAE;YAClChC,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;YAC3CkC,MAAI,CAACnD,OAAO,CAACiD,YAAY,CAAC,UAAU,CAAC;WACtC,CAAC,OAAOS,KAAK,EAAE;YACd/C,OAAO,CAAC+C,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;YACzC;YACA7D,uDAAS,CAAC;cACRmC,IAAI,EAAE,OAAO;cACbC,KAAK,EAAE,8BAA8B;cACrCC,IAAI,EAAE,+BAA+B;cACrCC,MAAM,EACJ,8DAA8D;cAChEC,iBAAiB,EAAE,KAAK;cACxBC,eAAe,EAAE,IAAI;cACrBC,WAAW,EAAE;gBACXC,WAAW,EAAE,qBAAqB;gBAClCC,KAAK,EAAE,cAAc;gBACrBL,MAAM,EAAE,eAAe,CAAE;;aAE5B,CAAC;;SAEL,MAAM;UACL;UACAtC,uDAAS,CAAC;YACRmC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,8BAA8B;YACrCC,IAAI,EAAE,2BAA2B;YACjCC,MAAM,EAAE,+CAA+C;YACvDC,iBAAiB,EAAE,KAAK;YACxBC,eAAe,EAAE,IAAI;YACrBC,WAAW,EAAE;cACXC,WAAW,EAAE,qBAAqB;cAClCC,KAAK,EAAE,cAAc;cACrBL,MAAM,EAAE,eAAe,CAAE;aAC1B;YACDM,OAAO,EAAEA,CAAA,KAAK;cAAA,IAAAkB,gBAAA;cACZ,MAAMhB,UAAU,IAAAgB,gBAAA,GACd9D,4DAAc,EAAE,cAAA8D,gBAAA,uBAAhBA,gBAAA,CAAkBd,aAAa,CAAC,kBAAkB,CAAC;cACrD,IAAIF,UAAU,EAAE;gBACdA,UAAU,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;kBACxCjD,wDAAU,EAAE;gBACd,CAAC,CAAC;;YAEN;WACD,CAAC;;;IAEL;EACH;EAEM+D,KAAKA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA/B,6OAAA;MACT,MAAMgC,KAAK,SAASD,MAAI,CAAC1D,eAAe,CAAC4D,MAAM,CAAC;QAC9CC,MAAM,EAAE,yCAAyC;QACjDC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,uCAAuC;UACjDC,OAAO,EAAEA,CAAA,KAAK;YACZ1D,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEsD,IAAI,EAAE,YAAY;UAClBE,QAAQ,EAAE,uCAAuC;UACjDC,OAAO,EAAEA,CAAA,KAAK;YACZR,MAAI,CAAC5D,aAAa,CAAC+C,aAAa,EAAE;YAClChC,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;YAC3C4C,MAAI,CAAC7D,OAAO,CAACsE,eAAe,CAAC,UAAU,CAAC;UAC1C;SACD;OAEJ,CAAC;MAEF,MAAMR,KAAK,CAACS,OAAO,EAAE;IAAC;EACxB;EAEMC,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA3C,6OAAA;MACjB,MAAMgC,KAAK,SAASW,MAAI,CAACtE,eAAe,CAAC4D,MAAM,CAAC;QAC9CC,MAAM,EAAE,0DAA0D;QAClEC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,uCAAuC;UACjDC,OAAO,EAAEA,CAAA,KAAK;YACZ1D,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEsD,IAAI,EAAE,UAAU;UAChBE,QAAQ,EAAE,uCAAuC;UACjDC,OAAO,EAAEA,CAAA,KAAK;YACZI,MAAI,CAACzE,OAAO,CAACiD,YAAY,CAAC,cAAc,CAAC;UAC3C;SACD;OAEJ,CAAC;MAEF,MAAMa,KAAK,CAACS,OAAO,EAAE;IAAC;EACxB;EAEMG,MAAMA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA7C,6OAAA;MACV,MAAM6C,MAAI,CAACtE,UAAU,CAACqE,MAAM,EAAE,CAAC,CAAE;MACjCC,MAAI,CAAC3E,OAAO,CAACiD,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAE;IAAA;EACxC;;qBAvNWzE,iBAAiB;;mBAAjBA,kBAAiB;AAAA;;QAAjBA,kBAAiB;EAAAoG,SAAA;EAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;;MCtB1BE,4DAFJ,iBAAY,kBACG,gBACA;MAAAA,oDAAA,+BAAmB;MAAAA,0DAAA,EAAY;MAExCA,4DADF,qBAAwB,oBACS;MAAnBA,wDAAA,mBAAAK,uDAAA;QAAAL,2DAAA,CAAAO,GAAA;QAAA,OAAAP,yDAAA,CAASD,GAAA,CAAAN,MAAA,EAAQ;MAAA,EAAC;MAC5BO,uDAAA,kBAA6D;MAKrEA,0DAJM,EAAa,EACD,EACF,EAEH;MAITA,4DAFJ,kBAAa,aACgC,aACZ;MAC3BA,uDAAA,iBAAsC;MACtCA,4DAAA,aAAwB;MAAAA,oDAAA,gDAA8B;MAAAA,0DAAA,EAAK;MAC3DA,4DAAA,YAAyB;MACvBA,oDAAA,qBAAY;MAAAA,4DAAA,SAAG;MAAAA,oDAAA,IAAmB;MAAAA,0DAAA,EAAI;MAACA,oDAAA,oFACzC;MAAAA,0DAAA,EAAI;MAKIA,4DAJR,eAAS,eACE,mBACmC,eACb,iBACN;MACjBA,oDAAA,2BAAa;MAAAA,4DAAA,gBAA4B;MAAAA,oDAAA,SAAC;MAC5CA,0DAD4C,EAAO,EAC3C;MACRA,4DAAA,wBAYC;MALCA,8DAAA,2BAAAW,+DAAAC,MAAA;QAAAZ,2DAAA,CAAAO,GAAA;QAAAP,gEAAA,CAAAD,GAAA,CAAA5B,aAAA,EAAAyC,MAAA,MAAAb,GAAA,CAAA5B,aAAA,GAAAyC,MAAA;QAAA,OAAAZ,yDAAA,CAAAY,MAAA;MAAA,EAA2B;MAC3BZ,wDAAA,mBAAAc,uDAAAF,MAAA;QAAAZ,2DAAA,CAAAO,GAAA;QAAA,OAAAP,yDAAA,CAASD,GAAA,CAAA1D,YAAA,CAAAuE,MAAA,CAAoB;MAAA,EAAC;MAQxCZ,0DAJS,EAAY,EACT,EACG,EACH,EACF;MAKFA,4DAJR,eAAS,eACE,mBACmC,eACb,iBACJ;MACnBA,oDAAA,iBACF;MAAAA,0DAAA,EAAQ;MACRA,4DAAA,qBAOC;MADCA,8DAAA,2BAAAe,+DAAAH,MAAA;QAAAZ,2DAAA,CAAAO,GAAA;QAAAP,gEAAA,CAAAD,GAAA,CAAA1E,eAAA,EAAAuF,MAAA,MAAAb,GAAA,CAAA1E,eAAA,GAAAuF,MAAA;QAAA,OAAAZ,yDAAA,CAAAY,MAAA;MAAA,EAA6B;MAKvCZ,0DAJS,EAAY,EACT,EACG,EACH,EACF;MAERA,4DADF,eAA2B,kBAC+C;MAAtBA,wDAAA,mBAAAgB,oDAAA;QAAAhB,2DAAA,CAAAO,GAAA;QAAA,OAAAP,yDAAA,CAASD,GAAA,CAAApD,SAAA,EAAW;MAAA,EAAC;MAACqD,oDAAA,eAAO;MAAAA,0DAAA,EAAS;MACxFA,4DAAA,kBAAsE;MAAnBA,wDAAA,mBAAAiB,oDAAA;QAAAjB,2DAAA,CAAAO,GAAA;QAAA,OAAAP,yDAAA,CAASD,GAAA,CAAA9B,MAAA,EAAQ;MAAA,EAAC;MAAC+B,oDAAA,eAAO;MAIrFA,0DAJqF,EAAS,EAClF,EACF,EACF,EACM;MAKRA,4DAHN,kBAAY,mBACG,mBACE,sBACmE;MAA1BA,wDAAA,mBAAAkB,wDAAA;QAAAlB,2DAAA,CAAAO,GAAA;QAAA,OAAAP,yDAAA,CAASD,GAAA,CAAAR,aAAA,EAAe;MAAA,EAAC;MAC3ES,uDAAA,2BAAgD;MAClDA,0DAAA,EAAa;MACbA,4DAAA,sBAAyD;MAAlBA,wDAAA,mBAAAmB,wDAAA;QAAAnB,2DAAA,CAAAO,GAAA;QAAA,OAAAP,yDAAA,CAASD,GAAA,CAAApB,KAAA,EAAO;MAAA,EAAC;MACtDqB,uDAAA,2BAAkD;MAClDA,4DAAA,YAAM;MAAAA,oDAAA,eAAO;MACfA,0DADe,EAAO,EACT;MACbA,4DAAA,sBAAyE;MACvEA,uDAAA,2BAAmD;MAI3DA,0DAHM,EAAa,EACD,EACF,EACH;;;MApEUA,uDAAA,IAAmB;MAAnBA,+DAAA,CAAAD,GAAA,CAAA5E,aAAA,CAAmB;MAe1B6E,uDAAA,IAA2B;MAA3BA,mEAAA,UAAAD,GAAA,CAAA5B,aAAA,CAA2B;MAH3B6B,wDAAA,oBAAmB;MAInBA,8DAAA,YAAAD,GAAA,CAAA5B,aAAA,CAA2B;MAsB3B6B,uDAAA,GAA6B;MAA7BA,mEAAA,UAAAD,GAAA,CAAA1E,eAAA,CAA6B;MAH7B2E,wDAAA,oBAAmB;MAInBA,8DAAA,YAAAD,GAAA,CAAA1E,eAAA,CAA6B;MAwBM2E,uDAAA,IAA2B;MAA3BA,wDAAA,eAAAA,6DAAA,IAAA0B,GAAA,EAA2B", "sources": ["./src/app/data-bl-success/data-bl-success-routing.module.ts", "./src/app/data-bl-success/data-bl-success.module.ts", "./src/app/data-bl-success/data-bl-success.page.ts", "./src/app/data-bl-success/data-bl-success.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { DataBlSuccessPage } from './data-bl-success.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: DataBlSuccessPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class DataBlSuccessPageRoutingModule {}\r\n", "import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { DataBlSuccessPageRoutingModule } from './data-bl-success-routing.module';\r\n\r\nimport { DataBlSuccessPage } from './data-bl-success.page';\r\nimport { SharedModule } from '../shared/shared.module'; // Import SharedModule\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    DataBlSuccessPageRoutingModule,\r\n    SharedModule\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  declarations: [DataBlSuccessPage]\r\n})\r\nexport class DataBlSuccessPageModule {}\r\n", "import {\r\n  Component,\r\n  ViewChild,\r\n  AfterViewInit,\r\n  ElementRef,\r\n  OnInit,\r\n  inject,\r\n} from '@angular/core';\r\nimport {\r\n  NavController,\r\n  LoadingController,\r\n  AlertController,\r\n  IonInput,\r\n} from '@ionic/angular';\r\nimport { SignalService } from '../services/signal.service';\r\nimport { TransformedDocData } from 'src/models/TransformedDocData';\r\nimport { ApiService } from '../services/api.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-data-bl-success',\r\n  templateUrl: './data-bl-success.page.html',\r\n  styleUrls: ['./data-bl-success.page.scss'],\r\n})\r\nexport class DataBlSuccessPage implements OnInit {\r\n  slidesData: TransformedDocData[] = [];\r\n  navCtrl = inject(NavController);\r\n  signalService = inject(SignalService);\r\n  loadingController = inject(LoadingController);\r\n  alertController = inject(AlertController);\r\n  BL_id?: string;\r\n  supplier_name?: string = '';\r\n  apiService = inject(ApiService);\r\n  id_BL_origine?: string;\r\n  date_BL_origine?: string = '';\r\n\r\n  @ViewChild('idBlInput') idBlInput!: IonInput;\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {\r\n    this.BL_id = history.state.BL_id;\r\n    this.supplier_name = history.state.supplier_name;\r\n    console.log('BL_id:', this.BL_id);\r\n\r\n    this.slidesData = this.signalService.getTransformedData();\r\n    if (this.slidesData.length === 0) {\r\n      this.navCtrl.navigateBack('/scan-bl');\r\n    }\r\n    console.log('Received data:', this.slidesData);\r\n\r\n    localStorage.removeItem('forceSupplierGlobal');\r\n  }\r\n\r\n  focusInput() {\r\n    if (this.idBlInput) {\r\n      setTimeout(() => {\r\n        this.idBlInput.setFocus();\r\n      }, 150);\r\n    }\r\n  }\r\n\r\n  onInputClick(event: Event) {\r\n    event.preventDefault();\r\n    const input = event.target as HTMLInputElement;\r\n    input.focus();\r\n  }\r\n\r\n\r\n  async IgnorerBL(){\r\n    Swal.fire({\r\n      icon: 'warning',\r\n      title: \"vous allez ignorer le BL\",\r\n      html: 'Veuillez confirmer ?',\r\n      footer: '<span class=\"btn_swal_custom\">Scanner un autre BL</span>',\r\n      showConfirmButton: false, // Remove the confirm button\r\n      showCloseButton: true, // Add a close button\r\n      customClass: {\r\n        closeButton: 'custom-close-button', // Custom class for the close button\r\n        popup: 'custom-popup', // Custom class for the popup for additional styling if needed\r\n        footer: 'custom-footer', // Custom class for the footer\r\n      },\r\n      didOpen: () => {\r\n        const footerLink =\r\n          Swal.getFooter()?.querySelector('.btn_swal_custom');\r\n        if (footerLink) {\r\n          footerLink.addEventListener('click', () => {\r\n            Swal.close();\r\n            this.signalService.removeAllData();\r\n            localStorage.removeItem('selectedSupplier');\r\n            this.navCtrl.navigateRoot('/scan-bl');\r\n          });\r\n        }\r\n      },\r\n    });\r\n  }\r\n\r\n\r\n  async sendBL() {\r\n    if (this.BL_id) {\r\n      if (this.id_BL_origine) {\r\n        try {\r\n          await this.apiService\r\n            .updateBLStatus(\r\n              this.BL_id,\r\n              'EN_COURS',\r\n              this.id_BL_origine,\r\n              this.date_BL_origine ?? '',\r\n              this.supplier_name ?? ''\r\n            )\r\n            .toPromise();\r\n          // Show success message\r\n          Swal.fire({\r\n            icon: 'success',\r\n            title: 'Le BL a été envoyé avec succès',\r\n            html: 'Vous pouvez retrouver les résultats du BL dans votre espace <b>WinPlusPharma</b> :<br><p><small>Menu → Achats → Réception → Import BL → Importer BLs Scannés</small></p>',\r\n            footer: \"<span class='btn_swal_custom'>D'accord</span>\",\r\n            showConfirmButton: false,\r\n            showCloseButton: true,\r\n            customClass: {\r\n              closeButton: 'custom-close-button',\r\n              popup: 'custom-popup',\r\n              footer: 'custom-footer',\r\n            },\r\n            didOpen: () => {\r\n              const footerLink = Swal.getFooter()?.querySelector('.btn_swal_custom');\r\n              if (footerLink) {\r\n                footerLink.addEventListener('click', () => {\r\n                  Swal.close();\r\n                  this.navCtrl.navigateRoot('/scan-bl');\r\n                });\r\n              }\r\n            },\r\n          });\r\n          // Navigate to scan-bl page and remove all data\r\n          this.signalService.removeAllData();\r\n          localStorage.removeItem('selectedSupplier');\r\n          this.navCtrl.navigateRoot('/scan-bl');\r\n        } catch (error) {\r\n          console.error('Error sending BL:', error);\r\n          // Show error message\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: \"Erreur lors de l'envoi du BL\",\r\n            html: 'Veuillez réessayer plus tard.',\r\n            footer:\r\n              '<a href=\"/guide\">Comment capturer une image de qualité ?</a>',\r\n            showConfirmButton: false, // Remove the confirm button\r\n            showCloseButton: true, // Add a close button\r\n            customClass: {\r\n              closeButton: 'custom-close-button', // Custom class for the close button\r\n              popup: 'custom-popup', // Custom class for the popup for additional styling if needed\r\n              footer: 'custom-footer', // Custom class for the footer\r\n            },\r\n          });\r\n        }\r\n      } else {\r\n        // Show error message of the BL ID Field Required\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: 'Erreur de validation du BL !',\r\n          html: 'Veuillez renseigner le BL',\r\n          footer: '<span class=\"btn_swal_custom\">Ressayer</span>',\r\n          showConfirmButton: false, // Remove the confirm button\r\n          showCloseButton: true, // Add a close button\r\n          customClass: {\r\n            closeButton: 'custom-close-button', // Custom class for the close button\r\n            popup: 'custom-popup', // Custom class for the popup for additional styling if needed\r\n            footer: 'custom-footer', // Custom class for the footer\r\n          },\r\n          didOpen: () => {\r\n            const footerLink =\r\n              Swal.getFooter()?.querySelector('.btn_swal_custom');\r\n            if (footerLink) {\r\n              footerLink.addEventListener('click', () => {\r\n                Swal.close();\r\n              });\r\n            }\r\n          },\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  async NewBL() {\r\n    const alert = await this.alertController.create({\r\n      header: 'Voulez-vous créer un nouveau document ?',\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button-rename-doc cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Nouveau BL',\r\n          cssClass: 'custom-alert-button-rename-doc rename',\r\n          handler: () => {\r\n            this.signalService.removeAllData();\r\n            localStorage.removeItem('selectedSupplier');\r\n            this.navCtrl.navigateForward('/scan-bl');\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async EditCurrentBL() {\r\n    const alert = await this.alertController.create({\r\n      header: 'Voulez-vous vraiment modifier les pages de ce document ?',\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button-rename-doc cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Modifier',\r\n          cssClass: 'custom-alert-button-rename-doc rename',\r\n          handler: () => {\r\n            this.navCtrl.navigateRoot('/process-doc');\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async logout() {\r\n    await this.apiService.logout();  // Wait for the confirmation dialog\r\n    this.navCtrl.navigateRoot('/login');  // Then navigate to login\r\n  }\r\n}\r\n", "<ion-header>\r\n  <ion-toolbar>\r\n    <ion-title>Envoyer les données</ion-title>\r\n    <ion-buttons slot=\"end\">\r\n      <ion-button (click)=\"logout()\">\r\n        <ion-icon slot=\"icon-only\" name=\"log-out-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n\r\n</ion-header>\r\n\r\n<ion-content>\r\n  <div class=\"modal-overlay data-bl-wrapper\">\r\n    <div class=\"modal-container\">\r\n      <button class=\"close-button\"></button>\r\n      <h2 class=\"modal-title\">Traitement terminé avec succès</h2>\r\n      <p class=\"modal-message\">\r\n        Votre BL de <b>{{ supplier_name }}</b> a été traité et est prêt à être envoyé.\r\n      </p>\r\n      <ion-row>\r\n        <ion-col>\r\n          <ion-item lines=\"none\" class=\"input-item\">\r\n            <div class=\"label-wrapper\">\r\n              <label for=\"id-bl\">\r\n                Numéro du BL <span class=\"required-star\">*</span>\r\n              </label>\r\n              <ion-input\r\n                #idBlInput\r\n                id=\"id-bl\"\r\n                [clearInput]=\"true\"\r\n                placeholder=\"Numéro du BL\"\r\n                type=\"text\"\r\n                value=\"{{ id_BL_origine }}\"\r\n                [(ngModel)]=\"id_BL_origine\"\r\n                (click)=\"onInputClick($event)\"\r\n                autocomplete=\"off\"\r\n                autocorrect=\"off\"\r\n                enterkeyhint=\"next\"\r\n              ></ion-input>\r\n            </div>\r\n          </ion-item>\r\n        </ion-col>\r\n      </ion-row>\r\n      <ion-row>\r\n        <ion-col>\r\n          <ion-item lines=\"none\" class=\"input-item\">\r\n            <div class=\"label-wrapper\">\r\n              <label for=\"date-bl\">\r\n                Date Bl\r\n              </label>\r\n              <ion-input\r\n                id=\"date-bl\"\r\n                [clearInput]=\"true\"\r\n                placeholder=\"Date BL\"\r\n                type=\"date\"\r\n                value=\"{{ date_BL_origine }}\"\r\n                [(ngModel)]=\"date_BL_origine\"\r\n              ></ion-input>\r\n            </div>\r\n          </ion-item>\r\n        </ion-col>\r\n      </ion-row>\r\n      <div class=\"modal-buttons\">\r\n        <button class=\"button button-cancel button-modal\" (click)=\"IgnorerBL()\">Ignorer</button>\r\n        <button class=\"button button-success button-modal\" (click)=\"sendBL()\">Envoyer</button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ion-content>\r\n\r\n<ion-footer>\r\n  <ion-toolbar>\r\n    <ion-buttons>\r\n      <ion-button class=\"menu-button active\" size=\"small\" (click)=\"EditCurrentBL()\">\r\n        <app-custom-icon name=\"files\"></app-custom-icon>\r\n      </ion-button>\r\n      <ion-button class=\"menu-button-middle\" (click)=\"NewBL()\">\r\n        <app-custom-icon name=\"extract\"></app-custom-icon>\r\n        <span>NOUVEAU</span>\r\n      </ion-button>\r\n      <ion-button class=\"menu-button\" size=\"small\" [routerLink]=\"['/profile']\">\r\n        <app-custom-icon name=\"settings\"></app-custom-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-footer>\r\n"], "names": ["RouterModule", "DataBlSuccessPage", "routes", "path", "component", "DataBlSuccessPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SharedModule", "DataBlSuccessPageModule", "declarations", "inject", "NavController", "LoadingController", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SignalService", "ApiService", "<PERSON><PERSON>", "constructor", "slidesData", "navCtrl", "signalService", "loadingController", "alertController", "supplier_name", "apiService", "date_BL_origine", "ngOnInit", "BL_id", "history", "state", "console", "log", "getTransformedData", "length", "navigateBack", "localStorage", "removeItem", "focusInput", "idBlInput", "setTimeout", "setFocus", "onInputClick", "event", "preventDefault", "input", "target", "focus", "IgnorerBL", "_this", "_asyncToGenerator", "fire", "icon", "title", "html", "footer", "showConfirmButton", "showCloseButton", "customClass", "closeButton", "popup", "did<PERSON><PERSON>", "_Swal$getFooter", "footerLink", "getFooter", "querySelector", "addEventListener", "close", "removeAllData", "navigateRoot", "sendBL", "_this2", "id_BL_origine", "_this2$date_BL_origin", "_this2$supplier_name", "updateBLStatus", "to<PERSON>romise", "_Swal$getFooter2", "error", "_Swal$getFooter3", "NewBL", "_this3", "alert", "create", "header", "buttons", "text", "role", "cssClass", "handler", "navigateForward", "present", "EditCurrentBL", "_this4", "logout", "_this5", "selectors", "viewQuery", "DataBlSuccessPage_Query", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DataBlSuccessPage_Template_ion_button_click_5_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵelement", "ɵɵtwoWayListener", "DataBlSuccessPage_Template_ion_input_ngModelChange_26_listener", "$event", "ɵɵtwoWayBindingSet", "DataBlSuccessPage_Template_ion_input_click_26_listener", "DataBlSuccessPage_Template_ion_input_ngModelChange_34_listener", "DataBlSuccessPage_Template_button_click_36_listener", "DataBlSuccessPage_Template_button_click_38_listener", "DataBlSuccessPage_Template_ion_button_click_43_listener", "DataBlSuccessPage_Template_ion_button_click_45_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpropertyInterpolate", "ɵɵproperty", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c1"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}