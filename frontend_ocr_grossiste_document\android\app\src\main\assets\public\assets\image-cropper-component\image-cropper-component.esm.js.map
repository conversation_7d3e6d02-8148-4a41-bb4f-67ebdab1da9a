{"version": 3, "names": ["patchBrowser", "importMeta", "url", "opts", "resourcesUrl", "URL", "href", "promiseResolve", "then", "options", "bootstrapLazy", "img", "rect", "quad", "license", "hidefooter", "handlersize", "inactiveSelections", "rotation", "viewBox", "activeStroke", "inActiveStroke", "selectedHandlerIndex", "points", "offsetX", "offsetY", "scale", "resetStates", "getAllSelections", "getPoints", "getQuad", "getRect", "detect"], "sources": ["node_modules/@stencil/core/internal/client/patch-browser.js", "@lazy-browser-entrypoint?app-data=conditional"], "sourcesContent": ["/*\n Stencil Client Patch Browser v4.12.0 | MIT Licensed | https://stenciljs.com\n */\nimport { BUILD, NAMESPACE } from '@stencil/core/internal/app-data';\nimport { consoleDevInfo, doc, promiseResolve, H } from '@stencil/core';\nconst patchBrowser = () => {\n    // NOTE!! This fn cannot use async/await!\n    if (BUILD.isDev && !BUILD.isTesting) {\n        consoleDevInfo('Running in development mode.');\n    }\n    if (BUILD.cloneNodeFix) {\n        // opted-in to polyfill cloneNode() for slot polyfilled components\n        patchCloneNodeFix(H.prototype);\n    }\n    const scriptElm = BUILD.scriptDataOpts\n        ? Array.from(doc.querySelectorAll('script')).find((s) => new RegExp(`\\/${NAMESPACE}(\\\\.esm)?\\\\.js($|\\\\?|#)`).test(s.src) ||\n            s.getAttribute('data-stencil-namespace') === NAMESPACE)\n        : null;\n    const importMeta = import.meta.url;\n    const opts = BUILD.scriptDataOpts ? (scriptElm || {})['data-opts'] || {} : {};\n    if (importMeta !== '') {\n        opts.resourcesUrl = new URL('.', importMeta).href;\n    }\n    return promiseResolve(opts);\n};\nconst patchCloneNodeFix = (HTMLElementPrototype) => {\n    const nativeCloneNodeFn = HTMLElementPrototype.cloneNode;\n    HTMLElementPrototype.cloneNode = function (deep) {\n        if (this.nodeName === 'TEMPLATE') {\n            return nativeCloneNodeFn.call(this, deep);\n        }\n        const clonedNode = nativeCloneNodeFn.call(this, false);\n        const srcChildNodes = this.childNodes;\n        if (deep) {\n            for (let i = 0; i < srcChildNodes.length; i++) {\n                // Node.ATTRIBUTE_NODE === 2, and checking because IE11\n                if (srcChildNodes[i].nodeType !== 2) {\n                    clonedNode.appendChild(srcChildNodes[i].cloneNode(true));\n                }\n            }\n        }\n        return clonedNode;\n    };\n};\nexport { patchBrowser };\n", "export { setNonce } from '@stencil/core';\nimport { bootstrapLazy } from '@stencil/core';\nimport { patchBrowser } from '@stencil/core/internal/client/patch-browser';\nimport { globalScripts } from '@stencil/core/internal/app-globals';\npatchBrowser().then(options => {\n  globalScripts();\n  return bootstrapLazy([/*!__STENCIL_LAZY_DATA__*/], options);\n});\n"], "mappings": "sFAKA,MAAMA,EAAe,KAajB,MAAMC,cAAyBC,IAC/B,MAAMC,EAAqE,GAC3E,GAAIF,IAAe,GAAI,CACnBE,EAAKC,aAAe,IAAIC,IAAI,IAAKJ,GAAYK,IACrD,CACI,OAAOC,EAAeJ,EAAK,ECnB/BH,IAAeQ,MAAKC,GAEXC,EAAc,mCAA8B,CAAAC,IAAS,KAAAC,KAAA,KAAAC,KAAA,KAAAC,QAAA,IAAAC,WAAA,IAAAC,YAAA,IAAAC,mBAAA,KAAAC,SAAA,IAAAC,QAAA,KAAAC,aAAA,KAAAC,eAAA,KAAAC,qBAAA,KAAAC,OAAA,KAAAC,QAAA,KAAAC,QAAA,KAAAC,MAAA,KAAAC,YAAA,KAAAC,iBAAA,KAAAC,UAAA,KAAAC,QAAA,KAAAC,QAAA,KAAAC,OAAA,YAAArB,IAAA,wBAAAC,KAAA,yBAAAC,KAAA,8BAAAJ"}