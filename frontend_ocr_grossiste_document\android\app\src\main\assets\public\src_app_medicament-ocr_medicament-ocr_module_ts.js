"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_medicament-ocr_medicament-ocr_module_ts"],{

/***/ 48024:
/*!*****************************************************************!*\
  !*** ./src/app/medicament-ocr/medicament-ocr-routing.module.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MedicamentOcrPageRoutingModule: () => (/* binding */ MedicamentOcrPageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _medicament_ocr_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./medicament-ocr.page */ 4594);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _MedicamentOcrPageRoutingModule;




const routes = [{
  path: '',
  component: _medicament_ocr_page__WEBPACK_IMPORTED_MODULE_0__.MedicamentOcrPage
}];
class MedicamentOcrPageRoutingModule {}
_MedicamentOcrPageRoutingModule = MedicamentOcrPageRoutingModule;
_MedicamentOcrPageRoutingModule.ɵfac = function MedicamentOcrPageRoutingModule_Factory(t) {
  return new (t || _MedicamentOcrPageRoutingModule)();
};
_MedicamentOcrPageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _MedicamentOcrPageRoutingModule
});
_MedicamentOcrPageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](MedicamentOcrPageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 86985:
/*!*********************************************************!*\
  !*** ./src/app/medicament-ocr/medicament-ocr.module.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MedicamentOcrPageModule: () => (/* binding */ MedicamentOcrPageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _medicament_ocr_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./medicament-ocr-routing.module */ 48024);
/* harmony import */ var _medicament_ocr_page__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./medicament-ocr.page */ 4594);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
var _MedicamentOcrPageModule;







class MedicamentOcrPageModule {}
_MedicamentOcrPageModule = MedicamentOcrPageModule;
_MedicamentOcrPageModule.ɵfac = function MedicamentOcrPageModule_Factory(t) {
  return new (t || _MedicamentOcrPageModule)();
};
_MedicamentOcrPageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
  type: _MedicamentOcrPageModule
});
_MedicamentOcrPageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _medicament_ocr_routing_module__WEBPACK_IMPORTED_MODULE_0__.MedicamentOcrPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](MedicamentOcrPageModule, {
    declarations: [_medicament_ocr_page__WEBPACK_IMPORTED_MODULE_1__.MedicamentOcrPage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _medicament_ocr_routing_module__WEBPACK_IMPORTED_MODULE_0__.MedicamentOcrPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule]
  });
})();

/***/ }),

/***/ 4594:
/*!*******************************************************!*\
  !*** ./src/app/medicament-ocr/medicament-ocr.page.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MedicamentOcrPage: () => (/* binding */ MedicamentOcrPage)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _capacitor_camera__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @capacitor/camera */ 54982);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs */ 10819);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _services_api_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/api.service */ 3366);
/* harmony import */ var _services_websocket_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/websocket.service */ 30765);
/* harmony import */ var _services_network_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../services/network.service */ 32404);
/* harmony import */ var _services_scanner_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../services/scanner.service */ 89116);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _check_network_check_network_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../check-network/check-network.component */ 93648);
/* harmony import */ var _custom_alert_custom_alert_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../custom-alert/custom-alert.component */ 32374);

var _MedicamentOcrPage;













const _c0 = ["fabButton"];
const _c1 = a0 => ({
  "loading": a0
});
function MedicamentOcrPage_div_11_span_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](1, "Cam\u00E9ra");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
}
function MedicamentOcrPage_div_11_span_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](1, "Scanner");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
}
function MedicamentOcrPage_div_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 12)(1, "div", 13)(2, "h2");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](3, "Aucun m\u00E9dicament scann\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](4, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](5, " Scannez l'\u00E9tiquette ou la bo\u00EEte d'un m\u00E9dicament pour obtenir des informations d\u00E9taill\u00E9es. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](6, "div", 14)(7, "ion-button", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function MedicamentOcrPage_div_11_Template_ion_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r1);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r1.openCamera());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](8, "ion-icon", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](9, MedicamentOcrPage_div_11_span_9_Template, 2, 0, "span", 17)(10, MedicamentOcrPage_div_11_span_10_Template, 2, 0, "span", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](11, "ion-button", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function MedicamentOcrPage_div_11_Template_ion_button_click_11_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r1);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r1.openGallery());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](12, "ion-icon", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](13, " Galerie ");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", !ctx_r1.isWeb);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx_r1.isWeb);
  }
}
function MedicamentOcrPage_div_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 20)(1, "h2", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](2, "Image scann\u00E9e");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](3, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](4, "img", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](5, "ion-button", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function MedicamentOcrPage_div_12_Template_ion_button_click_5_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r3);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r1.resetScan());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](6, "ion-icon", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](7, " Scanner \u00E0 nouveau ");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("src", ctx_r1.imagePreviewUrl, _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵsanitizeUrl"]);
  }
}
function MedicamentOcrPage_div_13_ion_card_4_ion_card_subtitle_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "ion-card-subtitle");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const suggestion_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](suggestion_r4.laboratory);
  }
}
function MedicamentOcrPage_div_13_ion_card_4_div_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 32)(1, "span", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](2, "Code Barre:");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](3, "span", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const suggestion_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](suggestion_r4.code_barre);
  }
}
function MedicamentOcrPage_div_13_ion_card_4_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 32)(1, "span", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](2, "PPV:");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](3, "span", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const suggestion_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate1"]("", suggestion_r4.ppv, " Dhs");
  }
}
function MedicamentOcrPage_div_13_ion_card_4_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 32)(1, "span", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](2, "Dosage:");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](3, "span", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const suggestion_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](suggestion_r4.dosage);
  }
}
function MedicamentOcrPage_div_13_ion_card_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "ion-card", 29)(1, "ion-card-header")(2, "ion-card-title");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](4, MedicamentOcrPage_div_13_ion_card_4_ion_card_subtitle_4_Template, 2, 1, "ion-card-subtitle", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](5, "ion-card-content")(6, "div", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](7, MedicamentOcrPage_div_13_ion_card_4_div_7_Template, 5, 1, "div", 31)(8, MedicamentOcrPage_div_13_ion_card_4_div_8_Template, 5, 1, "div", 31)(9, MedicamentOcrPage_div_13_ion_card_4_div_9_Template, 5, 1, "div", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const suggestion_r4 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtextInterpolate"](suggestion_r4.designation);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", suggestion_r4.laboratory);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", suggestion_r4.code_barre);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", suggestion_r4.ppv);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", suggestion_r4.dosage);
  }
}
function MedicamentOcrPage_div_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 26)(1, "h2", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](2, "Suggestions");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](3, "div", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](4, MedicamentOcrPage_div_13_ion_card_4_Template, 10, 5, "ion-card", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngForOf", ctx_r1.suggestions);
  }
}
function MedicamentOcrPage_div_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](1, "ion-icon", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](2, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](3, "Aucun r\u00E9sultat trouv\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](4, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](5, "Essayez de scanner \u00E0 nouveau ou v\u00E9rifiez que l'\u00E9tiquette est bien visible.");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
  }
}
class MedicamentOcrPage {
  constructor(navCtrl, apiService, loadingController, alertController, webSocketService, networkService, scannerService, toastController) {
    this.navCtrl = navCtrl;
    this.apiService = apiService;
    this.loadingController = loadingController;
    this.alertController = alertController;
    this.webSocketService = webSocketService;
    this.networkService = networkService;
    this.scannerService = scannerService;
    this.toastController = toastController;
    this.isConnected = true;
    this.progress = 0;
    this.isLoading = false;
    this.isFabOpen = false;
    this.hasScannedImage = false;
    this.suggestions = [];
    this.isWeb = false;
    this.TOGGLE_DELAY = 300;
    this.isAnimating = false;
    // Destroy subject for cleanup
    this.destroy$ = new rxjs__WEBPACK_IMPORTED_MODULE_10__.Subject();
    this.isWeb = src_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.platform === 'web';
  }
  ngOnInit() {
    this.initializeComponent();
  }
  ionViewWillEnter() {
    this.resetStates();
    this.initializeComponent();
  }
  ionViewWillLeave() {
    this.resetStates();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.jobId) {
      this.webSocketService.close(this.jobId);
    }
  }
  initializeComponent() {
    // Subscribe to the network status
    this.networkService.getNetworkStatus().subscribe(connected => {
      this.isConnected = connected;
    });
    this.resetStates();
  }
  resetStates() {
    this.isFabOpen = false;
    this.isLoading = false;
    this.isAnimating = false;
  }
  resetScan() {
    // When clicking "Scanner à nouveau", directly open the camera
    this.openCamera();
  }
  handleClickOutside(event) {
    var _this$fabButton;
    const fabElement = (_this$fabButton = this.fabButton) === null || _this$fabButton === void 0 ? void 0 : _this$fabButton.nativeElement;
    const clickedElement = event.target;
    if (this.isFabOpen && fabElement && !fabElement.contains(clickedElement)) {
      // Check if click was outside the FAB and its children
      if (!clickedElement.closest('ion-fab')) {
        this.onFabToggle();
      }
    }
  }
  onFabToggle() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      // Prevent toggle while animating
      if (_this.isAnimating) {
        return;
      }
      _this.isAnimating = true;
      _this.isFabOpen = !_this.isFabOpen;
      // Reset animation lock after transition completes
      setTimeout(() => {
        _this.isAnimating = false;
      }, _this.TOGGLE_DELAY);
    })();
  }
  triggerFabButtonClick() {
    try {
      var _this$fabButton2;
      if ((_this$fabButton2 = this.fabButton) !== null && _this$fabButton2 !== void 0 && _this$fabButton2.nativeElement) {
        this.fabButton.nativeElement.click();
      }
    } catch (err) {
      console.error('Error triggering fab button:', err);
      // Fallback behavior
      this.isFabOpen = !this.isFabOpen;
    }
  }
  openCamera() {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      // If FAB is open, close it
      if (_this2.isFabOpen) {
        _this2.onFabToggle();
      }
      // Reset previous scan data
      _this2.hasScannedImage = false;
      _this2.imagePreviewUrl = undefined;
      _this2.suggestions = [];
      if (_this2.isWeb) {
        // Launch scanner for web
        yield _this2.startScanning();
      } else {
        try {
          // Logic to open the camera and capture an image
          const image = yield _capacitor_camera__WEBPACK_IMPORTED_MODULE_1__.Camera.getPhoto({
            quality: 100,
            allowEditing: false,
            resultType: _capacitor_camera__WEBPACK_IMPORTED_MODULE_1__.CameraResultType.Uri,
            source: _capacitor_camera__WEBPACK_IMPORTED_MODULE_1__.CameraSource.Camera
          });
          if (image.webPath) {
            const response = yield fetch(image.webPath);
            const blob = yield response.blob();
            const file = new File([blob], 'captured-medicament.jpg', {
              type: 'image/jpeg'
            });
            _this2.imagePreviewUrl = image.webPath;
            _this2.hasScannedImage = true;
            yield _this2.processMedicamentImage(file);
          }
        } catch (error) {
          console.log(error);
          // Handle error appropriately
        }
      }
    })();
  }
  startScanning() {
    var _this3 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const loading = yield _this3.showLoading();
      try {
        const canScan = yield _this3.scannerService.launchScan();
        yield loading.dismiss();
        if (!canScan) {
          yield _this3.showErrorToast();
        }
      } catch (error) {
        yield loading.dismiss();
        yield _this3.showErrorToast();
      }
    })();
  }
  showLoading() {
    var _this4 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const loading = yield _this4.loadingController.create({
        message: 'Tentative de connexion au scanner...',
        spinner: 'circular',
        translucent: true,
        cssClass: 'custom-loading',
        duration: 10000 // Will auto-dismiss after 10 seconds
      });
      yield loading.present();
      return loading;
    })();
  }
  showErrorToast() {
    var _this5 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const toast = yield _this5.toastController.create({
        message: "L'application PostAgent n'est pas ouverte. Merci de l'ouvrir avant de prendre une photo.",
        duration: 3000,
        position: 'top',
        color: 'warning',
        buttons: [{
          text: 'OK',
          role: 'cancel'
        }]
      });
      yield toast.present();
    })();
  }
  openGallery() {
    var _this6 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      // If FAB is open, close it
      if (_this6.isFabOpen) {
        _this6.onFabToggle();
      }
      // Reset previous scan data
      _this6.hasScannedImage = false;
      _this6.imagePreviewUrl = undefined;
      _this6.suggestions = [];
      // Logic to open the gallery and select an image
      try {
        const image = yield _capacitor_camera__WEBPACK_IMPORTED_MODULE_1__.Camera.getPhoto({
          quality: 100,
          allowEditing: false,
          resultType: _capacitor_camera__WEBPACK_IMPORTED_MODULE_1__.CameraResultType.Uri,
          source: _capacitor_camera__WEBPACK_IMPORTED_MODULE_1__.CameraSource.Photos
        });
        if (image.webPath) {
          const response = yield fetch(image.webPath);
          const blob = yield response.blob();
          if (!_this6.verifySelectedImageBlobType(blob)) {
            return;
          }
          const file = new File([blob], `selected-medicament.${image.format}`, {
            type: image.format ? `image/${image.format}` : 'image/jpeg'
          });
          _this6.imagePreviewUrl = image.webPath;
          _this6.hasScannedImage = true;
          yield _this6.processMedicamentImage(file);
        }
      } catch (error) {
        console.log("getPhoto Error : ", error === null || error === void 0 ? void 0 : error.message);
      }
    })();
  }
  verifySelectedImageBlobType(blob) {
    //  should start with image/*
    const type = blob.type.split('/')[0];
    if (type !== 'image') {
      this.alertController.create({
        animated: true,
        header: 'Erreur',
        message: 'Veuillez sélectionner une image valide',
        buttons: ['OK']
      }).then(alert => alert.present());
      return false;
    }
    return true;
  }
  processMedicamentImage(file) {
    var _this7 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this7.jobId = _this7.apiService.generateJobId();
      const websocketUrl = `${src_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.webSocketUrl}/${_this7.jobId}`;
      _this7.webSocketService.connect(websocketUrl, _this7.jobId);
      // Subscribe to WebSocket messages for progress updates
      _this7.webSocketService.onMessage(_this7.jobId).subscribe(message => {
        if (message.progress !== undefined) {
          _this7.progress = message.progress;
          console.log('Progress:', _this7.progress);
        }
      });
      _this7.isLoading = true;
      _this7.suggestions = [];
      try {
        _this7.apiService.getMedicamentInfo(file, _this7.jobId).subscribe(result => {
          console.log('API response:', result);
          // Process the suggestions
          if (result && result.associations) {
            _this7.suggestions = result.associations.map(item => {
              return {
                designation: item.nom_base_winplus || 'Médicament inconnu',
                code_barre: item.code_barre_win || '',
                laboratory: '',
                ppv: item.prix_vente_base_winplus || 0,
                dosage: '',
                score: 0 // Not available in the response
              };
            });
          }
          _this7.isLoading = false;
          // Disconnect WebSocket after completion
          if (_this7.jobId) {
            _this7.webSocketService.close(_this7.jobId);
          }
        }, error => {
          console.error('Error processing medicament image:', error);
          _this7.isLoading = false;
          // Show error message
          _this7.showErrorAlert('Une erreur est survenue lors de l\'analyse de l\'image. Veuillez réessayer.');
          // Disconnect WebSocket
          if (_this7.jobId) {
            _this7.webSocketService.close(_this7.jobId);
          }
        });
      } catch (error) {
        console.error('Exception during API call:', error);
        _this7.isLoading = false;
        _this7.showErrorAlert('Une erreur inattendue est survenue. Veuillez réessayer.');
        if (_this7.jobId) {
          _this7.webSocketService.close(_this7.jobId);
        }
      }
    })();
  }
  showErrorAlert(message) {
    var _this8 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this8.alertController.create({
        header: 'Erreur',
        message: message,
        buttons: ['OK']
      });
      yield alert.present();
    })();
  }
  logout() {
    var _this9 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      yield _this9.apiService.logout();
      _this9.navCtrl.navigateRoot('/login');
    })();
  }
}
_MedicamentOcrPage = MedicamentOcrPage;
_MedicamentOcrPage.ɵfac = function MedicamentOcrPage_Factory(t) {
  return new (t || _MedicamentOcrPage)(_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_11__.NavController), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_services_api_service__WEBPACK_IMPORTED_MODULE_3__.ApiService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_12__.LoadingController), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_12__.AlertController), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_services_websocket_service__WEBPACK_IMPORTED_MODULE_4__.WebSocketService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_services_network_service__WEBPACK_IMPORTED_MODULE_5__.NetworkService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_services_scanner_service__WEBPACK_IMPORTED_MODULE_6__.ScannerService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_12__.ToastController));
};
_MedicamentOcrPage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineComponent"]({
  type: _MedicamentOcrPage,
  selectors: [["app-medicament-ocr"]],
  viewQuery: function MedicamentOcrPage_Query(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵviewQuery"](_c0, 5);
    }
    if (rf & 2) {
      let _t;
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵloadQuery"]()) && (ctx.fabButton = _t.first);
    }
  },
  hostBindings: function MedicamentOcrPage_HostBindings(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function MedicamentOcrPage_click_HostBindingHandler($event) {
        return ctx.handleClickOutside($event);
      }, false, _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresolveDocument"]);
    }
  },
  decls: 17,
  vars: 14,
  consts: [[3, "ngClass"], ["slot", "start"], ["routerLink", "/profile"], ["slot", "icon-only", "name", "menu-outline"], [1, "medicament-ocr-content", 3, "ngClass"], [1, "div-content"], ["class", "scan-wrapper", 4, "ngIf"], ["class", "scanned-image-container", 4, "ngIf"], ["class", "suggestions-container", 4, "ngIf"], ["class", "no-results", 4, "ngIf"], [1, "alert-progress", 3, "ngClass"], [3, "progress"], [1, "scan-wrapper"], [1, "content"], [1, "scan-buttons"], ["expand", "block", 1, "camera-button", 3, "click"], ["name", "camera-outline", "slot", "start"], [4, "ngIf"], ["expand", "block", 1, "gallery-button", 3, "click"], ["name", "images-outline", "slot", "start"], [1, "scanned-image-container"], [1, "section-title"], [1, "image-preview"], ["alt", "M\u00E9dicament scann\u00E9", 3, "src"], ["expand", "block", 1, "rescan-button", 3, "click"], ["name", "refresh-outline", "slot", "start"], [1, "suggestions-container"], [1, "suggestion-list"], ["class", "suggestion-card", 4, "ngFor", "ngForOf"], [1, "suggestion-card"], [1, "suggestion-details"], ["class", "detail-item", 4, "ngIf"], [1, "detail-item"], [1, "label"], [1, "value"], [1, "no-results"], ["name", "alert-circle-outline"]],
  template: function MedicamentOcrPage_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "ion-header", 0)(1, "ion-toolbar");
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](2, "ion-buttons", 1);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](3, "ion-title");
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](4, "Scanner M\u00E9dicament");
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](5, "ion-buttons", 1)(6, "ion-button", 2);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](7, "ion-icon", 3);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](8, "ion-content", 4)(9, "div", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](10, "app-check-network");
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](11, MedicamentOcrPage_div_11_Template, 14, 2, "div", 6)(12, MedicamentOcrPage_div_12_Template, 8, 1, "div", 7)(13, MedicamentOcrPage_div_13_Template, 5, 1, "div", 8)(14, MedicamentOcrPage_div_14_Template, 6, 0, "div", 9);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](15, "div", 10);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](16, "app-custom-alert", 11);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵpureFunction1"](8, _c1, ctx.isLoading));
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](8);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵpureFunction1"](10, _c1, ctx.isLoading));
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](3);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", !ctx.hasScannedImage);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx.hasScannedImage);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx.suggestions.length > 0);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx.hasScannedImage && ctx.suggestions.length === 0 && !ctx.isLoading);
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵpureFunction1"](12, _c1, ctx.isLoading));
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("progress", ctx.progress);
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_13__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_13__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_13__.NgIf, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonButtons, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonCard, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonCardContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonCardHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonCardSubtitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonCardTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonIcon, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.IonToolbar, _ionic_angular__WEBPACK_IMPORTED_MODULE_12__.RouterLinkDelegate, _angular_router__WEBPACK_IMPORTED_MODULE_14__.RouterLink, _check_network_check_network_component__WEBPACK_IMPORTED_MODULE_7__.CheckNetworkComponent, _custom_alert_custom_alert_component__WEBPACK_IMPORTED_MODULE_8__.CustomAlertComponent],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];[_nghost-%COMP%] {\n  height: 100dvh;\n}\n\n*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\nion-content[_ngcontent-%COMP%] {\n  --offset-top: 0px !important;\n}\n\nion-header[_ngcontent-%COMP%] {\n  height: 70px;\n  --border: 0;\n  display: flex;\n  align-items: center;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --border: 0;\n  --border-width: 0;\n  display: flex;\n  justify-content: center;\n  align-items: flex-end;\n  flex-direction: row;\n  --background: #fff !important;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%] {\n  font-size: 26px;\n  font-weight: 700;\n  color: #2f4fcd;\n  text-align: left;\n  width: 100%;\n  padding-left: 3.5rem;\n  padding-right: 0 !important;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  color: #101010;\n  padding-right: 1rem;\n}\n\n.scan-wrapper[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-top: 2rem;\n}\n\n  .scan-wrapper .medicine-scan-icon img {\n  width: 150px !important;\n  height: 150px !important;\n}\n\n  .scan-wrapper .arrow-bottom-icon img {\n  width: 100px !important;\n  height: 100px !important;\n}\n\n.scan-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\n  padding: 10px 50px 0 50px;\n}\n\n.scan-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 22px;\n  font-weight: 700;\n}\n\n.scan-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 12px;\n  text-align: center;\n  padding: 5px 10px;\n}\n\n.medicament-ocr-content[_ngcontent-%COMP%] {\n  --background: #ffffff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n}\n\n.div-content[_ngcontent-%COMP%] {\n  background: url(\"/assets/bg-scan-bl.png\") no-repeat center center fixed;\n  background-size: cover;\n  min-height: 100%;\n  padding-bottom: 80px;\n}\n\n.instructions[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.instruction-text[_ngcontent-%COMP%] {\n  font-size: 18px;\n  color: #433f3f;\n  margin-bottom: 10px;\n  font-weight: 600;\n  padding: 10px;\n}\n\n.section-title[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  margin-bottom: 10px;\n  color: #4b4b4b;\n  font-size: 18px;\n  margin-left: 16px;\n  font-weight: 600;\n}\n\n.scanned-image-container[_ngcontent-%COMP%] {\n  padding: 0 16px;\n}\n\n.image-preview[_ngcontent-%COMP%] {\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  margin-bottom: 16px;\n}\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  max-width: 100%;\n  max-height: 250px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.rescan-button[_ngcontent-%COMP%] {\n  --background: #2f4fcd;\n  margin: 0 auto 20px;\n  max-width: 300px;\n}\n\n.suggestions-container[_ngcontent-%COMP%] {\n  padding: 0 16px;\n}\n\n.suggestion-list[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\n.suggestion-card[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  border: 1px solid #e5ebfd;\n}\n\nion-card-header[_ngcontent-%COMP%] {\n  padding-bottom: 8px;\n}\n\nion-card-title[_ngcontent-%COMP%] {\n  font-size: 18px;\n  font-weight: 600;\n  color: #2f4fcd;\n}\n\nion-card-subtitle[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #666;\n}\n\n.suggestion-details[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.detail-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  padding: 4px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n.detail-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n.detail-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #555;\n}\n.detail-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\n  color: #333;\n}\n\n.no-results[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 30px 20px;\n}\n.no-results[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 64px;\n  color: #ccc;\n  margin-bottom: 16px;\n}\n.no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 20px;\n  color: #666;\n  margin-bottom: 8px;\n}\n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #888;\n  text-align: center;\n}\n\nion-fab[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n}\n\n  ion-fab-button.menu-button-middle::part(native) {\n  background: none;\n  border: 0;\n  box-shadow: none;\n  width: 100% !important;\n  color: #fff;\n}\n\n  .menu-button-middle {\n  background-color: #2f4fcd;\n  padding: 2px 12px;\n  border-radius: 14px;\n  width: 85px;\n  height: 60px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-bottom: 15px;\n}\n  .menu-button-middle app-custom-icon img {\n  width: 45px !important;\n  height: 45px !important;\n  color: #fff;\n}\n\n  ion-fab ion-fab-list {\n  display: flex;\n  flex-direction: row !important;\n  justify-content: space-around;\n  align-items: flex-end;\n  width: auto !important;\n  padding: 10px 20px;\n  margin-bottom: 100px;\n  height: 100vh;\n  --transition-duration: 300ms;\n}\n  ion-fab ion-fab-list.fab-active {\n  visibility: visible;\n  opacity: 1;\n  transform: translateY(0);\n  transition: all var(--transition-duration) ease-in-out;\n  pointer-events: all;\n}\n  ion-fab ion-fab-list.fab-active ion-fab-button {\n  transform: translateY(0);\n  opacity: 1;\n}\n  ion-fab ion-fab-list.fab-hidden {\n  visibility: hidden;\n  opacity: 0;\n  transform: translateY(20px);\n  transition: all var(--transition-duration) ease-in-out;\n  pointer-events: none;\n}\n  ion-fab ion-fab-list.fab-hidden ion-fab-button {\n  transform: translateY(40px);\n  opacity: 0;\n}\n  ion-fab ion-fab-list ion-fab-button {\n  transition: all var(--transition-duration) ease-in-out;\n}\n\n.content-fab-buttom[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n}\n\n  .content-fab-buttom app-custom-icon img {\n  width: 50px !important;\n  height: 50px !important;\n}\n\n  .content-fab-buttom ion-label {\n  color: #2f4fcd;\n  font-size: 20px;\n}\n\n  ion-fab ion-fab-list ion-fab-button {\n  padding: 10px;\n  width: 190px;\n  height: 135px;\n  transform: translateY(40px);\n  transition: all 0.6s ease-in-out;\n}\n  ion-fab ion-fab-list ion-fab-button.fab-active {\n  transform: translateY(0);\n}\n\n  ion-fab ion-fab-list ion-fab-button::part(native) {\n  border-radius: 16px !important;\n  --background: #f4f5f8 !important;\n}\n\n  .loading:not(.alert-progress) {\n  opacity: 0.5;\n  pointer-events: none;\n  --background: rgba(0, 0, 0, 0.1);\n}\n\n  .alert-progress {\n  position: absolute;\n  width: 100%;\n  top: 40%;\n  justify-content: center;\n  align-items: center;\n  display: none;\n}\n\n  .alert-progress.loading {\n  display: flex;\n}\n\n  .alert-progress app-custom-alert {\n  width: 90%;\n}\n\n.scan-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  margin-top: 20px;\n  padding: 0 20px;\n  width: 100%;\n}\n\n.camera-button[_ngcontent-%COMP%], .gallery-button[_ngcontent-%COMP%] {\n  --border-radius: 8px;\n  height: 48px;\n  font-weight: 500;\n}\n\n.camera-button[_ngcontent-%COMP%] {\n  --background: #4a90e2;\n  --background-activated: #3a80d2;\n}\n\n.gallery-button[_ngcontent-%COMP%] {\n  --background: #5c6bc0;\n  --background-activated: #4c5bb0;\n}\n\nion-card[_ngcontent-%COMP%] {\n  --background: #fff !important;\n}\n\n@media (prefers-color-scheme: dark) {\n  .bg-hide[_ngcontent-%COMP%] {\n    --background: rgba(255, 255, 255, 0.9);\n  }\n}\n@media (prefers-color-scheme: light) {\n  .bg-hide[_ngcontent-%COMP%] {\n    --background: rgba(0, 0, 0, 0.1);\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ })

}]);
//# sourceMappingURL=src_app_medicament-ocr_medicament-ocr_module_ts.js.map