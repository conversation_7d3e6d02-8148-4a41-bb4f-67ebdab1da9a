"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["node_modules_ionic_pwa-elements_dist_esm_pwa-camera-modal_entry_js"],{

/***/ 69577:
/*!*****************************************************************************!*\
  !*** ./node_modules/@ionic/pwa-elements/dist/esm/pwa-camera-modal.entry.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   pwa_camera_modal: () => (/* binding */ PWACameraModal)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index-1c5c47b4.js */ 54787);


const cameraModalCss = ":host{z-index:1000;position:fixed;top:0;left:0;width:100%;height:100%;display:-ms-flexbox;display:flex;contain:strict}.wrapper{-ms-flex:1;flex:1;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;background-color:rgba(0, 0, 0, 0.15)}.content{-webkit-box-shadow:0px 0px 5px rgba(0, 0, 0, 0.2);box-shadow:0px 0px 5px rgba(0, 0, 0, 0.2);width:600px;height:600px}";
const PWACameraModal = class {
  constructor(hostRef) {
    (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.r)(this, hostRef);
    this.onPhoto = (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.c)(this, "onPhoto", 7);
    this.noDeviceError = (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.c)(this, "noDeviceError", 7);
    this.facingMode = 'user';
    this.hidePicker = false;
  }
  present() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const camera = document.createElement('pwa-camera-modal-instance');
      camera.facingMode = _this.facingMode;
      camera.hidePicker = _this.hidePicker;
      camera.addEventListener('onPhoto', /*#__PURE__*/function () {
        var _ref = (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* (e) {
          if (!_this._modal) {
            return;
          }
          const photo = e.detail;
          _this.onPhoto.emit(photo);
        });
        return function (_x) {
          return _ref.apply(this, arguments);
        };
      }());
      camera.addEventListener('noDeviceError', /*#__PURE__*/function () {
        var _ref2 = (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* (e) {
          _this.noDeviceError.emit(e);
        });
        return function (_x2) {
          return _ref2.apply(this, arguments);
        };
      }());
      document.body.append(camera);
      _this._modal = camera;
    })();
  }
  dismiss() {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_this2._modal) {
        return;
      }
      _this2._modal && _this2._modal.parentNode.removeChild(_this2._modal);
      _this2._modal = null;
    })();
  }
  render() {
    return (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.h)("div", null);
  }
};
PWACameraModal.style = cameraModalCss;


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_pwa-elements_dist_esm_pwa-camera-modal_entry_js.js.map