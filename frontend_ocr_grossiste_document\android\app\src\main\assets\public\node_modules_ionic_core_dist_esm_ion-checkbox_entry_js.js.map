{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-checkbox_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAClB;AACX;AACnB;AAE7D,MAAMgB,cAAc,GAAG,wzKAAwzK;AAC/0K,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,aAAa,GAAG,2sLAA2sL;AACjuL,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjBrB,qDAAgB,CAAC,IAAI,EAAEqB,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAGpB,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACqB,QAAQ,GAAGrB,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACsB,OAAO,GAAGtB,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACuB,OAAO,GAAI,UAASC,WAAW,EAAG,EAAC;IACxC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAIC,KAAK,IAAK;MACzB,MAAMC,SAAS,GAAI,IAAI,CAACC,OAAO,GAAGF,KAAM;MACxC,IAAI,CAACP,SAAS,CAACU,IAAI,CAAC;QAChBD,OAAO,EAAED,SAAS;QAClBG,KAAK,EAAE,IAAI,CAACA;MAChB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,aAAa,GAAIC,EAAE,IAAK;MACzBA,EAAE,CAACC,cAAc,CAAC,CAAC;MACnB,IAAI,CAACC,QAAQ,CAAC,CAAC;MACf,IAAI,CAACT,UAAU,CAAC,CAAC,IAAI,CAACG,OAAO,CAAC;MAC9B,IAAI,CAACO,aAAa,GAAG,KAAK;IAC9B,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAChB,QAAQ,CAACS,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACQ,MAAM,GAAG,MAAM;MAChB,IAAI,CAAChB,OAAO,CAACQ,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACS,OAAO,GAAIN,EAAE,IAAK;MACnB,IAAI,IAAI,CAACO,QAAQ,EAAE;QACf;MACJ;MACA,IAAI,CAACR,aAAa,CAACC,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,CAACQ,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACpB,OAAO;IACxB,IAAI,CAACM,OAAO,GAAG,KAAK;IACpB,IAAI,CAACO,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACI,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACT,KAAK,GAAG,IAAI;IACjB,IAAI,CAACa,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,SAAS,GAAG,QAAQ;EAC7B;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACtB,mBAAmB,GAAGuB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3C,uDAAqB,CAAC,IAAI,CAAC4C,EAAE,CAAC,CAAC;EAChF;EACAf,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACgB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC,CAAC;IACxB;EACJ;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEZ,KAAK;MAAEZ,OAAO;MAAEW,QAAQ;MAAEU,EAAE;MAAEI,UAAU;MAAElB,aAAa;MAAEX,mBAAmB;MAAEF,OAAO;MAAEsB,OAAO;MAAED,cAAc;MAAED,IAAI;MAAEZ,KAAK;MAAEe;IAAW,CAAC,GAAG,IAAI;IACxJ,MAAMS,IAAI,GAAG3C,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4C,IAAI,GAAGF,UAAU,CAACC,IAAI,EAAEnB,aAAa,CAAC;IAC5C7B,uDAAiB,CAAC,IAAI,EAAE2C,EAAE,EAAEP,IAAI,EAAEd,OAAO,GAAGE,KAAK,GAAG,EAAE,EAAES,QAAQ,CAAC;IACjE,OAAQvC,qDAAC,CAACE,iDAAI,EAAE;MAAEsD,GAAG,EAAE,0CAA0C;MAAE,cAAc,EAAErB,aAAa,GAAG,OAAO,GAAI,GAAEP,OAAQ,EAAC;MAAE6B,KAAK,EAAEjD,qDAAkB,CAACgC,KAAK,EAAE;QACpJ,CAACc,IAAI,GAAG,IAAI;QACZ,SAAS,EAAE7C,qDAAW,CAAC,UAAU,EAAEwC,EAAE,CAAC;QACtC,kBAAkB,EAAErB,OAAO;QAC3B,mBAAmB,EAAEW,QAAQ;QAC7B,wBAAwB,EAAEJ,aAAa;QACvCuB,WAAW,EAAE,IAAI;QACjB,CAAE,oBAAmBd,OAAQ,EAAC,GAAG,IAAI;QACrC,CAAE,sBAAqBC,SAAU,EAAC,GAAG,IAAI;QACzC,CAAE,4BAA2BF,cAAe,EAAC,GAAG;MACpD,CAAC,CAAC;MAAEL,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,EAAEtC,qDAAC,CAAC,OAAO,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAmB,CAAC,EAAEzD,qDAAC,CAAC,OAAO,EAAE+C,MAAM,CAACC,MAAM,CAAC;MAAEQ,GAAG,EAAE,0CAA0C;MAAEG,IAAI,EAAE,UAAU;MAAE/B,OAAO,EAAEA,OAAO,GAAG,IAAI,GAAGa,SAAS;MAAEF,QAAQ,EAAEA,QAAQ;MAAEqB,EAAE,EAAEtC,OAAO;MAAEuC,QAAQ,EAAE,IAAI,CAAC9B,aAAa;MAAEK,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAEyB,GAAG,EAAGZ,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,EAAE1B,mBAAmB,CAAC,CAAC,EAAExB,qDAAC,CAAC,KAAK,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAC1f,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAER,EAAE,CAACc,WAAW,KAAK;MACpD,CAAC;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAAEhE,qDAAC,CAAC,MAAM,EAAE;MAAEwD,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAExD,qDAAC,CAAC,KAAK,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAiB,CAAC,EAAEzD,qDAAC,CAAC,KAAK,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE,eAAe;MAAEQ,OAAO,EAAE,WAAW;MAAED,IAAI,EAAE;IAAY,CAAC,EAAET,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3T;EACAF,UAAUA,CAACC,IAAI,EAAEnB,aAAa,EAAE;IAC5B,IAAIoB,IAAI,GAAGpB,aAAa,GAAInC,qDAAC,CAAC,MAAM,EAAE;MAAEF,CAAC,EAAE,aAAa;MAAEkE,IAAI,EAAE;IAAO,CAAC,CAAC,GAAKhE,qDAAC,CAAC,MAAM,EAAE;MAAEF,CAAC,EAAE,2BAA2B;MAAEkE,IAAI,EAAE;IAAO,CAAC,CAAE;IAC1I,IAAIV,IAAI,KAAK,IAAI,EAAE;MACfC,IAAI,GAAGpB,aAAa,GAAInC,qDAAC,CAAC,MAAM,EAAE;QAAEF,CAAC,EAAE,UAAU;QAAEkE,IAAI,EAAE;MAAO,CAAC,CAAC,GAAKhE,qDAAC,CAAC,MAAM,EAAE;QAAEF,CAAC,EAAE,kCAAkC;QAAEkE,IAAI,EAAE;MAAO,CAAC,CAAE;IAC9I;IACA,OAAOT,IAAI;EACf;EACA,IAAIN,EAAEA,CAAA,EAAG;IAAE,OAAO7C,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,IAAImB,WAAW,GAAG,CAAC;AACnBP,QAAQ,CAACkD,KAAK,GAAG;EACbC,GAAG,EAAEtD,oBAAoB;EACzBuD,EAAE,EAAErD;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-checkbox.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-c71c5417.js';\nimport { i as inheritAriaAttributes, d as renderHiddenInput } from './helpers-da915de8.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b9c0d1da.js';\n\nconst checkboxIosCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #0054e9);--border-color-checked:var(--ion-color-primary, #0054e9);--checkmark-color:var(--ion-color-primary-contrast, #fff);--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;width:var(--size);height:var(--size);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:50%;--border-width:0.125rem;--border-style:solid;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.23);--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--size:min(1.375rem, 55.836px);--checkmark-width:1.5px}:host(.checkbox-disabled){opacity:0.3}\";\nconst IonCheckboxIosStyle0 = checkboxIosCss;\n\nconst checkboxMdCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #0054e9);--border-color-checked:var(--ion-color-primary, #0054e9);--checkmark-color:var(--ion-color-primary-contrast, #fff);--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;width:var(--size);height:var(--size);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:calc(var(--size) * .125);--border-width:2px;--border-style:solid;--border-color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--checkmark-width:3;--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--transition:background 180ms cubic-bezier(0.4, 0, 0.2, 1);--size:18px}.checkbox-icon path{stroke-dasharray:30;stroke-dashoffset:30}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{stroke-dashoffset:0;-webkit-transition:stroke-dashoffset 90ms linear 90ms;transition:stroke-dashoffset 90ms linear 90ms}:host(.checkbox-disabled) .label-text-wrapper{opacity:0.38}:host(.checkbox-disabled) .native-wrapper{opacity:0.63}\";\nconst IonCheckboxMdStyle0 = checkboxMdCss;\n\nconst Checkbox = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inputId = `ion-cb-${checkboxIds++}`;\n        this.inheritedAttributes = {};\n        /**\n         * Sets the checked property and emits\n         * the ionChange event. Use this to update the\n         * checked state in response to user-generated\n         * actions such as a click.\n         */\n        this.setChecked = (state) => {\n            const isChecked = (this.checked = state);\n            this.ionChange.emit({\n                checked: isChecked,\n                value: this.value,\n            });\n        };\n        this.toggleChecked = (ev) => {\n            ev.preventDefault();\n            this.setFocus();\n            this.setChecked(!this.checked);\n            this.indeterminate = false;\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.onClick = (ev) => {\n            if (this.disabled) {\n                return;\n            }\n            this.toggleChecked(ev);\n        };\n        this.color = undefined;\n        this.name = this.inputId;\n        this.checked = false;\n        this.indeterminate = false;\n        this.disabled = false;\n        this.value = 'on';\n        this.labelPlacement = 'start';\n        this.justify = 'space-between';\n        this.alignment = 'center';\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    render() {\n        const { color, checked, disabled, el, getSVGPath, indeterminate, inheritedAttributes, inputId, justify, labelPlacement, name, value, alignment, } = this;\n        const mode = getIonMode(this);\n        const path = getSVGPath(mode, indeterminate);\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        return (h(Host, { key: '0ac95890562c7f035704c40959c69f8c8ca4bc9f', \"aria-checked\": indeterminate ? 'mixed' : `${checked}`, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'checkbox-checked': checked,\n                'checkbox-disabled': disabled,\n                'checkbox-indeterminate': indeterminate,\n                interactive: true,\n                [`checkbox-justify-${justify}`]: true,\n                [`checkbox-alignment-${alignment}`]: true,\n                [`checkbox-label-placement-${labelPlacement}`]: true,\n            }), onClick: this.onClick }, h(\"label\", { key: '3f9f7c8383dded8f7997086b25399d052df76b5c', class: \"checkbox-wrapper\" }, h(\"input\", Object.assign({ key: '6fb11d06c424c289357d5d7c1a4d1b967be231d0', type: \"checkbox\", checked: checked ? true : undefined, disabled: disabled, id: inputId, onChange: this.toggleChecked, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl) }, inheritedAttributes)), h(\"div\", { key: 'f577a272e5e3f9f1852fc95e40466c80b76309c7', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': el.textContent === '',\n            }, part: \"label\" }, h(\"slot\", { key: '7c9b0b4513e797a1acdf55a5f286563e5f397e9c' })), h(\"div\", { key: 'e47c50a078b8d761ddc5efcb9a9635281b5818f6', class: \"native-wrapper\" }, h(\"svg\", { key: '4dca47179ae15e9094e01c799ef4ed25fbb0d840', class: \"checkbox-icon\", viewBox: \"0 0 24 24\", part: \"container\" }, path)))));\n    }\n    getSVGPath(mode, indeterminate) {\n        let path = indeterminate ? (h(\"path\", { d: \"M6 12L18 12\", part: \"mark\" })) : (h(\"path\", { d: \"M5.9,12.5l3.8,3.8l8.8-8.8\", part: \"mark\" }));\n        if (mode === 'md') {\n            path = indeterminate ? (h(\"path\", { d: \"M2 12H22\", part: \"mark\" })) : (h(\"path\", { d: \"M1.73,12.91 8.1,19.28 22.79,4.59\", part: \"mark\" }));\n        }\n        return path;\n    }\n    get el() { return getElement(this); }\n};\nlet checkboxIds = 0;\nCheckbox.style = {\n    ios: IonCheckboxIosStyle0,\n    md: IonCheckboxMdStyle0\n};\n\nexport { Checkbox as ion_checkbox };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "inheritAriaAttributes", "renderHiddenInput", "c", "createColorClasses", "hostContext", "b", "getIonMode", "checkboxIosCss", "IonCheckboxIosStyle0", "checkboxMdCss", "IonCheckboxMdStyle0", "Checkbox", "constructor", "hostRef", "ionChange", "ionFocus", "ionBlur", "inputId", "checkboxIds", "inheritedAttributes", "setChecked", "state", "isChecked", "checked", "emit", "value", "toggleChecked", "ev", "preventDefault", "setFocus", "indeterminate", "onFocus", "onBlur", "onClick", "disabled", "color", "undefined", "name", "labelPlacement", "justify", "alignment", "componentWillLoad", "Object", "assign", "el", "focusEl", "focus", "render", "getSV<PERSON>ath", "mode", "path", "key", "class", "interactive", "type", "id", "onChange", "ref", "textContent", "part", "viewBox", "style", "ios", "md", "ion_checkbox"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}