{"version": 3, "file": "main.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAC0E;AACpB;AACW;AACP;;;AAE1D,MAAMK,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,qKAAkC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACC,iBAAiB;CACtF,EACD;EACEJ,IAAI,EAAE,EAAE;EACRK,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;CACZ,EACD;EACEN,IAAI,EAAE,YAAY;EAClBC,YAAY,EAAEA,CAAA,KAAM,iLAAwC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACI,oBAAoB,CAAC;EAC/FC,WAAW,EAAE,CAACX,0EAAe,EAAEC,mEAAW;CAC3C,EACD;EACEE,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,qKAAkC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,CAAC;EACtFI,WAAW,EAAE,CAACV,mEAAW;CAC1B,EACD;EACEE,IAAI,EAAE,OAAO;EACbC,YAAY,EAAEA,CAAA,KAAM,6JAA8B,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACM,eAAe,CAAC;EAChFD,WAAW,EAAE,CAACV,mEAAW;CAC1B,EACD;EACEE,IAAI,EAAE,OAAO;EACbC,YAAY,EAAEA,CAAA,KAAM,6OAA8B,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACO,eAAe,CAAC;EAChFF,WAAW,EAAE,CAACZ,+DAAS;CACxB,EACD;EACEI,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,qZAAkC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACQ,gBAAgB,CAAC;EACrFH,WAAW,EAAE,CAACZ,+DAAS;CACxB,EACD;EACEI,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KAAM,0RAAoC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACS,iBAAiB,CAAC;EACxFJ,WAAW,EAAE,CAACZ,+DAAS;CACxB,EACD;EACEI,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,sRAAkC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACU,gBAAgB,CAAC;EACrFL,WAAW,EAAE,CAACZ,+DAAS;CACxB,EACD;EACEI,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KAAM,0RAAoC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACW,iBAAiB,CAAC;EACxFN,WAAW,EAAE,CAACZ,+DAAS;CACxB,EACD;EACEI,IAAI,EAAE,aAAa;EACnBC,YAAY,EAAEA,CAAA,KAAM,sSAA0C,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACY,oBAAoB,CAAC;EACjGP,WAAW,EAAE,CAACZ,+DAAS;CACxB,EACD;EACEI,IAAI,EAAE,eAAe;EACrBC,YAAY,EAAEA,CAAA,KAAM,6LAA8C,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACa,sBAAsB;CACtG,EACD;EACEhB,IAAI,EAAE,eAAe;EACrBC,YAAY,EAAEA,CAAA,KAAM,6LAA8C,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACc,sBAAsB;CACvG,EACD;EACEjB,IAAI,EAAE,mBAAmB;EACzBC,YAAY,EAAEA,CAAA,KAAM,6RAAsD,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACe,0BAA0B,CAAC;EACnHV,WAAW,EAAE,CAACZ,+DAAS;CACxB,EACD;EACEI,IAAI,EAAE,iBAAiB;EACvBC,YAAY,EAAEA,CAAA,KAAM,sTAAkD,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACgB,uBAAuB;CAC5G,EACD;EACEnB,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,oKAAkC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACiB,iBAAiB;CACtF,EACD;EACEpB,IAAI,EAAE,gBAAgB;EACtBC,YAAY,EAAEA,CAAA,KAAM,gZAAgD,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACkB,uBAAuB;CAC1G,CAMF;AAQK,MAAOC,gBAAgB;oBAAhBA,gBAAgB;;mBAAhBA,iBAAgB;AAAA;;QAAhBA;AAAgB;;YAJzB3B,yDAAY,CAAC4B,OAAO,CAACxB,MAAM,EAAE;IAAEyB,kBAAkB,EAAE9B,8DAAAA;EAAiB,CAAE,CAAC,EAE/DC,yDAAY;AAAA;;sHAEX2B,gBAAgB;IAAAG,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFjBhC,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AChGyB;AACS;AAIA,CAAC;;;;;;;;;;AAE3DiC,+DAAQ,EAAE;AAOJ,MAAOG,YAAY;EAcvBC,YAAoBC,cAA8B,EAAUC,MAAc,EAAUC,UAAsB,EAASC,QAAkB;IAAjH,KAAAH,cAAc,GAAdA,cAAc;IAA0B,KAAAC,MAAM,GAANA,MAAM;IAAkB,KAAAC,UAAU,GAAVA,UAAU;IAAqB,KAAAC,QAAQ,GAARA,QAAQ;IAZ3H;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,KAAAN,WAAW,GAAGA,kEAAW;IAKvB,IAAI,CAACO,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,IAAI,CAACD,QAAQ,CAACE,KAAK,EAAE,CAACpC,IAAI,CAAC,MAAK;MAC9B,IAAI,CAACqC,cAAc,EAAE;MACrB,IAAI,CAACC,+BAA+B,EAAE;IACxC,CAAC,CAAC;EACJ;EAEQD,cAAcA,CAAA;IACpB;IACAE,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC;IACtCH,QAAQ,CAACI,eAAe,CAACF,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC;IAEjD;IACAH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACG,GAAG,CAAC,OAAO,CAAC;IACpCL,QAAQ,CAACI,eAAe,CAACF,SAAS,CAACG,GAAG,CAAC,OAAO,CAAC;IAE/C;IACAL,QAAQ,CAACC,IAAI,CAACK,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IAEjD;IACA,MAAMC,IAAI,GAAGP,QAAQ,CAACQ,aAAa,CAAC,MAAM,CAAC;IAC3CD,IAAI,CAACE,IAAI,GAAG,cAAc;IAC1BF,IAAI,CAACG,OAAO,GAAG,OAAO;IACtBV,QAAQ,CAACW,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC;EACjC;EAEMM,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACZ;MACAD,KAAI,CAAChB,cAAc,EAAE;MAErB;MACA,MAAMgB,KAAI,CAACtB,cAAc,CAACwB,IAAI,EAAE;MAEhC;MACA,MAAMC,iBAAiB,SAASH,KAAI,CAACtB,cAAc,CAAC0B,GAAG,CAAC,mBAAmB,CAAC;MAG5E,IAAI,CAACD,iBAAiB,EAAE;QACtB;QACAH,KAAI,CAACrB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;MAGvC;MACAL,KAAI,CAACrB,MAAM,CAAC2B,MAAM,CAACC,SAAS,CAACC,KAAK,IAAG;QACnC,IAAIA,KAAK,YAAYlC,4DAAe,EAAE;UACpC,IAAI,CAAC0B,KAAI,CAACpB,UAAU,CAAC6B,UAAU,EAAE,IAAI,CAACT,KAAI,CAACU,aAAa,CAACF,KAAK,CAACG,GAAG,CAAC,EAAE;YACnEX,KAAI,CAACrB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;;;MAGxC,CAAC,CAAC;IAAC;EACL;EAEAK,aAAaA,CAACC,GAAW;IACvB,MAAMC,YAAY,GAAG,CAAC,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;IAC9F,OAAOA,YAAY,CAACC,QAAQ,CAACF,GAAG,CAAC;EACnC;EAGc1B,+BAA+BA,CAAA;IAAA,IAAA6B,MAAA;IAAA,OAAAb,6OAAA;MAC3C,IAAI,CAAC1B,kEAAW,CAACwC,UAAU,EAAE;QAC3BC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE,MAAMH,MAAI,CAACpC,cAAc,CAACwC,KAAK,EAAE,CAAC,CAAC;;IACpC;EACH;;gBAjFW1C,YAAY;;mBAAZA,aAAY,EAAA2C,+DAAA,CAAAhD,qEAAA,GAAAgD,+DAAA,CAAAG,mDAAA,GAAAH,+DAAA,CAAAK,6DAAA,GAAAL,+DAAA,CAAAO,oDAAA;AAAA;;QAAZlD,aAAY;EAAAoD,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCfzBf,4DAAA,iBAAyE;MACvEA,uDAAA,wBAAuC;MACzCA,0DAAA,EAAU;;;MAFDA,wDAAA,YAAAA,6DAAA,IAAAsB,GAAA,EAAAN,GAAA,CAAA5D,WAAA,CAAAM,QAAA,YAA+D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCd;AACL;AAEY;AAElB;AACS;AAEU;AAEN;AAEQ;AACX;AACgB;AACb;AACM;;;;;AA0B5D,MAAOwE,SAAS;EACpB5E,YAAoB6E,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;IAC3BR,gFAAoB,CAACS,MAAM,CAAC;IAC5B;EACF;;aAJWF,SAAS;;mBAATA,UAAS,EAAAlC,sDAAA,CAAAhD,mEAAA;AAAA;;QAATkF,UAAS;EAAAK,SAAA,GAFRlF,wDAAY;AAAA;;aAVb,CACT;IAAEmF,OAAO,EAAEhB,+DAAkB;IAAEiB,QAAQ,EAAEf,8DAAkBA;EAAA,CAAE,EAC7D;IAAEc,OAAO,EAAEV,oEAAiB;IAAEW,QAAQ,EAAER,2EAAe;IAAES,KAAK,EAAE;EAAI,CAAG,EACvE;IAAEF,OAAO,EAAEV,oEAAiB;IAAEW,QAAQ,EAAEV,kFAAoB;IAAEW,KAAK,EAAE;EAAI,CAAE,EAC3EV,qEAAc,EACd;IACEQ,OAAO,EAAE,WAAW;IACpBG,QAAQ,EAAE;GACX,CACF;EAAA5F,OAAA,GAfCwE,oEAAa,EACbE,wDAAW,CAAC5E,OAAO,CAAE;IAAE+F,IAAI,EAAE,KAAK;IAAEC,yBAAyB,EAAE;EAAI,CAAE,CAAC,EACtEjG,iEAAgB,EAChBgF,uEAAkB,CAAC/E,OAAO,EAAE,EAC5BgF,mEAAgB;AAAA;;sHAcPK,SAAS;IAAAY,YAAA,GApBLzF,wDAAY;IAAAN,OAAA,GAEzBwE,oEAAa,EAAApB,wDAAA,EAEbvD,iEAAgB,EAAAyD,uEAAA,EAEhBwB,mEAAgB;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;AC1BmB;AACN;;;AAK3B,MAAO3G,SAAS;EAEpBoC,YAAoBE,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErC1B,WAAWA,CAAA;IACT,MAAMmH,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMzF,QAAQ,GAAGwF,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAEhD;IACA,IAAI,CAACzF,QAAQ,EAAE;MACb,IAAI,CAACF,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;MAClC,OAAO,KAAK;;IAGd,IAAIxB,QAAQ,KAAK,YAAY,EAAE;MAC7B;MACA,IAAIuF,SAAS,IAAII,UAAU,EAAE;QAC3B,IAAI;UACF,MAAMC,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAACN,SAAS,CAAC;UAC7D,MAAMO,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAACF,UAAU,CAAC;UAE/D,IAAIC,gBAAgB,IAAIE,iBAAiB,EAAE;YACzC,OAAO,IAAI;;SAEd,CAAC,OAAOC,KAAK,EAAE;UACd5D,OAAO,CAAC4D,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;;KAGpD,MAAM,IAAI/F,QAAQ,KAAK,eAAe,EAAE;MACvC;MACA,IAAIuF,SAAS,IAAIG,WAAW,IAAIC,UAAU,EAAE;QAC1C,IAAI;UACF,MAAMC,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAACN,SAAS,CAAC;UAC7D,MAAMS,kBAAkB,GAAG,IAAI,CAACH,oBAAoB,CAACH,WAAW,CAAC;UACjE,MAAMI,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAACF,UAAU,CAAC;UAE/D,IAAIC,gBAAgB,IAAII,kBAAkB,IAAIF,iBAAiB,EAAE;YAC/D,OAAO,IAAI;;SAEd,CAAC,OAAOC,KAAK,EAAE;UACd5D,OAAO,CAAC4D,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;;;IAKrD;IACA,IAAI,CAACjG,MAAM,CAAC0B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAChC,OAAO,KAAK;EACd;EAEQqE,oBAAoBA,CAACI,KAAa;IACxC,MAAMC,YAAY,GAAQb,qDAAS,CAACY,KAAK,CAAC;IAC1C,MAAME,UAAU,GAAGb,mCAAM,CAAC,CAAAY,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,GAAG,IAAG,IAAI,CAAC;IACnD,OAAOd,mCAAM,CAAC,IAAIe,IAAI,EAAE,CAAC,GAAGF,UAAU;EACxC;;aAxDW3I,SAAS;;mBAATA,UAAS,EAAA8E,sDAAA,CAAAhD,mDAAA;AAAA;;SAAT9B,UAAS;EAAA8I,OAAA,EAAT9I,UAAS,CAAA+I,IAAA;EAAAC,UAAA,EAFR;AAAM;;;;;;;;;;;;;;;;;;;;;ACL0B;AACP;AACK;;;AAYtC,MAAOjC,eAAe;EAE1B3E,YAAoBG,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAgB;EAE9C4G,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD,MAAMtB,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMzF,QAAQ,GAAGwF,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAEhD,IAAIzF,QAAQ,KAAK,YAAY,EAAE;MAC7B;MACA,IAAIuF,SAAS,IAAII,UAAU,EAAE;QAC3B,IAAI,CAAC,IAAI,CAAC5F,UAAU,CAAC6B,UAAU,EAAE,EAAE;UACjC,IAAI,CAACkF,MAAM,EAAE;UACb,OAAOL,gDAAU,CAAC,MAAM,IAAIM,KAAK,CAAC,mBAAmB,CAAC,CAAC;;QAGzD,MAAMC,MAAM,GAAGJ,GAAG,CAACK,KAAK,CAAC;UACvBC,OAAO,EAAEN,GAAG,CAACM,OAAO,CACjBC,GAAG,CAAC,eAAe,EAAE,UAAUxB,UAAU,EAAE,CAAC,CAC5CwB,GAAG,CAAC,mBAAmB,EAAE,cAAcC,IAAI,CAACC,KAAK,CAAC9B,SAAS,CAAC,CAAC+B,WAAW,EAAE;SAC9E,CAAC;QACF,OAAOT,IAAI,CAACU,MAAM,CAACP,MAAM,CAAC,CAACQ,IAAI,CAC7Bd,0DAAU,CAAEX,KAAK,IAAI;UACnB,IAAIA,KAAK,CAAC0B,MAAM,KAAK,GAAG,IAAI1B,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;YAChD,IAAI,CAACX,MAAM,EAAE;;UAEf,OAAOL,gDAAU,CAAC,MAAMV,KAAK,CAAC;QAChC,CAAC,CAAC,CACH;;KAEJ,MAAM,IAAI/F,QAAQ,KAAK,eAAe,EAAE;MACvC;MACA,IAAIuF,SAAS,IAAIG,WAAW,IAAIC,UAAU,EAAE;QAC1C,IAAI,CAAC,IAAI,CAAC5F,UAAU,CAAC6B,UAAU,EAAE,EAAE;UACjC,IAAI,CAACkF,MAAM,EAAE;UACb,OAAOL,gDAAU,CAAC,MAAM,IAAIM,KAAK,CAAC,mBAAmB,CAAC,CAAC;;QAGzD,MAAMC,MAAM,GAAGJ,GAAG,CAACK,KAAK,CAAC;UACvBC,OAAO,EAAEN,GAAG,CAACM,OAAO,CACjBC,GAAG,CAAC,eAAe,EAAE,UAAUxB,UAAU,EAAE,CAAC,CAC5CwB,GAAG,CAAC,qBAAqB,EAAE,gBAAgBC,IAAI,CAACC,KAAK,CAAC3B,WAAW,CAAC,CAAC4B,WAAW,EAAE,CAAC,CACjFH,GAAG,CAAC,mBAAmB,EAAE,cAAcC,IAAI,CAACC,KAAK,CAAC9B,SAAS,CAAC,CAAC+B,WAAW,EAAE;SAC9E,CAAC;QACF,OAAOT,IAAI,CAACU,MAAM,CAACP,MAAM,CAAC,CAACQ,IAAI,CAC7Bd,0DAAU,CAAEX,KAAK,IAAI;UACnB,IAAIA,KAAK,CAAC0B,MAAM,KAAK,GAAG,IAAI1B,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;YAChD,IAAI,CAACX,MAAM,EAAE;;UAEf,OAAOL,gDAAU,CAAC,MAAMV,KAAK,CAAC;QAChC,CAAC,CAAC,CACH;;;IAIL,OAAOc,IAAI,CAACU,MAAM,CAACX,GAAG,CAAC;EACzB;EAEAc,cAAcA,CAACzB,KAAa;IAC1B,MAAMC,YAAY,GAAQb,qDAAS,CAACY,KAAK,CAAC;IAC1C,MAAM0B,cAAc,GAAG,IAAItB,IAAI,CAACH,YAAY,CAACE,GAAG,GAAG,IAAI,CAAC;IACxD,OAAOuB,cAAc,GAAG,IAAItB,IAAI,EAAE;EACpC;EAIAS,MAAMA,CAAA;IACJtB,YAAY,CAACoC,UAAU,CAAC,WAAW,CAAC;IACpCpC,YAAY,CAACoC,UAAU,CAAC,aAAa,CAAC;IACtCpC,YAAY,CAACoC,UAAU,CAAC,OAAO,CAAC;IAChCpC,YAAY,CAACoC,UAAU,CAAC,aAAa,CAAC;IACtCpC,YAAY,CAACoC,UAAU,CAAC,SAAS,CAAC;IAClC;IACA;EACF;;mBA5EWrD,eAAe;;mBAAfA,gBAAe,EAAAjC,sDAAA,CAAAhD,6DAAA;AAAA;;SAAfiF,gBAAe;EAAA+B,OAAA,EAAf/B,gBAAe,CAAAgC;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;ACdwB;AACG;AAGxB;;;;AAGzB,MAAOlC,oBAAoB;EAE/BzE,YAAoBoI,OAAsB,EAAUC,cAA8B;IAA9D,KAAAD,OAAO,GAAPA,OAAO;IAAyB,KAAAC,cAAc,GAAdA,cAAc;EAAmB;EAErFtB,SAASA,CAACuB,OAAyB,EAAErB,IAAiB;IACpD,OAAOgB,0CAAI,CAAC,IAAI,CAACI,cAAc,CAACE,WAAW,EAAE,CAAC,CAACX,IAAI,CACjDM,yDAAS,CAACK,WAAW,IAAG;MACtB,IAAI,CAACA,WAAW,EAAE;QAChB;QACA,IAAI,CAACH,OAAO,CAACI,YAAY,CAAC,gBAAgB,CAAC;QAC3C;QACA,OAAO3B,gDAAU,CAAC;UAAEgB,MAAM,EAAE,CAAC;UAAEY,OAAO,EAAE;QAAwB,CAAE,CAAC;;MAErE;MACA,OAAOxB,IAAI,CAACU,MAAM,CAACW,OAAO,CAAC,CAACV,IAAI,CAC9Bd,0DAAU,CAAEX,KAAwB,IAAI;QACtC,IAAIA,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;UACxB;UAEAM,uDAAS,CAAC;YACRQ,KAAK,EAAE,cAAc;YACrBC,IAAI,EAAE,sDAAsD;YAC5DC,IAAI,EAAE,OAAO;YACbC,iBAAiB,EAAE;WACpB,CAAC,CAAC5K,IAAI,CAAC,MAAK;YACX,IAAI,CAACkK,OAAO,CAACI,YAAY,CAAC,QAAQ,CAAC;UACrC,CAAC,CAAC;;QAEJ;QACA,OAAO3B,gDAAU,CAACV,KAAK,CAAC;MAC1B,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;;wBAlCW1B,oBAAoB;;mBAApBA,qBAAoB,EAAA/B,sDAAA,CAAAhD,yDAAA,GAAAgD,sDAAA,CAAAG,qEAAA;AAAA;;SAApB4B,qBAAoB;EAAAiC,OAAA,EAApBjC,qBAAoB,CAAAkC;AAAA;;;;;;;;;;;;;;;;;;;;;;;;ACF3B,MAAO9I,eAAe;EAC1BmC,YAAoBC,cAA8B,EAAUC,MAAc;IAAtD,KAAAD,cAAc,GAAdA,cAAc;IAA0B,KAAAC,MAAM,GAANA,MAAM;EAAW;EAEvE1B,WAAWA,CAAA;IAAA,IAAA+C,KAAA;IAAA,OAAAC,6OAAA;MACf,MAAME,iBAAiB,SAASH,KAAI,CAACtB,cAAc,CAAC0B,GAAG,CAAC,mBAAmB,CAAC;MAC5E,IAAID,iBAAiB,EAAE;QACrBH,KAAI,CAACrB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC,OAAO,KAAK;;MAEd,OAAO,IAAI;IAAC;EACd;;mBAVW/D,eAAe;;mBAAfA,gBAAe,EAAA6E,sDAAA,CAAAhD,qEAAA,GAAAgD,sDAAA,CAAAG,mDAAA;AAAA;;SAAfhF,gBAAe;EAAA6I,OAAA,EAAf7I,gBAAe,CAAA8I,IAAA;EAAAC,UAAA,EAFd;AAAM;;;;;;;;;;;;;;;;;;;;;ACFmB;AACN;;;AAK3B,MAAO9I,WAAW;EAEtBkC,YAAoBE,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErC1B,WAAWA,CAAA;IACT,MAAMmH,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAEhD,IAAIF,SAAS,IAAIG,WAAW,IAAIC,UAAU,EAAE;MAC1C,IAAI;QACF;QACA,MAAMC,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAACN,SAAS,CAAC;QAC7D,MAAMS,kBAAkB,GAAG,IAAI,CAACH,oBAAoB,CAACH,WAAW,CAAC;QACjE,MAAMI,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAACF,UAAU,CAAC;QAE/D,IAAIC,gBAAgB,IAAII,kBAAkB,IAAIF,iBAAiB,EAAE;UAC/D;UACA,IAAI,CAAChG,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;UAClC,OAAO,KAAK;;OAEf,CAAC,OAAOuE,KAAK,EAAE;QACd5D,OAAO,CAAC4D,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;;IAInD;IACA,OAAO,IAAI;EACb;EAEQF,oBAAoBA,CAACI,KAAa;IACxC,MAAMC,YAAY,GAAQb,qDAAS,CAACY,KAAK,CAAC;IAC1C,MAAME,UAAU,GAAGb,mCAAM,CAAC,CAAAY,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,GAAG,IAAG,IAAI,CAAC;IACnD,OAAOd,mCAAM,CAAC,IAAIe,IAAI,EAAE,CAAC,GAAGF,UAAU;EACxC;;eAlCWzI,WAAW;;mBAAXA,YAAW,EAAA4E,sDAAA,CAAAhD,mDAAA;AAAA;;SAAX5B,YAAW;EAAA4I,OAAA,EAAX5I,YAAW,CAAA6I,IAAA;EAAAC,UAAA,EAFV;AAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACN8D;AACnC;AACc;AAKD;AAG7B;AACQ;;;;AAEvC,IAAKsC,OAGJ;AAHD,WAAKA,OAAO;EACVA,OAAA,yBAAqB;EACrBA,OAAA,uCAAmC;AACrC,CAAC,EAHIA,OAAO,KAAPA,OAAO;AAQN,MAAOlG,UAAU;EAIrBhD,YAAoBmJ,IAAgB,EAAUC,eAAgC;IAA1D,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,eAAe,GAAfA,eAAe;IAHrD,KAAAC,OAAO,GAAGvJ,kEAAW,CAACwJ,MAAM,CAAC,CAAE;IAKvC;IACQ,KAAAC,WAAW,GAAG;MACpBjC,OAAO,EAAE,IAAI0B,6DAAW,CAAC;QACvB,cAAc,EAAE;OACjB;KACF;EAPkF;EASnF;EACAQ,aAAaA,CAAA;IACX,OAAO,MAAM,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACzD;EAEQC,UAAUA,CAAC3H,GAAW,EAAE4H,MAAc,EAAEpJ,IAAU;IACxD6B,OAAO,CAACC,GAAG,CAAC,YAAYsH,MAAM,IAAI5H,GAAG,EAAE,EAAExB,IAAI,CAAC;EAChD;EAEQqJ,WAAWA,CAACC,QAAa;IAC/BzH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEwH,QAAQ,CAAC;EACpC;EAEQC,WAAWA,CAAC9D,KAAwB;IAC1C5D,OAAO,CAAC4D,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAOU,gDAAU,CAACV,KAAK,CAAC;EAC1B;EAEA;EACA+D,OAAOA,CAAA;IACL,OAAO,IAAI,CAACf,IAAI,CAACxH,GAAG,CAAC,GAAG,IAAI,CAAC0H,OAAO,GAAG,CAAC;EAC1C;EAEC;EACDc,WAAWA,CAAC7B,OAA2B;IACrC,MAAMpG,GAAG,GAAG,GAAG,IAAI,CAACmH,OAAO,eAAe;IAC1C,IAAI,CAACQ,UAAU,CAAC3H,GAAG,EAAE,MAAM,EAAEoG,OAAO,CAAC;IACrC,OAAO,IAAI,CAACa,IAAI,CAACiB,IAAI,CAAsBlI,GAAG,EAAEoG,OAAO,EAAE,IAAI,CAACiB,WAAW,CAAC,CAAC3B,IAAI,CAC7EqB,mDAAG,CAAC,IAAI,CAACc,WAAW,CAAC,EACrBjD,0DAAU,CAAC,IAAI,CAACmD,WAAW,CAAC,CAC7B;EACH;EAEA;EACAI,SAASA,CAAC/B,OAAqB,EAAEgC,WAAmB;IAClD,MAAMpI,GAAG,GAAG,GAAG,IAAI,CAACmH,OAAO,QAAQ;IACnC,MAAM/B,OAAO,GAAG,IAAI0B,6DAAW,CAAC;MAC9B,cAAc,EAAE,kBAAkB;MAClC,qBAAqB,EAAE,gBAAgBsB,WAAW;KACnD,CAAC;IACF,MAAMC,aAAa,GAAG;MAAE,GAAGjC,OAAO;MAAEkC,YAAY,EAAEF;IAAW,CAAE;IAC/D,IAAI,CAACT,UAAU,CAAC3H,GAAG,EAAE,MAAM,EAAEqI,aAAa,CAAC;IAC3C,OAAO,IAAI,CAACpB,IAAI,CAACiB,IAAI,CAAgBlI,GAAG,EAAEqI,aAAa,EAAE;MAAEjD;IAAO,CAAE,CAAC,CAACM,IAAI,CACxEqB,mDAAG,CAAC,IAAI,CAACc,WAAW,CAAC,EACrBjD,0DAAU,CAAC,IAAI,CAACmD,WAAW,CAAC,CAC7B;EACH;EAEA;EACAQ,eAAeA,CAACnC,OAA+B;IAC7C,MAAMpG,GAAG,GAAG,GAAG,IAAI,CAACmH,OAAO,mBAAmB;IAC9C,IAAI,CAACQ,UAAU,CAAC3H,GAAG,EAAE,MAAM,EAAEoG,OAAO,CAAC;IACrC,OAAO,IAAI,CAACa,IAAI,CAACiB,IAAI,CAA0BlI,GAAG,EAAEoG,OAAO,EAAE,IAAI,CAACiB,WAAW,CAAC,CAAC3B,IAAI,CACjFqB,mDAAG,CAAC,IAAI,CAACc,WAAW,CAAC,EACrBjD,0DAAU,CAAC,IAAI,CAACmD,WAAW,CAAC,CAC7B;EACH;EAEA;EACM/C,MAAMA,CAAA;IAAA,IAAA3F,KAAA;IAAA,OAAAC,6OAAA;MACV,OAAO,IAAIkJ,OAAO;QAAA,IAAAC,IAAA,GAAAnJ,6OAAA,CAAC,WAAOoJ,OAAO,EAAI;UACnC,MAAMC,KAAK,SAAStJ,KAAI,CAAC6H,eAAe,CAAC0B,MAAM,CAAC;YAC9CC,MAAM,EAAE,aAAa;YACrBtC,OAAO,EAAE,4BAA4B;YACrCuC,OAAO,EAAE,CACP;cACEpC,IAAI,EAAE,SAAS;cACfqC,IAAI,EAAE,QAAQ;cACdC,QAAQ,EAAE,4BAA4B;cACtCC,OAAO,EAAEA,CAAA,KAAK;gBACZ5I,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;gBAC7BoI,OAAO,EAAE,CAAC,CAAC;cACb;aACD,EACD;cACEhC,IAAI,EAAE,KAAK;cACXsC,QAAQ,EAAE,4BAA4B;cACtCC,OAAO,EAAEA,CAAA,KAAK;gBACZvF,YAAY,CAACoC,UAAU,CAAC,WAAW,CAAC;gBACpCpC,YAAY,CAACoC,UAAU,CAAC,aAAa,CAAC;gBACtCpC,YAAY,CAACoC,UAAU,CAAC,OAAO,CAAC;gBAChCpC,YAAY,CAACoC,UAAU,CAAC,SAAS,CAAC;gBAClCpC,YAAY,CAACoC,UAAU,CAAC,qBAAqB,CAAC;gBAC9CpC,YAAY,CAACoC,UAAU,CAAC,kBAAkB,CAAC;gBAC3C4C,OAAO,EAAE,CAAC,CAAC;cACb;aACD;WAEJ,CAAC;UAEF,MAAMC,KAAK,CAACO,OAAO,EAAE;QACvB,CAAC;QAAA,iBAAAC,EAAA;UAAA,OAAAV,IAAA,CAAAW,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACL;EAEA;EACAC,SAASA,CAACC,KAAW,EAAEC,KAAa,EAAEC,SAAkB;IACtDpJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC6G,OAAO,CAAC;IAE1C,MAAMuC,QAAQ,GAAa,IAAIC,QAAQ,EAAE;IACzCD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEL,KAAK,CAAC;IAC/BG,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEJ,KAAK,CAAC,CAAC,CAAE;IACnCE,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEH,SAAS,CAAChC,QAAQ,EAAE,CAAC,CAAC,CAAE;IACrD,MAAMzH,GAAG,GAAG,GAAG,IAAI,CAACmH,OAAO,cAAc;IACzC,IAAI,CAACQ,UAAU,CAAC3H,GAAG,EAAE,MAAM,EAAE0J,QAAQ,CAAC;IACtC,OAAO,IAAI,CAACzC,IAAI,CAACiB,IAAI,CAAMlI,GAAG,EAAE0J,QAAQ,CAAC,CAAChE,IAAI,CAC5CqB,mDAAG,CAAC,IAAI,CAACc,WAAW,CAAC,EACrBjD,0DAAU,CAAC,IAAI,CAACmD,WAAW,CAAC,CAC7B;EACH;EAEA;EACA8B,cAAcA,CAACN,KAAW,EAAEO,SAAiB,EAAEC,QAAiB,EAAEP,KAAc;IAC9E,MAAME,QAAQ,GAAa,IAAIC,QAAQ,EAAE;IACzCD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEL,KAAK,CAAC;IAC/BG,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEE,SAAS,CAAC;IACxCJ,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEJ,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE,CAAC,CAAC,CAAE;IACzC,IAAIO,QAAQ,EAAE;MACZL,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEG,QAAQ,CAAC;;IAGxC,OAAO,IAAI,CAAC9C,IAAI,CAACiB,IAAI,CAAM,GAAG,IAAI,CAACf,OAAO,oBAAoB,EAAEuC,QAAQ,CAAC;EAC3E;EAEA;EACAM,gBAAgBA,CAACT,KAAW,EAAEO,SAAiB,EAAEC,QAAiB,EAAEP,KAAc;IAChF,MAAME,QAAQ,GAAa,IAAIC,QAAQ,EAAE;IACzCD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEL,KAAK,CAAC;IAC/BG,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEE,SAAS,CAAC;IACxCJ,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEJ,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE,CAAC,CAAC,CAAE;IACzC,IAAIO,QAAQ,EAAE;MACZL,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEG,QAAQ,CAAC;;IAGxC,OAAO,IAAI,CAAC9C,IAAI,CAACiB,IAAI,CAAM,GAAG,IAAI,CAACf,OAAO,qBAAqB,EAAEuC,QAAQ,CAAC;EAC5E;EAEA;EACAO,gBAAgBA,CAAC7D,OAAgC,EAAEoD,KAAa;IAC9D;IACA,MAAMU,eAAe,GAAG;MAAE,GAAG9D,OAAO;MAAE+D,MAAM,EAAEX;IAAK,CAAE,CAAC,CAAE;IACxD,OAAO,IAAI,CAACvC,IAAI,CAACiB,IAAI,CAAM,GAAG,IAAI,CAACf,OAAO,sBAAsB,EAAE+C,eAAe,EAAE,IAAI,CAAC7C,WAAW,CAAC;EACtG;EAEA;EACA+C,eAAeA,CAACC,MAAmB,EAAEb,KAAa,EAAEc,SAAiB,EAAEC,OAAA,GAAmBvD,OAAO,CAACwD,QAAQ;IACxG,MAAMC,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAACnB,KAAK,KAAK;MAAE,GAAGA,KAAK;MAAEY,MAAM,EAAEX;IAAK,CAAE,CAAC,CAAC;IAEzE;IACA,MAAMmB,MAAM,GAAGjH,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,IAAI,eAAe;IAEjE;IACA,MAAMiH,WAAW,GAAG;MAClBP,MAAM,EAAEI,cAAc;MACtBI,QAAQ,EAAEN,OAAO,CAACO,WAAW,EAAE;MAC/BC,OAAO,EAAEJ,MAAM,CAAC;KACjB;IAED,OAAO,IAAI,CAAC1D,IAAI,CAACiB,IAAI,CAAC,GAAG,IAAI,CAACf,OAAO,qBAAqB,EAAEyD,WAAW,EAAE,IAAI,CAACvD,WAAW,CAAC;EAC5F;EAEA;EACA2D,cAAcA,CAACC,IAAY,EAAEtF,MAAc,EAAEuF,aAAqB,EAAEC,eAAuB,EAAEC,aAAqB;IAChH,OAAO,IAAI,CAACnE,IAAI,CAACoE,GAAG,CAAC,GAAG,IAAI,CAAClE,OAAO,yBAAyB8D,IAAI,EAAE,EAAE;MAAEtF,MAAM;MAAEuF,aAAa;MAAEC,eAAe;MAAEC;IAAa,CAAE,CAAC;EACjI;EAEA;EACAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACrE,IAAI,CAACxH,GAAG,CAAC,GAAG,IAAI,CAAC0H,OAAO,YAAY,CAAC;EACnD;EAEAoE,cAAcA,CAAChF,OAAe;IAC5B,MAAMiF,gBAAgB,GAAGjF,OAAO,KAAK,EAAE,GAAGA,OAAO,GAAG,oFAAoF;IAExIN,uDAAS,CAAC;MACRU,IAAI,EAAE,OAAO;MACbF,KAAK,EAAE,iCAAiC;MACxCgF,IAAI,EAAED,gBAAgB;MACtBE,MAAM,EAAE,8DAA8D;MACtEC,iBAAiB,EAAE,KAAK;MACxBC,eAAe,EAAE,IAAI;MACrBC,WAAW,EAAE;QACXC,WAAW,EAAE,qBAAqB;QAClCC,KAAK,EAAE,cAAc;QACrBL,MAAM,EAAE,eAAe,CAAc;;KAExC,CAAC;IAEF;IACA;IACAhI,YAAY,CAACoC,UAAU,CAAC,kBAAkB,CAAC;EAG7C;EAEAhG,UAAUA,CAAA;IACR,MAAMqE,KAAK,GAAGT,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMF,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAMzF,QAAQ,GAAGwF,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAEhD;IACA,IAAIzF,QAAQ,KAAK,YAAY,EAAE;MAC7B;MACA,IAAI,CAACiG,KAAK,IAAI,CAACV,SAAS,EAAE;QACxB,OAAO,KAAK;;KAEf,MAAM;MACL;MACA,IAAI,CAACU,KAAK,IAAI,CAACV,SAAS,IAAI,CAACG,WAAW,EAAE;QACxC,OAAO,KAAK;;;IAIhB,IAAI;MACF,MAAMQ,YAAY,GAAG,IAAI,CAAC4H,WAAW,CAAC7H,KAAK,CAAC;MAC5C,IAAI,CAAC,IAAI,CAAC8H,YAAY,CAAC7H,YAAY,CAAC,EAAE;QACpC,OAAO,KAAK;;KAEf,CAAC,OAAO8H,CAAC,EAAE;MACV7L,OAAO,CAAC4D,KAAK,CAAC,uBAAuB,EAAEiI,CAAC,CAAC;MACzC,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEQF,WAAWA,CAAC7H,KAAa;IAC/B,OAAOZ,qDAAS,CAACY,KAAK,CAAC;EACzB;EAEQ8H,YAAYA,CAAC7H,YAAiB;IACpC;IACA,MAAM+H,WAAW,GAAG5E,IAAI,CAAC6E,KAAK,CAAC7H,IAAI,CAAC8H,GAAG,EAAE,GAAG,IAAI,CAAC;IACjD,IAAIjI,YAAY,CAACE,GAAG,GAAG6H,WAAW,EAAE;MAClC,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAIA;EAEA;;;;;;EAMAG,iBAAiBA,CAAC/C,KAAW,EAAEC,KAAa;IAC1CnJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC6G,OAAO,CAAC;IAE1C,MAAMuC,QAAQ,GAAa,IAAIC,QAAQ,EAAE;IACzCD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEL,KAAK,CAAC;IAC/BG,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEJ,KAAK,CAAC,CAAC,CAAE;IAEnC,MAAMxJ,GAAG,GAAG,GAAG,IAAI,CAACmH,OAAO,sBAAsB;IACjD9G,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEN,GAAG,CAAC;IAEtC,OAAO,IAAI,CAACiH,IAAI,CAACiB,IAAI,CAAMlI,GAAG,EAAE0J,QAAQ,CAAC;EAC3C;;cAlRW5I,UAAU;;mBAAVA,WAAU,EAAAN,sDAAA,CAAAhD,4DAAA,GAAAgD,sDAAA,CAAAG,2DAAA;AAAA;;SAAVG,WAAU;EAAA0D,OAAA,EAAV1D,WAAU,CAAA2D,IAAA;EAAAC,UAAA,EAFT;AAAM;;;;;;;;;;;;;;;;;;;;;;;;;ACnByB;AACN;AAC4B;;AAK7D,MAAOlC,cAAc;EAGzB1E,YAAA;IAFQ,KAAAgP,aAAa,GAAG,IAAIJ,iDAAe,CAAU,IAAI,CAAC;IA0B1D,KAAAK,SAAS,GAAGJ,2CAAK,CACfC,+CAAS,CAAChK,MAAM,EAAE,QAAQ,CAAC,CAAC8C,IAAI,CAACgF,yCAAG,CAAC,MAAM,IAAI,CAAC,CAAC,EACjDkC,+CAAS,CAAChK,MAAM,EAAE,SAAS,CAAC,CAAC8C,IAAI,CAACgF,yCAAG,CAAC,MAAM,KAAK,CAAC,CAAC,EACnD,IAAImC,4CAAU,CAAEG,GAAsB,IAAI;MACxCA,GAAG,CAACjI,IAAI,CAACkI,SAAS,CAACC,MAAM,CAAC;MAC1BF,GAAG,CAACG,QAAQ,EAAE;IAChB,CAAC,CAAC,CACH;IA9BC,IAAI,CAACC,yBAAyB,EAAE;EAClC;EAEcA,yBAAyBA,CAAA;IAAA,IAAA/N,KAAA;IAAA,OAAAC,6OAAA;MACrC,MAAMqG,MAAM,SAAS8G,uDAAO,CAACY,SAAS,EAAE;MACxChO,KAAI,CAACyN,aAAa,CAAC/H,IAAI,CAACY,MAAM,CAAC2H,SAAS,CAAC;MACzCjN,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqF,MAAM,CAAC2H,SAAS,CAAC;MAExDb,uDAAO,CAACc,WAAW,CAAC,qBAAqB,EAAG5H,MAAM,IAAI;QACpDtF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqF,MAAM,CAAC2H,SAAS,CAAC;QACxDjO,KAAI,CAACyN,aAAa,CAAC/H,IAAI,CAACY,MAAM,CAAC2H,SAAS,CAAC;MAC3C,CAAC,CAAC;IAAC;EACL;EAEOE,gBAAgBA,CAAA;IACrB,OAAO,IAAI,CAACV,aAAa,CAACW,YAAY,EAAE;EAC1C;EAEapH,WAAWA,CAAA;IAAA,OAAA/G,6OAAA;MACtB,MAAMqG,MAAM,SAAS8G,uDAAO,CAACY,SAAS,EAAE;MACxC,OAAO1H,MAAM,CAAC2H,SAAS;IAAC;EAC1B;;kBAzBW9K,cAAc;;mBAAdA,eAAc;AAAA;;SAAdA,eAAc;EAAAgC,OAAA,EAAdhC,eAAc,CAAAiC,IAAA;EAAAC,UAAA,EAFb;AAAM;;;;;;;;;;;;;;;;;;;;;;ACAd,MAAOhE,cAAc;EACzB5C,YAAoB4P,OAAgB;IAAhB,KAAAA,OAAO,GAAPA,OAAO;EAAY;EAEjCnO,IAAIA,CAAA;IAAA,IAAAF,KAAA;IAAA,OAAAC,6OAAA;MACR,MAAMD,KAAI,CAACqO,OAAO,CAAC9E,MAAM,EAAE;IAAC;EAC9B;EAEMvD,GAAGA,CAACsI,GAAW,EAAEC,KAAU;IAAA,IAAAzN,MAAA;IAAA,OAAAb,6OAAA;MAC/B,MAAMa,MAAI,CAACuN,OAAO,CAACrI,GAAG,CAACsI,GAAG,EAAEC,KAAK,CAAC;IAAC;EACrC;EAEMnO,GAAGA,CAACkO,GAAW;IAAA,IAAAE,MAAA;IAAA,OAAAvO,6OAAA;MACnB,MAAMsO,KAAK,SAASC,MAAI,CAACH,OAAO,CAACjO,GAAG,CAACkO,GAAG,CAAC;MACzC,OAAOC,KAAK;IAAC;EACf;EAEMrN,KAAKA,CAAA;IAAA,IAAAuN,MAAA;IAAA,OAAAxO,6OAAA;MACTe,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtC,MAAMwN,MAAI,CAACJ,OAAO,CAACnN,KAAK,EAAE,CAAC,CAAC;IAAA;EAC9B;;kBAnBWG,cAAc;;mBAAdA,eAAc,EAAAF,sDAAA,CAAAhD,2DAAA;AAAA;;SAAdkD,eAAc;EAAA8D,OAAA,EAAd9D,eAAc,CAAA+D,IAAA;EAAAC,UAAA,EAFb;AAAM;;;;;;;;;;;;;;;ACJpB;AACA;AACA;AAEO,MAAM9G,WAAW,GAAG;EACzBwC,UAAU,EAAE,IAAI;EAChBlC,QAAQ,EAAE,QAAQ;EAGlB;EACA;EACA;EAEA;EACA;EACA;EAEA8P,YAAY,EAAE,uCAAuC;EACrD5G,MAAM,EAAE,sCAAsC;EAC9C6G,oBAAoB,EAAE;EAEtB;EACA;EACA;EAEA;EACA;EACA;EAGA;EACA;EACA;CACD;AAED;;;;;;;AAOA;;;;;;;;;;;;;;;;;;AC1C+C;AAGF;AACY;AACR;AAEjD,IAAIrQ,kEAAW,CAACwC,UAAU,EAAE;EAC1B8N,6DAAc,EAAE;;AAGlBxQ,+DAAQ,EAAE;AAEVyQ,sEAAA,EAAwB,CAACE,eAAe,CAAC3L,sDAAS,CAAC,CAChD4L,KAAK,CAACC,GAAG,IAAIlO,OAAO,CAACC,GAAG,CAACiO,GAAG,CAAC,CAAC;;;;;;;;;;ACdjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;;;;;;;;;;ACnPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;;;;;;;;;;ACtCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;;;;;;;;;;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["./src/app/app-routing.module.ts", "./src/app/app.component.ts", "./src/app/app.component.html", "./src/app/app.module.ts", "./src/app/interceptors/auth.guard.ts", "./src/app/interceptors/auth.interceptor.ts", "./src/app/interceptors/http-error.service.ts", "./src/app/interceptors/onboardin.guard.ts", "./src/app/interceptors/public.guard.ts", "./src/app/services/api.service.ts", "./src/app/services/network.service.ts", "./src/app/services/storage.service.ts", "./src/environments/environment.ts", "./src/main.ts", "./node_modules/@ionic/core/dist/esm/ lazy ^\\.\\/.*\\.entry\\.js$ include: \\.entry\\.js$ exclude: \\.system\\.entry\\.js$ namespace object", "./node_modules/@ionic/pwa-elements/dist/esm/ lazy ^\\.\\/.*\\.entry\\.js$ include: \\.entry\\.js$ exclude: \\.system\\.entry\\.js$ namespace object", "./node_modules/@stencil/core/internal/client/ lazy ^\\.\\/.*\\.entry\\.js.*$ include: \\.entry\\.js$ exclude: \\.system\\.entry\\.js$ strict namespace object", "./node_modules/moment/locale/ sync ^\\.\\/.*$"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { PreloadAllModules, RouterModule, Routes } from '@angular/router';\r\nimport { AuthGuard } from './interceptors/auth.guard';\r\nimport { OnboardingGuard } from './interceptors/onboardin.guard';\r\nimport { PublicGuard } from './interceptors/public.guard';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'home',\r\n    loadChildren: () => import('./welcome/welcome.module').then( m => m.WelcomePageModule)\r\n  },\r\n  {\r\n    path: '',\r\n    redirectTo: 'onboarding',\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'onboarding',\r\n    loadChildren: () => import('./onboarding/onboarding.module').then( m => m.OnboardingPageModule),\r\n    canActivate: [OnboardingGuard, PublicGuard]\r\n  },\r\n  {\r\n    path: 'welcome',\r\n    loadChildren: () => import('./welcome/welcome.module').then( m => m.WelcomePageModule),\r\n    canActivate: [PublicGuard]\r\n  },\r\n  {\r\n    path: 'login',\r\n    loadChildren: () => import('./login/login.module').then( m => m.LoginPageModule),\r\n    canActivate: [PublicGuard]\r\n  },\r\n  {\r\n    path: 'guide',\r\n    loadChildren: () => import('./guide/guide.module').then( m => m.GuidePageModule),\r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'scan-bl',\r\n    loadChildren: () => import('./scan-bl/scan-bl.module').then( m => m.ScanBLPageModule), \r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'doc-list',\r\n    loadChildren: () => import('./doc-list/doc-list.module').then( m => m.DocListPageModule), \r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'data-bl',\r\n    loadChildren: () => import('./data-bl/data-bl.module').then( m => m.DataBLPageModule), \r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'crop-doc',\r\n    loadChildren: () => import('./crop-doc/crop-doc.module').then( m => m.CropDocPageModule), \r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'process-doc',\r\n    loadChildren: () => import('./process-doc/process-doc.module').then( m => m.ProcessDocPageModule), \r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'network-error',\r\n    loadChildren: () => import('./network-error/network-error.module').then(m => m.NetworkErrorPageModule)\r\n  },\r\n  {\r\n    path: 'request-error',\r\n    loadChildren: () => import('./request-error/request-error.module').then( m => m.RequestErrorPageModule)\r\n  },\r\n  {\r\n    path: 'realtime-contours',\r\n    loadChildren: () => import('./realtime-contours/realtime-contours.module').then( m => m.RealtimeContoursPageModule),\r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'data-bl-success',\r\n    loadChildren: () => import('./data-bl-success/data-bl-success.module').then( m => m.DataBlSuccessPageModule)\r\n  },\r\n  {\r\n    path: 'profile',\r\n    loadChildren: () => import('./profile/profile.module').then( m => m.ProfilePageModule)\r\n  },\r  {\n    path: 'medicament-ocr',\n    loadChildren: () => import('./medicament-ocr/medicament-ocr.module').then( m => m.MedicamentOcrPageModule)\n  }\n\n\r\n\r\n\r\n\r\n];\r\n\r\n@NgModule({\r\n  imports: [\r\n    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })\r\n  ],\r\n  exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule { }\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { register } from 'swiper/element/bundle';\r\nimport { Router, NavigationStart } from '@angular/router';\r\nimport { ApiService } from './services/api.service';\r\nimport { Platform } from '@ionic/angular';\r\nimport { StorageService } from './services/storage.service';\r\nimport { environment } from '../environments/environment'; // Import the environment\r\n\r\nregister();\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: 'app.component.html',\r\n  styleUrls: ['app.component.scss'],\r\n})\r\nexport class AppComponent implements OnInit {\r\n  \r\n  // constructor(private networkService: NetworkService, private navCtrl: NavController) {\r\n  //   this.networkService.getNetworkStatus().subscribe((connected: boolean) => {\r\n  //     if (!connected) {\r\n  //       this.navCtrl.navigateRoot('/network-error');\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  environment = environment;\r\n\r\n  \r\n\r\n  constructor(private storageService: StorageService, private router: Router, private apiService: ApiService,private platform: Platform) {\r\n    this.initializeApp();\r\n  }\r\n\r\n  initializeApp() {\r\n    this.platform.ready().then(() => {\r\n      this.forceLightMode();\r\n      this.checkEnvironmentAndClearStorage();\r\n    });\r\n  }\r\n\r\n  private forceLightMode() {\r\n    // Remove dark mode from body\r\n    document.body.classList.remove('dark');\r\n    document.documentElement.classList.remove('dark');\r\n    \r\n    // Add light mode\r\n    document.body.classList.add('light');\r\n    document.documentElement.classList.add('light');\r\n\r\n    // Set attribute\r\n    document.body.setAttribute('data-theme', 'light');\r\n    \r\n    // Force color scheme\r\n    const meta = document.createElement('meta');\r\n    meta.name = 'color-scheme';\r\n    meta.content = 'light';\r\n    document.head.appendChild(meta);\r\n  }\r\n\r\n  async ngOnInit() {\r\n    // Ensure light mode is applied\r\n    this.forceLightMode();\r\n  \r\n    // Initialize storage\r\n    await this.storageService.init();\r\n  \r\n    // Check if the user has seen onboarding\r\n    const hasSeenOnboarding = await this.storageService.get('hasSeenOnboarding');\r\n\r\n\r\n    if (!hasSeenOnboarding) {\r\n      // Navigate to the onboarding screen if not seen before\r\n      this.router.navigate(['/onboarding']);\r\n    }\r\n  \r\n    // Handle route redirection for logged-in users\r\n    this.router.events.subscribe(event => {\r\n      if (event instanceof NavigationStart) {\r\n        if (!this.apiService.isLoggedIn() && !this.isPublicRoute(event.url)) {\r\n          this.router.navigate(['/welcome']);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  isPublicRoute(url: string): boolean {\r\n    const publicRoutes = ['/login', '/onboarding', '/welcome', '/network-error', '/request-error'];\r\n    return publicRoutes.includes(url);\r\n  }\r\n\r\n\r\n  private async checkEnvironmentAndClearStorage() {\r\n    if (!environment.production) {\r\n      console.log('Non-production environment detected. Clearing storage...');\r\n      await this.storageService.clear(); // Clear all storage\r\n    }\r\n  }\r\n}\r\n", "<ion-app [ngClass]=\"{'web-environment': environment.platform === 'web'}\">\n  <ion-router-outlet></ion-router-outlet>\n</ion-app>\n", "import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { RouteReuseStrategy } from '@angular/router';\r\n\r\nimport { IonicModule, IonicRouteStrategy } from '@ionic/angular';\r\n\r\nimport { AppComponent } from './app.component';\r\nimport { AppRoutingModule } from './app-routing.module';\r\n\r\nimport { defineCustomElements } from '@ionic/pwa-elements/loader';\r\nimport { DomSanitizer } from '@angular/platform-browser';\r\nimport { IonicStorageModule } from '@ionic/storage-angular';\r\nimport { register } from 'swiper/element/bundle';\r\nimport { HttpClient, HttpClientModule } from '@angular/common/http';\r\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\r\nimport { HttpErrorInterceptor } from './interceptors/http-error.service';\r\nimport { NetworkService } from './services/network.service';\r\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\r\nimport { ScannerModalComponent } from './components/scanner-modal/scanner-modal.component';\r\n\r\n\r\n@NgModule({\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  declarations: [AppComponent],\r\n  imports: [\r\n    BrowserModule,\r\n    IonicModule.forRoot( { mode: 'ios', innerHTMLTemplatesEnabled: true }),\r\n    AppRoutingModule,\r\n    IonicStorageModule.forRoot(),\r\n    HttpClientModule\r\n  ],\r\n  providers: [\r\n    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },\r\n    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true, },\r\n    { provide: HTTP_INTERCEPTORS, useClass: HttpErrorInterceptor, multi: true },\r\n    NetworkService,\r\n    {\r\n      provide: 'DARK_MODE',\r\n      useValue: false\r\n    }\r\n  ],\r\n  bootstrap: [AppComponent],\r\n})\r\nexport class AppModule {\r\n  constructor(private sanitizer: DomSanitizer) {\r\n    defineCustomElements(window);\r\n    // register();\r\n  }\r\n}\r\n", "// auth.guard.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { CanActivate, Router } from '@angular/router';\r\nimport { jwtDecode } from 'jwt-decode';\r\nimport * as moment from 'moment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthGuard implements CanActivate {\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  canActivate(): boolean {\r\n    const tokenUser = localStorage.getItem('tokenUser');\r\n    const tokenTenant = localStorage.getItem('tokenTenant');\r\n    const tokenLocal = localStorage.getItem('token');\r\n    const platform = localStorage.getItem('src_app');\r\n\r\n    // Check if platform is selected\r\n    if (!platform) {\r\n      this.router.navigate(['/welcome']);\r\n      return false;\r\n    }\r\n\r\n    if (platform === 'pharmalien') {\r\n      // Pharmalien platform: only needs tokenUser and tokenLocal\r\n      if (tokenUser && tokenLocal) {\r\n        try {\r\n          const isTokenUserValid = this.checkTokenExpiration(tokenUser);\r\n          const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);\r\n\r\n          if (isTokenUserValid && isTokenLocalValid) {\r\n            return true;\r\n          }\r\n        } catch (error) {\r\n          console.error('Token validation error:', error);\r\n        }\r\n      }\r\n    } else if (platform === 'winpluspharma') {\r\n      // WinPlus platform: needs all three tokens\r\n      if (tokenUser && tokenTenant && tokenLocal) {\r\n        try {\r\n          const isTokenUserValid = this.checkTokenExpiration(tokenUser);\r\n          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant);\r\n          const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);\r\n\r\n          if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {\r\n            return true;\r\n          }\r\n        } catch (error) {\r\n          console.error('Token validation error:', error);\r\n        }\r\n      }\r\n    }\r\n\r\n    // If any required token is missing or expired, redirect to login\r\n    this.router.navigate(['/login']);\r\n    return false;\r\n  }\r\n\r\n  private checkTokenExpiration(token: string): boolean {\r\n    const decodedToken: any = jwtDecode(token);\r\n    const expiration = moment(decodedToken?.exp * 1000);\r\n    return moment(new Date()) < expiration;\r\n  }\r\n}", "import { Injectable } from '@angular/core';\r\nimport { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { jwtDecode } from 'jwt-decode';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { ApiService } from '../services/api.service';\r\n\r\n\r\ninterface DecodedToken {\r\n  exp: number;\r\n  iat: number;\r\n  sub: string;  // typically the user identifier\r\n  // Add other expected fields here\r\n}\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n\r\n  constructor(private apiService: ApiService) { }\r\n\r\n  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\r\n    const tokenUser = localStorage.getItem('tokenUser');\r\n    const tokenTenant = localStorage.getItem('tokenTenant');\r\n    const tokenLocal = localStorage.getItem('token');\r\n    const platform = localStorage.getItem('src_app');\r\n\r\n    if (platform === 'pharmalien') {\r\n      // Pharmalien platform: only needs tokenUser and tokenLocal\r\n      if (tokenUser && tokenLocal) {\r\n        if (!this.apiService.isLoggedIn()) {\r\n          this.logout();\r\n          return throwError(() => new Error('Token has expired'));\r\n        }\r\n\r\n        const cloned = req.clone({\r\n          headers: req.headers\r\n            .set('Authorization', `Bearer ${tokenLocal}`)\r\n            .set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`)\r\n        });\r\n        return next.handle(cloned).pipe(\r\n          catchError((error) => {\r\n            if (error.status === 401 || error.status === 403) {\r\n              this.logout();\r\n            }\r\n            return throwError(() => error);\r\n          })\r\n        );\r\n      }\r\n    } else if (platform === 'winpluspharma') {\r\n      // WinPlus platform: needs all three tokens\r\n      if (tokenUser && tokenTenant && tokenLocal) {\r\n        if (!this.apiService.isLoggedIn()) {\r\n          this.logout();\r\n          return throwError(() => new Error('Token has expired'));\r\n        }\r\n\r\n        const cloned = req.clone({\r\n          headers: req.headers\r\n            .set('Authorization', `Bearer ${tokenLocal}`)\r\n            .set('AuthorizationTenant', `BearerTenant ${JSON.parse(tokenTenant).accessToken}`)\r\n            .set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`)\r\n        });\r\n        return next.handle(cloned).pipe(\r\n          catchError((error) => {\r\n            if (error.status === 401 || error.status === 403) {\r\n              this.logout();\r\n            }\r\n            return throwError(() => error);\r\n          })\r\n        );\r\n      }\r\n    }\r\n\r\n    return next.handle(req);\r\n  }\r\n\r\n  isTokenExpired(token: string): boolean {\r\n    const decodedToken: any = jwtDecode(token);\r\n    const expirationDate = new Date(decodedToken.exp * 1000);\r\n    return expirationDate < new Date();\r\n  }\r\n\r\n\r\n\r\n  logout() {\r\n    localStorage.removeItem('tokenUser');\r\n    localStorage.removeItem('tokenTenant');\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('credentials');\r\n    localStorage.removeItem('ocrMode');\r\n    // Keep src_app to remember platform choice\r\n    // Redirect the user to the login page or show a logout message\r\n  }\r\n}", "import { Injectable } from '@angular/core';\r\nimport { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError, from } from 'rxjs';\r\nimport { catchError, switchMap } from 'rxjs/operators';\r\nimport { NavController } from '@ionic/angular';\r\nimport { NetworkService } from '../services/network.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Injectable()\r\nexport class HttpErrorInterceptor implements HttpInterceptor {\r\n\r\n  constructor(private navCtrl: NavController, private networkService: NetworkService) {}\r\n\r\n  intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {\r\n    return from(this.networkService.isConnected()).pipe(\r\n      switchMap(isConnected => {\r\n        if (!isConnected) {\r\n          // Redirect to network error page if no internet connection\r\n          this.navCtrl.navigateRoot('/network-error');\r\n          // Return an empty observable since we don't want to proceed with the request\r\n          return throwError({ status: 0, message: 'No Internet Connection' });\r\n        }\r\n        // Proceed with the request\r\n        return next.handle(request).pipe(\r\n          catchError((error: HttpErrorResponse) => {\r\n            if (error.status === 401) {\r\n              // Redirect to login page if unauthorized\r\n\r\n              Swal.fire({\r\n                title: 'Non Autorisé',\r\n                text: 'Vous devez vous connecter pour accéder à cette page.',\r\n                icon: 'error',\r\n                confirmButtonText: 'Login'\r\n              }).then(() => {\r\n                this.navCtrl.navigateRoot('/login');\r\n              });\r\n            } \r\n            // this.navCtrl.navigateRoot('/request-error');\r\n            return throwError(error);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n}", "import { Injectable } from '@angular/core';\r\nimport { CanActivate, Router } from '@angular/router';\r\nimport { StorageService } from '../services/storage.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class OnboardingGuard implements CanActivate {\r\n  constructor(private storageService: StorageService, private router: Router) {}\r\n\r\n  async canActivate(): Promise<boolean> {\r\n    const hasSeenOnboarding = await this.storageService.get('hasSeenOnboarding');\r\n    if (hasSeenOnboarding) {\r\n      this.router.navigate(['/welcome']);\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n}", "// public.guard.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { CanActivate, Router } from '@angular/router';\r\nimport { jwtDecode } from 'jwt-decode';\r\nimport * as moment from 'moment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PublicGuard implements CanActivate {\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  canActivate(): boolean {\r\n    const tokenUser = localStorage.getItem('tokenUser');\r\n    const tokenTenant = localStorage.getItem('tokenTenant');\r\n    const tokenLocal = localStorage.getItem('token');\r\n\r\n    if (tokenUser && tokenTenant && tokenLocal) {\r\n      try {\r\n        // Check if tokens are valid\r\n        const isTokenUserValid = this.checkTokenExpiration(tokenUser);\r\n        const isTokenTenantValid = this.checkTokenExpiration(tokenTenant);\r\n        const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);\r\n\r\n        if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {\r\n          // If user is already authenticated, redirect to scan-bl\r\n          this.router.navigate(['/scan-bl']);\r\n          return false;\r\n        }\r\n      } catch (error) {\r\n        console.error('Token validation error:', error);\r\n      }\r\n    }\r\n\r\n    // Allow access to public route if not authenticated\r\n    return true;\r\n  }\r\n\r\n  private checkTokenExpiration(token: string): boolean {\r\n    const decodedToken: any = jwtDecode(token);\r\n    const expiration = moment(decodedToken?.exp * 1000);\r\n    return moment(new Date()) < expiration;\r\n  }\r\n}\r\n", "import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError  } from 'rxjs';\r\nimport { environment } from '../../environments/environment';\r\nimport { Coordinates } from '../../models/coordinates';\r\nimport { ProcessDocData } from 'src/models/ProcessDocData';\r\nimport { ProcessImageSuppRequest } from 'src/models/ProcessImageSuppRequest';\r\nimport { ImageData } from 'src/models/ImageData';\r\nimport { catchError, tap, switchMap } from 'rxjs/operators';\r\nimport { LoginRequest, LoginResponse, TenantLoginRequest, TenantLoginResponse, PharmalienLoginRequest, PharmalienLoginResponse } from '../../models/login';\r\nimport { AlertController } from '@ionic/angular';\r\nimport Swal from 'sweetalert2';\r\nimport { jwtDecode } from 'jwt-decode';\r\n\r\nenum OcrMode {\r\n  STANDARD = 'STANDARD',\r\n  MINDEE_ADVANCED = 'MINDEE_ADVANCED'\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ApiService {\r\n  private baseUrl = environment.apiUrl;  // Ensure your environment file has the correct base URL\r\n\r\n\r\n  constructor(private http: HttpClient, private alertController: AlertController,) { }\r\n\r\n  // Set common HTTP options\r\n  private httpOptions = {\r\n    headers: new HttpHeaders({\r\n      'Content-Type': 'application/json'\r\n    })\r\n  };\r\n\r\n  // Generate a unique job ID\r\n  generateJobId(): string {\r\n    return 'job_' + Math.random().toString(36).substr(2, 9);\r\n  }\r\n\r\n  private logRequest(url: string, method: string, body?: any) {\r\n    console.log(`Request: ${method} ${url}`, body);\r\n  }\r\n\r\n  private logResponse(response: any) {\r\n    console.log('Response:', response);\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    console.error('API Error:', error);\r\n    return throwError(error);\r\n  }\r\n\r\n  // Fetch root endpoint\r\n  getRoot(): Observable<any> {\r\n    return this.http.get(`${this.baseUrl}/`);\r\n  }\r\n\r\n   // Tenant Login\r\n  tenantLogin(request: TenantLoginRequest): Observable<TenantLoginResponse> {\r\n    const url = `${this.baseUrl}/tenant_login`;\r\n    this.logRequest(url, 'POST', request);\r\n    return this.http.post<TenantLoginResponse>(url, request, this.httpOptions).pipe(\r\n      tap(this.logResponse),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // User Login\r\n  userLogin(request: LoginRequest, tenantToken: string): Observable<LoginResponse> {\r\n    const url = `${this.baseUrl}/login`;\r\n    const headers = new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      'AuthorizationTenant': `BearerTenant ${tenantToken}`\r\n    });\r\n    const bodyWithToken = { ...request, tenant_token: tenantToken };\r\n    this.logRequest(url, 'POST', bodyWithToken);\r\n    return this.http.post<LoginResponse>(url, bodyWithToken, { headers }).pipe(\r\n      tap(this.logResponse),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Pharmalien Login\r\n  pharmalienLogin(request: PharmalienLoginRequest): Observable<PharmalienLoginResponse> {\r\n    const url = `${this.baseUrl}/pharmalien_login`;\r\n    this.logRequest(url, 'POST', request);\r\n    return this.http.post<PharmalienLoginResponse>(url, request, this.httpOptions).pipe(\r\n      tap(this.logResponse),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Logout function\r\n  async logout(): Promise<void> {\r\n    return new Promise(async (resolve) => {\r\n      const alert = await this.alertController.create({\r\n        header: 'Déconnexion',\r\n        message: `Confirmer la déconnexion ?`,\r\n        buttons: [\r\n          {\r\n            text: 'Annuler',\r\n            role: 'cancel',\r\n            cssClass: 'custom-alert-button cancel',\r\n            handler: () => {\r\n              console.log('Confirm Cancel');\r\n              resolve(); // Resolve even if canceled\r\n            },\r\n          },\r\n          {\r\n            text: 'Oui',\r\n            cssClass: 'custom-alert-button danger',\r\n            handler: () => {\r\n              localStorage.removeItem('tokenUser');\r\n              localStorage.removeItem('tokenTenant');\r\n              localStorage.removeItem('token');\r\n              localStorage.removeItem('ocrMode');\r\n              localStorage.removeItem('forceSupplierGlobal');\r\n              localStorage.removeItem('selectedSupplier');\r\n              resolve(); // Resolve after logout\r\n            },\r\n          },\r\n        ],\r\n      });\r\n  \r\n      await alert.present();\r\n    });\r\n  }\r\n\r\n  // Smart Crop API\r\n  smartCrop(image: File, jobId: string, isScanner: boolean): Observable<any> {\r\n    console.log('API Base URL:', this.baseUrl);\r\n\r\n    const formData: FormData = new FormData();\r\n    formData.append('image', image);\r\n    formData.append('job_id', jobId);  // Include the job ID\r\n    formData.append('isScanner', isScanner.toString());  // Include the isScanner flag\r\n    const url = `${this.baseUrl}/smart_crop/`;\r\n    this.logRequest(url, 'POST', formData);\r\n    return this.http.post<any>(url, formData).pipe(\r\n      tap(this.logResponse),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Magic Pro Filter API\r\n  magicProFilter(image: File, modelName: string, randomId?: string, jobId?: string): Observable<any> {\r\n    const formData: FormData = new FormData();\r\n    formData.append('image', image);\r\n    formData.append('model_name', modelName);\r\n    formData.append('job_id', jobId ?? \"\");  // Include the job ID\r\n    if (randomId) {\r\n      formData.append('random_id', randomId);\r\n    }\r\n\r\n    return this.http.post<any>(`${this.baseUrl}/magic_pro_filter/`, formData);\r\n  }\r\n\r\n  // Identify Supplier API\r\n  identifySupplier(image: File, modelName: string, randomId?: string, jobId?: string): Observable<any> {\r\n    const formData: FormData = new FormData();\r\n    formData.append('image', image);\r\n    formData.append('model_name', modelName);\r\n    formData.append('job_id', jobId ?? \"\");  // Include the job ID\r\n    if (randomId) {\r\n      formData.append('random_id', randomId);\r\n    }\r\n\r\n    return this.http.post<any>(`${this.baseUrl}/identify_supplier/`, formData);\r\n  }\r\n\r\n  // Process Image Supp API\r\n  processImageSupp(request: ProcessImageSuppRequest, jobId: string): Observable<any> {\r\n    // const jobId = this.generateJobId();  // Get the job ID\r\n    const modifiedRequest = { ...request, job_id: jobId };  // Include the job ID in the request\r\n    return this.http.post<any>(`${this.baseUrl}/process_image_supp/`, modifiedRequest, this.httpOptions);\r\n  }\r\n\r\n  // Process OCR Multi API\r\n  processOcrMulti(images: ImageData[], jobId: string, random_id: string, ocrMode: OcrMode = OcrMode.STANDARD): Observable<any> {\r\n    const modifiedImages = images.map(image => ({ ...image, job_id: jobId }));\r\n\r\n    // Get src_app from localStorage, default to 'winpluspharma'\r\n    const srcApp = localStorage.getItem('src_app') || 'winpluspharma';\r\n\r\n    // Create the request body with images, ocrMode, and src_app\r\n    const requestBody = {\r\n      images: modifiedImages,\r\n      ocr_mode: ocrMode.toLowerCase(), // Convert to lowercase to match backend expectation\r\n      src_app: srcApp // Include platform source\r\n    };\r\n\r\n    return this.http.post(`${this.baseUrl}/process_ocr_multi/`, requestBody, this.httpOptions);\r\n  }\r\n\r\n  // Update BL Status API to EN_COURS\r\n  updateBLStatus(blId: string, status: string, id_BL_origine: string, date_BL_origine: string, supplier_name: string): Observable<any> {\r\n    return this.http.put(`${this.baseUrl}/winplus/updateStatus/${blId}`, { status, id_BL_origine, date_BL_origine, supplier_name });\r\n  }\r\n\r\n  // Get All Suppliers list\r\n  getAllSuppliers(): Observable<any> {\r\n    return this.http.get(`${this.baseUrl}/suppliers`);\r\n  }\r\n\r\n  showErrorAlert(message: string) {\r\n    const messageDisplayed = message !== \"\" ? message : \"Il y a eu une erreur de compréhension de la requête. Veuillez réessayer plus tard.\";\r\n  \r\n    Swal.fire({\r\n      icon: 'error',\r\n      title: 'Format de l\\'image incorrecte !',\r\n      html: messageDisplayed,\r\n      footer: '<a href=\"/guide\">Comment capturer une image de qualité ?</a>',\r\n      showConfirmButton: false,  // Remove the confirm button\r\n      showCloseButton: true,     // Add a close button\r\n      customClass: {\r\n        closeButton: 'custom-close-button',  // Custom class for the close button\r\n        popup: 'custom-popup',               // Custom class for the popup for additional styling if needed\r\n        footer: 'custom-footer'              // Custom class for the footer\r\n      }\r\n    });\r\n\r\n    // localStorage.removeItem('ocrMode');\r\n    // localStorage.removeItem('forceSupplierGlobal');\r\n    localStorage.removeItem('selectedSupplier');\r\n\r\n\r\n  }\r\n\r\n  isLoggedIn(): boolean {\r\n    const token = localStorage.getItem('token');\r\n    const tokenUser = localStorage.getItem('tokenUser');\r\n    const tokenTenant = localStorage.getItem('tokenTenant');\r\n    const platform = localStorage.getItem('src_app');\r\n\r\n    // Check required tokens based on platform\r\n    if (platform === 'pharmalien') {\r\n      // Pharmalien only needs token and tokenUser\r\n      if (!token || !tokenUser) {\r\n        return false;\r\n      }\r\n    } else {\r\n      // WinPlus needs all three tokens\r\n      if (!token || !tokenUser || !tokenTenant) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    try {\r\n      const decodedToken = this.decodeToken(token);\r\n      if (!this.isTokenValid(decodedToken)) {\r\n        return false;\r\n      }\r\n    } catch (e) {\r\n      console.error('Token decoding failed', e);\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private decodeToken(token: string): any {\r\n    return jwtDecode(token);\r\n  }\r\n\r\n  private isTokenValid(decodedToken: any): boolean {\r\n    // Check if token has expired\r\n    const currentTime = Math.floor(Date.now() / 1000);\r\n    if (decodedToken.exp < currentTime) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n\r\n\r\n  // ## -- Medicament API -- ##\r\n\r\n  /**\r\n   * Get medicament information and suggestions from OCR\r\n   * @param image Image file to analyze\r\n   * @param jobId Job ID for tracking progress\r\n   * @returns Promise with medicament suggestions\r\n   */\r\n  getMedicamentInfo(image: File, jobId: string): Observable<any> {\r\n    console.log('API Base URL:', this.baseUrl);\r\n\r\n    const formData: FormData = new FormData();\r\n    formData.append('image', image);\r\n    formData.append('job_id', jobId);  // Include the job ID\r\n    \r\n    const url = `${this.baseUrl}/medicament_ocr_tap/`;\r\n    console.log('Making request to:', url);\r\n    \r\n    return this.http.post<any>(url, formData);\r\n  }\r\n}\r\n", "import { Injectable } from '@angular/core';\r\nimport { Network } from '@capacitor/network';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { merge, fromEvent, map, Observable, Observer } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NetworkService {\r\n  private networkStatus = new BehaviorSubject<boolean>(true);\r\n\r\n  constructor() {\r\n    this.initializeNetworkListener();\r\n  }\r\n\r\n  private async initializeNetworkListener() {\r\n    const status = await Network.getStatus();\r\n    this.networkStatus.next(status.connected);\r\n    console.log('Initial Network Status:', status.connected);\r\n\r\n    Network.addListener('networkStatusChange', (status) => {\r\n      console.log('Network status changed:', status.connected);\r\n      this.networkStatus.next(status.connected);\r\n    });\r\n  }\r\n\r\n  public getNetworkStatus() {\r\n    return this.networkStatus.asObservable();\r\n  }\r\n\r\n  public async isConnected(): Promise<boolean> {\r\n    const status = await Network.getStatus();\r\n    return status.connected;\r\n  }\r\n  \r\n  isOnline$ = merge(\r\n    fromEvent(window, 'online').pipe(map(() => true)),\r\n    fromEvent(window, 'offline').pipe(map(() => false)),\r\n    new Observable((sub: Observer<boolean>) => {\r\n      sub.next(navigator.onLine);\r\n      sub.complete();\r\n    }),\r\n  );\r\n}\r\n", "import { Injectable } from '@angular/core';\r\nimport { Storage } from '@ionic/storage-angular'; // For Ionic Storage\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class StorageService {\r\n  constructor(private storage: Storage) {}\r\n\r\n  async init() {\r\n    await this.storage.create();\r\n  }\r\n\r\n  async set(key: string, value: any): Promise<void> {\r\n    await this.storage.set(key, value);\r\n  }\r\n  \r\n  async get(key: string): Promise<any> {\r\n    const value = await this.storage.get(key);\r\n    return value;\r\n  }\r\n\r\n  async clear(): Promise<void> {\r\n    console.log('Clearing all storage...');\r\n    await this.storage.clear(); // Clear all stored data\r\n  }\r\n}", "// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nexport const environment = {\r\n  production: true,\r\n  platform: 'mobile', // 'web' or 'mobile'\r\n\r\n\r\n  // webSocketUrl: 'ws://192.168.101.176:8085/ws',\r\n  // apiUrl: 'http://192.168.101.176:8085',  // Adjust to your FastAPI local URL\r\n  // webSockeRealTimetUrl: 'ws://192.168.101.176:8085/websocket/realtime_processing',\r\n\r\n  // webSocketUrl: 'ws://192.168.101.176:8088/ws',\r\n  // apiUrl: 'http://192.168.101.176:8088',  // Adjust to your FastAPI local URL\r\n  // webSockeRealTimetUrl: 'ws://192.168.101.176:8088/websocket/realtime_processing'\r\n\r\n  webSocketUrl: 'wss://winproduit.sophatel.com:8001/ws',\r\n  apiUrl: 'https://winproduit.sophatel.com:8001',  // Adjust to your FastAPI local URL\r\n  webSockeRealTimetUrl: 'wss://winproduit.sophatel.com:8001/websocket/realtime_processing'\r\n\r\n  // webSocketUrl: 'wss://windoc-api.sophatel.com/ws',\r\n  // apiUrl: 'https://windoc-api.sophatel.com',  // Adjust to your FastAPI local URL\r\n  // webSockeRealTimetUrl: 'wss://windoc-api.sophatel.com/websocket/realtime_processing'\r\n\r\n  // webSocketUrl: 'wss://winproduit.sophatel.com:8000/ws',\r\n  // apiUrl: 'https://winproduit.sophatel.com:8000',  // Adjust to your FastAPI local URL\r\n  // webSockeRealTimetUrl: 'wss://winproduit.sophatel.com:8000/websocket/realtime_processing'\r\n\r\n\r\n  // webSocketUrl: 'wss://ocr-api.abdohero.com/ws', // Secure WebSocket URL\r\n  // apiUrl: 'https://ocr-api.abdohero.com',  // Adjust to your FastAPI server URL\r\n  // webSockeRealTimetUrl: 'wss://ocr-api.abdohero.com/websocket/realtime_processing'\r\n};\r\n\r\n/*\r\n * For easier debugging in development mode, you can import the following file\r\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n *\r\n * This import should be commented out in production mode because it will have a negative impact\r\n * on performance if an error is thrown.\r\n */\r\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.", "import { enableProdMode } from '@angular/core';\nimport { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\n\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\nimport { register } from 'swiper/element/bundle';\n\nif (environment.production) {\n  enableProdMode();\n}\n\nregister();\n\nplatformBrowserDynamic().bootstrapModule(AppModule)\n  .catch(err => console.log(err));\n", "var map = {\n\t\"./ion-accordion_2.entry.js\": [\n\t\t37518,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-accordion_2_entry_js\"\n\t],\n\t\"./ion-action-sheet.entry.js\": [\n\t\t41981,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-action-sheet_entry_js\"\n\t],\n\t\"./ion-alert.entry.js\": [\n\t\t71603,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-alert_entry_js\"\n\t],\n\t\"./ion-app_8.entry.js\": [\n\t\t82273,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-app_8_entry_js\"\n\t],\n\t\"./ion-avatar_3.entry.js\": [\n\t\t19642,\n\t\t\"node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js\"\n\t],\n\t\"./ion-back-button.entry.js\": [\n\t\t32095,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-back-button_entry_js\"\n\t],\n\t\"./ion-backdrop.entry.js\": [\n\t\t72335,\n\t\t\"node_modules_ionic_core_dist_esm_ion-backdrop_entry_js\"\n\t],\n\t\"./ion-breadcrumb_2.entry.js\": [\n\t\t78221,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-breadcrumb_2_entry_js\"\n\t],\n\t\"./ion-button_2.entry.js\": [\n\t\t47184,\n\t\t\"node_modules_ionic_core_dist_esm_ion-button_2_entry_js\"\n\t],\n\t\"./ion-card_5.entry.js\": [\n\t\t38759,\n\t\t\"node_modules_ionic_core_dist_esm_ion-card_5_entry_js\"\n\t],\n\t\"./ion-checkbox.entry.js\": [\n\t\t24248,\n\t\t\"node_modules_ionic_core_dist_esm_ion-checkbox_entry_js\"\n\t],\n\t\"./ion-chip.entry.js\": [\n\t\t69863,\n\t\t\"node_modules_ionic_core_dist_esm_ion-chip_entry_js\"\n\t],\n\t\"./ion-col_3.entry.js\": [\n\t\t51769,\n\t\t\"node_modules_ionic_core_dist_esm_ion-col_3_entry_js\"\n\t],\n\t\"./ion-datetime-button.entry.js\": [\n\t\t2569,\n\t\t\"default-node_modules_ionic_core_dist_esm_data-ae11fd43_js\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-datetime-button_entry_js\"\n\t],\n\t\"./ion-datetime_3.entry.js\": [\n\t\t76534,\n\t\t\"default-node_modules_ionic_core_dist_esm_data-ae11fd43_js\",\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-datetime_3_entry_js\"\n\t],\n\t\"./ion-fab_3.entry.js\": [\n\t\t25458,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-fab_3_entry_js\"\n\t],\n\t\"./ion-img.entry.js\": [\n\t\t70654,\n\t\t\"node_modules_ionic_core_dist_esm_ion-img_entry_js\"\n\t],\n\t\"./ion-infinite-scroll_2.entry.js\": [\n\t\t36034,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-infinite-scroll_2_entry_js\"\n\t],\n\t\"./ion-input-password-toggle.entry.js\": [\n\t\t5196,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-input-password-toggle_entry_js\"\n\t],\n\t\"./ion-input.entry.js\": [\n\t\t20761,\n\t\t\"default-node_modules_ionic_core_dist_esm_input_utils-09c71bc7_js-node_modules_ionic_core_dist-8b8a84\",\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-input_entry_js\"\n\t],\n\t\"./ion-item-option_3.entry.js\": [\n\t\t6492,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-item-option_3_entry_js\"\n\t],\n\t\"./ion-item_8.entry.js\": [\n\t\t29557,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-item_8_entry_js\"\n\t],\n\t\"./ion-loading.entry.js\": [\n\t\t68353,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-loading_entry_js\"\n\t],\n\t\"./ion-menu_3.entry.js\": [\n\t\t51024,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-menu_3_entry_js\"\n\t],\n\t\"./ion-modal.entry.js\": [\n\t\t29160,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-modal_entry_js\"\n\t],\n\t\"./ion-nav_2.entry.js\": [\n\t\t60393,\n\t\t\"node_modules_ionic_core_dist_esm_ion-nav_2_entry_js\"\n\t],\n\t\"./ion-picker-column-option.entry.js\": [\n\t\t68442,\n\t\t\"node_modules_ionic_core_dist_esm_ion-picker-column-option_entry_js\"\n\t],\n\t\"./ion-picker-column.entry.js\": [\n\t\t43110,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-picker-column_entry_js\"\n\t],\n\t\"./ion-picker.entry.js\": [\n\t\t15575,\n\t\t\"node_modules_ionic_core_dist_esm_ion-picker_entry_js\"\n\t],\n\t\"./ion-popover.entry.js\": [\n\t\t16772,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-popover_entry_js\"\n\t],\n\t\"./ion-progress-bar.entry.js\": [\n\t\t34810,\n\t\t\"node_modules_ionic_core_dist_esm_ion-progress-bar_entry_js\"\n\t],\n\t\"./ion-radio_2.entry.js\": [\n\t\t14639,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-radio_2_entry_js\"\n\t],\n\t\"./ion-range.entry.js\": [\n\t\t90628,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-range_entry_js\"\n\t],\n\t\"./ion-refresher_2.entry.js\": [\n\t\t10852,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-refresher_2_entry_js\"\n\t],\n\t\"./ion-reorder_2.entry.js\": [\n\t\t61479,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-reorder_2_entry_js\"\n\t],\n\t\"./ion-ripple-effect.entry.js\": [\n\t\t24065,\n\t\t\"node_modules_ionic_core_dist_esm_ion-ripple-effect_entry_js\"\n\t],\n\t\"./ion-route_4.entry.js\": [\n\t\t57971,\n\t\t\"node_modules_ionic_core_dist_esm_ion-route_4_entry_js\"\n\t],\n\t\"./ion-searchbar.entry.js\": [\n\t\t93184,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-searchbar_entry_js\"\n\t],\n\t\"./ion-segment_2.entry.js\": [\n\t\t469,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-segment_2_entry_js\"\n\t],\n\t\"./ion-select_3.entry.js\": [\n\t\t78471,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-select_3_entry_js\"\n\t],\n\t\"./ion-spinner.entry.js\": [\n\t\t40388,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-spinner_entry_js\"\n\t],\n\t\"./ion-split-pane.entry.js\": [\n\t\t42392,\n\t\t\"node_modules_ionic_core_dist_esm_ion-split-pane_entry_js\"\n\t],\n\t\"./ion-tab-bar_2.entry.js\": [\n\t\t36059,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-tab-bar_2_entry_js\"\n\t],\n\t\"./ion-tab_2.entry.js\": [\n\t\t5427,\n\t\t\"node_modules_ionic_core_dist_esm_ion-tab_2_entry_js\"\n\t],\n\t\"./ion-text.entry.js\": [\n\t\t50198,\n\t\t\"node_modules_ionic_core_dist_esm_ion-text_entry_js\"\n\t],\n\t\"./ion-textarea.entry.js\": [\n\t\t1735,\n\t\t\"default-node_modules_ionic_core_dist_esm_input_utils-09c71bc7_js-node_modules_ionic_core_dist-8b8a84\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-textarea_entry_js\"\n\t],\n\t\"./ion-toast.entry.js\": [\n\t\t7510,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-toast_entry_js\"\n\t],\n\t\"./ion-toggle.entry.js\": [\n\t\t45297,\n\t\t\"common\",\n\t\t\"node_modules_ionic_core_dist_esm_ion-toggle_entry_js\"\n\t]\n};\nfunction webpackAsyncContext(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\treturn Promise.resolve().then(() => {\n\t\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\t\te.code = 'MODULE_NOT_FOUND';\n\t\t\tthrow e;\n\t\t});\n\t}\n\n\tvar ids = map[req], id = ids[0];\n\treturn Promise.all(ids.slice(1).map(__webpack_require__.e)).then(() => {\n\t\treturn __webpack_require__(id);\n\t});\n}\nwebpackAsyncContext.keys = () => (Object.keys(map));\nwebpackAsyncContext.id = 88996;\nmodule.exports = webpackAsyncContext;", "var map = {\n\t\"./pwa-action-sheet.entry.js\": [\n\t\t75222,\n\t\t\"node_modules_ionic_pwa-elements_dist_esm_pwa-action-sheet_entry_js\"\n\t],\n\t\"./pwa-camera-modal-instance.entry.js\": [\n\t\t89253,\n\t\t\"node_modules_ionic_pwa-elements_dist_esm_pwa-camera-modal-instance_entry_js\"\n\t],\n\t\"./pwa-camera-modal.entry.js\": [\n\t\t69577,\n\t\t\"node_modules_ionic_pwa-elements_dist_esm_pwa-camera-modal_entry_js\"\n\t],\n\t\"./pwa-camera.entry.js\": [\n\t\t52217,\n\t\t\"node_modules_ionic_pwa-elements_dist_esm_pwa-camera_entry_js\"\n\t],\n\t\"./pwa-toast.entry.js\": [\n\t\t70071,\n\t\t\"node_modules_ionic_pwa-elements_dist_esm_pwa-toast_entry_js\"\n\t]\n};\nfunction webpackAsyncContext(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\treturn Promise.resolve().then(() => {\n\t\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\t\te.code = 'MODULE_NOT_FOUND';\n\t\t\tthrow e;\n\t\t});\n\t}\n\n\tvar ids = map[req], id = ids[0];\n\treturn __webpack_require__.e(ids[1]).then(() => {\n\t\treturn __webpack_require__(id);\n\t});\n}\nwebpackAsyncContext.keys = () => (Object.keys(map));\nwebpackAsyncContext.id = 95235;\nmodule.exports = webpackAsyncContext;", "function webpackEmptyAsyncContext(req) {\n\t// Here Promise.resolve().then() is used instead of new Promise() to prevent\n\t// uncaught exception popping up in devtools\n\treturn Promise.resolve().then(() => {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t});\n}\nwebpackEmptyAsyncContext.keys = () => ([]);\nwebpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;\nwebpackEmptyAsyncContext.id = 54140;\nmodule.exports = webpackEmptyAsyncContext;", "var map = {\n\t\"./af\": 85637,\n\t\"./af.js\": 85637,\n\t\"./ar\": 6777,\n\t\"./ar-dz\": 74508,\n\t\"./ar-dz.js\": 74508,\n\t\"./ar-kw\": 67504,\n\t\"./ar-kw.js\": 67504,\n\t\"./ar-ly\": 95373,\n\t\"./ar-ly.js\": 95373,\n\t\"./ar-ma\": 92412,\n\t\"./ar-ma.js\": 92412,\n\t\"./ar-ps\": 78823,\n\t\"./ar-ps.js\": 78823,\n\t\"./ar-sa\": 36670,\n\t\"./ar-sa.js\": 36670,\n\t\"./ar-tn\": 36448,\n\t\"./ar-tn.js\": 36448,\n\t\"./ar.js\": 6777,\n\t\"./az\": 23009,\n\t\"./az.js\": 23009,\n\t\"./be\": 28299,\n\t\"./be.js\": 28299,\n\t\"./bg\": 4685,\n\t\"./bg.js\": 4685,\n\t\"./bm\": 11171,\n\t\"./bm.js\": 11171,\n\t\"./bn\": 23590,\n\t\"./bn-bd\": 5841,\n\t\"./bn-bd.js\": 5841,\n\t\"./bn.js\": 23590,\n\t\"./bo\": 54309,\n\t\"./bo.js\": 54309,\n\t\"./br\": 54130,\n\t\"./br.js\": 54130,\n\t\"./bs\": 8033,\n\t\"./bs.js\": 8033,\n\t\"./ca\": 55294,\n\t\"./ca.js\": 55294,\n\t\"./cs\": 53028,\n\t\"./cs.js\": 53028,\n\t\"./cv\": 5807,\n\t\"./cv.js\": 5807,\n\t\"./cy\": 70342,\n\t\"./cy.js\": 70342,\n\t\"./da\": 38269,\n\t\"./da.js\": 38269,\n\t\"./de\": 11489,\n\t\"./de-at\": 42123,\n\t\"./de-at.js\": 42123,\n\t\"./de-ch\": 17757,\n\t\"./de-ch.js\": 17757,\n\t\"./de.js\": 11489,\n\t\"./dv\": 28152,\n\t\"./dv.js\": 28152,\n\t\"./el\": 7687,\n\t\"./el.js\": 7687,\n\t\"./en-au\": 46668,\n\t\"./en-au.js\": 46668,\n\t\"./en-ca\": 76798,\n\t\"./en-ca.js\": 76798,\n\t\"./en-gb\": 53615,\n\t\"./en-gb.js\": 53615,\n\t\"./en-ie\": 91364,\n\t\"./en-ie.js\": 91364,\n\t\"./en-il\": 79907,\n\t\"./en-il.js\": 79907,\n\t\"./en-in\": 70533,\n\t\"./en-in.js\": 70533,\n\t\"./en-nz\": 33190,\n\t\"./en-nz.js\": 33190,\n\t\"./en-sg\": 51096,\n\t\"./en-sg.js\": 51096,\n\t\"./eo\": 3962,\n\t\"./eo.js\": 3962,\n\t\"./es\": 37726,\n\t\"./es-do\": 65010,\n\t\"./es-do.js\": 65010,\n\t\"./es-mx\": 63654,\n\t\"./es-mx.js\": 63654,\n\t\"./es-us\": 59043,\n\t\"./es-us.js\": 59043,\n\t\"./es.js\": 37726,\n\t\"./et\": 25343,\n\t\"./et.js\": 25343,\n\t\"./eu\": 90728,\n\t\"./eu.js\": 90728,\n\t\"./fa\": 60787,\n\t\"./fa.js\": 60787,\n\t\"./fi\": 71771,\n\t\"./fi.js\": 71771,\n\t\"./fil\": 45335,\n\t\"./fil.js\": 45335,\n\t\"./fo\": 69761,\n\t\"./fo.js\": 69761,\n\t\"./fr\": 1670,\n\t\"./fr-ca\": 28991,\n\t\"./fr-ca.js\": 28991,\n\t\"./fr-ch\": 97280,\n\t\"./fr-ch.js\": 97280,\n\t\"./fr.js\": 1670,\n\t\"./fy\": 24203,\n\t\"./fy.js\": 24203,\n\t\"./ga\": 69858,\n\t\"./ga.js\": 69858,\n\t\"./gd\": 38605,\n\t\"./gd.js\": 38605,\n\t\"./gl\": 27365,\n\t\"./gl.js\": 27365,\n\t\"./gom-deva\": 33896,\n\t\"./gom-deva.js\": 33896,\n\t\"./gom-latn\": 95587,\n\t\"./gom-latn.js\": 95587,\n\t\"./gu\": 97950,\n\t\"./gu.js\": 97950,\n\t\"./he\": 92029,\n\t\"./he.js\": 92029,\n\t\"./hi\": 51897,\n\t\"./hi.js\": 51897,\n\t\"./hr\": 29816,\n\t\"./hr.js\": 29816,\n\t\"./hu\": 22253,\n\t\"./hu.js\": 22253,\n\t\"./hy-am\": 28196,\n\t\"./hy-am.js\": 28196,\n\t\"./id\": 51307,\n\t\"./id.js\": 51307,\n\t\"./is\": 95474,\n\t\"./is.js\": 95474,\n\t\"./it\": 23099,\n\t\"./it-ch\": 45807,\n\t\"./it-ch.js\": 45807,\n\t\"./it.js\": 23099,\n\t\"./ja\": 19127,\n\t\"./ja.js\": 19127,\n\t\"./jv\": 30182,\n\t\"./jv.js\": 30182,\n\t\"./ka\": 10758,\n\t\"./ka.js\": 10758,\n\t\"./kk\": 93444,\n\t\"./kk.js\": 93444,\n\t\"./km\": 72034,\n\t\"./km.js\": 72034,\n\t\"./kn\": 46223,\n\t\"./kn.js\": 46223,\n\t\"./ko\": 83064,\n\t\"./ko.js\": 83064,\n\t\"./ku\": 8714,\n\t\"./ku-kmr\": 10961,\n\t\"./ku-kmr.js\": 10961,\n\t\"./ku.js\": 8714,\n\t\"./ky\": 12062,\n\t\"./ky.js\": 12062,\n\t\"./lb\": 84796,\n\t\"./lb.js\": 84796,\n\t\"./lo\": 19279,\n\t\"./lo.js\": 19279,\n\t\"./lt\": 106,\n\t\"./lt.js\": 106,\n\t\"./lv\": 11840,\n\t\"./lv.js\": 11840,\n\t\"./me\": 42240,\n\t\"./me.js\": 42240,\n\t\"./mi\": 13588,\n\t\"./mi.js\": 13588,\n\t\"./mk\": 15518,\n\t\"./mk.js\": 15518,\n\t\"./ml\": 37823,\n\t\"./ml.js\": 37823,\n\t\"./mn\": 98657,\n\t\"./mn.js\": 98657,\n\t\"./mr\": 61285,\n\t\"./mr.js\": 61285,\n\t\"./ms\": 43014,\n\t\"./ms-my\": 86253,\n\t\"./ms-my.js\": 86253,\n\t\"./ms.js\": 43014,\n\t\"./mt\": 20167,\n\t\"./mt.js\": 20167,\n\t\"./my\": 47940,\n\t\"./my.js\": 47940,\n\t\"./nb\": 50014,\n\t\"./nb.js\": 50014,\n\t\"./ne\": 49023,\n\t\"./ne.js\": 49023,\n\t\"./nl\": 34208,\n\t\"./nl-be\": 71412,\n\t\"./nl-be.js\": 71412,\n\t\"./nl.js\": 34208,\n\t\"./nn\": 81354,\n\t\"./nn.js\": 81354,\n\t\"./oc-lnc\": 40870,\n\t\"./oc-lnc.js\": 40870,\n\t\"./pa-in\": 80389,\n\t\"./pa-in.js\": 80389,\n\t\"./pl\": 7342,\n\t\"./pl.js\": 7342,\n\t\"./pt\": 34774,\n\t\"./pt-br\": 73003,\n\t\"./pt-br.js\": 73003,\n\t\"./pt.js\": 34774,\n\t\"./ro\": 85333,\n\t\"./ro.js\": 85333,\n\t\"./ru\": 73451,\n\t\"./ru.js\": 73451,\n\t\"./sd\": 43921,\n\t\"./sd.js\": 43921,\n\t\"./se\": 59682,\n\t\"./se.js\": 59682,\n\t\"./si\": 80582,\n\t\"./si.js\": 80582,\n\t\"./sk\": 4348,\n\t\"./sk.js\": 4348,\n\t\"./sl\": 95337,\n\t\"./sl.js\": 95337,\n\t\"./sq\": 39358,\n\t\"./sq.js\": 39358,\n\t\"./sr\": 50683,\n\t\"./sr-cyrl\": 69382,\n\t\"./sr-cyrl.js\": 69382,\n\t\"./sr.js\": 50683,\n\t\"./ss\": 51156,\n\t\"./ss.js\": 51156,\n\t\"./sv\": 29855,\n\t\"./sv.js\": 29855,\n\t\"./sw\": 18536,\n\t\"./sw.js\": 18536,\n\t\"./ta\": 15373,\n\t\"./ta.js\": 15373,\n\t\"./te\": 37809,\n\t\"./te.js\": 37809,\n\t\"./tet\": 61297,\n\t\"./tet.js\": 61297,\n\t\"./tg\": 92527,\n\t\"./tg.js\": 92527,\n\t\"./th\": 85862,\n\t\"./th.js\": 85862,\n\t\"./tk\": 79331,\n\t\"./tk.js\": 79331,\n\t\"./tl-ph\": 44387,\n\t\"./tl-ph.js\": 44387,\n\t\"./tlh\": 3592,\n\t\"./tlh.js\": 3592,\n\t\"./tr\": 79732,\n\t\"./tr.js\": 79732,\n\t\"./tzl\": 99570,\n\t\"./tzl.js\": 99570,\n\t\"./tzm\": 83553,\n\t\"./tzm-latn\": 7699,\n\t\"./tzm-latn.js\": 7699,\n\t\"./tzm.js\": 83553,\n\t\"./ug-cn\": 25674,\n\t\"./ug-cn.js\": 25674,\n\t\"./uk\": 69974,\n\t\"./uk.js\": 69974,\n\t\"./ur\": 45773,\n\t\"./ur.js\": 45773,\n\t\"./uz\": 357,\n\t\"./uz-latn\": 77135,\n\t\"./uz-latn.js\": 77135,\n\t\"./uz.js\": 357,\n\t\"./vi\": 20043,\n\t\"./vi.js\": 20043,\n\t\"./x-pseudo\": 40767,\n\t\"./x-pseudo.js\": 40767,\n\t\"./yo\": 80150,\n\t\"./yo.js\": 80150,\n\t\"./zh-cn\": 21828,\n\t\"./zh-cn.js\": 21828,\n\t\"./zh-hk\": 86644,\n\t\"./zh-hk.js\": 86644,\n\t\"./zh-mo\": 79305,\n\t\"./zh-mo.js\": 79305,\n\t\"./zh-tw\": 31860,\n\t\"./zh-tw.js\": 31860\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 35358;"], "names": ["PreloadAllModules", "RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "OnboardingGuard", "PublicGuard", "routes", "path", "loadChildren", "then", "m", "WelcomePageModule", "redirectTo", "pathMatch", "OnboardingPageModule", "canActivate", "LoginPageModule", "GuidePageModule", "ScanBLPageModule", "DocListPageModule", "DataBLPageModule", "CropDocPageModule", "ProcessDocPageModule", "NetworkErrorPageModule", "RequestErrorPageModule", "RealtimeContoursPageModule", "DataBlSuccessPageModule", "ProfilePageModule", "MedicamentOcrPageModule", "AppRoutingModule", "forRoot", "preloadingStrategy", "imports", "i1", "exports", "register", "NavigationStart", "environment", "AppComponent", "constructor", "storageService", "router", "apiService", "platform", "initializeApp", "ready", "forceLightMode", "checkEnvironmentAndClearStorage", "document", "body", "classList", "remove", "documentElement", "add", "setAttribute", "meta", "createElement", "name", "content", "head", "append<PERSON><PERSON><PERSON>", "ngOnInit", "_this", "_asyncToGenerator", "init", "hasSeenOnboarding", "get", "navigate", "events", "subscribe", "event", "isLoggedIn", "isPublicRoute", "url", "publicRoutes", "includes", "_this2", "production", "console", "log", "clear", "i0", "ɵɵdirectiveInject", "StorageService", "i2", "Router", "i3", "ApiService", "i4", "Platform", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "BrowserModule", "RouteReuseStrategy", "IonicModule", "IonicRouteStrategy", "defineCustomElements", "IonicStorageModule", "HttpClientModule", "HTTP_INTERCEPTORS", "HttpErrorInterceptor", "NetworkService", "AuthInterceptor", "AppModule", "sanitizer", "window", "ɵɵinject", "Dom<PERSON><PERSON><PERSON>zer", "bootstrap", "provide", "useClass", "multi", "useValue", "mode", "innerHTMLTemplatesEnabled", "declarations", "jwtDecode", "moment", "tokenUser", "localStorage", "getItem", "tokenTenant", "tokenLocal", "isTokenUserValid", "checkTokenExpiration", "isTokenLocalValid", "error", "isTokenTenantValid", "token", "decodedToken", "expiration", "exp", "Date", "factory", "ɵfac", "providedIn", "throwError", "catchError", "intercept", "req", "next", "logout", "Error", "cloned", "clone", "headers", "set", "JSON", "parse", "accessToken", "handle", "pipe", "status", "isTokenExpired", "expirationDate", "removeItem", "from", "switchMap", "<PERSON><PERSON>", "navCtrl", "networkService", "request", "isConnected", "navigateRoot", "message", "fire", "title", "text", "icon", "confirmButtonText", "NavController", "HttpHeaders", "tap", "OcrMode", "http", "alertController", "baseUrl", "apiUrl", "httpOptions", "generateJobId", "Math", "random", "toString", "substr", "logRequest", "method", "logResponse", "response", "handleError", "getRoot", "tenantLogin", "post", "userLogin", "tenantToken", "bodyWithToken", "tenant_token", "pharmalien<PERSON><PERSON>in", "Promise", "_ref", "resolve", "alert", "create", "header", "buttons", "role", "cssClass", "handler", "present", "_x", "apply", "arguments", "smartCrop", "image", "jobId", "isScanner", "formData", "FormData", "append", "magicProFilter", "modelName", "randomId", "identifySupplier", "processImageSupp", "modifiedRequest", "job_id", "processOcrMulti", "images", "random_id", "ocrMode", "STANDARD", "modifiedImages", "map", "srcApp", "requestBody", "ocr_mode", "toLowerCase", "src_app", "updateBLStatus", "blId", "id_BL_origine", "date_BL_origine", "supplier_name", "put", "getAllSuppliers", "showError<PERSON><PERSON>t", "messageDisplayed", "html", "footer", "showConfirmButton", "showCloseButton", "customClass", "closeButton", "popup", "decodeToken", "isTokenValid", "e", "currentTime", "floor", "now", "getMedicamentInfo", "HttpClient", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Network", "BehaviorSubject", "merge", "fromEvent", "Observable", "networkStatus", "isOnline$", "sub", "navigator", "onLine", "complete", "initializeNetworkListener", "getStatus", "connected", "addListener", "getNetworkStatus", "asObservable", "storage", "key", "value", "_this3", "_this4", "Storage", "webSocketUrl", "webSockeRealTimetUrl", "enableProdMode", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err"], "sourceRoot": "webpack:///", "x_google_ignoreList": [14, 15, 16, 17]}