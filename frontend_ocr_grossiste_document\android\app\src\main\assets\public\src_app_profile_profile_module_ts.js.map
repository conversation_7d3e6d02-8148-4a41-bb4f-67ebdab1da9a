{"version": 3, "file": "src_app_profile_profile_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAEV;;;AAE7C,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,sDAAWA;CACvB,CACF;AAMK,MAAOI,wBAAwB;4BAAxBA,wBAAwB;;mBAAxBA,yBAAwB;AAAA;;QAAxBA;AAAwB;;YAHzBL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,wBAAwB;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFzBT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAEuB;AAEvB;;AAWvC,MAAOa,iBAAiB;qBAAjBA,iBAAiB;;mBAAjBA,kBAAiB;AAAA;;QAAjBA;AAAiB;;YAP1BH,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,6EAAwB;AAAA;;sHAIfQ,iBAAiB;IAAAC,YAAA,GAFbb,sDAAW;IAAAM,OAAA,GALxBG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,6EAAwB;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACHtB,MAAOJ,WAAW;EAItBc,YAAqBC,OAAsB,EACjCC,UAAqB,EACrBC,QAAiB,EACjBC,WAAuB,EACvBC,eAAgC;IAJrB,KAAAJ,OAAO,GAAPA,OAAO;IAClB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IANzB,KAAAC,IAAI,GAAiB,IAAI;IAyBzB,KAAAC,QAAQ,GAAG,KAAK;EAjBZ;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACH,IAAI,GAAG,IAAI,CAACF,WAAW,CAACE,IAAI,EAAE;EACrC;EAEAI,MAAMA,CAAA;IAAA,IAAAC,qBAAA;IACP;IACG,IAAI,IAAI,CAACR,QAAQ,CAACS,QAAQ,EAAE,IAAK,EAAAD,qBAAA,OAAI,CAACR,QAAQ,CAACS,QAAQ,EAAqB,cAAAD,qBAAA,uBAA3CA,qBAAA,CAA6CE,YAAY,IAAG,CAAC,EAAE;MAC9F,IAAI,CAACZ,OAAO,CAACa,GAAG,EAAE;KACnB,MAAM;MACL,IAAI,CAACb,OAAO,CAACc,YAAY,CAAC,UAAU,CAAC;;IAEvC,IAAI,CAACd,OAAO,CAACa,GAAG,EAAE;EACpB;EAMAE,UAAUA,CAACC,IAAY;IACrB,IAAI,CAAChB,OAAO,CAACiB,eAAe,CAAC,IAAID,IAAI,EAAE,CAAC;EAC1C;EAEAE,kBAAkBA,CAAA;IAChB,IAAI,CAAClB,OAAO,CAACiB,eAAe,CAAC,WAAW,CAAC;EAC3C;EAEAE,cAAcA,CAAA;IACZC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,MAAM,EAAE,IAAI,CAACjB,QAAQ,CAAC;EACvD;EAIA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEAkB,QAAQA,CAAA;IACN,IAAI,CAACpB,eAAe,CAACqB,MAAM,CAAC;MAC1BC,QAAQ,EAAC,IAAI;MACbC,MAAM,EAAG,8BAA8B;MACvCC,OAAO,EAAC,qCAAqC;MAC7CC,OAAO,EAAC,CACN;QACEC,IAAI,EAAC,KAAK;QACVC,OAAO,EAACA,CAAA,KAAI;UACVC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;UACpCD,YAAY,CAACC,UAAU,CAAC,aAAa,CAAC;UACtCD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;UAChCD,YAAY,CAACC,UAAU,CAAC,SAAS,CAAC;UAClCD,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;UAC9CD,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;UAE3C,IAAI,CAACjC,OAAO,CAACc,YAAY,CAAC,QAAQ,CAAC;QACrC;OAED,EACD;QACEgB,IAAI,EAAC,KAAK;QACVI,IAAI,EAAC;OAEN;KAEJ,CAAC,CAACC,IAAI,CAACC,KAAK,IAAEA,KAAK,CAACC,OAAO,EAAE,CAAC;EACjC;EAKQ7B,cAAcA,CAAA;IACpB;IACAY,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACgB,MAAM,CAAC,MAAM,CAAC;IACtClB,QAAQ,CAACmB,eAAe,CAACjB,SAAS,CAACgB,MAAM,CAAC,MAAM,CAAC;IAEjD;IACAlB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACkB,GAAG,CAAC,OAAO,CAAC;IACpCpB,QAAQ,CAACmB,eAAe,CAACjB,SAAS,CAACkB,GAAG,CAAC,OAAO,CAAC;IAE/C;IACApB,QAAQ,CAACC,IAAI,CAACoB,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IAEjD;IACA,MAAMC,IAAI,GAAGtB,QAAQ,CAACuB,aAAa,CAAC,MAAM,CAAC;IAC3CD,IAAI,CAACE,IAAI,GAAG,cAAc;IAC1BF,IAAI,CAACG,OAAO,GAAG,OAAO;IACtBzB,QAAQ,CAAC0B,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC;EACjC;EAGMM,OAAOA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACX,MAAMd,KAAK,SAASa,KAAI,CAAC7C,eAAe,CAACqB,MAAM,CAAC;QAC9CE,MAAM,EAAE,4BAA4B;QACpCwB,QAAQ,EAAE,cAAc;QACxBvB,OAAO,EAAE;;;;;;;;;;;;;;;;;;OAkBR;QACDC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,QAAQ;UACdI,IAAI,EAAE;SACP;OAEJ,CAAC;MAEF,MAAME,KAAK,CAACC,OAAO,EAAE;MAErB;MACA,MAAMe,WAAW,GAAGhC,QAAQ,CAACiC,cAAc,CAAC,cAAc,CAAC;MAC3D,IAAID,WAAW,EAAE;QACfA,WAAW,CAACE,gBAAgB,CAAC,OAAO,EAAE,MAAK;UACzClB,KAAK,CAACmB,OAAO,EAAE;UACfN,KAAI,CAACO,iBAAiB,EAAE;QAC1B,CAAC,CAAC;;IACH;EACH;EAEMA,iBAAiBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAP,6OAAA;MACrB,MAAMd,KAAK,SAASqB,MAAI,CAACrD,eAAe,CAACqB,MAAM,CAAC;QAC9CE,MAAM,EAAE,8BAA8B;QACtCwB,QAAQ,EAAE,cAAc;QACxBvB,OAAO,EAAE;;;;;;;;;;OAUR;QACDC,OAAO,EAAE,CAAC,QAAQ;OACnB,CAAC;MAEF,MAAMO,KAAK,CAACC,OAAO,EAAE;IAAC;EACxB;EAEMqB,MAAMA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAT,6OAAA;MACV,MAAMS,MAAI,CAAC1D,UAAU,CAACyD,MAAM,EAAE,CAAC,CAAE;MACjCC,MAAI,CAAC3D,OAAO,CAAC4D,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAE;IAAA;EACxC;;eA9KW3E,WAAW;;mBAAXA,YAAW,EAAA4E,+DAAA,CAAArE,yDAAA,GAAAqE,+DAAA,CAAAG,6DAAA,GAAAH,+DAAA,CAAAK,qDAAA,GAAAL,+DAAA,CAAAO,+DAAA,GAAAP,+DAAA,CAAArE,2DAAA;AAAA;;QAAXP,YAAW;EAAAsF,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCVpBhB,4DAFJ,iBAAY,kBACG,mBACyB;MAAAA,oDAAA,sBAAU;MAAAA,0DAAA,EAAY;MAExDA,4DADF,qBAA0B,oBACO;MAAnBA,wDAAA,mBAAAsB,iDAAA;QAAA,OAASL,GAAA,CAAArE,MAAA,EAAQ;MAAA,EAAC;MAC5BoD,uDAAA,kBAAkE;MAEtEA,0DADE,EAAa,EACD;MAEZA,4DADF,qBAAwB,oBACS;MAAnBA,wDAAA,mBAAAwB,iDAAA;QAAA,OAASP,GAAA,CAAApB,MAAA,EAAQ;MAAA,EAAC;MAC5BG,uDAAA,kBAA6D;MAIrEA,0DAHM,EAAa,EACD,EACF,EACH;MAMTA,4DAHJ,mBAAa,cACoB,qBAEM;MACjCA,uDAAA,cAA8E;MAChFA,0DAAA,EAAa;MAGXA,4DADF,mBAA0F,cAClE;MAAAA,oDAAA,IAAsC;MAAAA,0DAAA,EAAK;MAEjEA,4DAAA,qBAA0B;MAAAA,oDAAA,IAA6E;MACzGA,0DADyG,EAAY,EAC1G;MAITA,4DADF,gBAAU,oBACoC;MAApBA,wDAAA,mBAAAyB,gDAAA;QAAA,OAASR,GAAA,CAAA9B,OAAA,EAAS;MAAA,EAAC;MACzCa,uDAAA,oBAAoE;MACpEA,4DAAA,iBAAW;MAAAA,oDAAA,qBAAQ;MACrBA,0DADqB,EAAY,EACtB;MACXA,4DAAA,oBAAqD;MACnDA,uDAAA,oBAAwD;MACxDA,4DAAA,iBAAW;MAAAA,oDAAA,qCAAwB;MACrCA,0DADqC,EAAY,EACtC;MAEXA,4DAAA,oBAA8C;MAArBA,wDAAA,mBAAA0B,gDAAA;QAAA,OAAST,GAAA,CAAAtD,QAAA,EAAU;MAAA,EAAC;MAC3CqC,uDAAA,oBAAyD;MACzDA,4DAAA,iBAAW;MAAAA,oDAAA,wBAAW;MAI9BA,0DAJ8B,EAAY,EACzB,EACF,EACP,EACM;;;MAtBcA,uDAAA,IAAsC;MAAtCA,gEAAA,KAAAiB,GAAA,CAAAzE,IAAA,kBAAAyE,GAAA,CAAAzE,IAAA,CAAAqF,SAAA,OAAAZ,GAAA,CAAAzE,IAAA,kBAAAyE,GAAA,CAAAzE,IAAA,CAAAsF,QAAA,KAAsC;MAElC9B,uDAAA,GAA6E;MAA7EA,+DAAA,EAAAiB,GAAA,CAAAzE,IAAA,kBAAAyE,GAAA,CAAAzE,IAAA,CAAAwF,aAAA,0DAA6E;;;;;;;;;;;;;;;;;;;;;;;;AC3BlD;;;;AASrD,MAAOxB,WAAW;EAOpBtE,YACYK,eAAgC,EAChCH,UAAqB,EACrBD,OAAsB;IAFtB,KAAAI,eAAe,GAAfA,eAAe;IACf,KAAAH,UAAU,GAAVA,UAAU;IACV,KAAAD,OAAO,GAAPA,OAAO;IARnB,KAAAK,IAAI,GAAIyF,qDAAM,CAAa,IAAI,CAAC;IAChC,KAAAC,MAAM,GAAID,qDAAM,CAAe,IAAI,CAAC;IAC5B,KAAAE,cAAc,GAAG,WAAW;IAC5B,KAAAC,gBAAgB,GAAG,aAAa;IAOpC,IAAI,CAACC,QAAQ,EAAE;EAClB;EAGQA,QAAQA,CAAA;IACZ,MAAMC,WAAW,GAAG,IAAI,CAACC,cAAc,EAAE;IACzC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAC7C,IAAI,CAACjG,IAAI,CAACkG,GAAG,CAACJ,WAAW,CAAC;IAC1B,IAAI,CAACJ,MAAM,CAACQ,GAAG,CAACF,aAAa,CAAC;EAClC;EAGOD,cAAcA,CAAA;IAClB,MAAMD,WAAW,GAAGnE,YAAY,CAACwE,OAAO,CAAC,IAAI,CAACR,cAAc,CAAC;IAC7D,MAAMS,UAAU,GAAG,IAAI,CAACC,SAAS,CAAOP,WAAY,CAAC;IACrD,IAAI,CAAC9F,IAAI,CAACkG,GAAG,CAACE,UAAU,CAAC;IACzB,OAAOA,UAAU;EACrB;EAEQH,gBAAgBA,CAAA;IACpB,MAAMP,MAAM,GAAG/D,YAAY,CAACwE,OAAO,CAAC,IAAI,CAACP,gBAAgB,CAAC;IAC1D,MAAMQ,UAAU,GAAG,IAAI,CAACC,SAAS,CAASX,MAAO,CAAC;IAClD,OAAOU,UAAU;EACrB;EAGQC,SAASA,CAAIrG,IAAY;IAC7B,IAAI;MACA,OAAOsG,IAAI,CAACC,KAAK,CAACvG,IAAI,CAAC;KAC1B,CAAC,OAAOwG,CAAC,EAAE;MACR,IAAI,CAACzG,eAAe,CAACqB,MAAM,CAAC;QACxBC,QAAQ,EAAG,IAAI;QACfC,MAAM,EAAE,yBAAyB;QACjCC,OAAO,EAAE,wFAAwF;QACjGC,OAAO,EAAE,CACT;UACIC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAEA,CAAA,KAAK;YACVC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;YACpCD,YAAY,CAACC,UAAU,CAAC,aAAa,CAAC;YACtCD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;YAChCD,YAAY,CAACC,UAAU,CAAC,SAAS,CAAC;YAClCD,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;YAC9CD,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;YAE3C,IAAI,CAACjC,OAAO,CAAC4D,YAAY,CAAC,QAAQ,CAAC;UACvC;SACH;OAGJ,CAAC,CAACzB,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;MACjC,OAAO,IAAI;;EAEnB;;eAjESgC,WAAW;;mBAAXA,YAAW,EAAAR,sDAAA,CAAArE,2DAAA,GAAAqE,sDAAA,CAAAG,oDAAA,GAAAH,sDAAA,CAAArE,yDAAA;AAAA;;SAAX6E,YAAW;EAAA0C,OAAA,EAAX1C,YAAW,CAAA2C,IAAA;EAAAC,UAAA,EAFR;AAAM", "sources": ["./src/app/profile/profile-routing.module.ts", "./src/app/profile/profile.module.ts", "./src/app/profile/profile.page.ts", "./src/app/profile/profile.page.html", "./src/app/services/user.service.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { ProfilePage } from './profile.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: ProfilePage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class ProfilePageRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { ProfilePageRoutingModule } from './profile-routing.module';\r\n\r\nimport { ProfilePage } from './profile.page';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    ProfilePageRoutingModule\r\n  ],\r\n  declarations: [ProfilePage]\r\n})\r\nexport class ProfilePageModule {}\r\n", "import { Component, OnInit,  } from '@angular/core';\r\nimport { AlertController, NavController } from '@ionic/angular';\r\nimport { ApiService } from '../services/api.service';\r\nimport { Location } from '@angular/common';\r\nimport { UserService } from '../services/user.service';\r\nimport { User } from './user.model';\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.page.html',\r\n  styleUrls: ['./profile.page.scss'],\r\n})\r\nexport class ProfilePage implements OnInit {\r\n\r\n  user : User | null = null;\r\n\r\n  constructor( private navCtrl: NavController ,\r\n    private apiService:ApiService,\r\n    private location:Location,\r\n    private userService:UserService,\r\n    private alertController: AlertController,\r\n\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.forceLightMode();\r\n    this.user = this.userService.user();    \r\n  }\r\n\r\n  goBack() {\r\n // check if there is pages before \r\n    if (this.location.getState() && (this.location.getState() as unknown as any)?.navigationId > 1) {\r\n      this.navCtrl.pop();\r\n    } else {\r\n      this.navCtrl.navigateBack('/scan-bl');\r\n    }\r\n    this.navCtrl.pop();\r\n  }\r\n\r\n  darkMode = false;\r\n\r\n \r\n\r\n  navigateTo(page: string) {\r\n    this.navCtrl.navigateForward(`/${page}`);\r\n  }\r\n\r\n  navigateToPayments() {\r\n    this.navCtrl.navigateForward('/payments');\r\n  }\r\n\r\n  toggleDarkMode() {\r\n    document.body.classList.toggle('dark', this.darkMode);\r\n  }\r\n\r\n \r\n\r\n  // onAbout() {\r\n  //    this.alertController.create({\r\n  //     animated : true,\r\n  //     header:'À propos',\r\n\r\n  //     message:'Sophatel Tous droits réservés',\r\n  //     buttons:['OK']\r\n  //    }).then(alert=>alert.present())\r\n  // }\r\n\r\n  onLogout() {\r\n    this.alertController.create({\r\n      animated:true,\r\n      header : \"Confirmation de déconnection\",\r\n      message:\"Voulez-vous vraiment se déconnecter\",\r\n      buttons:[\r\n        {\r\n          text:\"Oui\",\r\n          handler:()=>{\r\n            localStorage.removeItem('tokenUser');\r\n            localStorage.removeItem('tokenTenant');\r\n            localStorage.removeItem('token');\r\n            localStorage.removeItem('ocrMode');\r\n            localStorage.removeItem('forceSupplierGlobal');\r\n            localStorage.removeItem('selectedSupplier');\r\n            \r\n            this.navCtrl.navigateBack('/login');\r\n          },\r\n          \r\n        },\r\n        {\r\n          text:\"Non\",\r\n          role:'cancel'\r\n          \r\n        }\r\n      ]\r\n    }).then(alert=>alert.present())\r\n  }\r\n\r\n\r\n\r\n\r\n  private forceLightMode() {\r\n    // Remove dark mode from body\r\n    document.body.classList.remove('dark');\r\n    document.documentElement.classList.remove('dark');\r\n    \r\n    // Add light mode\r\n    document.body.classList.add('light');\r\n    document.documentElement.classList.add('light');\r\n\r\n    // Set attribute\r\n    document.body.setAttribute('data-theme', 'light');\r\n    \r\n    // Force color scheme\r\n    const meta = document.createElement('meta');\r\n    meta.name = 'color-scheme';\r\n    meta.content = 'light';\r\n    document.head.appendChild(meta);\r\n  }\r\n\r\n\r\n  async onAbout() {\r\n    const alert = await this.alertController.create({\r\n      header: 'À propos de l\\'application',\r\n      cssClass: 'custom-alert',\r\n      message: `\r\n        <div class=\"about-content\">\r\n          <h4>Description</h4>\r\n          <p>Avec <b>WinDoc</b>, scannez vos <b>Bons de Livraison</b> en quelques secondes. Les données extraites seront disponibles dans votre espace personnel sur <b>WinPlusPharma</b>.</p>\r\n          <p><small>Menu → Achats → Réception → Import BL → Importer BLs Scannés</small></p>\r\n          \r\n          <h4>Conditions d'utilisation</h4>\r\n          <ul>\r\n            <li>Cette application est réservée à un usage professionnel</li>\r\n            <li>Les données capturées sont traitées de manière confidentielle</li>\r\n            <li>L'utilisateur s'engage à respecter les bonnes pratiques de numérisation</li>\r\n          </ul>\r\n  \r\n          <h4>Version</h4>\r\n          <p>Version 1.0.0</p>\r\n          \r\n          <small class=\"privacy-link\" id=\"privacy-link\">Politique de confidentialité</small>\r\n        </div>\r\n      `,\r\n      buttons: [\r\n        {\r\n          text: 'Fermer',\r\n          role: 'cancel'\r\n        }\r\n      ]\r\n    });\r\n  \r\n    await alert.present();\r\n  \r\n    // Add click listener for privacy link\r\n    const privacyLink = document.getElementById('privacy-link');\r\n    if (privacyLink) {\r\n      privacyLink.addEventListener('click', () => {\r\n        alert.dismiss();\r\n        this.showPrivacyPolicy();\r\n      });\r\n    }\r\n  }\r\n  \r\n  async showPrivacyPolicy() {\r\n    const alert = await this.alertController.create({\r\n      header: 'Politique de confidentialité',\r\n      cssClass: 'custom-alert',\r\n      message: `\r\n        <div class=\"privacy-content\">\r\n          <p>Nous nous engageons à protéger vos données personnelles :</p>\r\n          <ul>\r\n            <li>Les données capturées sont stockées de manière sécurisée</li>\r\n            <li>Aucune information personnelle n'est partagée avec des tiers</li>\r\n            <li>Les documents sont traités conformément au RGPD</li>\r\n            <li>Vous disposez d'un droit d'accès et de rectification de vos données</li>\r\n          </ul>\r\n        </div>\r\n      `,\r\n      buttons: ['Fermer']\r\n    });\r\n  \r\n    await alert.present();\r\n  }\r\n\r\n  async logout() {\r\n    await this.apiService.logout();  // Wait for the confirmation dialog\r\n    this.navCtrl.navigateRoot('/login');  // Then navigate to login\r\n  }\r\n\r\n}\r\n", "<ion-header>\r\n  <ion-toolbar>\r\n    <ion-title style=\"margin-left:10px\">Paramétres</ion-title>\r\n    <ion-buttons slot=\"start\">\r\n      <ion-button (click)=\"goBack()\">\r\n        <ion-icon slot=\"icon-only\" name=\"chevron-back-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-buttons slot=\"end\">\r\n      <ion-button (click)=\"logout()\">\r\n        <ion-icon slot=\"icon-only\" name=\"log-out-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n\r\n<ion-content>\r\n  <div class=\"profile-container\">\r\n    <!-- Avatar and user info -->\r\n    <ion-avatar class=\"profile-avatar\">\r\n      <img src=\"https://gravatar.com/avatar/placeholder?d=mp\" alt=\"profile picture\">\r\n    </ion-avatar>\r\n    \r\n    <ion-text color=\"white\" style=\"display: flex;flex-direction: column;align-items: center;\">\r\n      <h2 class=\"user-name\">{{user?.firstname}} {{user?.lastname}}</h2>\r\n      <!-- <p class=\"user-email\">john.doeexample.com</p> -->\r\n      <ion-badge color=\"light\" >{{user?.mainAuthority === 'ROLE_PHARMACIEN' ? 'PHARMACIEN':'AIDE PHARMACIE'}}</ion-badge>\r\n    </ion-text>\r\n\r\n    <!-- List of actions -->\r\n    <ion-list>\r\n      <ion-item button detail (click)=\"onAbout()\">\r\n        <ion-icon slot=\"start\" name=\"information-circle-outline\"></ion-icon>\r\n        <ion-label>À propos</ion-label>\r\n      </ion-item>\r\n      <ion-item button detail routerLink=\"/medicament-ocr\">\r\n        <ion-icon slot=\"start\" name=\"medkit-outline\"></ion-icon>\r\n        <ion-label>Recherche de médicaments</ion-label>\r\n      </ion-item>\r\n\r\n      <ion-item button detail  (click)=\"onLogout()\">\r\n        <ion-icon slot=\"start\" name=\"log-out-outline\"></ion-icon>\r\n        <ion-label>Déconnexion</ion-label>\r\n      </ion-item>\r\n    </ion-list>\r\n  </div>\r\n</ion-content>", "import { Injectable, signal, Signal } from \"@angular/core\";\r\nimport { Tenant, User } from \"../profile/user.model\";\r\nimport { <PERSON>ert<PERSON>ontroller, NavController } from \"@ionic/angular\";\r\nimport { ApiService } from \"./api.service\";\r\n\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n  })\r\nexport class UserService {\r\n\r\n    user  = signal<User| null>(null);\r\n    tenant  = signal<Tenant| null>(null);\r\n    private USER_TOKEN_KEY = 'tokenUser';\r\n    private TENANT_TOKEN_KEY = 'tokenTenant';\r\n\r\n    constructor(\r\n        private alertController: AlertController,\r\n        private apiService:ApiService,\r\n        private navCtrl: NavController\r\n    ) {\r\n        this.loadAuth()\r\n     }\r\n\r\n\r\n     private loadAuth(){\r\n         const currentUser = this.getCurrentUser();\r\n         const currentTenant = this.getCurrentTenant();\r\n         this.user.set(currentUser);\r\n         this.tenant.set(currentTenant);\r\n     }\r\n\r\n\r\n    private getCurrentUser() {\r\n        const currentUser = localStorage.getItem(this.USER_TOKEN_KEY);\r\n        const parsedUser = this.parseUser<User>(currentUser!);\r\n        this.user.set(parsedUser)\r\n        return parsedUser;\r\n    }\r\n\r\n    private getCurrentTenant(){\r\n        const tenant = localStorage.getItem(this.TENANT_TOKEN_KEY);\r\n        const parsedUser = this.parseUser<Tenant>(tenant!);\r\n        return parsedUser;\r\n    }\r\n\r\n\r\n    private parseUser<T>(user: string ) : T | null {\r\n        try {\r\n            return JSON.parse(user);\r\n        } catch (e) {\r\n            this.alertController.create({\r\n                animated : true,\r\n                header: 'une erreur est survenue',\r\n                message: 'on ne peut pas récupérer les informations de l\\'utilisateur, Merci de vous reconnecter',\r\n                buttons: [\r\n                {\r\n                    text: 'OK',\r\n                    handler: () => {\r\n                        localStorage.removeItem('tokenUser');\r\n                        localStorage.removeItem('tokenTenant');\r\n                        localStorage.removeItem('token');\r\n                        localStorage.removeItem('ocrMode');\r\n                        localStorage.removeItem('forceSupplierGlobal');\r\n                        localStorage.removeItem('selectedSupplier');\r\n                        \r\n                        this.navCtrl.navigateRoot('/login');\r\n                    }\r\n                }\r\n                ]\r\n            \r\n            }).then(alert => alert.present());\r\n            return null;\r\n        }\r\n    }\r\n}"], "names": ["RouterModule", "ProfilePage", "routes", "path", "component", "ProfilePageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "ProfilePageModule", "declarations", "constructor", "navCtrl", "apiService", "location", "userService", "alertController", "user", "darkMode", "ngOnInit", "forceLightMode", "goBack", "_this$location$getSta", "getState", "navigationId", "pop", "navigateBack", "navigateTo", "page", "navigateForward", "navigateToPayments", "toggleDarkMode", "document", "body", "classList", "toggle", "onLogout", "create", "animated", "header", "message", "buttons", "text", "handler", "localStorage", "removeItem", "role", "then", "alert", "present", "remove", "documentElement", "add", "setAttribute", "meta", "createElement", "name", "content", "head", "append<PERSON><PERSON><PERSON>", "onAbout", "_this", "_asyncToGenerator", "cssClass", "privacyLink", "getElementById", "addEventListener", "dismiss", "showPrivacyPolicy", "_this2", "logout", "_this3", "navigateRoot", "i0", "ɵɵdirectiveInject", "NavController", "i2", "ApiService", "i3", "Location", "i4", "UserService", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectors", "decls", "vars", "consts", "template", "ProfilePage_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProfilePage_Template_ion_button_click_5_listener", "ɵɵelement", "ProfilePage_Template_ion_button_click_8_listener", "ProfilePage_Template_ion_item_click_20_listener", "ProfilePage_Template_ion_item_click_28_listener", "ɵɵadvance", "ɵɵtextInterpolate2", "firstname", "lastname", "ɵɵtextInterpolate", "mainAuthority", "signal", "tenant", "USER_TOKEN_KEY", "TENANT_TOKEN_KEY", "loadAuth", "currentUser", "getCurrentUser", "currentTenant", "get<PERSON><PERSON><PERSON><PERSON><PERSON>t", "set", "getItem", "parsedUser", "parseUser", "JSON", "parse", "e", "ɵɵinject", "factory", "ɵfac", "providedIn"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}