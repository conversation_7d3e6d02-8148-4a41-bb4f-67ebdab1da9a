"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_crop-doc_crop-doc_module_ts"],{

/***/ 84609:
/*!***********************************************************************************!*\
  !*** ./src/app/components/image-cropper-custom/image-cropper-custom.component.ts ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ImageCropperCustomComponent: () => (/* binding */ ImageCropperCustomComponent)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 60316);

var _ImageCropperCustomComponent;
// image-cropper-custom.component.ts



const _c0 = ["svgElement"];
function ImageCropperCustomComponent__svg_image_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "image", 8);
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("href", ctx_r1.img == null ? null : ctx_r1.img.src)("width", ctx_r1.currentWidth)("height", ctx_r1.currentHeight);
  }
}
function ImageCropperCustomComponent__svg_g_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "g")(1, "circle", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("pointerdown", function ImageCropperCustomComponent__svg_g_8_Template_circle_pointerdown_1_listener($event) {
      const i_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r3).index;
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r1.startDragging($event, i_r4));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "circle", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const point_r5 = ctx.$implicit;
    const i_r4 = ctx.index;
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("data-index", i_r4)("cx", point_r5.x)("cy", point_r5.y)("pointer-events", "all");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("touch-active", ctx_r1.activePoint === i_r4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("data-index", i_r4)("cx", point_r5.x)("cy", point_r5.y)("pointer-events", "none");
  }
}
function ImageCropperCustomComponent__svg_g_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "g")(1, "rect", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("pointerdown", function ImageCropperCustomComponent__svg_g_9_Template_rect_pointerdown_1_listener($event) {
      const j_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r6).index;
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r1.startMidpointDragging($event, j_r7));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const midpoint_r8 = ctx.$implicit;
    const j_r7 = ctx.index;
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("touch-active", ctx_r1.activeMidpoint === j_r7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("data-index", "mid-" + j_r7)("width", ctx_r1.getMidpointDimensions(j_r7).width)("height", ctx_r1.getMidpointDimensions(j_r7).height)("x", ctx_r1.getMidpointX(midpoint_r8, j_r7))("y", ctx_r1.getMidpointY(midpoint_r8, j_r7))("rx", ctx_r1.getMidpointRadius(j_r7))("ry", ctx_r1.getMidpointRadius(j_r7));
  }
}
function ImageCropperCustomComponent__svg_g_10__svg_image_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "image", 19);
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("href", ctx_r1.img == null ? null : ctx_r1.img.src)("width", ctx_r1.currentWidth)("height", ctx_r1.currentHeight);
  }
}
function ImageCropperCustomComponent__svg_g_10__svg_ng_container_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "line", 20)(2, "line", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("x1", ctx_r1.quad.points[ctx_r1.activePoint].x)("y1", ctx_r1.quad.points[ctx_r1.activePoint].y - 50)("x2", ctx_r1.quad.points[ctx_r1.activePoint].x)("y2", ctx_r1.quad.points[ctx_r1.activePoint].y + 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("x1", ctx_r1.quad.points[ctx_r1.activePoint].x - 50)("y1", ctx_r1.quad.points[ctx_r1.activePoint].y)("x2", ctx_r1.quad.points[ctx_r1.activePoint].x + 50)("y2", ctx_r1.quad.points[ctx_r1.activePoint].y);
  }
}
function ImageCropperCustomComponent__svg_g_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "g", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "circle", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](2, "clipPath", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "circle");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "g", 15)(5, "g");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](6, ImageCropperCustomComponent__svg_g_10__svg_image_6_Template, 1, 3, "image", 16)(7, ImageCropperCustomComponent__svg_g_10__svg_ng_container_7_Template, 3, 8, "ng-container", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](8, "circle", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("cx", ctx_r1.magnifierPosition.x)("cy", ctx_r1.magnifierPosition.y)("r", ctx_r1.magnifierRadius);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("cx", ctx_r1.magnifierPosition.x)("cy", ctx_r1.magnifierPosition.y)("r", ctx_r1.magnifierRadius);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("transform", ctx_r1.getZoomTransform());
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r1.img);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r1.activePoint !== null);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("cx", ctx_r1.magnifierPosition.x)("cy", ctx_r1.magnifierPosition.y)("r", ctx_r1.magnifierRadius);
  }
}
class ImageCropperCustomComponent {
  onPointerEnd() {
    this.stopDragging();
  }
  onPointerMove(event) {
    if (!this.isDragging) return;
    event.preventDefault();
    event.stopPropagation();
    this.handleMove(event);
  }
  onTouchStart(event) {
    if (event.touches.length !== 1) return; // Only handle single touches
    event.preventDefault();
    event.stopPropagation();
    const touch = event.touches[0];
    const element = document.elementFromPoint(touch.clientX, touch.clientY);
    if (element !== null && element !== void 0 && element.classList.contains('handle-touch-area')) {
      const index = parseInt(element.getAttribute('data-index') || '0', 10);
      this.onPointTouchStart(event, index);
    }
  }
  onTouchMove(event) {
    if (!this.isDragging || event.touches.length !== 1) return;
    event.preventDefault();
    event.stopPropagation();
    const touch = event.touches[0];
    this.handleMove(touch);
  }
  onTouchEnd() {
    this.stopDragging();
  }
  onDocumentMouseMove(event) {
    this.handleMove(event);
  }
  // In image-cropper-custom.component.ts
  set imageUrl(url) {
    if (url) {
      const img = new Image();
      img.onload = () => {
        this.img = img;
        this.currentWidth = img.naturalWidth;
        this.currentHeight = img.naturalHeight;
        // Apply pending coordinates if they exist
        if (this._pendingCoordinates) {
          this.initialCoordinates = this._pendingCoordinates;
          this._pendingCoordinates = null;
        } else {
          this.initializeDefaultPoints();
        }
        this.cdRef.detectChanges();
      };
      img.src = url;
    }
  }
  set initialCoordinates(coords) {
    if (!coords || coords.length === 0) {
      this.initializeDefaultPoints();
      return;
    }
    const validCoords = coords.slice(0, 4).map(coord => ({
      x: typeof coord.x === 'number' ? coord.x : 0,
      y: typeof coord.y === 'number' ? coord.y : 0
    }));
    while (validCoords.length < 4) {
      validCoords.push({
        x: 0,
        y: 0
      });
    }
    if (this.currentWidth > 0) {
      this.quad.points = validCoords.map(point => {
        const x = this.convertCoordinate(point.x, this.currentWidth);
        const y = this.convertCoordinate(point.y, this.currentHeight);
        return this.clampCoordinates({
          x,
          y
        });
      });
      this.calculateMidpoints(); // Add this line to calculate midpoints
      this.cdRef.detectChanges();
    } else {
      this._pendingCoordinates = validCoords;
    }
  }
  set rotation(value) {
    if (this._rotation !== value) {
      const deltaAngle = value - (this._rotation || 0);
      this._rotation = value;
      if (this.quad.points && this.validateCoordinates(this.quad.points)) {
        this.rotateCoordinates(deltaAngle);
      }
      this.cdRef.detectChanges();
    }
  }
  convertCoordinate(value, max) {
    return value <= 1 ? value * max : value;
  }
  constructor(cdRef) {
    this.cdRef = cdRef;
    this.width = '100%';
    this.height = '100%';
    this.coordinatesChange = new _angular_core__WEBPACK_IMPORTED_MODULE_1__.EventEmitter();
    this._rotation = 0;
    this._pendingCoordinates = null;
    this.img = null;
    this.quad = {
      points: []
    };
    this.currentWidth = 0;
    this.currentHeight = 0;
    this.activePoint = null;
    this.isDragging = false;
    this.isMovingCropArea = false;
    this.lastMousePosition = null;
    this.touchStartPos = null;
    this.lastTouchPos = null;
    this.isTouchMoving = false;
    this.touchActive = false;
    this.isTouchHandled = false;
    this.showMagnifier = false;
    this.magnifierPosition = {
      x: 0,
      y: 0
    };
    this.magnifierRadius = 350; // Radius of the magnifier
    this.zoomFactor = 4; // How much to zoom in
    this.midpoints = []; // Array to store midpoint coordinates
    this.activeMidpoint = null; // Track the active midpoint being dragged
    this.midpointDragConstraint = null; // Constraint for midpoint movement
  }
  ngOnInit() {
    if (!this.quad.points.length) {
      this.initializeDefaultPoints();
    } else {
      this.validateQuad();
    }
  }
  get rotation() {
    return this._rotation || 0;
  }
  initializeDefaultPoints() {
    if (this.currentWidth > 0 && this.currentHeight > 0) {
      const margin = Math.min(50, Math.min(this.currentWidth, this.currentHeight) / 10);
      this.quad.points = [{
        x: margin,
        y: margin
      }, {
        x: this.currentWidth - margin,
        y: margin
      }, {
        x: this.currentWidth - margin,
        y: this.currentHeight - margin
      }, {
        x: margin,
        y: this.currentHeight - margin
      }].map(point => this.clampCoordinates(point));
      this.calculateMidpoints(); // Add this line
    }
  }
  // handleTouchMove(event: TouchEvent) {
  //   if (!this.isDragging && !this.isMovingCropArea) return;
  //   event.preventDefault();
  //   const touch = event.touches[0];
  //   const pos = this.getTouchPosition(touch);
  //   if (this.isDragging && this.activePoint !== null) {
  //     this.quad.points[this.activePoint] = pos;
  //   } else if (this.isMovingCropArea) {
  //     const delta = {
  //       x: pos.x - (this.lastTouchPos?.x || 0),
  //       y: pos.y - (this.lastTouchPos?.y || 0)
  //     };
  //     this.quad.points = this.quad.points.map(p => ({
  //       x: p.x + delta.x,
  //       y: p.y + delta.y
  //     }));
  //   }
  //   this.lastTouchPos = pos;
  // }
  getTouchPosition(event) {
    const svg = this.svgElement.nativeElement;
    const CTM = svg.getScreenCTM();
    if (!CTM) return {
      x: 0,
      y: 0
    };
    const clientX = 'touches' in event ? event.clientX : event.clientX;
    const clientY = 'touches' in event ? event.clientY : event.clientY;
    return {
      x: (clientX - CTM.e) / CTM.a,
      y: (clientY - CTM.f) / CTM.d
    };
  }
  set inactiveSelections(value) {
    // Maintain compatibility with original component
  }
  calculateRotatedDimensions() {
    const angle = Math.abs(this._rotation % 180); // Normalize angle to 0-180
    const rad = angle * Math.PI / 180;
    const cos = Math.cos(rad);
    const sin = Math.sin(rad);
    // Calculate rotated dimensions
    let rotatedWidth = Math.abs(this.currentWidth * cos) + Math.abs(this.currentHeight * sin);
    let rotatedHeight = Math.abs(this.currentWidth * sin) + Math.abs(this.currentHeight * cos);
    // If image is vertical (height > width)
    if (this.currentHeight > this.currentWidth) {
      if (angle > 45 && angle < 135) {
        // When rotated horizontally (closer to 90 degrees)
        // Adjust the dimensions to maintain aspect ratio
        const scale = this.currentHeight / this.currentWidth;
        rotatedWidth = Math.min(rotatedWidth, this.currentHeight);
        rotatedHeight = rotatedWidth / scale;
      } else {
        // When vertical or close to vertical, use original dimensions
        rotatedWidth = this.currentWidth;
        rotatedHeight = this.currentHeight;
      }
    }
    return {
      width: rotatedWidth,
      height: rotatedHeight
    };
  }
  getViewBox() {
    if (!this.currentWidth || !this.currentHeight) {
      return '0 0 800 600';
    }
    const {
      width,
      height
    } = this.calculateRotatedDimensions();
    // Calculate the center point
    const centerX = this.currentWidth / 2;
    const centerY = this.currentHeight / 2;
    // Calculate the viewBox dimensions
    const viewBoxWidth = width;
    const viewBoxHeight = height;
    // Calculate the offset to center the content
    const offsetX = centerX - viewBoxWidth / 2;
    const offsetY = centerY - viewBoxHeight / 2;
    return `${offsetX} ${offsetY} ${viewBoxWidth} ${viewBoxHeight}`;
  }
  // Update clampCoordinates to use the new dimensions
  clampCoordinates(coord) {
    const {
      width,
      height
    } = this.calculateRotatedDimensions();
    const centerX = this.currentWidth / 2;
    const centerY = this.currentHeight / 2;
    // Calculate bounds based on rotated dimensions
    const minX = centerX - width / 2;
    const maxX = centerX + width / 2;
    const minY = centerY - height / 2;
    const maxY = centerY + height / 2;
    return {
      x: Math.max(minX, Math.min(maxX, coord.x)),
      y: Math.max(minY, Math.min(maxY, coord.y))
    };
  }
  getRotationTransform() {
    const centerX = this.currentWidth / 2;
    const centerY = this.currentHeight / 2;
    return `rotate(${this.rotation} ${centerX} ${centerY})`;
  }
  getPolygonPoints() {
    return this.quad.points.map(point => `${point.x},${point.y}`).join(' ');
  }
  getOverlayPath() {
    const points = this.quad.points;
    const width = this.currentWidth;
    const height = this.currentHeight;
    if (!points || points.length < 4) {
      return `M 0,0 H ${width} V ${height} H 0 Z`;
    }
    return `
      M 0,0 H ${width} V ${height} H 0 Z
      M ${points[0].x},${points[0].y}
      L ${points[1].x},${points[1].y}
      L ${points[2].x},${points[2].y}
      L ${points[3].x},${points[3].y} Z
    `;
  }
  getCropTransform() {
    return ''; // For additional transformations if needed
  }
  getMousePosition(event) {
    const svg = this.svgElement.nativeElement;
    const CTM = svg.getScreenCTM();
    if (!CTM) return {
      x: 0,
      y: 0
    };
    return {
      x: (event.clientX - CTM.e) / CTM.a,
      y: (event.clientY - CTM.f) / CTM.d
    };
  }
  startDragging(event, index) {
    if (this.isTouchHandled) return;
    event.preventDefault();
    event.stopPropagation();
    this.isTouchHandled = true;
    this.isDragging = true;
    this.activePoint = index;
    this.showMagnifier = true;
    const coords = this.getSVGCoordinates(event);
    this.updateMagnifierPosition(coords);
    if (event.pointerType === 'touch') {
      this.lastTouchPos = this.clampCoordinates(coords);
    } else {
      this.lastMousePosition = this.clampCoordinates(coords);
    }
    this.cdRef.detectChanges();
  }
  startMovingCropArea(event) {
    event.preventDefault();
    this.isMovingCropArea = true;
    if (event instanceof TouchEvent) {
      this.lastMousePosition = this.getSVGCoordinates(event.touches[0]);
    } else {
      this.lastMousePosition = this.getSVGCoordinates(event);
    }
  }
  onPointTouchStart(event, index) {
    console.log('onPointTouchStart', event, index);
    if (this.isTouchHandled) return; // Prevent multiple triggers
    event.preventDefault();
    event.stopPropagation();
    this.isTouchHandled = true;
    this.touchActive = true;
    this.isDragging = true;
    this.activePoint = index;
    const touch = event.touches[0];
    this.lastTouchPos = this.getSVGCoordinates(touch);
    this.cdRef.detectChanges();
  }
  handleTouchMove(event) {
    event.preventDefault();
    event.stopPropagation();
    if (!this.isDragging && !this.isMovingCropArea) return;
    const touch = event.touches[0];
    if (this.isDragging && this.activePoint !== null) {
      const currentPos = this.getSVGCoordinates(touch);
      // Direct position update for more responsive feeling
      this.quad.points[this.activePoint] = currentPos;
      this.lastTouchPos = currentPos;
      // Force change detection
      this.cdRef.detectChanges();
    }
  }
  onPolygonTouchStart(event) {
    event.preventDefault();
    event.stopPropagation();
    // this.startMovingCropArea(event);
  }
  handleMouseMove(event) {
    this.handleMove(event);
  }
  handleStart(event, index) {
    this.activePoint = index;
    this.isDragging = true;
  }
  // private handleAreaStart(event: PointerPosition) {
  //   this.isMovingCropArea = true;
  //   this.lastMousePosition = this.getPosition(event);
  // }
  handleMove(input) {
    if (!this.isDragging) return;
    const currentPos = this.getSVGCoordinates(input);
    let clampedPos = this.clampCoordinates(currentPos);
    if (this.activePoint !== null) {
      const newPoints = [...this.quad.points];
      newPoints[this.activePoint] = clampedPos;
      if (this.isValidQuadrilateral(newPoints)) {
        this.quad.points[this.activePoint] = clampedPos;
        this.calculateMidpoints(); // Recalculate midpoints after corner move
        this.updateMagnifierPosition(clampedPos);
      }
    } else if (this.activeMidpoint !== null) {
      const midpoint = this.midpoints[this.activeMidpoint];
      if (this.midpointDragConstraint === 'horizontal') {
        clampedPos.y = midpoint.y; // Lock y-axis
      } else if (this.midpointDragConstraint === 'vertical') {
        clampedPos.x = midpoint.x; // Lock x-axis
      }
      this.updateCornerPointsFromMidpoint(this.activeMidpoint, clampedPos);
      this.calculateMidpoints(); // Recalculate midpoints after move
      this.updateMagnifierPosition(clampedPos);
    }
    this.lastMousePosition = clampedPos;
    this.cdRef.detectChanges();
  }
  isValidQuadrilateral(points) {
    if (points.length !== 4) return false;
    // Minimum distance between points (in pixels)
    const minDistance = 20;
    // Check minimum distance between all points
    for (let i = 0; i < points.length; i++) {
      for (let j = i + 1; j < points.length; j++) {
        const distance = this.getDistance(points[i], points[j]);
        if (distance < minDistance) {
          return false;
        }
      }
    }
    // Check if points form a convex quadrilateral
    return this.isConvex(points);
  }
  getDistance(p1, p2) {
    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }
  isConvex(points) {
    if (points.length !== 4) return false;
    let sign = 0;
    for (let i = 0; i < points.length; i++) {
      const p1 = points[i];
      const p2 = points[(i + 1) % 4];
      const p3 = points[(i + 2) % 4];
      const crossProduct = (p2.x - p1.x) * (p3.y - p1.y) - (p2.y - p1.y) * (p3.x - p1.x);
      if (i === 0) {
        sign = Math.sign(crossProduct);
      } else if (Math.sign(crossProduct) !== sign && crossProduct !== 0) {
        return false;
      }
    }
    return true;
  }
  arePointsWithinBounds(points) {
    return points.every(point => point.x >= 0 && point.x <= this.currentWidth && point.y >= 0 && point.y <= this.currentHeight);
  }
  // Generic position getter that works with both touch and mouse events
  // private getPosition(event: MouseEvent | TouchEvent): Coordinates {
  //   const svg = this.svgElement.nativeElement;
  //   const CTM = svg.getScreenCTM();
  //   if (!CTM) return { x: 0, y: 0 };
  //   // 2) Extract clientX, clientY via our helper
  //   const { clientX, clientY } = this.getPointerPosition(event);
  //   return {
  //     x: (clientX - CTM.e) / CTM.a,
  //     y: (clientY - CTM.f) / CTM.d,
  //   };
  // }
  getSVGCoordinates(input) {
    const svg = this.svgElement.nativeElement;
    const CTM = svg.getScreenCTM();
    if (!CTM) return {
      x: 0,
      y: 0
    };
    let clientX;
    let clientY;
    if (input instanceof MouseEvent) {
      clientX = input.clientX;
      clientY = input.clientY;
    } else {
      clientX = input.clientX;
      clientY = input.clientY;
    }
    return {
      x: (clientX - CTM.e) / CTM.a,
      y: (clientY - CTM.f) / CTM.d
    };
  }
  // The helper function
  getPointerPosition(input) {
    if (input instanceof MouseEvent) {
      // It's a MouseEvent
      return {
        clientX: input.clientX,
        clientY: input.clientY
      };
    } else {
      // It's a single Touch object
      return {
        clientX: input.clientX,
        clientY: input.clientY
      };
    }
  }
  stopDragging() {
    this.isDragging = false;
    this.activePoint = null;
    this.activeMidpoint = null; // Reset midpoint
    this.midpointDragConstraint = null; // Reset constraint
    this.lastMousePosition = null;
    this.touchActive = false;
    this.isTouchHandled = false;
    this.showMagnifier = false;
    this.cdRef.detectChanges();
  }
  getQuad() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      // If there's a rotation, we need to apply the inverse rotation to get the original coordinates
      if (_this._rotation !== 0) {
        const centerX = _this.currentWidth / 2;
        const centerY = _this.currentHeight / 2;
        const rad = -_this._rotation * Math.PI / 180; // Note the negative angle for inverse rotation
        const cos = Math.cos(rad);
        const sin = Math.sin(rad);
        const rotatedPoints = _this.quad.points.map(point => {
          // Translate to origin
          const dx = point.x - centerX;
          const dy = point.y - centerY;
          // Rotate
          const rotatedX = centerX + (dx * cos - dy * sin);
          const rotatedY = centerY + (dx * sin + dy * cos);
          return {
            x: Math.round(rotatedX),
            y: Math.round(rotatedY)
          };
        });
        return {
          points: rotatedPoints
        };
      }
      // If no rotation, return the points as is
      return {
        points: _this.quad.points.map(point => ({
          x: Math.round(point.x),
          y: Math.round(point.y)
        }))
      };
    })();
  }
  validateQuad() {
    if (!this.quad.points) return;
    this.quad.points = this.quad.points.map(point => this.clampCoordinates(point));
  }
  // In image-cropper-custom.component.ts
  rotateCoordinates(angle) {
    if (!this.quad.points || this.quad.points.length !== 4) {
      console.error('Invalid points array for rotation');
      return;
    }
    const centerX = this.currentWidth / 2;
    const centerY = this.currentHeight / 2;
    const rad = angle * Math.PI / 180;
    const cos = Math.cos(rad);
    const sin = Math.sin(rad);
    // Rotate corner points
    this.quad.points = this.quad.points.map(point => {
      if (typeof point.x === 'undefined' || typeof point.y === 'undefined') {
        console.error('Invalid point coordinates:', point);
        return point;
      }
      // Translate point to origin
      const dx = point.x - centerX;
      const dy = point.y - centerY;
      // Rotate point
      const newX = centerX + (dx * cos - dy * sin);
      const newY = centerY + (dx * sin + dy * cos);
      // Clamp coordinates to image boundaries
      return this.clampCoordinates({
        x: newX,
        y: newY
      });
    });
    // Recalculate midpoints after rotating corner points
    this.calculateMidpoints();
    this.cdRef.detectChanges();
  }
  validateCoordinates(points) {
    if (!points || points.length !== 4) {
      console.error('Invalid number of points:', points);
      return false;
    }
    return points.every((point, index) => {
      if (typeof point.x === 'undefined' || typeof point.y === 'undefined') {
        console.error(`Invalid coordinates at index ${index}:`, point);
        return false;
      }
      return true;
    });
  }
  getMagnifierTransform() {
    return ''; // No transform needed for the magnifier group
  }
  updateMagnifierPosition(point) {
    if (this.activePoint !== null) {
      const activePoint = this.quad.points[this.activePoint];
      // Apply rotation to the point coordinates
      const centerX = this.currentWidth / 2;
      const centerY = this.currentHeight / 2;
      const rotatedPoint = this.rotatePoint(activePoint, {
        x: centerX,
        y: centerY
      }, this.rotation);
      const margin = this.magnifierRadius * 1.2;
      // Calculate position based on rotated coordinates
      let magnifierX = rotatedPoint.x;
      let magnifierY = rotatedPoint.y;
      if (rotatedPoint.y < this.currentHeight / 2) {
        magnifierY = rotatedPoint.y + margin;
      } else {
        magnifierY = rotatedPoint.y - margin;
      }
      if (rotatedPoint.x < this.currentWidth / 2) {
        magnifierX = rotatedPoint.x + margin;
      } else {
        magnifierX = rotatedPoint.x - margin;
      }
      this.magnifierPosition = {
        x: magnifierX,
        y: magnifierY
      };
      this.cdRef.detectChanges();
    }
  }
  // Helper method to rotate a point
  rotatePoint(point, center, angleDegrees) {
    const angleRadians = angleDegrees * Math.PI / 180;
    const cos = Math.cos(angleRadians);
    const sin = Math.sin(angleRadians);
    const dx = point.x - center.x;
    const dy = point.y - center.y;
    return {
      x: center.x + (dx * cos - dy * sin),
      y: center.y + (dx * sin + dy * cos)
    };
  }
  // Update the getZoomTransform method to center on the active point
  getZoomTransform() {
    if (this.activePoint === null) return '';
    const activePoint = this.quad.points[this.activePoint];
    const scale = this.zoomFactor;
    // Calculate center of rotation
    const centerX = this.currentWidth / 2;
    const centerY = this.currentHeight / 2;
    // Calculate offset to keep the active point centered in the magnifier
    const dx = this.magnifierPosition.x - activePoint.x * scale;
    const dy = this.magnifierPosition.y - activePoint.y * scale;
    // Include rotation in the transformation
    return `translate(${dx}, ${dy}) translate(${centerX * scale}, ${centerY * scale}) rotate(${this.rotation}) translate(${-centerX * scale}, ${-centerY * scale}) scale(${scale})`;
  }
  // getZoomTransform(): string {
  //   if (this.activePoint === null) return '';
  //   const activePoint = this.quad.points[this.activePoint];
  //   const scale = this.zoomFactor;
  //   const centerX = this.currentWidth / 2;
  //   const centerY = this.currentHeight / 2;
  //   const dx = this.magnifierPosition.x - activePoint.x * scale;
  //   const dy = this.magnifierPosition.y - activePoint.y * scale;
  //   return `translate(${dx}, ${dy}) translate(${centerX * scale}, ${centerY * scale}) rotate(${this.rotation}) translate(${-centerX * scale}, ${-centerY * scale}) scale(${scale})`;
  // }
  getRotatedIntersectionLines() {
    if (this.activePoint === null) return {
      vertical: null,
      horizontal: null
    };
    const point = this.quad.points[this.activePoint];
    const lineLength = 50;
    // Calculate unrotated lines first
    const vertical = {
      x1: point.x,
      y1: point.y - lineLength,
      x2: point.x,
      y2: point.y + lineLength
    };
    const horizontal = {
      x1: point.x - lineLength,
      y1: point.y,
      x2: point.x + lineLength,
      y2: point.y
    };
    // No need to rotate the lines as they will be transformed by the parent group's transform
    return {
      vertical,
      horizontal
    };
  }
  calculateMidpoints() {
    this.midpoints = [];
    for (let i = 0; i < 4; i++) {
      const p1 = this.quad.points[i];
      const p2 = this.quad.points[(i + 1) % 4];
      const midpoint = {
        x: (p1.x + p2.x) / 2,
        y: (p1.y + p2.y) / 2
      };
      this.midpoints.push(midpoint);
    }
  }
  startMidpointDragging(event, index) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
    this.activeMidpoint = index;
    this.showMagnifier = false;
    // Calculate the current angle of the edge
    const edgeAngle = this.getEdgeAngle(index);
    const normalizedAngle = Math.abs(edgeAngle % 180); // Normalize to 0-180°
    // Determine if the edge is more horizontal or vertical
    if (normalizedAngle < 45 || normalizedAngle > 135) {
      // Edge is more horizontal, so drag should be vertical
      this.midpointDragConstraint = 'vertical';
    } else {
      // Edge is more vertical, so drag should be horizontal
      this.midpointDragConstraint = 'horizontal';
    }
    const coords = this.getSVGCoordinates(event);
    this.updateMagnifierPosition(coords);
    this.lastMousePosition = this.clampCoordinates(coords);
    this.cdRef.detectChanges();
  }
  updateCornerPointsFromMidpoint(midpointIndex, newPos) {
    const prevIndex = midpointIndex;
    const nextIndex = (midpointIndex + 1) % 4;
    const prevPoint = this.quad.points[prevIndex];
    const nextPoint = this.quad.points[nextIndex];
    if (this.midpointDragConstraint === 'horizontal') {
      prevPoint.x = newPos.x;
      nextPoint.x = newPos.x;
    } else if (this.midpointDragConstraint === 'vertical') {
      prevPoint.y = newPos.y;
      nextPoint.y = newPos.y;
    }
  }
  getEdgeAngle(index) {
    const p1 = this.quad.points[index];
    const p2 = this.quad.points[(index + 1) % 4];
    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;
    return Math.atan2(dy, dx) * (180 / Math.PI); // Angle in degrees
  }
  getMidpointWidth(index) {
    // Top and bottom midpoints (0 and 2)
    if (index === 0 || index === 2) {
      return 250; // Wider for horizontal handles
    }
    // Left and right midpoints (1 and 3)
    return 70; // Narrower for vertical handles
  }
  getMidpointHeight(index) {
    // Top and bottom midpoints (0 and 2)
    if (index === 0 || index === 2) {
      return 70; // Shorter for horizontal handles
    }
    // Left and right midpoints (1 and 3)
    return 250; // Taller for vertical handles
  }
  getMidpointX(midpoint, index) {
    const {
      width
    } = this.getMidpointDimensions(index);
    return midpoint.x - width / 2;
  }
  getMidpointY(midpoint, index) {
    const {
      height
    } = this.getMidpointDimensions(index);
    return midpoint.y - height / 2;
  }
  getMidpointRadius(index) {
    return 35; // Consistent border radius for all handles
  }
  getMidpointDimensions(index) {
    // Use your existing getEdgeAngle(index):
    const angle = Math.abs(this.getEdgeAngle(index) % 180);
    // If angle < 45° or > 135° => Edge is more "horizontal"
    // Else => Edge is more "vertical"
    if (angle < 45 || angle > 135) {
      // "Horizontal" handle
      return {
        width: 250,
        height: 70
      };
    } else {
      // "Vertical" handle
      return {
        width: 70,
        height: 250
      };
    }
  }
}
_ImageCropperCustomComponent = ImageCropperCustomComponent;
_ImageCropperCustomComponent.ɵfac = function ImageCropperCustomComponent_Factory(t) {
  return new (t || _ImageCropperCustomComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_1__.ChangeDetectorRef));
};
_ImageCropperCustomComponent.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
  type: _ImageCropperCustomComponent,
  selectors: [["app-image-cropper-custom"]],
  viewQuery: function ImageCropperCustomComponent_Query(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵviewQuery"](_c0, 5);
    }
    if (rf & 2) {
      let _t;
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵloadQuery"]()) && (ctx.svgElement = _t.first);
    }
  },
  hostBindings: function ImageCropperCustomComponent_HostBindings(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("pointerup", function ImageCropperCustomComponent_pointerup_HostBindingHandler() {
        return ctx.onPointerEnd();
      })("pointercancel", function ImageCropperCustomComponent_pointercancel_HostBindingHandler() {
        return ctx.onPointerEnd();
      })("pointermove", function ImageCropperCustomComponent_pointermove_HostBindingHandler($event) {
        return ctx.onPointerMove($event);
      })("touchstart", function ImageCropperCustomComponent_touchstart_HostBindingHandler($event) {
        return ctx.onTouchStart($event);
      })("touchmove", function ImageCropperCustomComponent_touchmove_HostBindingHandler($event) {
        return ctx.onTouchMove($event);
      })("touchend", function ImageCropperCustomComponent_touchend_HostBindingHandler() {
        return ctx.onTouchEnd();
      })("mousemove", function ImageCropperCustomComponent_mousemove_HostBindingHandler($event) {
        return ctx.onDocumentMouseMove($event);
      }, false, _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresolveDocument"]);
    }
  },
  inputs: {
    width: "width",
    height: "height",
    imageUrl: "imageUrl",
    initialCoordinates: "initialCoordinates",
    rotation: "rotation"
  },
  outputs: {
    coordinatesChange: "coordinatesChange"
  },
  decls: 11,
  vars: 13,
  consts: [["svgElement", ""], [1, "cropper-container"], [1, "cropper-svg", 3, "touchmove", "touchend", "touchcancel", "mouseleave"], ["preserveAspectRatio", "xMidYMid meet", "style", "image-rendering: optimizeQuality", 4, "ngIf"], ["fill", "rgba(0, 0, 0, 0.5)", 1, "overlay"], [1, "crop-outline", 3, "touchstart"], [4, "ngFor", "ngForOf"], ["class", "magnifier", 4, "ngIf"], ["preserveAspectRatio", "xMidYMid meet", 2, "image-rendering", "optimizeQuality"], ["r", "160", 1, "handle-touch-area", 3, "pointerdown"], ["r", "60", 1, "handle-point"], [1, "midpoint-handle", 3, "pointerdown"], [1, "magnifier"], [1, "magnifier-background"], ["id", "magnifierClip"], ["clip-path", "url(#magnifierClip)"], ["preserveAspectRatio", "xMidYMid meet", 4, "ngIf"], [4, "ngIf"], [1, "magnifier-border"], ["preserveAspectRatio", "xMidYMid meet"], [1, "magnifier-intersection-line"]],
  template: function ImageCropperCustomComponent_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 1);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "svg", 2, 0);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("touchmove", function ImageCropperCustomComponent_Template_svg_touchmove_1_listener($event) {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx.handleTouchMove($event));
      })("touchend", function ImageCropperCustomComponent_Template_svg_touchend_1_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx.stopDragging());
      })("touchcancel", function ImageCropperCustomComponent_Template_svg_touchcancel_1_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx.stopDragging());
      })("mouseleave", function ImageCropperCustomComponent_Template_svg_mouseleave_1_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx.stopDragging());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "g");
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](4, ImageCropperCustomComponent__svg_image_4_Template, 1, 3, "image", 3);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](5, "path", 4);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](6, "g")(7, "polygon", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("touchstart", function ImageCropperCustomComponent_Template_polygon_touchstart_7_listener($event) {
        _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx.onPolygonTouchStart($event));
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](8, ImageCropperCustomComponent__svg_g_8_Template, 3, 10, "g", 6)(9, ImageCropperCustomComponent__svg_g_9_Template, 2, 9, "g", 6);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](10, ImageCropperCustomComponent__svg_g_10_Template, 9, 12, "g", 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵstyleProp"]("width", ctx.width)("height", ctx.height);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("viewBox", ctx.getViewBox());
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("transform", ctx.getRotationTransform());
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.img);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("d", ctx.getOverlayPath());
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("transform", ctx.getCropTransform());
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("points", ctx.getPolygonPoints());
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx.quad == null ? null : ctx.quad.points);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx.midpoints);
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.showMagnifier);
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_2__.NgIf],
  styles: [".cropper-container {\n  position: relative;\n  overflow: visible; /* Changed from hidden to allow handles to extend */\n  background: #fff;\n  display: flex; /* Optional: centers the SVG if parent is larger */\n  justify-content: center;\n  align-items: center;\n}\n\n.cropper-svg {\n  width: 100%;\n  height: 100%;\n  touch-action: none;\n  -webkit-user-select: none;\n  user-select: none;\n  display: block;\n}\n.cropper-svg path {\n  fill: rgba(0, 0, 0, 0);\n  d: path(\"M 0 0 H 3060 V 4080 H 0 Z M 32.6 922.1 L 3374.65 826.047 L 2717.3 3631.2 L -49878 3533.3 Z\");\n}\n\n.handle-point {\n  fill: white;\n  stroke: #29BF9C;\n  stroke-width: 15;\n  pointer-events: none;\n  transition: transform 0.1s, stroke-width 0.1s;\n}\n.handle-point.touch-active {\n  fill: #29BF9C;\n  transform: scale(1);\n  stroke-width: 15;\n}\n\n.crop-outline {\n  fill: none;\n  stroke: #29BF9C;\n  stroke-width: 15;\n  cursor: move;\n  touch-action: none;\n  pointer-events: all;\n}\n\n.handle-point.active {\n  fill: #29BF9C;\n}\n\n.overlay {\n  pointer-events: none;\n}\n\n.handle-point.touch-active {\n  transform: scale(1);\n  transition: transform 0.1s;\n}\n\n.handle-touch-area {\n  fill: transparent;\n  stroke: none;\n  cursor: pointer;\n  touch-action: none;\n  pointer-events: all;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.handle-point {\n  fill: white;\n  stroke: #29BF9C;\n  stroke-width: 15;\n  pointer-events: none;\n  transition: transform 0.1s ease-out, stroke-width 0.1s ease-out;\n}\n.handle-point.touch-active {\n  transform: scale(1);\n  stroke-width: 15;\n  fill: #29BF9C;\n}\n\n.handle-touch-area:active + .handle-point {\n  transform: scale(1);\n  fill: #29BF9C;\n}\n\n.cropper-svg {\n  width: 100%;\n  height: 100%;\n  touch-action: none;\n  -webkit-user-select: none;\n  user-select: none;\n  -webkit-touch-callout: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.magnifier-crop-outline {\n  fill: none;\n  stroke: #29BF9C;\n  stroke-width: 30;\n  pointer-events: none;\n}\n\n.magnifier-handle-point {\n  fill: white;\n  stroke: #29BF9C;\n  stroke-width: 160;\n  pointer-events: none;\n}\n.magnifier-handle-point.touch-active {\n  fill: #29BF9C;\n}\n\n.magnifier-intersection-line {\n  stroke: #29BF9C;\n  stroke-width: 8;\n  stroke-dasharray: 20, 20;\n  pointer-events: none;\n}\n\n.magnifier-center-point {\n  fill: white;\n  stroke: #29BF9C;\n  stroke-width: 8;\n  pointer-events: none;\n}\n\n.magnifier-background {\n  fill: white;\n  opacity: 0.9;\n}\n\n.magnifier-border {\n  fill: none;\n  stroke: #29BF9C;\n  stroke-width: 15;\n}\n\n.magnifier {\n  pointer-events: none;\n}\n\n.crop-area-container {\n  pointer-events: none;\n}\n\n.handle-container {\n  pointer-events: all;\n}\n\n.handle-point-background {\n  fill: white;\n  stroke: none;\n  pointer-events: none;\n}\n\n.handle-point {\n  fill: white;\n  stroke: #29BF9C;\n  stroke-width: 10;\n  pointer-events: none;\n  transition: transform 0.1s ease-out, stroke-width 0.1s ease-out;\n}\n.handle-point.touch-active {\n  transform: scale(1);\n  stroke-width: 15;\n  fill: #29BF9C;\n}\n\n.handle-touch-area {\n  fill: transparent;\n  stroke: none;\n  cursor: pointer;\n  touch-action: none;\n  pointer-events: all;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.crop-outline {\n  fill: none;\n  stroke: #29BF9C;\n  stroke-width: 15;\n  cursor: move;\n  touch-action: none;\n  pointer-events: all;\n}\n\n.overlay {\n  pointer-events: none;\n  fill: rgba(0, 0, 0, 0.5);\n}\n\n.midpoint-handle {\n  fill: white;\n  stroke: #29BF9C;\n  stroke-width: 15;\n  cursor: move;\n  pointer-events: all;\n  transition: all 0.2s ease;\n}\n.midpoint-handle.touch-active {\n  fill: #29BF9C;\n  stroke: white;\n  transform: scale(1);\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
  encapsulation: 2
});

/***/ }),

/***/ 70462:
/*!*****************************************************!*\
  !*** ./src/app/crop-doc/crop-doc-routing.module.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CropDocPageRoutingModule: () => (/* binding */ CropDocPageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _crop_doc_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./crop-doc.page */ 98528);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _CropDocPageRoutingModule;




const routes = [{
  path: '',
  component: _crop_doc_page__WEBPACK_IMPORTED_MODULE_0__.CropDocPage
}];
class CropDocPageRoutingModule {}
_CropDocPageRoutingModule = CropDocPageRoutingModule;
_CropDocPageRoutingModule.ɵfac = function CropDocPageRoutingModule_Factory(t) {
  return new (t || _CropDocPageRoutingModule)();
};
_CropDocPageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _CropDocPageRoutingModule
});
_CropDocPageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](CropDocPageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 63495:
/*!*********************************************!*\
  !*** ./src/app/crop-doc/crop-doc.module.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CropDocPageModule: () => (/* binding */ CropDocPageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _crop_doc_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./crop-doc-routing.module */ 70462);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _crop_doc_page__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./crop-doc.page */ 98528);
/* harmony import */ var _components_image_cropper_custom_image_cropper_custom_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/image-cropper-custom/image-cropper-custom.component */ 84609);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
var _CropDocPageModule;




 // Import SharedModule



class CropDocPageModule {}
_CropDocPageModule = CropDocPageModule;
_CropDocPageModule.ɵfac = function CropDocPageModule_Factory(t) {
  return new (t || _CropDocPageModule)();
};
_CropDocPageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineNgModule"]({
  type: _CropDocPageModule
});
_CropDocPageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonicModule, _crop_doc_routing_module__WEBPACK_IMPORTED_MODULE_0__.CropDocPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsetNgModuleScope"](CropDocPageModule, {
    declarations: [_crop_doc_page__WEBPACK_IMPORTED_MODULE_2__.CropDocPage, _components_image_cropper_custom_image_cropper_custom_component__WEBPACK_IMPORTED_MODULE_3__.ImageCropperCustomComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonicModule, _crop_doc_routing_module__WEBPACK_IMPORTED_MODULE_0__.CropDocPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule]
  });
})();

/***/ }),

/***/ 98528:
/*!*******************************************!*\
  !*** ./src/app/crop-doc/crop-doc.page.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CropDocPage: () => (/* binding */ CropDocPage)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _services_api_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/api.service */ 3366);
/* harmony import */ var _services_signal_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/signal.service */ 56658);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _services_websocket_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/websocket.service */ 30765);
/* harmony import */ var _services_network_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../services/network.service */ 32404);
/* harmony import */ var _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../custom-icon/custom-icon.component */ 40816);
/* harmony import */ var _check_network_check_network_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../check-network/check-network.component */ 93648);
/* harmony import */ var _custom_alert_custom_alert_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../custom-alert/custom-alert.component */ 32374);
/* harmony import */ var _components_image_cropper_custom_image_cropper_custom_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/image-cropper-custom/image-cropper-custom.component */ 84609);

var _CropDocPage;


 // Import the ApiService












const _c0 = ["cropper"];
const _c1 = ["cropperComponent"];
const _c2 = a0 => ({
  "loading": a0
});
class CropDocPage {
  constructor(route, location, loadingController, alertController, webSocketService, networkService, platform, renderer) {
    this.route = route;
    this.location = location;
    this.loadingController = loadingController;
    this.alertController = alertController;
    this.webSocketService = webSocketService;
    this.networkService = networkService;
    this.platform = platform;
    this.renderer = renderer;
    this.imageUrl = null;
    this.coordinates = [];
    this.uuid = null;
    this.needs_rotation = false;
    this.rotationAngle = 0;
    this.navCtrl = (0,_angular_core__WEBPACK_IMPORTED_MODULE_10__.inject)(_ionic_angular__WEBPACK_IMPORTED_MODULE_11__.NavController);
    this.apiService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_10__.inject)(_services_api_service__WEBPACK_IMPORTED_MODULE_1__.ApiService); // Inject the ApiService
    this.signalService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_10__.inject)(_services_signal_service__WEBPACK_IMPORTED_MODULE_2__.SignalService);
    this.progress = 0;
    this.isLoading = false;
    this.isConnected = true; // Track network status
    this.currentWidth = 0;
    this.currentHeight = 0;
    // this.jobId = this.apiService.generateJobId(); // Generate job ID once
  }
  ngAfterViewInit() {
    // Suppose your incoming image is W=3060 by H=4080:
    this.currentWidth = 3060;
    this.currentHeight = 4080;
    const params = this.location.getState();
    if (!params.imageUrl || !params.coordinates || !params.uuid) {
      this.navCtrl.navigateBack('/scan-bl');
      return;
    }
    this.isValidBlobUrl(params.imageUrl).then(isValid => {
      if (!isValid) {
        this.navCtrl.navigateBack('/scan-bl');
        return;
      }
      this.imageUrl = params === null || params === void 0 ? void 0 : params.imageUrl;
      this.coordinates = params === null || params === void 0 ? void 0 : params.coordinates;
      this.uuid = params === null || params === void 0 ? void 0 : params.uuid;
      this.needs_rotation = params === null || params === void 0 ? void 0 : params.needs_rotation;
      setTimeout(() => {
        this.openCropper(params.imageUrl, params.coordinates);
      }, 500);
      console.log(this.location.getState());
    });
    console.log(this.location.getState());
    if (params !== null && params !== void 0 && params.needs_rotation) {
      this.showRotationAlert();
    }
    // Subscribe to the network status
    this.networkService.getNetworkStatus().subscribe(connected => {
      this.isConnected = connected;
    });
    if (this.platform.is('android')) {
      this.renderer.addClass(document.body, 'android-specific');
    } else {
      this.renderer.addClass(document.body, 'other-platform');
    }
    // // const jobId = this.apiService.generateJobId();  // Get the job ID
    // const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;
    // this.webSocketService.connect(websocketUrl, this.jobId);
    // this.webSocketService.onMessage(this.jobId).subscribe((message) => {
    //   // console.log('Received message:', message);
    //   if (message.progress !== undefined) {
    //     this.progress = message.progress;
    //   }
    //   console.log("progress __ :" , this.progress)
    // });
  }
  showRotationAlert() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this.alertController.create({
        header: 'Attention',
        message: "L'image est peut-être pivotée, veuillez la corriger si nécessaire",
        buttons: ['OK']
      });
      yield alert.present();
    })();
  }
  // A helper function to rotate a single point by 90° clockwise about (0,0)
  rotatePoint90Clockwise(pt, oldWidth) {
    var _pt$x, _pt$y;
    // If pt.x or pt.y can be missing, you can do:
    const x = (_pt$x = pt.x) !== null && _pt$x !== void 0 ? _pt$x : 0; // fallback to 0 if undefined
    const y = (_pt$y = pt.y) !== null && _pt$y !== void 0 ? _pt$y : 0; // fallback to 0 if undefined
    console.log({
      x: y,
      y: oldWidth - x
    });
    // Then apply the rotation math
    return {
      x: y,
      y: oldWidth - x
    };
  }
  // rotateImage(angle: number) {
  //     // Add animation to the button
  //     const button = document.querySelector('.rotate-btn ion-icon');
  //     button?.classList.add('rotating');
  //     // Remove animation class after completion
  //     setTimeout(() => {
  //         button?.classList.remove('rotating');
  //     }, 500);
  //     // Normalize the angle within 0° to 360°
  //     this.rotationAngle = (this.rotationAngle + angle) % 360;
  //     if (this.rotationAngle < 0) {
  //         this.rotationAngle += 360; // Ensure positive angle
  //     }
  //     if (this.coordinates.length !== 4) {
  //         console.error("Invalid number of coordinates for rotation:", this.coordinates);
  //         return;
  //     }
  //     // Validate all points to ensure there are no NaN values
  //     this.coordinates.forEach((point, index) => {
  //         if (isNaN(point.x) || isNaN(point.y)) {
  //             throw new Error(`Point ${index} has invalid coordinates: (${point.x}, ${point.y})`);
  //         }
  //     });
  //     // Rotate the points according to the current angle
  //     switch (this.rotationAngle) {
  //         case 90:
  //             this.coordinates = this.coordinates.map((point) => this.rotatePoint90Clockwise(point, this.currentWidth));
  //             break;
  //         case 180:
  //             this.coordinates = this.coordinates.map((point) => ({
  //                 x: this.currentWidth - point.x,
  //                 y: this.currentHeight - point.y,
  //             }));
  //             break;
  //         case 270:
  //             this.coordinates = this.coordinates.map((point) => this.rotatePoint90Clockwise(point, this.currentWidth));
  //             this.coordinates.reverse(); // Reverse to fix the polygon's point order
  //             break;
  //         default:
  //             break; // No rotation
  //     }
  //       console.log("coordinates:", this.coordinates);
  //     // Update the cropper display
  //     if (this.cropper) {
  //         const cropperElement = this.cropper.nativeElement.querySelector('app-image-cropper-custom');
  //         if (cropperElement) {
  //             cropperElement.style.transform = `rotate(${this.rotationAngle}deg)`;
  //         }
  //     }
  // }
  rotateImage(angle) {
    // Add animation to the button
    const button = document.querySelector('.rotate-btn ion-icon');
    button === null || button === void 0 || button.classList.add('rotating');
    // Remove animation class after completion
    setTimeout(() => {
      button === null || button === void 0 || button.classList.remove('rotating');
    }, 500);
    // Update rotation angle
    this.rotationAngle = (this.rotationAngle + angle) % 360;
    if (this.rotationAngle < 0) {
      this.rotationAngle += 360;
    }
    // Update the cropper component's rotation
    if (this.cropperComponent) {
      this.cropperComponent.rotation = this.rotationAngle;
    }
  }
  /**
  * Rotates a single (x,y) corner by a multiple of 90 degrees clockwise.
  * Also handles bounding box swaps if angle = 90 or 270.
  *
  * @param pt    The corner (x,y).
  * @param angle  The rotation in degrees (only 90, 180, or 270).
  * @returns The rotated corner (x,y).
  */
  rotateCorners(pt, angle) {
    var _pt$x2, _pt$y2;
    const x = (_pt$x2 = pt.x) !== null && _pt$x2 !== void 0 ? _pt$x2 : 0; // fallback if undefined
    const y = (_pt$y2 = pt.y) !== null && _pt$y2 !== void 0 ? _pt$y2 : 0;
    // We'll use local copies so we can change them
    let x2 = x;
    let y2 = y;
    // For convenience in formulas
    const w = this.currentWidth;
    const h = this.currentHeight;
    // Choose a formula based on angle
    switch (angle) {
      case 90:
        //  x' = y
        //  y' = (oldWidth) - x
        x2 = y;
        y2 = w - x;
        // bounding box becomes (h, w) after 90° rotation
        break;
      case 180:
        //  x' = (oldWidth) - x
        //  y' = (oldHeight) - y
        x2 = w - x;
        y2 = h - y;
        // bounding box remains (w,h) for 180° around top-left
        break;
      case 270:
        //  x' = (oldHeight) - y
        //  y' = x
        x2 = h - y;
        y2 = x;
        // bounding box becomes (h, w) after 270° rotation
        break;
      default:
        // If angle is something else, do nothing or handle error
        console.warn(`rotateCorners() received an unexpected angle: ${angle}`);
        break;
    }
    return {
      x: x2,
      y: y2
    };
  }
  // openCropper(imageUrl: string = '', coordinates: any[] = []) {
  //   const cropper = document.querySelector('image-cropper') as any;
  //   coordinates = this.convertCoordinates(coordinates);
  //   this.coordinates = coordinates;
  //   if (cropper && this.imageUrl) {
  //     const image = new Image();
  //     image.src = imageUrl;
  //     image.onload = () => {
  //       // Store dimensions for rotation calculations
  //       this.currentWidth = image.width;
  //       this.currentHeight = image.height;
  //       // Update cropper styles based on image dimensions
  //       const cropperElement = cropper.parentElement;
  //       if (cropperElement) {
  //         Object.assign(cropperElement.style, this.getCropperStyles());
  //       }
  //       cropper.img = image;
  //       cropper.inactiveSelections = [];
  //       cropper.quad = { points: coordinates };
  //       cropper.rotation = this.rotationAngle;
  //     };
  //   }
  // }
  openCropper(imageUrl = '', coordinates = []) {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!imageUrl || !coordinates.length) return;
      console.log('Opening cropper with:', {
        imageUrl,
        coordinates
      });
      // Convert coordinates if needed
      const convertedCoordinates = _this2.convertCoordinates(coordinates);
      console.log('Converted coordinates:', convertedCoordinates);
      // Wait for component to be available
      yield new Promise(resolve => setTimeout(resolve, 100));
      if (_this2.cropperComponent) {
        // Set image first
        _this2.cropperComponent.imageUrl = imageUrl;
        // Wait for image to load
        yield new Promise(resolve => setTimeout(resolve, 200));
        // Then set coordinates
        _this2.cropperComponent.initialCoordinates = convertedCoordinates;
        // Update rotation if needed
        _this2.cropperComponent.rotation = _this2.rotationAngle;
      } else {
        console.error('Cropper component not found');
      }
    })();
  }
  // async getUpdatedCoordinates() {
  //   this.jobId = this.apiService.generateJobId(); // Generate job ID
  //   const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;
  //   console.log('WebSocket URL crop-doc:', websocketUrl);
  //   this.webSocketService.connect(websocketUrl, this.jobId);
  //   this.webSocketService.onMessage(this.jobId).subscribe((message) => {
  //     if (message.progress !== undefined) {
  //       this.progress = message.progress;
  //       console.log("progress __ :", this.progress);
  //     }
  //   });
  //   // const loading = await this.presentLoading(); // Show loading spinner
  //   this.isLoading = true;
  //   console.log('Request:', this.coordinates);
  //   const cropper = document.querySelector('image-cropper') as any;
  //   if (cropper) {
  //     const updatedQuad = await cropper.getQuad();
  //     const coordinates : Coordinates[] = updatedQuad.points;
  //     const that = this;
  //     // Create the request object
  //     const model_name =  (localStorage.getItem('selectedSupplier') && localStorage.getItem('selectedSupplier') != 'undefined' && localStorage.getItem('selectedSupplier') != 'AUTRE') ? localStorage.getItem('selectedSupplier') : 'GLOBAL';
  //     const request = {
  //       coordinates,
  //       random_id: this.uuid,
  //       model_name: model_name?.toString(),
  //       rotation:this.rotationAngle
  //     };
  //     console.log('Request:', request);
  //     // Call the processImageSupp API
  //     this.apiService.processImageSupp(request, this.jobId).subscribe(
  //       (response) => {
  //         // loading.dismiss();
  //         this.isLoading = false;
  //         console.log('API response:', response);
  //         // Navigate to the process-doc page with the response data
  //         this.signalService.setData(response);
  //         this.navCtrl.navigateForward('/process-doc', { state: response });
  //         // Disconnect WebSocket after completion
  //         this.webSocketService.close(this.jobId!);
  //       },
  //       (error) => {
  //         // loading.dismiss();
  //         this.isLoading = false;
  //         const errorMessage = `
  //         <h3>Erreur lors le traitement du document</h3>
  //         <ul>
  //           <li>Verfier les contours du document</li>
  //           <li>Verifier la luminosité de l'image</li>
  //           <li>Verifier la qualité de l'image</li>
  //           <li>Supprimer les objets inutiles dans l'image </li>
  //         </ul>
  //         `;
  //         this.apiService.showErrorAlert( errorMessage);
  //         console.error('API error 22:', error.error.message);
  //         console.error(errorMessage);
  //         // Disconnect WebSocket after completion
  //         this.webSocketService.close(this.jobId!);
  //       }
  //     );
  //   } else {
  //     console.log('Cropper not found');
  //   }
  //   this.webSocketService.onMessage(this.jobId).subscribe((message) => {
  //     if (message.progress !== undefined) {
  //       this.progress = message.progress;
  //     }
  //   });
  // }
  getUpdatedCoordinates() {
    var _this3 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_this3.cropperComponent) {
        console.error('Cropper component not found');
        return;
      }
      _this3.isLoading = true;
      _this3.jobId = _this3.apiService.generateJobId();
      try {
        const quad = yield _this3.cropperComponent.getQuad();
        const coordinates = quad.points;
        console.log('Original coordinates:', _this3.coordinates);
        console.log('Updated coordinates:', coordinates);
        const model_name = localStorage.getItem('selectedSupplier') || 'GLOBAL';
        const request = {
          coordinates,
          random_id: _this3.uuid,
          model_name: model_name === 'AUTRE' || model_name === 'undefined' ? 'GLOBAL' : model_name,
          rotation: _this3.rotationAngle
        };
        console.log('Sending request:', request);
        // Setup WebSocket
        const websocketUrl = `${src_environments_environment__WEBPACK_IMPORTED_MODULE_3__.environment.webSocketUrl}/${_this3.jobId}`;
        _this3.webSocketService.connect(websocketUrl, _this3.jobId);
        _this3.webSocketService.onMessage(_this3.jobId).subscribe(message => {
          if (message.progress !== undefined) {
            _this3.progress = message.progress;
          }
        });
        // Call API
        _this3.apiService.processImageSupp(request, _this3.jobId).subscribe(response => {
          _this3.isLoading = false;
          _this3.signalService.setData(response);
          _this3.navCtrl.navigateForward('/process-doc', {
            state: response
          });
          _this3.webSocketService.close(_this3.jobId);
        }, error => {
          _this3.isLoading = false;
          const errorMessage = `
          <h3>Erreur lors le traitement du document</h3>
          <ul>
            <li>Verfier les contours du document</li>
            <li>Verifier la luminosité de l'image</li>
            <li>Verifier la qualité de l'image</li>
            <li>Supprimer les objets inutiles dans l'image </li>
          </ul>
        `;
          _this3.apiService.showErrorAlert(errorMessage);
          _this3.webSocketService.close(_this3.jobId);
        });
      } catch (error) {
        _this3.isLoading = false;
        console.error('Error getting coordinates:', error);
      }
    })();
  }
  // convertCoordinates(arr : any[]) {
  //   return arr.map(item => {
  //     return {
  //       x: parseFloat(item[0].toFixed(1)),
  //       y: parseFloat(item[1].toFixed(1))
  //     };
  //   });
  // }
  convertCoordinates(arr) {
    return arr.map(item => {
      // Handle both array format [x, y] and object format {x, y}
      if (Array.isArray(item)) {
        return {
          x: parseFloat(item[0].toFixed(1)),
          y: parseFloat(item[1].toFixed(1))
        };
      } else {
        return {
          x: parseFloat((item.x || 0).toFixed(1)),
          y: parseFloat((item.y || 0).toFixed(1))
        };
      }
    });
  }
  isValidBlobUrl(blobUrl) {
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        const response = yield fetch(blobUrl);
        return response.ok; // Returns true if the response is ok (status is in the range 200-299)
      } catch (error) {
        return false; // Returns false if there is an error (e.g., network issue, invalid URL)
      }
    })();
  }
  presentLoading() {
    var _this4 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const loading = yield _this4.loadingController.create({
        message: 'Chargement...',
        spinner: 'circles'
        // duration: 30000 // Optional: specify a timeout for the loading spinner
      });
      yield loading.present();
      return loading;
    })();
  }
  removeCroppedImage() {
    var _this5 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      console.log('Remove cropped image');
      const alert = yield _this5.alertController.create({
        header: 'Supprimer le document',
        message: `Êtes-vous sûr de vouloir supprimer Tous les documents ?`,
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            _this5.signalService.removeAllData();
            // redirect to scan-bl
            _this5.navCtrl.navigateRoot('/scan-bl');
          }
        }]
      });
      yield alert.present();
    })();
  }
  reTakePhoto() {
    var _this6 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this6.alertController.create({
        header: 'Supprimer le document',
        message: `Êtes-vous sûr de vouloir prends une nouvelle photo ?`,
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Oui',
          cssClass: 'custom-alert-button',
          handler: () => {
            // this.signalService.removeLastIndex();
            // redirect to scan-bl
            _this6.navCtrl.navigateRoot('/scan-bl');
          }
        }]
      });
      yield alert.present();
    })();
  }
  clearCroppedImage() {
    console.log('Clear cropped image');
    this.signalService.removeAllData();
    this.navCtrl.navigateRoot('/scan-bl');
  }
  getCropperStyles() {
    if (this.imageUrl) {
      const img = new Image();
      img.src = this.imageUrl;
      const viewportHeight = window.innerHeight;
      const availableHeight = viewportHeight - 180; // Subtract header and footer height
      return {
        // width: this.calculateCropperWidth(img.width, img.height),
        width: '100%',
        height: `${availableHeight}px`,
        maxHeight: '85vh',
        margin: 'auto',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      };
    }
    return {
      width: '90%',
      height: '100%'
    };
  }
  calculateCropperWidth(imgWidth, imgHeight) {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const aspectRatio = imgWidth / imgHeight;
    const isVertical = imgHeight > imgWidth;
    if (isVertical) {
      // Increase these values for larger vertical images
      const maxWidth = viewportWidth * 0.98; // Changed from 0.85 to 0.98 (98% of viewport width)
      const calculatedHeight = maxWidth / aspectRatio;
      if (calculatedHeight > viewportHeight * 0.90) {
        // Changed from 0.7 to 0.90 (90% of viewport height)
        const maxHeight = viewportHeight * 0.90; // Changed from 0.7 to 0.90
        return `${maxHeight * aspectRatio}px`;
      }
      return `${maxWidth}px`;
    } else {
      // Increase this value for larger horizontal images
      const maxWidth = viewportWidth * 0.98; // Changed from 0.9 to 0.98 (98% of viewport width)
      return `${maxWidth}px`;
    }
  }
  onResize() {
    this.openCropper(this.imageUrl, this.coordinates);
  }
}
_CropDocPage = CropDocPage;
_CropDocPage.ɵfac = function CropDocPage_Factory(t) {
  return new (t || _CropDocPage)(_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_12__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_13__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_14__.LoadingController), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_14__.AlertController), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_services_websocket_service__WEBPACK_IMPORTED_MODULE_4__.WebSocketService), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_services_network_service__WEBPACK_IMPORTED_MODULE_5__.NetworkService), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_11__.Platform), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_10__.Renderer2));
};
_CropDocPage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdefineComponent"]({
  type: _CropDocPage,
  selectors: [["app-crop-doc"]],
  viewQuery: function CropDocPage_Query(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵviewQuery"](_c0, 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵviewQuery"](_c1, 5);
    }
    if (rf & 2) {
      let _t;
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵloadQuery"]()) && (ctx.cropper = _t.first);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵloadQuery"]()) && (ctx.cropperComponent = _t.first);
    }
  },
  hostBindings: function CropDocPage_HostBindings(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("resize", function CropDocPage_resize_HostBindingHandler() {
        return ctx.onResize();
      }, false, _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresolveWindow"]);
    }
  },
  decls: 29,
  vars: 19,
  consts: [["cropper", ""], ["cropperComponent", ""], [3, "ngClass"], ["slot", "start"], [3, "click"], ["name", "chevron-back-outline"], [1, "crop-doc-content", 3, "ngClass"], ["id", "cropper", 2, "position", "absolute", "top", "0", "left", "0", "right", "0", "bottom", "0"], [1, "rotate-button-container"], [1, "rotate-btn", 3, "click"], ["name", "refresh-outline"], [3, "imageUrl", "initialCoordinates", "rotation", "ngStyle"], [1, "alert-progress", 3, "ngClass"], [3, "progress"], ["size", "small", 1, "menu-button", "active", 3, "click"], ["name", "delete"], [1, "menu-button-middle", 3, "click"], ["name", "extract"], ["name", "re-scan-2"]],
  template: function CropDocPage_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵgetCurrentView"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](0, "ion-header", 2)(1, "ion-toolbar")(2, "ion-title");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](3, "Scanner votre BL");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](4, "ion-buttons", 3)(5, "ion-button", 4);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function CropDocPage_Template_ion_button_click_5_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx.clearCroppedImage());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](6, "ion-icon", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](7, "ion-content", 6);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](8, "app-check-network");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](9, "div", 7, 0)(11, "div", 8)(12, "button", 9);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function CropDocPage_Template_button_click_12_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx.rotateImage(90));
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](13, "ion-icon", 10);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](14, "app-image-cropper-custom", 11, 1);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](16, "div", 12);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](17, "app-custom-alert", 13);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](18, "ion-footer", 2)(19, "ion-toolbar")(20, "ion-buttons")(21, "ion-button", 14);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function CropDocPage_Template_ion_button_click_21_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx.removeCroppedImage());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](22, "app-custom-icon", 15);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](23, "ion-button", 16);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function CropDocPage_Template_ion_button_click_23_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx.getUpdatedCoordinates());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](24, "app-custom-icon", 17);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](25, "span");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](26, "VALIDER");
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](27, "ion-button", 14);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function CropDocPage_Template_ion_button_click_27_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx.reTakePhoto());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](28, "app-custom-icon", 18);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵpureFunction1"](11, _c2, ctx.isLoading));
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](7);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵpureFunction1"](13, _c2, ctx.isLoading));
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](5);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵclassProp"]("needs-rotation", ctx.needs_rotation);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("imageUrl", ctx.imageUrl)("initialCoordinates", ctx.coordinates)("rotation", ctx.rotationAngle)("ngStyle", ctx.getCropperStyles());
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵpureFunction1"](15, _c2, ctx.isLoading));
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("progress", ctx.progress);
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵpureFunction1"](17, _c2, ctx.isLoading));
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_13__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_13__.NgStyle, _ionic_angular__WEBPACK_IMPORTED_MODULE_14__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_14__.IonButtons, _ionic_angular__WEBPACK_IMPORTED_MODULE_14__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_14__.IonFooter, _ionic_angular__WEBPACK_IMPORTED_MODULE_14__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_14__.IonIcon, _ionic_angular__WEBPACK_IMPORTED_MODULE_14__.IonTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_14__.IonToolbar, _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_6__.CustomIconComponent, _check_network_check_network_component__WEBPACK_IMPORTED_MODULE_7__.CheckNetworkComponent, _custom_alert_custom_alert_component__WEBPACK_IMPORTED_MODULE_8__.CustomAlertComponent, _components_image_cropper_custom_image_cropper_custom_component__WEBPACK_IMPORTED_MODULE_9__.ImageCropperCustomComponent],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\n[_nghost-%COMP%] {\n  height: 100dvh;\n}\n\nion-content[_ngcontent-%COMP%]::part(scroll) {\n  overflow-y: hidden !important;\n  --overflow: hidden !important;\n}\n\nion-content[_ngcontent-%COMP%] {\n  --offset-top: 0px !important;\n}\n\nion-header[_ngcontent-%COMP%] {\n  height: 70px;\n  --border: 0;\n  display: flex;\n  align-items: center;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  height: 100%;\n  --border: 0;\n  --border-width: 0;\n  display: flex;\n  justify-content: center;\n  align-items: flex-end;\n  flex-direction: row;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%] {\n  font-size: 26px;\n  font-weight: 700;\n  color: #2f4fcd;\n  text-align: left;\n  width: 100%;\n  padding-left: 2rem;\n  margin-left: 1rem;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  color: #101010;\n  padding-right: 1rem;\n}\n\n.crop-doc-wrapper[_ngcontent-%COMP%] {\n  background: url(\"/assets/bg-scan-bl.png\") no-repeat center center fixed;\n  background-size: cover;\n  height: 80%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  margin-top: 2rem;\n}\n\n#cropper[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  padding: 20px;\n  overflow: hidden;\n}\n#cropper[_ngcontent-%COMP%]   image-cropper[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  max-width: 100%;\n  max-height: calc(100vh - 140px);\n}\n#cropper[_ngcontent-%COMP%]   image-cropper[_ngcontent-%COMP%]     canvas {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n}\n\n  image-cropper {\n  max-height: 80vh !important;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n@media (max-width: 768px) {\n    image-cropper {\n    max-height: 70vh !important;\n  }\n}\n@media (max-width: 480px) {\n    image-cropper {\n    max-height: 60vh !important;\n  }\n}\n\n  .crop-doc-wrapper .file-import-icon img {\n  width: 150px !important;\n  height: 150px !important;\n}\n\n  .crop-doc-wrapper .arrow-bottom-icon img {\n  width: 100px !important;\n  height: 100px !important;\n}\n\n.crop-doc-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\n  padding: 10px 50px 0 50px;\n}\n\n.crop-doc-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 22px;\n  font-weight: 700;\n}\n\n.crop-doc-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 12px;\n  text-align: justify;\n  padding: 5px 10px;\n}\n\n.crop-doc-content[_ngcontent-%COMP%] {\n  --offset-top: 0px !important;\n  --background: rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n  overflow: hidden;\n  height: 100%;\n}\n\n.empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.document-icon[_ngcontent-%COMP%] {\n  font-size: 100px;\n  color: #c4c4c4;\n}\n\nh2[_ngcontent-%COMP%] {\n  color: #555555;\n  font-size: 18px;\n  margin-top: 20px;\n}\n\np[_ngcontent-%COMP%] {\n  color: #888888;\n  font-size: 14px;\n  margin-top: 10px;\n  margin-bottom: 30px;\n}\n\n.arrow-icon[_ngcontent-%COMP%] {\n  font-size: 30px;\n  color: #3b82f6;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  background-color: #e5e7eb;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%], ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-evenly;\n  align-items: center;\n  flex-direction: row;\n}\n\n  ion-button.menu-button app-custom-icon img {\n  width: 30px !important;\n  height: 30px !important;\n  color: #000;\n}\n\n  .menu-button.active app-custom-icon img {\n  color: #2f4fcd;\n}\n\n  .menu-button-middle {\n  background-color: #2f4fcd;\n  padding: 2px 12px;\n  border-radius: 14px;\n  width: 160px;\n  height: 60px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n  margin-bottom: 15px;\n}\n  .menu-button-middle app-custom-icon img {\n  width: 35px !important;\n  height: 35px !important;\n  color: #fff;\n}\n  .menu-button-middle span {\n  color: #fff;\n  font-weight: 500;\n  font-size: 16px;\n  padding-left: 10px;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #3b82f6;\n}\n\nion-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  position: relative;\n  background-color: #dddbff;\n  height: 110px;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --border-width: 0;\n}\n\nion-fab[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n}\n\n  ion-fab-button.menu-button-middle::part(native) {\n  background: none;\n  border: 0;\n  box-shadow: none;\n  width: 100% !important;\n  color: #fff;\n}\n\n  ion-fab ion-fab-list {\n  display: flex;\n  flex-direction: row !important;\n  justify-content: space-around;\n  align-items: flex-end;\n  width: auto !important;\n  padding: 10px 20px;\n  margin-bottom: 100px;\n  height: 100vh;\n  transition: all 0.3s ease;\n}\n  ion-fab ion-fab-list.fab-active {\n  visibility: visible;\n  opacity: 1;\n  pointer-events: all;\n}\n  ion-fab ion-fab-list.fab-hidden {\n  visibility: hidden;\n  opacity: 0;\n  pointer-events: none;\n}\n\n.bg-hide[_ngcontent-%COMP%] {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.initial-bg[_ngcontent-%COMP%] {\n  --background: none;\n}\n\n.content-fab-buttom[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n}\n\n  .content-fab-buttom app-custom-icon img {\n  width: 50px !important;\n  height: 50px !important;\n}\n\n  .content-fab-buttom ion-label {\n  color: #2f4fcd;\n  font-size: 20px;\n}\n\n  ion-fab ion-fab-list ion-fab-button {\n  padding: 10px;\n  width: 200px;\n  height: 135px;\n  transform: translateY(40px);\n  transition: all 0.6s ease-in-out;\n}\n  ion-fab ion-fab-list ion-fab-button.fab-active {\n  transform: translateY(0);\n}\n\n  ion-fab ion-fab-list ion-fab-button::part(native) {\n  border-radius: 16px !important;\n}\n\n  .custom-alert-button, custom-alert-button-rename-doc[_ngcontent-%COMP%] {\n  display: inline-block;\n  text-align: center;\n  font-size: 14px !important;\n  font-weight: bold;\n}\n\n  .custom-alert-button.cancel,   .custom-alert-button-rename-doc.cancel {\n  color: #2563eb;\n}\n\n  .android-specific .custom-alert-button.cancel,   .custom-alert-button-rename-doc.cancel {\n  width: 35%;\n}\n\n  .android-specific .custom-alert-button.cancel,   .custom-alert-button-rename-doc.cancel {\n  width: 35%;\n}\n\n  .custom-alert-button-rename-doc.cancel {\n  font-size: 16px;\n}\n\n  .custom-alert-button.danger {\n  color: red;\n}\n\n  .ios-specific .custom-alert-button.danger {\n  width: 50%;\n}\n\n  .android-specific .custom-alert-button.danger {\n  width: 55%;\n}\n\n  .custom-alert-button-rename-doc.rename {\n  font-size: 16px;\n  color: #535353;\n}\n\n\n\n\n\n  .loading:not(.alert-progress) {\n  opacity: 0.5;\n  pointer-events: none; \n\n  --background: rgba(0, 0, 0, 0.1);\n}\n\n  ion-modal {\n  height: 23%;\n  width: 90%;\n  position: absolute;\n  top: 35%;\n  left: 5%;\n  --background:none;\n  --backdrop-opacity: var(--ion-backdrop-opacity, 0);\n}\n\n  .alert-progress {\n  position: absolute;\n  width: 100%;\n  top: 40%;\n  justify-content: center;\n  align-items: center;\n}\n\n  .alert-progress app-custom-alert {\n  width: 90%;\n}\n\n  .alert-progress {\n  display: none;\n}\n\n  .alert-progress.loading {\n  display: flex;\n}\n\n\n\n\n\n.rotate-button[_ngcontent-%COMP%] {\n  --padding-start: 8px;\n  --padding-end: 8px;\n  margin-right: 12px;\n}\n.rotate-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n  color: #2f4fcd;\n  transition: transform 0.3s ease;\n}\n.rotate-button[_ngcontent-%COMP%]:active   ion-icon[_ngcontent-%COMP%] {\n  transform: rotate(90deg);\n}\n\n@keyframes _ngcontent-%COMP%_rotate-animation {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n.rotating[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_rotate-animation 0.5s ease;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --background: #ffffff;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n}\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  --background-hover: rgba(47, 79, 205, 0.1);\n  --background-activated: rgba(47, 79, 205, 0.2);\n  --border-radius: 50%;\n}\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\n  color: #2f4fcd;\n}\n\n.rotate-button-container[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  z-index: 1000;\n  width: 50%;\n}\n\n.rotate-btn[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.95);\n  border: none;\n  border-radius: 50%;\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  transition: all 0.3s ease;\n}\n.rotate-btn[_ngcontent-%COMP%]:hover {\n  background: #ffffff;\n  box-shadow: 0 4px 12px rgba(47, 79, 205, 0.2);\n  transform: translateY(-2px);\n}\n.rotate-btn[_ngcontent-%COMP%]:active {\n  transform: translateY(0) scale(0.95);\n}\n.rotate-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n  color: #2f4fcd;\n  transition: transform 0.3s ease;\n}\n.rotate-btn[_ngcontent-%COMP%]:active   ion-icon[_ngcontent-%COMP%] {\n  transform: rotate(90deg);\n}\n\n.rotate-btn[_ngcontent-%COMP%] {\n  position: relative;\n}\n.rotate-btn.needs-rotation[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: rgba(47, 79, 205, 0.3);\n  animation: _ngcontent-%COMP%_ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n}\n\n  image-cropper .cropper-controls-visual.dashed {\n  pointer-events: all; \n\n}\n\n  image-cropper .cropper-controls-hit {\n  pointer-events: all; \n\n  fill: transparent; \n\n}\n\n  image-cropper image {\n  pointer-events: none; \n\n}\n\n  image-cropper svg.cropper-svg {\n  pointer-events: bounding-box; \n\n}\n\n@keyframes _ngcontent-%COMP%_rotate-animation {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes _ngcontent-%COMP%_ping {\n  0% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  75%, 100% {\n    transform: scale(2);\n    opacity: 0;\n  }\n}\n.rotating[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_rotate-animation 0.5s ease;\n}\n\n#cropper[_ngcontent-%COMP%]   image-cropper[_ngcontent-%COMP%] {\n  transition: transform 0.3s ease;\n}\n\n@media (max-width: 768px) {\n  .rotate-btn[_ngcontent-%COMP%] {\n    width: 42px;\n    height: 42px;\n  }\n  .rotate-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n    font-size: 20px;\n  }\n  .rotate-button-container[_ngcontent-%COMP%] {\n    top: 15px;\n    right: 15px;\n  }\n}\n@media (prefers-color-scheme: dark) {\n  .crop-doc-content[_ngcontent-%COMP%] {\n    --background: rgba(255, 255, 255, 0.95);\n  }\n  ion-content[_ngcontent-%COMP%] {\n    --background: #fff ;\n  }\n  ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n    --background: #fff !important;\n  }\n}\n@media (prefers-color-scheme: light) {\n  .crop-doc-content[_ngcontent-%COMP%] {\n    --background: rgba(0, 0, 0, 0.05);\n    --offset-top: 0px !important;\n  }\n  ion-content[_ngcontent-%COMP%] {\n    --background: #fff ;\n  }\n  ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n    --background: #fff !important;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ }),

/***/ 30765:
/*!***********************************************!*\
  !*** ./src/app/services/websocket.service.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WebSocketService: () => (/* binding */ WebSocketService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 10819);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _WebSocketService;


class WebSocketService {
  constructor() {
    this.subjects = {};
  }
  connect(url, jobId) {
    this.socket = new WebSocket(url);
    this.socket.onopen = event => {
      console.log('WebSocket connection established:', event);
    };
    this.socket.onmessage = event => {
      const data = JSON.parse(event.data);
      if (data.job_id && this.subjects[data.job_id]) {
        this.subjects[data.job_id].next(data);
      } else if (this.subjects[jobId]) {
        this.subjects[jobId].next(data);
      }
    };
    this.socket.onerror = event => {
      console.error('WebSocket error observed:', event);
      this.reconnect(url, jobId);
    };
    this.socket.onclose = event => {
      console.log('WebSocket connection closed:', event);
      this.reconnect(url, jobId);
    };
  }
  reconnect(url, jobId) {
    setTimeout(() => {
      this.connect(url, jobId);
    }, 1000); // Retry connection after 1 second
  }
  send(data) {
    var _this$socket;
    if (((_this$socket = this.socket) === null || _this$socket === void 0 ? void 0 : _this$socket.readyState) === WebSocket.OPEN) {
      var _this$socket2;
      (_this$socket2 = this.socket) === null || _this$socket2 === void 0 || _this$socket2.send(JSON.stringify(data));
    } else {
      console.error('WebSocket connection is not open.');
    }
  }
  onMessage(jobId) {
    if (!this.subjects[jobId]) {
      this.subjects[jobId] = new rxjs__WEBPACK_IMPORTED_MODULE_0__.Subject();
    }
    return this.subjects[jobId].asObservable();
  }
  close(jobId) {
    if (this.socket) {
      this.socket.close();
      delete this.subjects[jobId];
    }
  }
}
_WebSocketService = WebSocketService;
_WebSocketService.ɵfac = function WebSocketService_Factory(t) {
  return new (t || _WebSocketService)();
};
_WebSocketService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
  token: _WebSocketService,
  factory: _WebSocketService.ɵfac,
  providedIn: 'root'
});

/***/ })

}]);
//# sourceMappingURL=src_app_crop-doc_crop-doc_module_ts.js.map