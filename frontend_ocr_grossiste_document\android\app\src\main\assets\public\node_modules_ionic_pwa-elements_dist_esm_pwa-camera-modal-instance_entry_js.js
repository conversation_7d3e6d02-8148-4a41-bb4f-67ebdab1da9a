"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["node_modules_ionic_pwa-elements_dist_esm_pwa-camera-modal-instance_entry_js"],{

/***/ 89253:
/*!**************************************************************************************!*\
  !*** ./node_modules/@ionic/pwa-elements/dist/esm/pwa-camera-modal-instance.entry.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   pwa_camera_modal_instance: () => (/* binding */ PWACameraModal)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index-1c5c47b4.js */ 54787);


const cameraModalInstanceCss = ":host{z-index:1000;position:fixed;top:0;left:0;width:100%;height:100%;display:-ms-flexbox;display:flex;contain:strict;--inset-width:600px;--inset-height:600px}.wrapper{-ms-flex:1;flex:1;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;background-color:rgba(0, 0, 0, 0.15)}.content{-webkit-box-shadow:0px 0px 5px rgba(0, 0, 0, 0.2);box-shadow:0px 0px 5px rgba(0, 0, 0, 0.2);width:var(--inset-width);height:var(--inset-height);max-height:100%}@media only screen and (max-width: 600px){.content{width:100%;height:100%}}";
const PWACameraModal = class {
  constructor(hostRef) {
    var _this = this;
    (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.r)(this, hostRef);
    this.onPhoto = (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.c)(this, "onPhoto", 7);
    this.noDeviceError = (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.c)(this, "noDeviceError", 7);
    this.handlePhoto = /*#__PURE__*/function () {
      var _ref = (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* (photo) {
        _this.onPhoto.emit(photo);
      });
      return function (_x) {
        return _ref.apply(this, arguments);
      };
    }();
    this.handleNoDeviceError = /*#__PURE__*/function () {
      var _ref2 = (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* (photo) {
        _this.noDeviceError.emit(photo);
      });
      return function (_x2) {
        return _ref2.apply(this, arguments);
      };
    }();
    this.facingMode = 'user';
    this.hidePicker = false;
    this.noDevicesText = 'No camera found';
    this.noDevicesButtonText = 'Choose image';
  }
  handleBackdropClick(e) {
    if (e.target !== this.el) {
      this.onPhoto.emit(null);
    }
  }
  handleComponentClick(e) {
    e.stopPropagation();
  }
  handleBackdropKeyUp(e) {
    if (e.key === "Escape") {
      this.onPhoto.emit(null);
    }
  }
  render() {
    return (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.h)("div", {
      class: "wrapper",
      onClick: e => this.handleBackdropClick(e)
    }, (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.h)("div", {
      class: "content"
    }, (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.h)("pwa-camera", {
      onClick: e => this.handleComponentClick(e),
      facingMode: this.facingMode,
      hidePicker: this.hidePicker,
      handlePhoto: this.handlePhoto,
      handleNoDeviceError: this.handleNoDeviceError,
      noDevicesButtonText: this.noDevicesButtonText,
      noDevicesText: this.noDevicesText
    })));
  }
  get el() {
    return (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_1__.g)(this);
  }
};
PWACameraModal.style = cameraModalInstanceCss;


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_pwa-elements_dist_esm_pwa-camera-modal-instance_entry_js.js.map