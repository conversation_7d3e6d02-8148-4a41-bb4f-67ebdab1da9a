{"version": 3, "file": "node_modules_ionic_pwa-elements_dist_esm_pwa-toast_entry_js.js", "mappings": ";;;;;;;;;;;;;;AAA2F;AAE3F,MAAMO,QAAQ,GAAG,8pBAA8pB;AAE/qB,MAAMC,QAAQ,GAAG,MAAM;EACrBC,WAAWA,CAACC,OAAO,EAAE;IACnBT,qDAAgB,CAAC,IAAI,EAAES,OAAO,CAAC;IAC/B,IAAI,CAACC,OAAO,GAAGC,SAAS;IACxB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,OAAO,GAAG,IAAI;EACrB;EACAC,QAAQA,CAAA,EAAG;IACT,MAAMC,OAAO,GAAG;MACdC,GAAG,EAAE,CAAC,CAAC,IAAI,CAACH;IACd,CAAC;IACD,IAAI,IAAI,CAACA,OAAO,KAAK,IAAI,EAAE;MACzBE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAACF,OAAO;IAC/B;IACA,OAAO;MACLI,KAAK,EAAEF;IACT,CAAC;EACH;EACAG,gBAAgBA,CAAA,EAAG;IACjBC,UAAU,CAAC,MAAM;MACf,IAAI,CAACN,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;IACFM,UAAU,CAAC,MAAM;MACf,IAAI,CAACC,KAAK,CAAC,CAAC;IACd,CAAC,EAAE,IAAI,CAACR,QAAQ,CAAC;EACnB;EACAQ,KAAKA,CAAA,EAAG;IACN,IAAI,CAACP,OAAO,GAAG,IAAI;IACnBM,UAAU,CAAC,MAAM;MACf,IAAI,CAACE,EAAE,CAACC,UAAU,CAACC,WAAW,CAAC,IAAI,CAACF,EAAE,CAAC;IACzC,CAAC,EAAE,IAAI,CAAC;EACV;EACAG,gBAAgBA,CAAA,EAAG;IACjB,OAAQvB,qDAAC,CAAC,KAAK,EAAE;MAAEgB,KAAK,EAAE;IAAU,CAAC,EAAEhB,qDAAC,CAAC,KAAK,EAAE;MAAEgB,KAAK,EAAE;IAAQ,CAAC,EAAE,IAAI,CAACP,OAAO,CAAC,CAAC;EACpF;EACA,IAAIW,EAAEA,CAAA,EAAG;IAAE,OAAOlB,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpCsB,MAAMA,CAAA,EAAG;IAAE,OAAOxB,qDAAC,CAACI,iDAAI,EAAE,IAAI,CAACS,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACU,gBAAgB,CAAC,CAAC,CAAC;EAAE;AACvE,CAAC;AACDjB,QAAQ,CAACmB,KAAK,GAAGpB,QAAQ", "sources": ["./node_modules/@ionic/pwa-elements/dist/esm/pwa-toast.entry.js"], "sourcesContent": ["import { r as registerInstance, h, g as getElement, H as Host } from './index-1c5c47b4.js';\n\nconst toastCss = \":host{position:fixed;bottom:20px;left:0;right:0;display:-ms-flexbox;display:flex;opacity:0}:host(.in){-webkit-transition:opacity 300ms;transition:opacity 300ms;opacity:1}:host(.out){-webkit-transition:opacity 1s;transition:opacity 1s;opacity:0}.wrapper{-ms-flex:1;flex:1;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.toast{font-family:-apple-system, system-ui, \\\"Helvetica Neue\\\", Roboto, sans-serif;background-color:#eee;color:black;border-radius:5px;padding:10px 15px;font-size:14px;font-weight:500;-webkit-box-shadow:0px 1px 2px rgba(0, 0, 0, 0.20);box-shadow:0px 1px 2px rgba(0, 0, 0, 0.20)}\";\n\nconst PWAToast = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.message = undefined;\n    this.duration = 2000;\n    this.closing = null;\n  }\n  hostData() {\n    const classes = {\n      out: !!this.closing\n    };\n    if (this.closing !== null) {\n      classes['in'] = !this.closing;\n    }\n    return {\n      class: classes\n    };\n  }\n  componentDidLoad() {\n    setTimeout(() => {\n      this.closing = false;\n    });\n    setTimeout(() => {\n      this.close();\n    }, this.duration);\n  }\n  close() {\n    this.closing = true;\n    setTimeout(() => {\n      this.el.parentNode.removeChild(this.el);\n    }, 1000);\n  }\n  __stencil_render() {\n    return (h(\"div\", { class: \"wrapper\" }, h(\"div\", { class: \"toast\" }, this.message)));\n  }\n  get el() { return getElement(this); }\n  render() { return h(Host, this.hostData(), this.__stencil_render()); }\n};\nPWAToast.style = toastCss;\n\nexport { PWAToast as pwa_toast };\n"], "names": ["r", "registerInstance", "h", "g", "getElement", "H", "Host", "toastCss", "PWAToast", "constructor", "hostRef", "message", "undefined", "duration", "closing", "hostData", "classes", "out", "class", "componentDidLoad", "setTimeout", "close", "el", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "__stencil_render", "render", "style", "pwa_toast"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}