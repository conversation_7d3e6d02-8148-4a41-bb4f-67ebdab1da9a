#!/usr/bin/env python3
"""
Test script to verify that empty designation filtering works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.app.utils.document_model import DocumentModel
from src.app.app import filter_empty_designations

def test_empty_designation_filter():
    """Test the filter_empty_designations function"""
    
    # Create test table rows
    test_rows = []
    
    # Valid row 1
    row1 = DocumentModel.Table()
    row1.designation = "BRUFEN 400MG 30CP CO"
    row1.quantity = "1"
    row1.pph = "23.323"
    row1.ppv = "35.3"
    test_rows.append(row1)
    
    # Empty designation row
    row2 = DocumentModel.Table()
    row2.designation = ""
    row2.quantity = "0"
    row2.pph = "23.323"
    row2.ppv = "35.3"
    test_rows.append(row2)
    
    # Whitespace only designation row
    row3 = DocumentModel.Table()
    row3.designation = "   "
    row3.quantity = "2"
    row3.pph = "10.0"
    row3.ppv = "15.0"
    test_rows.append(row3)
    
    # Valid row 2
    row4 = DocumentModel.Table()
    row4.designation = "VOLTARENE 50MG 30CP CO"
    row4.quantity = "1"
    row4.pph = "44.136"
    row4.ppv = "66.8"
    test_rows.append(row4)
    
    # None designation row
    row5 = DocumentModel.Table()
    row5.designation = None
    row5.quantity = "3"
    row5.pph = "5.0"
    row5.ppv = "8.0"
    test_rows.append(row5)
    
    print(f"Original rows count: {len(test_rows)}")
    print("Original rows:")
    for i, row in enumerate(test_rows):
        print(f"  Row {i+1}: designation='{row.designation}', quantity='{row.quantity}'")
    
    # Apply filter
    filtered_rows = filter_empty_designations(test_rows, "Test")
    
    print(f"\nFiltered rows count: {len(filtered_rows)}")
    print("Filtered rows:")
    for i, row in enumerate(filtered_rows):
        print(f"  Row {i+1}: designation='{row.designation}', quantity='{row.quantity}'")
    
    # Verify results
    expected_count = 2  # Only row1 and row4 should remain
    if len(filtered_rows) == expected_count:
        print(f"\n✅ Test PASSED: Expected {expected_count} rows, got {len(filtered_rows)}")
        
        # Check that the right rows remain
        designations = [row.designation for row in filtered_rows]
        if "BRUFEN 400MG 30CP CO" in designations and "VOLTARENE 50MG 30CP CO" in designations:
            print("✅ Correct rows were preserved")
        else:
            print("❌ Wrong rows were preserved")
            print(f"Got designations: {designations}")
    else:
        print(f"❌ Test FAILED: Expected {expected_count} rows, got {len(filtered_rows)}")
    
    return len(filtered_rows) == expected_count

def test_database_filter():
    """Test the database-level filtering"""
    
    # Simulate database data structure
    test_data = [
        {
            'table': [
                {
                    'id': 1,
                    'designation': 'BRUFEN 400MG 30CP CO',
                    'quantity': '1',
                    'pph': '23.323',
                    'ppv': '35.3'
                },
                {
                    'id': 2,
                    'designation': '',  # Empty designation
                    'quantity': '0',
                    'pph': '23.323',
                    'ppv': '35.3'
                },
                {
                    'id': 3,
                    'designation': 'VOLTARENE 50MG 30CP CO',
                    'quantity': '1',
                    'pph': '44.136',
                    'ppv': '66.8'
                },
                {
                    'id': 4,
                    'designation': '   ',  # Whitespace only
                    'quantity': '2',
                    'pph': '10.0',
                    'ppv': '15.0'
                }
            ]
        }
    ]
    
    print(f"\nDatabase test - Original table count: {len(test_data[0]['table'])}")
    
    # Apply database-level filter (simulate the logic from operations.py)
    for data in test_data:
        if 'table' in data:
            original_count = len(data['table'])
            data['table'] = [row for row in data['table'] 
                           if row.get('designation') and str(row.get('designation')).strip()]
            filtered_count = len(data['table'])
            print(f"Database filter: Removed {original_count - filtered_count} rows with empty designations")
    
    print(f"Database test - Filtered table count: {len(test_data[0]['table'])}")
    
    # Verify results
    expected_count = 2  # Only rows with valid designations should remain
    actual_count = len(test_data[0]['table'])
    
    if actual_count == expected_count:
        print(f"✅ Database test PASSED: Expected {expected_count} rows, got {actual_count}")
        return True
    else:
        print(f"❌ Database test FAILED: Expected {expected_count} rows, got {actual_count}")
        return False

if __name__ == "__main__":
    print("Testing empty designation filtering...")
    
    test1_passed = test_empty_designation_filter()
    test2_passed = test_database_filter()
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests PASSED!")
        sys.exit(0)
    else:
        print("\n❌ Some tests FAILED!")
        sys.exit(1)
