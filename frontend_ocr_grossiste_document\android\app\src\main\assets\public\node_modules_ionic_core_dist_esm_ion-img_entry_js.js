"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["node_modules_ionic_core_dist_esm_ion-img_entry_js"],{

/***/ 70654:
/*!************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ion-img.entry.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ion_img: () => (/* binding */ Img)
/* harmony export */ });
/* harmony import */ var _index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-c71c5417.js */ 5634);
/* harmony import */ var _helpers_da915de8_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers-da915de8.js */ 21895);
/* harmony import */ var _ionic_global_b9c0d1da_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ionic-global-b9c0d1da.js */ 36331);
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */



const imgCss = ":host{display:block;-o-object-fit:contain;object-fit:contain}img{display:block;width:100%;height:100%;-o-object-fit:inherit;object-fit:inherit;-o-object-position:inherit;object-position:inherit}";
const IonImgStyle0 = imgCss;
const Img = class {
  constructor(hostRef) {
    (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
    this.ionImgWillLoad = (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.d)(this, "ionImgWillLoad", 7);
    this.ionImgDidLoad = (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.d)(this, "ionImgDidLoad", 7);
    this.ionError = (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.d)(this, "ionError", 7);
    this.inheritedAttributes = {};
    this.onLoad = () => {
      this.ionImgDidLoad.emit();
    };
    this.onError = () => {
      this.ionError.emit();
    };
    this.loadSrc = undefined;
    this.loadError = undefined;
    this.alt = undefined;
    this.src = undefined;
  }
  srcChanged() {
    this.addIO();
  }
  componentWillLoad() {
    this.inheritedAttributes = (0,_helpers_da915de8_js__WEBPACK_IMPORTED_MODULE_1__.h)(this.el, ['draggable']);
  }
  componentDidLoad() {
    this.addIO();
  }
  addIO() {
    if (this.src === undefined) {
      return;
    }
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window && 'IntersectionObserverEntry' in window && 'isIntersecting' in window.IntersectionObserverEntry.prototype) {
      this.removeIO();
      this.io = new IntersectionObserver(data => {
        /**
         * On slower devices, it is possible for an intersection observer entry to contain multiple
         * objects in the array. This happens when quickly scrolling an image into view and then out of
         * view. In this case, the last object represents the current state of the component.
         */
        if (data[data.length - 1].isIntersecting) {
          this.load();
          this.removeIO();
        }
      });
      this.io.observe(this.el);
    } else {
      // fall back to setTimeout for Safari and IE
      setTimeout(() => this.load(), 200);
    }
  }
  load() {
    this.loadError = this.onError;
    this.loadSrc = this.src;
    this.ionImgWillLoad.emit();
  }
  removeIO() {
    if (this.io) {
      this.io.disconnect();
      this.io = undefined;
    }
  }
  render() {
    const {
      loadSrc,
      alt,
      onLoad,
      loadError,
      inheritedAttributes
    } = this;
    const {
      draggable
    } = inheritedAttributes;
    return (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.f, {
      key: '14d24d65ec8e5522192ca58035264971b1ab883b',
      class: (0,_ionic_global_b9c0d1da_js__WEBPACK_IMPORTED_MODULE_2__.b)(this)
    }, (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.h)("img", {
      key: '345ba155a5fdce5e66c397a599b7333d37d9cb1d',
      decoding: "async",
      src: loadSrc,
      alt: alt,
      onLoad: onLoad,
      onError: loadError,
      part: "image",
      draggable: isDraggable(draggable)
    }));
  }
  get el() {
    return (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.i)(this);
  }
  static get watchers() {
    return {
      "src": ["srcChanged"]
    };
  }
};
/**
 * Enumerated strings must be set as booleans
 * as Stencil will not render 'false' in the DOM.
 * The need to explicitly render draggable="true"
 * as only certain elements are draggable by default.
 * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/draggable.
 */
const isDraggable = draggable => {
  switch (draggable) {
    case 'true':
      return true;
    case 'false':
      return false;
    default:
      return undefined;
  }
};
Img.style = IonImgStyle0;


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_core_dist_esm_ion-img_entry_js.js.map