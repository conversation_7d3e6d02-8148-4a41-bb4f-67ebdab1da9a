import os

from PIL import Image
from pytesseract import image_to_string
import re
import json
# from easyocr import Reader
from fuzzywuzzy import process, fuzz
from src.app.utils import models_utils
from src.app.utils import constants
from src.app.utils.root_names_constants import CORRECT_WORDS
import logging
import traceback
from src.app.utils.helpers import *
from src.app.services.ocr_service import OCRService
import random
from pathlib import Path
from src.app.config import SYS_ARGV
from typing import List
from datetime import datetime
from src.app.utils.document_model import DocumentModel

# Initialize OCR service at the top of the file after imports
ocr_service = OCRService()

# Add base_path and temp_path definitions
if SYS_ARGV == 'api':
    base_path = Path(__file__).resolve().parent.parent.parent.parent
else:
    base_path = Path(__file__).resolve().parent.parent.parent.parent

temp_path = base_path / 'temp'

"""-----************ OCR Pytesseract && EasyOCR ************ -----"""

## Tessearct Configuration
# # Function to get text from image using Pytesseract
# def get_pytesseract_text(image, module='default'):
#     """Get text from image using Pytesseract with default configuration."""
#     try:
#         custom_config = r'--oem 3 --psm 6'
#         # earthMomma1, earthMomma2_18k  earthMomma_5k_intr   ninetyseven1   ninetyseven2   ninetysevenlite   earthmomma_lite
#         if module != 'default':
#             result = image_to_string(image, config=custom_config, lang=module)
#         else:
#             result = image_to_string(image, config=custom_config)
#         # Split the result into lines and join them back with a newline character
#         result_with_newlines = '--n'.join(result.splitlines())
#         return result_with_newlines
#     except Exception as e:
#         logging.error(f"Error in get_pytesseract_text: {traceback.format_exc()}")
#         raise e
#
#
# def get_pytesseract_advanced_text(image, module='default'):
#     """Get text from image using Pytessferact with advanced configuration."""
#     try:
#         # Example of using a different PSM and OEM
#         custom_config = r'--oem 1 --psm 3'
#         if module != 'default':
#             result = image_to_string(image, config=custom_config, lang=module)
#         else:
#             result = image_to_string(image, config=custom_config)
#         # Split the result into lines and join them back with a newline character
#         result_with_newlines = '--n'.join(result.splitlines())
#         return result_with_newlines
#
#     except Exception as e:
#         logging.error(f"Error in get_pytesseract_advanced_text: {traceback.format_exc()}")
#         raise e


# In the get_pytesseract_text function, replace with:


## DocTR Configuration
def get_pytesseract_text(image, module='default'):
    try:
        # Save image to temporary file
        temp_image_path = str(temp_path / f"temp_image_{random.randint(1000, 9999)}.jpg")
        image.save(temp_image_path)

        # Use OCR service
        result = ocr_service.extract_text(temp_image_path, use_row_threshold=True)

        # Clean up
        os.remove(temp_image_path)

        # Join lines with --n separator
        return '--n'.join(result.raw_lines)
    except Exception as e:
        logging.error(f"Error in get_pytesseract_text: {traceback.format_exc()}")
        raise e


# In the get_pytesseract_advanced_text function, replace with:
def get_pytesseract_advanced_text(image, module='default'):
    try:
        # Save image to temporary file
        temp_image_path = str(temp_path / f"temp_image_adv_{random.randint(1000, 9999)}.jpg")
        image.save(temp_image_path)

        # Use OCR service
        result = ocr_service.extract_text(temp_image_path, use_row_threshold=True)

        # Clean up
        os.remove(temp_image_path)

        # Join lines with --n separator
        return '--n'.join(result.raw_lines)
    except Exception as e:
        logging.error(f"Error in get_pytesseract_advanced_text: {traceback.format_exc()}")
        raise e

# # Function to get text from image using EasyOCR
# def get_easyocr_text(image):
#     try:
#         reader = Reader(['fr'], gpu=True)  # Assuming no GPU
#         results = reader.readtext(image)
#         result_joined = " ".join([text[1] for text in results])
#         return results, result_joined
#     except Exception as e:
#         logging.error(f"Error in get_easyocr_text: {traceback.format_exc()}")
#         raise e


"""-----************ Correct the words based on known presentation terms ************ -----"""


def correct_presentation_terms(line, start_index, presentation_terms, default_threshold=80):
    try:
        if start_index is None:
            # logging.info("Warning: start_index was None, setting to 0")
            start_index = 0
        words = line.split()
        corrected_line = []

        # Iterate over all words, starting from the specified index
        for i in range(len(words)):
            word = words[i]
            # Reset threshold to default for each word
            threshold = default_threshold
            if not is_exact_match_part_name(word, constants.DOSAGES) and not is_exact_match_part_name(word,
                                                                                                      constants.PRESENTATION):
                if i >= start_index + 1:
                    # Check if the word matches any presentation terms
                    matches = process.extract(word, presentation_terms, scorer=fuzz.ratio, limit=None)

                    # Adjust threshold for short words
                    if len(word) <= 3:
                        threshold = 50

                    if matches and matches[0][1] >= threshold:
                        corrected_word = matches[0][0]
                        corrected_line.append(corrected_word)
                        # logging.info(
                        #     f"Corrected '{word}' to '{corrected_word}' based on presentation terms with score : {matches[0][1]}")
                    else:
                        corrected_line.append(word)
                else:
                    # Append words before the start index unchanged
                    corrected_line.append(word)
            else:
                corrected_line.append(word)

        return ' '.join(corrected_line)

    except Exception as e:
        logging.error(f"Error in correct_presentation_terms: {traceback.format_exc()}")
        raise e


# def correct_bt_bte_formats(line):
#     """ Corrects formats of 'BT/' and 'BTE/' in the given line based on specific rules. """
#     # Remove space after the 'BT/ '
#     line = line.replace('/ ', '/')
#     # Updated regex to capture 1-3 letters followed by '/' regardless of what follows, if it's not purely numeric
#     # pattern = re.compile(r'\b([A-Za-z]{1,3})/(\b[^0-9]*)')
#     pattern = re.compile(r'(\b[A-Za-z]*)(BT[E]?)/')
#
#     def replacement(match):
#         prefix = match.group(1)
#         bt_bte = match.group(2)
#         # Determine whether to replace with 'BT/' or 'BTE/'
#         # if len(prefix) == 1 or len(prefix) == 2:
#         #     return ' BT/' + postfix  # Adding space before 'BT/' and keeping following text
#         # elif len(prefix) == 3:
#         #     return ' BTE/' + postfix  # Adding space before 'BTE/' and keeping following text
#         # return match.group(0)  # Default return the original if no condition is met
#         # Determine whether the prefix is valid, i.e., empty or ends with a non-alphanumeric (like '&')
#         if not prefix.isalpha():  # Check if prefix is not purely alphabetic
#             # If prefix is not alphabetic (e.g., '&'), replace with correct 'BT' or 'BTE'
#             return f' {bt_bte}/'
#         return match.group(0)  # If the prefix is alphabetic, keep as it is (likely no correction needed)
#
#     corrected_line = pattern.sub(replacement, line)
#
#     # # Handle both 'BT / ' and 'BTE / ' replacements in one step
#     # corrected_line = re.sub(r'(BT[E]?)\s*/\s*', r'\1/', corrected_line)
#
#
#     logging.info("corrected_line : __________ ", corrected_line)
#     return corrected_line

def correct_bt_bte_formats_v1(line):

    """Corrects formats of 'BT/' and 'BTE/' in the given line based on specific rules."""
    try:
        # First, we want to replace incorrect '&T/' and similar patterns directly.
        line = re.sub(r'&T/', ' BT/', line)
        line = re.sub(r'&TE/', ' BTE/', line)

        # Then handle cases with spaces and other prefixes
        # Regex to find malformed 'BT/' and 'BTE/' patterns, including checking for extra characters just before
        pattern = re.compile(r'(\&?[A-Za-z]*)?(BT[E]?)/')

        def replacement(match):
            prefix = match.group(1) if match.group(1) else ''
            bt_bte = match.group(2)
            # If the prefix is non-alphabetic or missing, replace correctly
            if not prefix.isalpha() and prefix != '&':
                return f'{bt_bte}/'
            # If it's '&', replace directly with space prefixed
            if prefix == '&':
                return f' {bt_bte}/'
            return match.group(0)  # No change for alphabetic prefixes

        corrected_line = pattern.sub(replacement, line)

        return corrected_line

    except Exception as e:
        logging.error(f"Error in correct_bt_bte_formats_v1: {traceback.format_exc()}")
        raise e


"""-----************ Correct the root name ************ -----"""


def combine_ocr_outputs(model, text1, correct_words, threshold=90):
    """Combines OCR outputs and corrects them based on a list of known words."""
    try:
        lines = text1.split('--n')
        combined_text = []
        word_indices = []  # List to hold the word indices

        # Ensure all correct words are strings
        correct_words = [str(word) for word in CORRECT_WORDS if isinstance(word, (str, int))]

        # if model != constants.SOPHACA:
        #     # # Step 5: Apply corrections for 'BT/' and 'BTE/' formats
        #     lines = [correct_bt_bte_formats_v2(line) for line in lines]
        lines = [correct_bt_bte_formats_v2(line) for line in lines]

        for line in lines:
            root_name_next_word = ''
            line_cleaned = remove_selected_special_characters(line)
            selected_clean_word, word_index = select_name_root_from_line(line_cleaned)
            words = line_cleaned.split()

            if selected_clean_word and not is_numeric(selected_clean_word):
                matches = process.extract(selected_clean_word, correct_words, scorer=fuzz.ratio, limit=None)
                matched_words = [(match, score) for match, score in matches if score >= threshold]

                chosen_word = selected_clean_word
                found_combined_words = False
                if matched_words:

                    best_match = max(matched_words, key=lambda x: x[1])
                    chosen_word = best_match[0]
                    # logging.info(f"Cleaned: '{selected_clean_word}', Corrected: '{chosen_word}', Score: {best_match[1]}', line: {line}")

                    # Check for combination of words if conditions met (next word after selected_word , )
                    # Check if selected_word + " " + next_word is in the array
                    if (chosen_word in constants.first_root_names or len(chosen_word) < 4) and (
                            word_index + 1 < len(words)):

                        word_index += 1
                        root_name_next_word = words[word_index]
                        next_word = words[word_index]
                        combined_word = chosen_word + " " + next_word
                        combined_matches = process.extract(combined_word, correct_words, scorer=fuzz.ratio, limit=None)
                        combined_best_match = max(combined_matches, key=lambda x: x[1])
                        if combined_best_match[1] >= threshold:
                            chosen_word = combined_best_match[0]  # Use the combined word
                            found_combined_words = True

                    # Check for combination of words if conditions met (next word after selected_word , )
                    # Check if next_word is in the array (another word in array)
                    if word_index + 1 < len(words):

                        next_word = words[word_index + 1]
                        if not re.search(r'\d', next_word) and len(next_word) > 4:
                            # Check if the next word does not match any unwanted patterns
                            if not is_exact_match_hold_name(next_word, constants.DOSAGES) and not is_exact_match_hold_name(
                                    next_word,
                                    constants.PRESENTATION):

                                combined_matches = process.extract(next_word, correct_words, scorer=fuzz.ratio,
                                                                   limit=None)
                                combined_best_match = max(combined_matches, key=lambda x: x[1])
                                # logging.info(f"Cleaned: '{next_word}', Corrected: '{combined_best_match[0]}', Score: {best_match[1]}")
                                if combined_best_match[1] >= 80:
                                    line = line.replace(next_word, combined_best_match[0])

                replace_string = selected_clean_word if root_name_next_word == '' else selected_clean_word + " " + root_name_next_word
                chosen_word = chosen_word + " " + root_name_next_word if (
                        root_name_next_word != '' and not found_combined_words == False) else chosen_word
                # logging.info("word selected : " + selected_clean_word )
                # logging.info("word replace_string : " + replace_string )
                # logging.info("word chosen_word : " + chosen_word )
                # logging.info("line : " + line + "  -  word index : " + str(word_index))

                line = line.replace(replace_string, " " + chosen_word)
                word_indices.append(word_index)  # Append the word index if a suitable word was found
            else:
                # if the line didn't contain a number and the first word is less tha 4 character,
                # then we need to store his index for structure Data ('GPM') : word_index = 0
                if len(line) > 10:
                    word_indices.append(0)
                # word_indices.append(-1)  # Append -1 if no suitable word was found

            # # WARNING !! this script is word for some word and makes a mistake for other !!!
            # Correct the presentation terms from the identified index onwards
            corrected_text = correct_presentation_terms(line, word_index, constants.content_correction, 80)

            # logging.info("line 1: " + corrected_text)
            # combined_text.append(corrected_text)
            combined_text.append(line)
            # logging.info("line 2: " + line)

        return word_indices, '\n'.join(combined_text)

    except Exception as e:
        logging.error(f"Error in combine_ocr_outputs: {traceback.format_exc()}")
        raise e


"""-----************ Apply the filters and Fusion data ************ -----"""


def process_image(model, image, module):
    """Process an image and apply OCR using Pytesseract with different configurations."""
    try:
        # Using Pytesseract with default and advanced configurations
        pytesseract_text = get_pytesseract_text(image, module)
        pytesseract_advanced_text = get_pytesseract_advanced_text(image, module)

        # # Using EasyOCR
        # easyocr_text, easyocr_text_joined = get_easyocr_text(image)

        # Combining outputs from all OCR tools
        word_indices, fused_text = combine_ocr_outputs(model, pytesseract_text, CORRECT_WORDS, 20)

        return word_indices, fused_text.replace('--n', '\n'), pytesseract_text.replace('--n',
                                                                                       '\n'), pytesseract_advanced_text.replace(
            '--n', '\n')

    except Exception as e:
        logging.error(f"Error in process_image: {traceback.format_exc()}")
        raise e


"""-----************ Correct Numbers between Presentation and Dosages ************ -----"""


# check if word contain twi suffixes (ex: 10.0Gi60ML)
def find_first_suffix_end_index(part, suffixes):
    """Finds the end index of the first suffix in a part."""
    try:
        # Normalize the part for case insensitivity
        part_lower = part.lower()
        # Find all occurrences of suffixes in the part and their indices
        suffix_occurrences = []
        for suffix in suffixes:
            suffix_lower = suffix.lower()
            index = part_lower.find(suffix_lower)
            while index != -1:  # Find all occurrences of the suffix
                suffix_occurrences.append((suffix, index, len(suffix)))
                index = part_lower.find(suffix_lower, index + 1)

        # Filter overlapping suffixes
        suffix_occurrences = [s for s in suffix_occurrences if not any(
            s[1] > other[1] and s[1] + s[2] <= other[1] + other[2] for other in suffix_occurrences if s != other)]

        # Ensure there are at least two distinct suffix occurrences
        if len(suffix_occurrences) >= 2:
            # logging.info("suffix_occurrences", suffix_occurrences)
            suffix_occurrences.sort(key=lambda x: x[1])
            first_suffix = suffix_occurrences[0]
            end_index_of_first_suffix = first_suffix[1] + first_suffix[2]
            return True, end_index_of_first_suffix

        return False, None

    except Exception as e:
        logging.error(f"Error in find_first_suffix_end_index: {traceback.format_exc()}")
        raise e


# check if for the part that combine with the suffix (Pres/Dosage) and with number or some other characters
def check_combine_the_suffix_with_number(part, correction_dict, line):
    """Check and combine suffix with number if needed."""
    try:
        # Get the suffix from the part, can be: presentation or can be dosage
        check_in_dosage = check_presence(constants.DOSAGES, part)
        check_in_presentation = check_presence(constants.PRESENTATION, part)

        suffix = ''
        if check_in_dosage[0]:
            suffix = check_in_dosage[1]
        elif check_in_presentation[0]:
            suffix = check_in_presentation[1]

        index_start_suffix = part.lower().index(suffix.lower())
        index_end_suffix = index_start_suffix + len(suffix)

        # check if the suffix is the last part of word (ex: S1ML)
        if is_suffix_end_part(suffix.lower(), part.lower()):

            dosage_pres = part[index_start_suffix:]
            number_dos_pre = correct_number(part[:index_start_suffix])
            correct_term = number_dos_pre + dosage_pres

        # check if the suffix is the first part of word (ex: BL/1o0)
        else:
            # check if the suffix is the last part of word (ex: S1ML)
            dosage_pres = part[:index_end_suffix]
            number_dos_pre = correct_number(part[index_end_suffix:])
            correct_term = dosage_pres + number_dos_pre

        line = line.replace(part, correct_term)

        return line

    except Exception as e:
        logging.error(f"Error in check_combine_the_suffix_with_number: {traceback.format_exc()}")
        raise e


# check if for the part that not combine with the suffix (Pres/Dosage) and with number or some other characters
def check_not_combine_the_suffix_with_number(part, parts, index, correction_dict, line):
    """Check and correct parts that do not combine suffix with number."""
    try:
        dosage_pres = part

        # Check if the number is front of the suffix (ex: BT oS  or  BT / oS)
        if check_presence(constants.prs_have_numbers_in_front, part)[0]:
            if contains_number(parts[index + 1]) or parts[index + 1].lower() not in ['/', '|', 'de']:
                number_dos_pre = correct_number(parts[index + 1])
                correct_term = dosage_pres + " " + number_dos_pre
                replace_term = dosage_pres + " " + parts[index + 1]


            else:
                number_dos_pre = correct_number(parts[index + 2])
                correct_term = dosage_pres + " " + parts[index + 1] + " " + number_dos_pre
                replace_term = dosage_pres + " " + parts[index + 1] + " " + parts[index + 2]

            line = line.replace(replace_term, correct_term)

        # Check if the number is before of the suffix (ex: iS CP  or  1o ML)
        # and don't check for previous word that have another suffix (ex: BT/10o CP)
        else:
            # correct (ex: iS CP  or  1o ML __to__ 15 CP  or  10 ML )
            if not check_presence(constants.DOSAGES, parts[index - 1])[0] and not check_presence(constants.PRESENTATION,
                                                                                                 parts[index - 1])[0]:
                number_dos_pre = correct_number(parts[index - 1])
                correct_term = number_dos_pre + " " + dosage_pres
                replace_term = parts[index - 1] + " " + dosage_pres

                line = line.replace(replace_term, correct_term)

            # correct (ex: BT/10o CP __to__ BT/100 CP)
            else:
                part = parts[index - 1]
                suffix = check_presence(constants.PRESENTATION, part)[1] \
                    if check_presence(constants.PRESENTATION, part)[0] \
                    else check_presence(constants.DOSAGES, part)[1]

                index_start_suffix = part.lower().index(suffix.lower())
                index_end_suffix = index_start_suffix + len(suffix)
                number_dos_pre = correct_number(part[index_end_suffix:])

                correct_term = suffix + number_dos_pre if index_start_suffix == 0 else number_dos_pre + suffix

                line = line.replace(part, correct_term)

        return line

    except Exception as e:
        logging.error(f"Error in check_not_combine_the_suffix_with_number: {traceback.format_exc()}")
        raise e


# Function to apply regex corrections based on patterns
def apply_regex_corrections_of_numbers_dosage_pres(row, correction_dict):
    """Apply regex corrections based on patterns."""
    try:
        # remove two space and replace it with one space
        line = row.replace('  ', ' ')
        parts = line.split()

        # Specific correction
        line = row.replace('SMG', '5MG')

        for index, part in enumerate(parts[1:], start=1):  # This skips the first element 'root_name' of the list
            check_in_dosage = check_presence(constants.DOSAGES, part)
            check_in_presentation = check_presence(constants.PRESENTATION, part)

            # check if for the part that combine Two the suffix (Dosage) and with number or some other characters
            # (ex: 1Gi200MG must be 1G/200MG)
            result, end_index = find_first_suffix_end_index(part, constants.DOSAGES)
            first_part_number_suffix = part[:end_index]
            second_part_number_suffix = part[(end_index + 1):] if result else None

            # if the part contain two suffix (ex: tQ1Gf12SMG => 701G/125MG)
            # and the second word is None (ex: 24SACH => there is 'SACH' and 'CH' but there is not two words)
            if result and second_part_number_suffix:

                pipe = part[end_index:end_index + 1]

                replace = first_part_number_suffix + pipe + second_part_number_suffix
                to_replace = first_part_number_suffix + '/' + second_part_number_suffix

                line = line.replace(replace, to_replace)
                # correct the number of first suffiex
                line = check_combine_the_suffix_with_number(first_part_number_suffix, correction_dict, line)
                # correct the number of first suffiex
                line = check_combine_the_suffix_with_number(second_part_number_suffix, correction_dict, line)



            # check if for the part that combine with the suffix (Pres/Dosage) and with number
            # (ex: S1MG)
            elif ((check_in_dosage[1] is not None and contains_number(part)) or
                  (check_in_presentation[1] is not None and contains_number(part))):

                line = (check_combine_the_suffix_with_number
                        (part, correction_dict, line))


            # check if for the part that not combine with the suffix (Pres/Dosage) and with number or some other characters
            # (ex: BL/ 1.0  or  &0 CPR  or BT/10o CP)
            elif is_exact_match_hold_name_with_value(part, constants.DOSAGES)[1] is not None or \
                    is_exact_match_hold_name_with_value(part, constants.PRESENTATION)[1] is not None:

                line = check_not_combine_the_suffix_with_number(part, parts, index, correction_dict, line)

            # # WARNING !! this script is word for some word and makes a mistake for other !!!
            # # check if for the part that combine with the suffix (Pres/Dosage) and without number
            # # (ex: SiMG)
            # elif ((check_in_dosage[1] is not None and contains_number(part)) == False or
            #       (check_in_presentation[1] is not None and not contains_number(part) == False)):
            #
            #     line = (check_combine_the_suffix_with_number
            #             (part, correction_dict, line))

        return line

    except Exception as e:
        logging.error(f"Error in apply_regex_corrections_of_numbers_dosage_pres: {traceback.format_exc()}")
        raise e


# Remove head of the table based on specific rules
def remove_head_table(lines: List[str], threshold: int = 80, model: str = 'GLOBAL') -> List[str]:
    """
    Remove unwanted lines from a table based on specified rules.

    RULE 000:
      Remove lines containing specific words ('désignation', 'designation', 'destination',
      'design', 'no lot', 'DATE CODE', 'PEREM PRODUIT') with fuzzy match >= 85% (case insensitive),
      and remove all lines before it.

    With a modified RULE 2:
      If a line has no number, we skip it.
      But if the next line also has no number, we remove *both* lines
      and also remove all previously filtered lines.
    """
    if not lines:
        logging.info("DEBUG: No lines to process.")
        return []

    def contains_number(line: str) -> bool:
        return any(char.isdigit() for char in line)

    def contains_letter(line: str) -> bool:
        return any(char.isalpha() for char in line)

    def is_strict_date_pattern(line: str) -> bool:
        # Match something like XX/XX/XXXX or XX-XX-XXXX with no spaces
        # Example: 01/02/2024, 12-31-2025
        return bool(re.search(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{4}\b', line))

    # RULE 000: Remove lines containing specific fuzzy-matched keywords and all lines before it
    fuzzy_keywords = ['désignation', 'designation', 'destination', 'no lot', 'DATE CODE', 'PEREM PRODUIT']
    matched_index = None

    for i, line in enumerate(lines):
        lower_line = line.lower()
        if any(fuzz.partial_ratio(keyword.lower(), lower_line) >= 98 for keyword in fuzzy_keywords):
            matched_index = i
            logging.info(f"DEBUG (rule 000): Line {i + 1!r} matches fuzzy keyword rule. Removing it and all lines before it: {repr(line)}")
            break

    # If a match is found, remove all lines before and including the matched line
    if matched_index is not None:
        lines = lines[matched_index + 1:]

    filtered_lines = []
    header_detected = False

    i = 0  # We'll use a while loop to control the index
    while i < len(lines):
        line = lines[i]
        lower_line = line.lower().strip()

        # --- RULE 1: Check if line is a "header" line ---
        if not header_detected:
            current_header_words = sum(1 for w in constants.header_words if w in lower_line)

            # (a) containing >= 2 known header words
            # (b) containing NO digits
            if current_header_words >= 2 and not contains_number(line):
                logging.info(f"DEBUG (rule 01): Line {i + 1!r} removed by HEADER rule "
                      f"(contains >=2 header words and no digits): {repr(line)}")
                header_detected = True
                i += 1
                continue  # skip this line, don't add to filtered_lines

        # # --- RULE 2 (Modified): Remove lines that don't contain any number ---
        # if not contains_number(line):
        #     # Check if there is a "next line" and whether it also has no number
        #     if i + 1 < len(lines) and (not contains_number(lines[i + 1])):
        #         # If next line also has no number => remove both lines
        #         logging.info(f"DEBUG (rule 02+): Line {i+1!r} and Line {i+2!r} both have NO number. "
        #               f"Removing them and clearing all previously filtered lines.")
        #         # Clear everything we have so far:
        #         filtered_lines.clear()
        #         # Skip these two lines
        #         i += 2
        #         continue
        #     else:
        #         # Otherwise, just remove the current line
        #         logging.info(f"DEBUG (rule 02): Line {i + 1!r} removed by NO-NUMBER rule: {repr(line)}")
        #         i += 1
        #         continue

        # --- RULE 2 (Modified): Remove lines that don't contain any number ---
        if not contains_number(line):
            logging.info(f"DEBUG (rule 02): Line {i + 1!r} removed by NO-NUMBER rule: {repr(line)}")
            i += 1
            continue

        # --- RULE 3: Remove lines that don't contain any letter ---
        if not contains_letter(line):
            logging.info(f"DEBUG (rule 03): Line {i + 1!r} removed by NO-LETTER rule: {repr(line)}")
            i += 1
            continue

        # --- RULE 4: Remove lines containing a date pattern (xx/xx/xxxx), except for model 'SPR' ---
        if is_strict_date_pattern(line) and 'spr' not in model.lower():
            logging.info(f"DEBUG (rule 04): Line {i + 1!r} removed by DATE rule: {repr(line)}")
            i += 1
            continue

        # --- RULE 5: Remove lines containing specific words (strict exact-match in split) ---
        specific_words = ['arret', 'avoirs', 'avoir', 'du', 'au', 'total', 'réf', 'ref', 'tva', 'code', 'periode', 'comul',
                          'taux', 'ttc', 'net' 'debut', 'page', 'tuyaux', 'ligne', 'tout', 'colis',
                          'T.U.G.', 'T.U.G', 'T.Q.L.', 'T.Q.L', 'merci', 'contacter', 'ligne']
        tokenized = lower_line.split()
        if any(word in tokenized for word in specific_words):
            logging.info(f"DEBUG (rule 05): Line {i + 1!r} removed by SPECIFIC-WORDS rule: {repr(line)}")
            i += 1
            continue

        # If it got here, it passes all filters
        filtered_lines.append(line)

        i += 1  # Move to next line

    # Print the final lines that survived
    logging.info("\nDEBUG: LINES THAT PASSED ALL RULES:")
    for idx, line in enumerate(filtered_lines, 1):
        logging.info(f"{idx:>2}: {line}")

    return filtered_lines


# Remove All lines less than 5 words
def remove_lines_with_few_words(lines: List[str], min_word_count: int = 4) -> List[str]:
    """
    Remove lines that have fewer than a specified number of words.

    :param lines: List of lines to process.
    :param min_word_count: Minimum number of words required for a line to be retained.
    :return: Filtered list of lines.
    """
    try:
        filtered_lines = [line for line in lines if len(line.split()) >= min_word_count]
        logging.info(f"Removed lines with fewer than {min_word_count} words.")
        return filtered_lines
    except Exception as e:
        logging.error(f"Error in remove_lines_with_few_words: {traceback.format_exc()}")
        raise e


# Normalize date based on model-specific formats
def normalize_date(date_str, model):
    """
    Normalize and validate date based on model-specific formats.
    :param date_str: The date string to normalize.
    :param model: The model to determine the expected date format.
    :return: Normalized date in 'dd/mm/yyyy' format or None if invalid.
    """
    try:
        # Replace common OCR errors
        date_str = date_str.replace('o', '0').replace('O', '0').replace('l', '1').replace('I', '1')

        # Define model-specific date formats
        model_date_formats = {
            'SPR': ['%d/%m/%Y'],
            'SOPHACA': [ '%m%y'],  # SOPHACA expects mm/yy or mmyy
            'COOPER_PHARMA_CASA': ['%m/%y', '%m%y'],
            'GPM': ['%m/%y', '%m%y'],
            # Add more models as needed
        }

        # Get expected formats for the model
        expected_formats = model_date_formats.get(model, ['%d/%m/%Y'])

        # Iterate through expected formats to parse the date
        for fmt in expected_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                # Convert two-digit year to four-digit year
                if parsed_date.year < 100:
                    parsed_date = parsed_date.replace(year=2000 + parsed_date.year)
                # Ensure year is >= 2000
                if parsed_date.year >= 2000:
                    return parsed_date.strftime('%d/%m/%Y')
            except ValueError:
                continue  # Try the next format if the current one fails

        # If no formats match, return None
        return None

    except Exception as e:
        logging.error(f"Error in normalize_date: {traceback.format_exc()}")
        return None

"""-----************ OCR Final for Table part ************ -----"""


def OCR_All_TABLE(image_path, model, module='default'):
    """Perform OCR on the table part of the image."""
    try:
        image = Image.open(image_path)

        # # Step 1 : Extract text from the image
        # # Step 2 : Correct Root Name
        # # Step 3 : Find and Correct Presentation && Dosage terms // Commented !!
        word_indices, text, tess, advanced_tess = process_image(model, image, module)

        lines = text.split('\n')

        # # Step 4: Filter out empty lines and lines with less than 10 characters
        filtered_lines = [line for line in lines if len(line.strip()) >= 10]

        # # # Step 5: Apply corrections for 'BT/' and 'BTE/' formats
        # filtered_and_formatted_lines = [correct_bt_bte_formats_v1(line) for line in filtered_lines]
        # filtered_and_formatted_lines = [correct_bt_bte_formats_v2(line) for line in filtered_lines]


        # # Step 5.1: Remove header line if found
        filtered_lines = remove_head_table(filtered_lines, model =model)

        # Step 5.2: Remove lines with fewer than 5 words
        filtered_lines = remove_lines_with_few_words(filtered_lines, min_word_count=5)

        # # Step 6: Remove double space and replace its by one space
        data_without_two_space = replace_double_spaces(filtered_lines)  # was filtered_and_formatted_lines
        
        logging.info('data_without_two_space : %s', data_without_two_space)

        # # # Step 7: Correct Numbers data for Presentation and Dosages
        corrected_numbers_lines = [apply_regex_corrections_of_numbers_dosage_pres(line, constants.quantity_correction_dict)
                                   for line in data_without_two_space]

        logging.info('corrected_numbers_lines : %s', corrected_numbers_lines)

        # # # Step 8: Correct Numbers data for Presentation and Dosages
        corrected_additional_lines, additional_section_start_index = models_utils.process_model_text_additional_section(
            model, corrected_numbers_lines)

        logging.info('additional_section_start_index : %s', additional_section_start_index)

        # # # # # Step 9 ? : Correct Numbers (Qty, PPV, PU, TTC, ...)
        corrected_numbers_prices, attribute_indexes = models_utils.correct_chiffres_calculated(corrected_additional_lines,
                                                                                               model,
                                                                                               additional_section_start_index)
        logging.info('attribute_indexes : ', attribute_indexes)
        #
        # # # Final Step : variable contain final result to return
        data_to_loop = corrected_numbers_prices  # was corrected_numbers_lines


        data_final = DocumentModel()
        tnp_result = None

        # # Step 10: Structuring Data for each Model:

        # data_final, tnp_result = models_utils.structure_data(data_to_loop, attribute_indexes,
        #                                                                 additional_section_start_index)

        logging.info("DATA STRUCTURING STARTED FOR THE MODEL : %s", model)
        
        if model in (constants.COOPER_PHARMA_CASA):
            data_final, tnp_result = models_utils.structure_data_COOPER_PHARMA_CASA(data_to_loop, attribute_indexes,
                                                                        additional_section_start_index)
            logging.info("DATA STRCUTING FINISHED FOR THE MODEL COOPER_PHARMA_CASA")
            # logging.info(data_final.to_json())

        elif model in (constants.GPM, constants.CPRE, constants.SOPHANORD): 
            data_final, tnp_result = models_utils.structure_data_GPM(data_to_loop, attribute_indexes,
                                                                     additional_section_start_index)
            logging.info("DATA STRCUTING FINISHED FOR THE MODEL GPM")
            # logging.info(data_final.to_json())

        elif model in (constants.SOPHADIMS, constants.SOPHAFAS, constants.SOPHACHARK, constants.RECAMED, constants.REPHAK, constants.SOPHASAIS):
            data_final, tnp_result = models_utils.structure_data_SOPHADIMS(data_to_loop, attribute_indexes,
                                                                           additional_section_start_index)
            logging.info("DATA STRCUTING FINISHED FOR THE MODEL SOPHADIMS")
            # logging.info(data_final.to_json())

        elif model in (constants.SPR):
            data_final, tnp_result = models_utils.structure_data_SPR(data_to_loop, attribute_indexes,
                                                                     additional_section_start_index)
            logging.info("DATA STRCUTING FINISHED FOR THE MODEL SPR")
            # logging.info(structured_data)

        elif model in (constants.SOPHACA, constants.SOPHAGHARB, constants.UGP, constants.GIPHAR, constants.DIPHARM):
            data_final, tnp_result = models_utils.structure_data_SOPHACA(data_to_loop, attribute_indexes,
                                                                         additional_section_start_index)
            logging.info("DATA STRCUTING FINISHED FOR THE MODEL SOPHACA")
            # logging.info(structured_data)
        #
        else:
            data_final = DocumentModel()
            logging.error(f"Model {model} is not supported.")

        # Step 11: Date Normalization
        documentModel = DocumentModel()
        documentModel.table = []

        # Normalize designation in table rows
        logging.info('data_final.table : %s', data_final.table)
        for row in data_final.table:
            # Clean designation
            if row.designation:
                original_designation = row.designation
                words = row.designation.split()
                # Skip invalid words at the start
                index = 0
                while index < len(words) and not is_valid_word_2(words[index]):
                    index += 1
                # Take the words from the first valid word onwards
                filtered_words = words[index:]
                row.designation = ' '.join(filtered_words)

                # Log if designation becomes empty after filtering
                if original_designation and not row.designation:
                    logging.warning(f"Designation became empty after filtering: '{original_designation}' -> '{row.designation}' (ID: {row.id})")
            else:
                logging.warning(f"Row has empty designation from start (ID: {row.id})")
            # Normalize date
            if hasattr(row, 'date_per') and row.date_per:
                normalized_date = normalize_date(row.date_per, model)
                row.date_per = normalized_date if normalized_date else None
            # Append to documentModel.table
            documentModel.table.append(row)

        return data_to_loop, documentModel, tnp_result, tess

    except Exception as e:
        logging.error(f"Error in OCR_All_TABLE: {traceback.format_exc()}")
        raise e



"""-----************ Testing ************ -----"""

# image_path = '../data/output_preprocessing/BL_table_cropped_processed_C_Hide.jpg'
# model = 'SOPHACA'
# result = OCR_All_TABLE(image_path, model)
# print (result)
