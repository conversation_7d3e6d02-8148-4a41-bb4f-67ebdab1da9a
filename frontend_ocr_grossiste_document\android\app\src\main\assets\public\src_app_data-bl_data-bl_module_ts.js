"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_data-bl_data-bl_module_ts"],{

/***/ 87490:
/*!***************************************************!*\
  !*** ./src/app/data-bl/data-bl-routing.module.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DataBLPageRoutingModule: () => (/* binding */ DataBLPageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _data_bl_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./data-bl.page */ 8548);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _DataBLPageRoutingModule;




const routes = [{
  path: '',
  component: _data_bl_page__WEBPACK_IMPORTED_MODULE_0__.DataBLPage
}];
class DataBLPageRoutingModule {}
_DataBLPageRoutingModule = DataBLPageRoutingModule;
_DataBLPageRoutingModule.ɵfac = function DataBLPageRoutingModule_Factory(t) {
  return new (t || _DataBLPageRoutingModule)();
};
_DataBLPageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _DataBLPageRoutingModule
});
_DataBLPageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](DataBLPageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 80715:
/*!*******************************************!*\
  !*** ./src/app/data-bl/data-bl.module.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DataBLPageModule: () => (/* binding */ DataBLPageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _data_bl_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./data-bl-routing.module */ 87490);
/* harmony import */ var _data_bl_page__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./data-bl.page */ 8548);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
var _DataBLPageModule;





 // Import SharedModule

class DataBLPageModule {}
_DataBLPageModule = DataBLPageModule;
_DataBLPageModule.ɵfac = function DataBLPageModule_Factory(t) {
  return new (t || _DataBLPageModule)();
};
_DataBLPageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
  type: _DataBLPageModule
});
_DataBLPageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _data_bl_routing_module__WEBPACK_IMPORTED_MODULE_0__.DataBLPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](DataBLPageModule, {
    declarations: [_data_bl_page__WEBPACK_IMPORTED_MODULE_1__.DataBLPage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _data_bl_routing_module__WEBPACK_IMPORTED_MODULE_0__.DataBLPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule]
  });
})();

/***/ }),

/***/ 8548:
/*!*****************************************!*\
  !*** ./src/app/data-bl/data-bl.page.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DataBLPage: () => (/* binding */ DataBLPage)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _services_signal_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/signal.service */ 56658);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../custom-icon/custom-icon.component */ 40816);

var _DataBLPage;








const _c0 = ["topSwiper"];
const _c1 = ["bottomSwiper"];
const _c2 = () => ["/profile"];
function DataBLPage_swiper_slide_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "swiper-slide", 19)(1, "ion-row")(2, "ion-col", 20)(3, "ion-card", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](4, "img", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "div", 23)(6, "ion-card-header")(7, "ion-card-subtitle");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "ion-card-content")(10, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](12, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()()()();
  }
  if (rf & 2) {
    const slide_r2 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("src", slide_r2.image, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](slide_r2.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](slide_r2.date);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("Page ", slide_r2.page_index, "");
  }
}
function DataBLPage_swiper_slide_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "swiper-slide")(1, "ion-card-content")(2, "div", 24)(3, "ion-row")(4, "ion-col")(5, "ion-item", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "ion-input", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "ion-row")(8, "ion-col")(9, "ion-item", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](10, "ion-input", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](11, "ion-col")(12, "ion-item", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](13, "ion-input", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](14, "ion-row")(15, "ion-col")(16, "ion-item", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](17, "ion-input", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](18, "ion-col")(19, "ion-item", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](20, "ion-input", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](21, "ion-row")(22, "ion-col")(23, "ion-item", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](24, "ion-input", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()()();
  }
  if (rf & 2) {
    const product_r3 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpropertyInterpolate"]("value", product_r3.designation);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("clearInput", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpropertyInterpolate"]("value", product_r3.quantity);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("clearInput", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpropertyInterpolate"]("value", product_r3.expiryDate);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("clearInput", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpropertyInterpolate"]("value", product_r3.ppv);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("clearInput", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpropertyInterpolate"]("value", product_r3.pph);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("clearInput", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpropertyInterpolate"]("value", product_r3.total);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("clearInput", true);
  }
}
class DataBLPage {
  // slidesData = [
  //   {
  //     image: 'assets/doc1.jpg',
  //     title: 'Scan 01:11:2023 01:57:06',
  //     date: 'Aujourd\'hui',
  //     page_index: '1',
  //     products: [
  //       { designation: 'REPADINA 10OVUL OV', quantity: 2, expiryDate: '2023-12-01', ppv: 99.00, pph: 66.50, total: 133.00 },
  //       { designation: 'APIXOL SPR AD 30M AE', quantity: 3, expiryDate: '2024-01-01', ppv: 153.40, pph: 101.00, total: 303.00 },
  //       { designation: 'MAXICLAV 1G/125MG 24SACH SA', quantity: 4, expiryDate: '2024-04-01', ppv: 36.70, pph: 24.00, total: 96.00 },
  //     ]
  //   },
  //   {
  //     image: 'assets/doc2.png',
  //     title: 'Scan 01:11:2023 02:57:06',
  //     date: 'Aujourd\'hui',
  //     page_index: '2',
  //     products: [
  //       { designation: 'BIOMARTIAL 30 GELULE', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },
  //       { designation: 'ACFOL 5MG BT 28CP', quantity: 10, expiryDate: '2024-02-01', ppv: 26.20, pph: 17.34, total: 173.39 },
  //       { designation: 'BIOMARTIAL PLUS 30CP', quantity: 2, expiryDate: '2025-01-01', ppv: 133.00, pph: 93.10, total: 186.20 },
  //       { designation: 'MENOPHYT BT/30CPS', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },
  //     ]
  //   },
  //   {
  //     image: 'assets/doc3.png',
  //     title: 'Scan 01:11:2023 03:57:06',
  //     date: 'Aujourd\'hui',
  //     page_index: '3',
  //     products: [
  //       { designation: 'CARBOFLORE BT/30GLLES', quantity: 2, expiryDate: '2023-12-01', ppv: 99.00, pph: 66.50, total: 133.00 },
  //       { designation: 'DIGESTAL BT/30CPS', quantity: 3, expiryDate: '2024-01-02', ppv: 153.40, pph: 101.00, total: 303.00 },
  //       { designation: 'FORTIVISION BT/30DG', quantity: 4, expiryDate: '2024-04-24', ppv: 36.70, pph: 24.00, total: 96.00 },
  //     ]
  //   },
  //   {
  //     image: 'assets/doc2.png',
  //     title: 'Scan 01:11:2023 02:57:06',
  //     date: 'Aujourd\'hui',
  //     page_index: '2',
  //     products: [
  //       { designation: 'LEVUPHTA 0.05% COLLYRE', quantity: 2, expiryDate: '2023-12-01', ppv: 99.00, pph: 66.50, total: 133.00 },
  //       { designation: 'CETIRAL 10MG BT/15 CP', quantity: 3, expiryDate: '2024-01-01', ppv: 153.40, pph: 101.00, total: 303.00 },
  //       { designation: 'PURCARB BTE/30 GELULES', quantity: 4, expiryDate: '2024-04-01', ppv: 36.70, pph: 24.00, total: 96.00 },
  //     ]
  //   },
  //   {
  //     image: 'assets/doc3.png',
  //     title: 'Scan 01:11:2023 03:57:06',
  //     date: 'Aujourd\'hui',
  //     page_index: '3',
  //     products: [
  //       { designation: 'CATAFLAM 50MG BT/10 CP', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },
  //       { designation: 'ANAPRED 20MG BTE/20 CPS', quantity: 10, expiryDate: '2024-02-01', ppv: 26.20, pph: 17.34, total: 173.39 },
  //       { designation: 'NEOFORTAN 160MG BT/10 CP EF', quantity: 2, expiryDate: '2025-01-01', ppv: 133.00, pph: 93.10, total: 186.20 },
  //       { designation: 'CARDIX 6.25 MG BT/28 CP', quantity: 6, expiryDate: '2023-05-01', ppv: 123.00, pph: 86.10, total: 516.60 },
  //     ]
  //   },
  // ];
  constructor() {
    this.currentSlideProducts = [];
    this.currentProductIndex = 1;
    this.currentPageIndex = 1;
    this.slidesData = [];
    this.navCtrl = (0,_angular_core__WEBPACK_IMPORTED_MODULE_3__.inject)(_ionic_angular__WEBPACK_IMPORTED_MODULE_4__.NavController);
    this.signalService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_3__.inject)(_services_signal_service__WEBPACK_IMPORTED_MODULE_1__.SignalService);
    this.loadingController = (0,_angular_core__WEBPACK_IMPORTED_MODULE_3__.inject)(_ionic_angular__WEBPACK_IMPORTED_MODULE_5__.LoadingController);
    this.alertController = (0,_angular_core__WEBPACK_IMPORTED_MODULE_3__.inject)(_ionic_angular__WEBPACK_IMPORTED_MODULE_5__.AlertController);
  }
  ngOnInit() {
    var _this$topSwiper, _this$bottomSwiper;
    this.slidesData = this.signalService.getTransformedData();
    if (this.slidesData.length === 0) {
      this.navCtrl.navigateBack('/scan-bl');
    }
    (_this$topSwiper = this.topSwiper) === null || _this$topSwiper === void 0 || _this$topSwiper.nativeElement.swiper.update();
    (_this$bottomSwiper = this.bottomSwiper) === null || _this$bottomSwiper === void 0 || _this$bottomSwiper.nativeElement.swiper.update();
    console.log('Received data:', this.slidesData);
  }
  ngAfterViewInit() {
    this.initSwipers();
  }
  onSlideChange() {
    console.log("changedslidechange");
    this.initSwipers();
  }
  initSwipers() {
    if (this.topSwiper && this.topSwiper.nativeElement && this.bottomSwiper && this.bottomSwiper.nativeElement) {
      const topSwiperInstance = this.topSwiper.nativeElement.swiper;
      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;
      this.updateBottomSwiper();
    }
  }
  updateBottomSwiper() {
    if (this.topSwiper && this.topSwiper.nativeElement) {
      var _this$slidesData$acti;
      const topSwiperInstance = this.topSwiper.nativeElement.swiper;
      const activeIndex = topSwiperInstance.realIndex;
      this.currentPageIndex = activeIndex + 1; // Update the current page index
      this.currentSlideProducts = ((_this$slidesData$acti = this.slidesData[activeIndex]) === null || _this$slidesData$acti === void 0 ? void 0 : _this$slidesData$acti.products) || [];
      // this.currentSlideProducts = this.slidesData[activeIndex].products;
      this.updateProductIndex();
    }
  }
  updateProductIndex() {
    if (this.bottomSwiper && this.bottomSwiper.nativeElement) {
      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;
      const activeProductIndex = bottomSwiperInstance.realIndex;
      this.currentProductIndex = activeProductIndex + 1;
    }
  }
  slideNext() {
    if (this.bottomSwiper && this.bottomSwiper.nativeElement) {
      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;
      bottomSwiperInstance.slideNext();
      this.updateProductIndex();
    }
  }
  slidePrev() {
    if (this.bottomSwiper && this.bottomSwiper.nativeElement) {
      const bottomSwiperInstance = this.bottomSwiper.nativeElement.swiper;
      bottomSwiperInstance.slidePrev();
      this.updateProductIndex();
    }
  }
  NewBL() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this.alertController.create({
        header: 'Voulez-vous créer un nouveau document ?',
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button-rename-doc cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Nouveau BL',
          cssClass: 'custom-alert-button-rename-doc rename',
          handler: () => {
            _this.signalService.removeAllData();
            localStorage.removeItem('selectedSupplier');
            _this.navCtrl.navigateForward('/scan-bl');
          }
        }]
      });
      yield alert.present();
    })();
  }
  EditCurrentBL() {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this2.alertController.create({
        header: 'Voulez-vous vraiment modifier les pages de ce document ?',
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button-rename-doc cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Modifier',
          cssClass: 'custom-alert-button-rename-doc rename',
          handler: () => {
            _this2.navCtrl.navigateRoot('/process-doc');
          }
        }]
      });
      yield alert.present();
    })();
  }
}
_DataBLPage = DataBLPage;
_DataBLPage.ɵfac = function DataBLPage_Factory(t) {
  return new (t || _DataBLPage)();
};
_DataBLPage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
  type: _DataBLPage,
  selectors: [["app-data-bl"]],
  viewQuery: function DataBLPage_Query(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵviewQuery"](_c0, 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵviewQuery"](_c1, 7);
    }
    if (rf & 2) {
      let _t;
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵloadQuery"]()) && (ctx.topSwiper = _t.first);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵloadQuery"]()) && (ctx.bottomSwiper = _t.first);
    }
  },
  decls: 34,
  vars: 7,
  consts: [["topSwiper", ""], ["bottomSwiper", ""], [1, "data-bl-wrapper"], [1, "section-title"], ["effect", "coverflow", "grab-cursor", "true", "centered-slides", "true", "slides-per-view", "auto", "coverflow-effect-rotate", "50", "coverflow-effect-stretch", "0", "coverflow-effect-depth", "100", "coverflow-effect-modifier", "1", "coverflow-effect-slide-shadows", "true", 1, "swiper", "top-swiper", 3, "swiperslidechange"], ["class", "test", 4, "ngFor", "ngForOf"], [1, "card-data-bl"], ["name", "arrow-left", 3, "click"], [1, "content-header"], ["name", "arrow-right", 3, "click"], [1, "hr-card"], [1, "bottom-swiper"], [4, "ngFor", "ngForOf"], ["size", "small", 1, "menu-button", "active", 3, "click"], ["name", "files"], [1, "menu-button-middle", 3, "click"], ["name", "extract"], ["size", "small", 1, "menu-button", 3, "routerLink"], ["name", "settings"], [1, "test"], ["size", "12", 1, "slide-content"], [1, "card-doc"], [1, "slide-image", 3, "src"], [1, "content-global-card"], [1, "data-bl"], ["lines", "none", 1, "input-item"], ["label", "D\u00E9signation", "labelPlacement", "stacked", "placeholder", "Enter la D\u00E9signation du produit ...", 3, "clearInput", "value"], ["label", "Quantit\u00E9 Livr\u00E9", "labelPlacement", "stacked", "placeholder", "Enter la Quantit\u00E9 livr\u00E9", "type", "number", 3, "clearInput", "value"], ["lines", "none", 1, "input-item", "input-item-date"], ["label", "Date de p\u00E9remption", "labelPlacement", "stacked", "placeholder", "Enter la Date de p\u00E9remption", "type", "date", 3, "clearInput", "value"], ["label", "Prix (PPV)", "labelPlacement", "stacked", "placeholder", "Enter le Prix (PPV)", "type", "number", 3, "clearInput", "value"], ["label", "Prix (PPH)", "labelPlacement", "stacked", "placeholder", "Enter la Prix (PPH)", "type", "number", 3, "clearInput", "value"], ["label", "Total (TTC)", "labelPlacement", "stacked", "placeholder", "Enter le Total (TTC)", "type", "number", 3, "clearInput", "value"]],
  template: function DataBLPage_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "Extraction des donn\u00E9es");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "ion-content", 2)(5, "h2", 3);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6, "Pages");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "swiper-container", 4, 0);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("swiperslidechange", function DataBLPage_Template_swiper_container_swiperslidechange_7_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx.onSlideChange());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, DataBLPage_swiper_slide_9_Template, 14, 4, "swiper-slide", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "ion-card", 6)(11, "ion-card-header")(12, "app-custom-icon", 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function DataBLPage_Template_app_custom_icon_click_12_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx.slidePrev());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "div", 8)(14, "ion-card-title");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](15);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](16, "ion-card-subtitle");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](17);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](18, "app-custom-icon", 9);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function DataBLPage_Template_app_custom_icon_click_18_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx.slideNext());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](19, "hr", 10);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](20, "swiper-container", 11, 1);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](22, DataBLPage_swiper_slide_22_Template, 25, 12, "swiper-slide", 12);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](23, "ion-footer")(24, "ion-toolbar")(25, "ion-buttons")(26, "ion-button", 13);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function DataBLPage_Template_ion_button_click_26_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx.EditCurrentBL());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](27, "app-custom-icon", 14);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](28, "ion-button", 15);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function DataBLPage_Template_ion_button_click_28_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx.NewBL());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](29, "app-custom-icon", 16);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](30, "span");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](31, "NOUVEAU");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](32, "ion-button", 17);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](33, "app-custom-icon", 18);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](9);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx.slidesData);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate2"]("Produit ", ctx.currentProductIndex, " / ", ctx.currentSlideProducts.length, "");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("Page ", ctx.currentPageIndex, "");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx.currentSlideProducts);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](10);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction0"](6, _c2));
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgForOf, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonButtons, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonCard, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonCardContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonCardHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonCardSubtitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonCardTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonCol, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonFooter, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonInput, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonItem, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonRow, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonToolbar, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.NumericValueAccessor, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.TextValueAccessor, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.RouterLinkDelegate, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterLink, _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_2__.CustomIconComponent],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\nion-content[_ngcontent-%COMP%]::part(scroll) {\n  scrollbar-width: none !important;\n  -ms-overflow-style: none !important;\n}\n\nion-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  --offset-top: 0px !important;\n}\n\nion-header[_ngcontent-%COMP%] {\n  height: 80px;\n  --border: 0;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  height: 100%;\n  --border: 0;\n  --border-width: 0;\n  display: flex;\n  justify-content: center;\n  align-items: flex-end;\n  flex-direction: row;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%] {\n  font-size: 26px;\n  font-weight: 700;\n  color: #2f4fcd;\n  text-align: left;\n  width: 100%;\n  padding-left: 2rem;\n}\n\n  ion-header ion-toolbar app-custom-icon .custom-icon {\n  width: 40px !important;\n  height: 40px !important;\n  margin-right: 5px !important;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  color: #101010;\n  padding-right: 1rem;\n}\n\n.data-bl-wrapper[_ngcontent-%COMP%] {\n  background: url(\"/assets/bg-scan-bl.png\") no-repeat center center fixed;\n  background-size: cover;\n  height: 80%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  margin-top: 2rem;\n}\n\n  .scan-bl-wrapper .file-import-icon img {\n  width: 150px !important;\n  height: 150px !important;\n}\n\n  .scan-bl-wrapper .arrow-bottom-icon img {\n  width: 100px !important;\n  height: 100px !important;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\n  padding: 10px 50px 0 50px;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 22px;\n  font-weight: 700;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 12px;\n  text-align: justify;\n  padding: 5px 10px;\n}\n\n.scan-bl-content[_ngcontent-%COMP%] {\n  --background: #ffffff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n}\n\n.empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.document-icon[_ngcontent-%COMP%] {\n  font-size: 100px;\n  color: #c4c4c4;\n}\n\nion-label[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\nion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  font-weight: bold;\n  color: #404040;\n  font-size: 14px;\n}\n\nion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  color: #888888;\n  font-size: 14px;\n  margin-top: 10px;\n  margin-bottom: 10px;\n  opacity: 0.5;\n}\n\nh2[_ngcontent-%COMP%] {\n  color: #555555;\n  font-size: 18px;\n  margin-top: 20px;\n}\n\n.arrow-icon[_ngcontent-%COMP%] {\n  font-size: 30px;\n  color: #3b82f6;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  background-color: #e5e7eb;\n}\n\n.camera-button[_ngcontent-%COMP%] {\n  --background: #3b82f6;\n  --background-activated: #2563eb;\n  border-radius: 50%;\n  width: 60px;\n  height: 60px;\n  margin-top: -30px;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #3b82f6;\n}\n\nion-button[_ngcontent-%COMP%] {\n  --color: #3b82f6;\n}\n\nion-button[slot=icon-only][_ngcontent-%COMP%] {\n  --color: #3b82f6;\n}\n\nion-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  position: relative;\n  background-color: #dddbff;\n  height: 110px;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --border-width: 0;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%], ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-evenly;\n  align-items: center;\n  flex-direction: row;\n}\n\n  ion-button.menu-button app-custom-icon img {\n  width: 30px !important;\n  height: 30px !important;\n  color: #000;\n}\n\n  .menu-button.active app-custom-icon img {\n  color: #2f4fcd;\n}\n\n  .menu-button-middle {\n  background-color: #2f4fcd;\n  padding: 2px 12px;\n  border-radius: 14px;\n  width: 160px;\n  height: 60px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n  margin-bottom: 15px;\n}\n  .menu-button-middle app-custom-icon img {\n  width: 35px !important;\n  height: 35px !important;\n  color: #fff;\n}\n  .menu-button-middle span {\n  color: #fff;\n  font-weight: 500;\n  font-size: 16px;\n  padding-left: 10px;\n}\n\n.section-title[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  margin-bottom: 0;\n  color: #4b4b4b;\n  font-size: 16px;\n  margin-left: 16px;\n  opacity: 0.5;\n}\n\nswiper-container.swiper[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%] {\n  padding: 0;\n  margin: 0;\n  border-radius: 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin-top: 10px;\n  margin-left: 10px;\n}\n\n.card-doc[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: flex-start;\n  width: 90%;\n  height: 100%;\n  box-shadow: none;\n  margin: 0;\n  padding: 0;\n  border: 2px solid #e5ebfd;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.card-doc[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 150px;\n  object-fit: cover;\n}\n\n.card-doc[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 10px 20px;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: flex-start;\n}\n.card-doc[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-subtitle[_ngcontent-%COMP%] {\n  width: 100%;\n  font-size: 13px;\n  text-align: left;\n  font-weight: 600;\n  color: #202020;\n}\n\n.content-global-card[_ngcontent-%COMP%] {\n  width: 100%;\n  background-color: #fff;\n  border-top: 1px solid #e5ebfd;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.card-doc[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  width: 100%;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-between;\n  padding-bottom: 10px;\n}\n.card-doc[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1n) {\n  color: #4b4b4b;\n  font-size: 14px;\n  opacity: 0.5;\n}\n.card-doc[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2n) {\n  color: #070707;\n  font-size: 14px;\n  font-weight: 500;\n  opacity: 1;\n}\n\n\n\n.swiper[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.swiper[_ngcontent-%COMP%]   swiper-slide[_ngcontent-%COMP%] {\n  background-position: center;\n  background-size: cover;\n  width: 250px;\n}\n\n  .swiper swiper-slide .swiper-slide-shadow,   .swiper swiper-slide .swiper-slide-shadow-left,   .swiper swiper-slide .swiper-slide-shadow-right {\n  background: none !important;\n  --background: none !important;\n}\n\n.card-doc[_ngcontent-%COMP%] {\n  left: 0px !important;\n}\n\nswiper-container.bottom-swiper[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.card-data-bl[_ngcontent-%COMP%] {\n  width: 90%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: flex-start;\n  background-color: rgba(241, 244, 255, 0.4);\n  border-radius: 10px;\n  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);\n  margin: 40px 22px;\n}\n\n.card-data-bl[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  padding: 0 5px;\n}\n\n  .card-data-bl ion-item {\n  font-size: 16px;\n  font-weight: 600;\n  color: #050505;\n  text-align: left;\n  --background: none; \n\n  background-color: none;\n  padding: 0 10px;\n}\n\nion-item[_ngcontent-%COMP%]::part(native) {\n  padding-left: 0 !important;\n  --padding-start: 0px !important;\n  --inner-padding-end: 0px !important;\n}\n\n  .card-data-bl ion-item ion-input div.label-text-wrapper {\n  margin-bottom: 5px;\n  font-size: 14px;\n  color: #1F41BB;\n  font-weight: bold;\n}\n\n  .card-data-bl ion-item ion-input div.native-wrapper {\n  width: 100%;\n  font-size: 12px;\n  font-weight: 600;\n  color: #050505;\n  text-align: left;\n  margin: 5px 0;\n  padding: 8px 10px;\n  border: 1px solid #1F41BB;\n  border-radius: 10px;\n  --background: #fff; \n\n  background-color: #fff;\n}\n\n  .card-data-bl ion-row:last-child ion-item ion-input div.native-wrapper {\n  margin-bottom: 20px;\n}\n\n  .card-data-bl .native-wrapper {\n  display: flex !important;\n  justify-content: center !important;\n  align-items: center !important;\n  flex-direction: column !important;\n  padding: 14px 10px !important;\n}\n  .card-data-bl .native-wrapper button {\n  display: none;\n}\n\n.card-data-bl[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\n  padding-top: 30px;\n  padding-bottom: 10px;\n  text-align: center;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-direction: row;\n  width: 100%;\n}\n\n.card-data-bl[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-subtitle[_ngcontent-%COMP%] {\n  font-size: 12px;\n  font-weight: 500;\n  color: #202020;\n  margin-top: 5px;\n}\n\n.card-data-bl[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  font-size: 18px;\n  font-weight: bold;\n  color: #202020;\n}\n\n  .card-data-bl ion-card-header app-custom-icon {\n  color: #202020;\n  opacity: 0.5;\n  cursor: pointer;\n}\n  .card-data-bl ion-card-header app-custom-icon img {\n  width: 45px !important;\n  height: 45px !important;\n}\n  .card-data-bl ion-card-header app-custom-icon:first-child {\n  margin-left: 10px;\n}\n  .card-data-bl ion-card-header app-custom-icon:last-child {\n  margin-right: 10px;\n}\n\n.card-data-bl[_ngcontent-%COMP%]   .hr-card[_ngcontent-%COMP%] {\n  width: 80%;\n  height: 3px;\n  border-bottom: 2px dotted #8b8b8b;\n  padding: 0 20px;\n  margin-top: 0;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ })

}]);
//# sourceMappingURL=src_app_data-bl_data-bl_module_ts.js.map